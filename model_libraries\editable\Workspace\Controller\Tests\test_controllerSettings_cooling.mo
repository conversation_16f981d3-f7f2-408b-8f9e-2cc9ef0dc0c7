within Workspace.Controller.Tests;
model test_controllerSettings_cooling
  .Workspace.Controller.ControllerSettings_cooling controllerSettings(
    minfanfreq=100,
    maxfanfreq=1500)
    annotation (Placement(transformation(extent={{-36.0,20.0},{-16.0,40.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression SST(
    y=273.15)
    annotation (Placement(transformation(extent={{-80.0,14.0},{-60.0,34.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression Frequence(
    y=140)
    annotation (Placement(transformation(extent={{-80.0,-6.0},{-60.0,14.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression SH(
    y=5)
    annotation (Placement(transformation(extent={{-80.0,34.0},{-60.0,54.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression OAT(
    y=308.15)
    annotation (Placement(transformation(extent={{-78.0,52.0},{-58.0,72.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression LWT(
    y=280.15)
    annotation (Placement(transformation(extent={{-79.59846107234728,67.7764220849099},{-59.59846107234727,87.7764220849099}},origin={0.0,0.0},rotation=0.0)));
equation
  connect(SST.y,controllerSettings.measurementBus.T_sst)
    annotation (Line(points={{-59,24},{-46.8,24},{-46.8,29.456993148288937},{-37.55275989484145,29.456993148288937}},color={0,0,127}));
  connect(Frequence.y,controllerSettings.measurementBus.compressorFrequency)
    annotation (Line(points={{-59,4},{-45.8,4},{-45.8,29.456993148288937},{-37.55275989484145,29.456993148288937}},color={0,0,127}));
  connect(SH.y,controllerSettings.measurementBus.dT_ssh)
    annotation (Line(points={{-59,44},{-47.8,44},{-47.8,29.456993148288937},{-37.55275989484145,29.456993148288937}},color={0,0,127}));
  connect(OAT.y,controllerSettings.measurementBus.T_oat)
    annotation (Line(points={{-57,62},{-47.109952374916176,62},{-47.109952374916176,29.456993148288937},{-37.55275989484145,29.456993148288937}},color={0,0,127}));
  connect(LWT.y,controllerSettings.measurementBus.T_lwt)
    annotation (Line(points={{-58.59846107234726,77.7764220849099},{-44.28181276957331,77.7764220849099},{-44.28181276957331,29.456993148288937},{-37.55275989484145,29.456993148288937}},color={0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end test_controllerSettings_cooling;
