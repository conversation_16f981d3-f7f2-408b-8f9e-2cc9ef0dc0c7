{"version": "1", "models": [{"name": "Workspace.System_ASHP_R290.HPC.System_model.BaseCycles.OL_modular", "views": [{"name": "view", "stickies": ["eN14511_CO_2C.inst_net_cap", "eN14511_CO_2C.inst_net_pow", "sourceBrine.T", "sinkBrine.T", "BlocA.sourceAir.Tdb", "BlocA.compressor.summary.capacity", "BlocA.compressor.summary.T_dis", "BlocA.compressor.summary.P_compression"], "stickyPositions": [{"component": "sourceBrine", "x": -32.331665, "y": -88.634186}, {"component": "sinkBrine", "x": -35.55117, "y": 110.53655}, {"component": "BlocA", "x": -128.41939, "y": 33.619465}], "plots": []}, {"name": "New view", "stickies": ["BlocA.compressor.summary.P_compression", "BlocA.motor.fanMotor.Power_in", "BlocA.systemVariables.summary.CoolCap[1]"], "stickyPositions": [{"component": "BlocA", "x": -112.32437, "y": -31.770447}], "plots": []}, {"name": "New view 2", "stickies": ["BlocA.compressor.summary.P_compression", "BlocA.motor.fanMotor.Power_in", "BlocA.systemVariables.summary.CoolCap[1]", "BlocA.systemVariables.summary.EERcool"], "stickyPositions": [{"component": "BlocA", "x": -112.32437, "y": -31.770447}], "plots": []}]}, {"name": "Workspace.HPH_MODULAR_ILHAM", "views": [{"name": "New view", "stickies": ["BlocA.fanCurve.summary.speed", "pumpPoly.summary.P_pump", "pumpPoly.summary.P_motor"], "stickyPositions": [{"component": "BlocA", "x": -94.50468, "y": -86.90983}, {"component": "BlocA.fanCurve", "x": -16.0, "y": 1.1515847}, {"component": "pumpPoly", "x": -36.136833, "y": -107.719925}], "plots": []}, {"name": "New view 2", "stickies": ["speed_A.setPoint", "BlocA.fanCurve.summary.speed", "BlocA.systemVariables.summary.COPHeat", "BlocA.systemVariables.summary.HeatCap_total"], "stickyPositions": [{"component": "BlocA", "x": -61.880703, "y": 65.23036}, {"component": "speed_A", "x": -73.741745, "y": -48.0}], "plots": []}, {"name": "New view 3", "stickies": ["speed_A.setPoint", "BlocA.fanCurve.summary.speed", "BlocA.systemVariables.summary.COPHeat", "BlocA.systemVariables.summary.HeatCap_total", "BlocA.motor.shaftSpeed_set"], "stickyPositions": [{"component": "BlocA", "x": -61.880703, "y": 65.23036}, {"component": "speed_A", "x": -73.741745, "y": -48.0}, {"component": "BlocA.motor", "x": -48.74495, "y": -60.034573}], "plots": []}]}, {"name": "Workspace.HPC_MODULAR_ILHAM", "views": [{"name": "New view", "stickies": ["pumpPoly.summary.P_pump", "pumpPoly.summary.P_motor"], "stickyPositions": [{"component": "pumpPoly", "x": -66.32481, "y": -48.816315}], "plots": []}]}, {"name": "Workspace.TestCOMPRESSEUR", "views": [{"name": "New view", "stickies": ["compVS_2P.summary.Tsat_dis", "compVS_2P.summary.Tsat_suc", "liquid.summary.dTsh", "discharge.summary.dTsh", "speed.setPoint", "liquid.dTsh_fixed", "liquid.dTsh_set", "discharge.dTsh_set", "node.Sub_node.Tsat_set", "discharge.Tsat_set"], "stickyPositions": [{"component": "compVS_2P", "x": 18.482424, "y": -51.73174}, {"component": "liquid", "x": -117.42483, "y": 69.9459}, {"component": "discharge", "x": 14.260533, "y": 65.11793}, {"component": "speed", "x": -30.160809, "y": 49.999252}], "plots": []}]}, {"name": "Workspace.OPtifan", "views": [{"name": "New view", "stickies": ["node2.Tsat", "evapAir.summary.Qdr"], "stickyPositions": [{"component": "node2", "x": -12.0, "y": -54.0}, {"component": "evapAir", "x": -62.0, "y": -42.0}], "plots": []}, {"name": "New view 2", "stickies": ["motor.shaftSpeed_set", "actuator2.setPoint", "systemVariables.summary.HeatCap_total", "systemVariables.summary.COPHeat"], "stickyPositions": [{"component": "motor", "x": -106.03263, "y": 57.818005}], "plots": []}, {"name": "New view 3", "stickies": ["motor.shaftSpeed_set", "actuator2.setPoint", "systemVariables.summary.HeatCap_total", "systemVariables.summary.COPHeat"], "stickyPositions": [{"component": "motor", "x": -106.03263, "y": 57.818005}], "plots": []}, {"name": "New view 4", "stickies": ["motor.shaftSpeed_set", "actuator2.setPoint", "systemVariables.summary.HeatCap_total", "systemVariables.summary.COPHeat", "node2.Tsat"], "stickyPositions": [{"component": "motor", "x": -106.03263, "y": 57.818005}, {"component": "node2", "x": -45.37812, "y": -7.4261036}], "plots": []}, {"name": "New view 5", "stickies": ["motor.shaftSpeed_set", "actuator2.setPoint", "systemVariables.summary.HeatCap_total", "systemVariables.summary.COPHeat", "node2.Tsat", "compVS_2P.summary.Tsat_dis", "compVS_2P.summary.Tsat_suc", "compVS_2P.summary.P_compression", "motor.summary.power_motor"], "stickyPositions": [{"component": "motor", "x": -106.03263, "y": 57.818005}, {"component": "node2", "x": -44.754833, "y": -8.3610325}, {"component": "compVS_2P", "x": 80.982796, "y": 48.83221}], "plots": []}]}, {"name": "Workspace.OPtifanFroid", "views": [{"name": "New view", "stickies": ["motor.shaftSpeed_set", "actuator2.setPoint", "systemVariables.summary.EERcool", "node2.Tsat", "systemVariables.summary.CoolCap_total"], "stickyPositions": [{"component": "node2", "x": -56.076775, "y": -1.4856046}], "plots": []}, {"name": "New view 2", "stickies": ["motor.shaftSpeed_set", "actuator2.setPoint", "systemVariables.summary.EERcool", "node2.Tsat", "systemVariables.summary.CoolCap_total", "compVS_2P.summary.Tsat_dis"], "stickyPositions": [{"component": "actuator2", "x": 128.16156, "y": 32.56376}, {"component": "motor", "x": -12.0, "y": 74.41493}, {"component": "node2", "x": -56.38094, "y": -1.4856046}], "plots": []}]}, {"name": "Workspace.System.HPC.BaseCycle.System_61AQ", "views": [{"name": "Main_view", "stickies": ["ECAT.AmbientAirDBTemp_K.setPoint", "ECAT.EvapBrineLWT_K.setPoint", "ECAT.EvapBrineEWT_K.setPoint", "ECAT.TargetCoolingCapacity_W.setPoint"], "stickyPositions": [], "plots": []}]}, {"name": "Workspace.System.HPH.R290.CL_61AQ", "views": [{"name": "Main_view", "stickies": ["ECAT.HeatingAmbientAirDBTemp_K.setPoint", "ECAT.CondBrineEWT_K.setPoint", "ECAT.CondBrineLWT_K.setPoint", "ECAT.TargetHeatingCapacity_W.setPoint"], "stickyPositions": [{"component": "ECAT", "x": 29.958292, "y": 84.65313}], "plots": []}]}, {"name": "Workspace.System.Multimodule.HPC.System_61AQ_Modular", "views": [{"name": "Main_view", "stickies": ["Module_2.Module.controlledPower", "Module_2.Module.controlledCapacity", "ECAT.PubUnitPower_W.value", "ECAT.AmbientAirDBTemp_K.setPoint", "ECAT.EvapBrineLWT_K.fixed", "ECAT.EvapBrineEWT_K.fixed", "ECAT.TargetCoolingCapacity_W.setPoint", "ECAT.PubCoolingCapacity_W.value"], "stickyPositions": [{"component": "Module_2", "x": 19.054672, "y": 14.492285}, {"component": "ECAT", "x": -94.34725, "y": 79.916855}], "plots": []}]}]}