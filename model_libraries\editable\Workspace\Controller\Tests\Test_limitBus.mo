within Workspace.Controller.Tests;
model Test_limitBus
  parameter.Modelica.SIunits.Power capacity_setpoint=150000;
  parameter.Modelica.SIunits.Temperature T_sst_min_limit_comp=0;
  parameter.Modelica.SIunits.Temperature T_sdt_max_limit_comp=67;
  parameter.Modelica.SIunits.Temperature T_dgt_max_limit_comp=94;
  parameter.Modelica.SIunits.Temperature T_sst_min_limit_exv=0.5;
  parameter.Modelica.SIunits.Temperature T_sst_max_limit=17;
  parameter.Modelica.SIunits.Temperature dT_dsh_min_limit=7;
  parameter.Modelica.SIunits.Temperature dT_sbc_setpoint=-5;
  parameter.Modelica.SIunits.Temperature T_dgt_max_limit_exv=93;
  parameter.Modelica.SIunits.Temperature dT_esh_setpoint=10;
  parameter Real fanSpeed_setpoint=0.8;
  parameter Real rel_cooler_level_setPoint=0.7;
  parameter Real ecoEXV_max_opening=1;
  parameter.Modelica.SIunits.Temperature T_sdt_min_limit=20;
  parameter.Modelica.SIunits.Temperature T_sdt_max_limit_fan=65;
  parameter.Modelica.SIunits.Temperature T_dgt_max_limit_fan=92;
  .Modelica.Blocks.Sources.RealExpression T_sst_min_limit_ex(
    y=T_sst_min_limit_exv)
    annotation (Placement(transformation(extent={{-149.0,105.0},{-119.0,119.0}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Sources.RealExpression T_sdt_max_limit_fa(
    y=T_sdt_max_limit_fan)
    annotation (Placement(transformation(extent={{-151.0,25.0},{-121.0,39.0}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Sources.RealExpression T_sdt_max_limit_com(
    y=T_sdt_max_limit_comp)
    annotation (Placement(transformation(extent={{-151.0,41.0},{-121.0,55.0}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Sources.RealExpression T_dgt_max_limit_fa(
    y=T_dgt_max_limit_fan)
    annotation (Placement(transformation(extent={{-151.0,58.0},{-121.0,70.0}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Sources.RealExpression T_sdt_min_limi(
    y=T_sdt_min_limit)
    annotation (Placement(transformation(extent={{-151.29741379310343,8.152915020571461},{-121.29741379310343,23.252257393221633}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Sources.RealExpression T_sst_min_limit_com(
    y=T_sst_min_limit_comp)
    annotation (Placement(transformation(extent={{-149.0,117.0},{-119.0,131.0}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Sources.RealExpression T_dgt_max_limit_com(
    y=T_dgt_max_limit_comp)
    annotation (Placement(transformation(extent={{-149.0,87.0},{-119.0,101.0}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Sources.RealExpression T_dgt_max_limit_ex(
    y=T_dgt_max_limit_exv)
    annotation (Placement(transformation(extent={{-149.0,71.0},{-119.0,85.0}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Sources.RealExpression dT_sbc_setpoin(
    y=dT_sbc_setpoint)
    annotation (Placement(transformation(extent={{-153.0,-6.0},{-123.0,6.0}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Sources.RealExpression dT_esh_setpoin(
    y=dT_esh_setpoint)
    annotation (Placement(transformation(extent={{-153.0,-18.0},{-123.0,-6.0}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Sources.RealExpression capacity_setpoin(
    y=capacity_setpoint)
    annotation (Placement(transformation(extent={{-153.0,-48.42226578845363},{-123.0,-35.57773421154637}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Sources.RealExpression dT_dsh_min_limi(
    y=dT_dsh_min_limit)
    annotation (Placement(transformation(extent={{-153.0,-35.0},{-123.0,-21.0}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Sources.RealExpression fanSpeed_setpoin(
    y=fanSpeed_setpoint)
    annotation (Placement(transformation(extent={{-153.0,-69.0},{-123.0,-55.0}},rotation=0.0,origin={0.0,0.0})));
  .Workspace.Interfaces.LimitsBus limitsBusA
    annotation (Placement(transformation(extent={{-65.17076629245895,30.82923370754105},{-42.82923370754105,53.17076629245895}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.LimitsBus limitsBusB
    annotation (Placement(transformation(extent={{-65.17076629245895,-23.17076629245895},{-42.82923370754105,-0.8292337075410501}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression rel_cooler_level_setPoin(
    y=rel_cooler_level_setPoint)
    annotation (Placement(transformation(extent={{-153.03965515075723,-112.0},{-122.96034484924277,-92.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression ecoEXV_max_openin(
    y=ecoEXV_max_opening)
    annotation (Placement(transformation(extent={{-153.03965515075723,-94.0},{-122.96034484924277,-74.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_sst_max_limi(
    y=T_sst_max_limit)
    annotation (Placement(transformation(extent={{-149.0,132.0},{-119.0,144.0}},rotation=0.0,origin={0.0,0.0})));
equation
  connect(T_sst_min_limit_com.y,limitsBusA.T_sst_min_limit_comp)
    annotation (Line(points={{-117.5,124},{-85.75,124},{-85.75,42},{-54,42}},color={0,0,127}));
  connect(T_sst_min_limit_com.y,limitsBusB.T_sst_min_limit_comp)
    annotation (Line(points={{-117.5,124},{-85.75,124},{-85.75,-12},{-54,-12}},color={0,0,127}));
  connect(T_sst_min_limit_ex.y,limitsBusA.T_sst_min_limit_exv)
    annotation (Line(points={{-117.5,112},{-85.75,112},{-85.75,42},{-54,42}},color={0,0,127}));
  connect(T_sst_min_limit_ex.y,limitsBusB.T_sst_min_limit_exv)
    annotation (Line(points={{-117.5,112},{-85.75,112},{-85.75,-12},{-54,-12}},color={0,0,127}));
  connect(T_dgt_max_limit_com.y,limitsBusA.T_dgt_max_limit_comp)
    annotation (Line(points={{-117.5,94},{-85.75,94},{-85.75,42},{-54,42}},color={0,0,127}));
  connect(T_dgt_max_limit_com.y,limitsBusB.T_dgt_max_limit_comp)
    annotation (Line(points={{-117.5,94},{-85.75,94},{-85.75,-12},{-54,-12}},color={0,0,127}));
  connect(T_dgt_max_limit_ex.y,limitsBusA.T_dgt_max_limit_exv)
    annotation (Line(points={{-117.5,78},{-85.75,78},{-85.75,42},{-54,42}},color={0,0,127}));
  connect(T_dgt_max_limit_ex.y,limitsBusB.T_dgt_max_limit_exv)
    annotation (Line(points={{-117.5,78},{-85.75,78},{-85.75,-12},{-54,-12}},color={0,0,127}));
  connect(T_dgt_max_limit_fa.y,limitsBusA.T_dgt_max_limit_fan)
    annotation (Line(points={{-119.5,64},{-86.75,64},{-86.75,42},{-54,42}},color={0,0,127}));
  connect(T_dgt_max_limit_fa.y,limitsBusB.T_dgt_max_limit_fan)
    annotation (Line(points={{-119.5,64},{-86.75,64},{-86.75,-12},{-54,-12}},color={0,0,127}));
  connect(T_sdt_max_limit_com.y,limitsBusA.T_sdt_max_limit_comp)
    annotation (Line(points={{-119.5,48},{-86.75,48},{-86.75,42},{-54,42}},color={0,0,127}));
  connect(T_sdt_max_limit_com.y,limitsBusB.T_sdt_max_limit_comp)
    annotation (Line(points={{-119.5,48},{-86.75,48},{-86.75,-12},{-54,-12}},color={0,0,127}));
  connect(T_sdt_max_limit_fa.y,limitsBusA.T_sdt_max_limit_fan)
    annotation (Line(points={{-119.5,32},{-86.75,32},{-86.75,42},{-54,42}},color={0,0,127}));
  connect(T_sdt_max_limit_fa.y,limitsBusB.T_sdt_max_limit_fan)
    annotation (Line(points={{-119.5,32},{-86.75,32},{-86.75,-12},{-54,-12}},color={0,0,127}));
  connect(T_sdt_min_limi.y,limitsBusA.T_sdt_min_limit)
    annotation (Line(points={{-119.79741379310343,15.702586206896546},{-86.75,15.702586206896546},{-86.75,42},{-54,42}},color={0,0,127}));
  connect(T_sdt_min_limi.y,limitsBusB.T_sdt_min_limit)
    annotation (Line(points={{-119.79741379310343,15.702586206896546},{-86.75,15.702586206896546},{-86.75,-12},{-54,-12}},color={0,0,127}));
  connect(dT_sbc_setpoin.y,limitsBusA.dT_sbc_setpoint)
    annotation (Line(points={{-121.5,0},{-87.75,0},{-87.75,42},{-54,42}},color={0,0,127}));
  connect(dT_sbc_setpoin.y,limitsBusB.dT_sbc_setpoint)
    annotation (Line(points={{-121.5,0},{-87.75,0},{-87.75,-12},{-54,-12}},color={0,0,127}));
  connect(dT_esh_setpoin.y,limitsBusA.dT_esh_setpoint)
    annotation (Line(points={{-121.5,-12},{-87.75,-12},{-87.75,42},{-54,42}},color={0,0,127}));
  connect(dT_esh_setpoin.y,limitsBusB.dT_esh_setpoint)
    annotation (Line(points={{-121.5,-12},{-54,-12}},color={0,0,127}));
  connect(dT_dsh_min_limi.y,limitsBusA.dT_dsh_min_limt)
    annotation (Line(points={{-121.5,-28},{-87.75,-28},{-87.75,42},{-54,42}},color={0,0,127}));
  connect(dT_dsh_min_limi.y,limitsBusB.dT_dsh_min_limt)
    annotation (Line(points={{-121.5,-28},{-87.75,-28},{-87.75,-12},{-54,-12}},color={0,0,127}));
  connect(capacity_setpoin.y,limitsBusA.capacity_setpoint)
    annotation (Line(points={{-121.5,-42},{-87.75,-42},{-87.75,42},{-54,42}},color={0,0,127}));
  connect(capacity_setpoin.y,limitsBusB.capacity_setpoint)
    annotation (Line(points={{-121.5,-42},{-87.75,-42},{-87.75,-12},{-54,-12}},color={0,0,127}));
  connect(fanSpeed_setpoin.y,limitsBusA.fanSpeed_setpoint)
    annotation (Line(points={{-121.5,-62},{-87.75,-62},{-87.75,42},{-54,42}},color={0,0,127}));
  connect(fanSpeed_setpoin.y,limitsBusB.fanSpeed_setpoint)
    annotation (Line(points={{-121.5,-62},{-87.75,-62},{-87.75,-12},{-54,-12}},color={0,0,127}));
  connect(ecoEXV_max_openin.y,limitsBusA.ecoEXV_max_opening)
    annotation (Line(points={{-121.45637933416705,-84},{-87.72818966708353,-84},{-87.72818966708353,42},{-54,42}},color={0,0,127}));
  connect(ecoEXV_max_openin.y,limitsBusB.ecoEXV_max_opening)
    annotation (Line(points={{-121.45637933416705,-84},{-87.72818966708353,-84},{-87.72818966708353,-12},{-54,-12}},color={0,0,127}));
  connect(rel_cooler_level_setPoin.y,limitsBusA.rel_cooler_level_setpoint)
    annotation (Line(points={{-121.45637933416705,-102},{-87.72818966708353,-102},{-87.72818966708353,42},{-54,42}},color={0,0,127}));
  connect(rel_cooler_level_setPoin.y,limitsBusB.rel_cooler_level_setpoint)
    annotation (Line(points={{-121.45637933416705,-102},{-87.72818966708353,-102},{-87.72818966708353,-12},{-54,-12}},color={0,0,127}));
  connect(T_sst_max_limi.y,limitsBusA.T_sst_max_limit)
    annotation (Line(points={{-117.5,138},{-85.75,138},{-85.75,42},{-54,42}},color={0,0,127}));
  connect(T_sst_max_limi.y,limitsBusB.T_sst_max_limit)
    annotation (Line(points={{-117.5,138},{-85.75,138},{-85.75,-12},{-54,-12}},color={0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          fillColor={130,108,108},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end Test_limitBus;
