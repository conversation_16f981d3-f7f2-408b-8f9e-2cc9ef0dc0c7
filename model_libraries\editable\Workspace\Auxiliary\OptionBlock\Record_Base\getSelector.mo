within Workspace.Auxiliary.OptionBlock.Record_Base;
function getSelector
   input Workspace.Auxiliary.OptionBlock.Record_Base.Selector Selector ;
   output Workspace.Auxiliary.OptionBlock.Record_Base.UnitBase_1C Unit ;
protected
constant Workspace.Auxiliary.OptionBlock.Record_Base.Unit_Z_040 Unit_Z_040;
constant Workspace.Auxiliary.OptionBlock.Record_Base.Unit_Z_050 Unit_Z_050;
constant Workspace.Auxiliary.OptionBlock.Record_Base.Unit_Z_060 Unit_Z_060;
constant Workspace.Auxiliary.OptionBlock.Record_Base.Unit_Z_070 Unit_Z_070;
   Workspace.Auxiliary.OptionBlock.Record_Base.UnitBase_1C[:] Unit_base={Unit_Z_040,Unit_Z_050,Unit_Z_060,Unit_Z_070};
algorithm
   Unit := Unit_base[Integer(Selector)];
end getSelector;