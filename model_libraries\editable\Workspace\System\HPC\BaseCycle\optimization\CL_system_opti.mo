within Workspace.System.HPC.BaseCycle.optimization;
model CL_system_opti
  extends.Workspace.System.HPC.BaseCycle.CL_system;
  output Real EERA;
  //output Real EERB;
  output Real EER;
  output.Modelica.SIunits.Power CoolingCapacityA;
  output.Modelica.SIunits.Power CoolingCapacityB;
  output.Modelica.SIunits.Power CoolingCapacity;
  output.Modelica.SIunits.Power PcompressorA;
  output.Modelica.SIunits.Power PcompressorB;
  output.Modelica.SIunits.Power Pcompressor;
  output.Modelica.SIunits.Power PfanA;
  output.Modelica.SIunits.Power PfanB;
  output.Modelica.SIunits.Power Pfan;
  output.Modelica.SIunits.Power PinputA;
  output.Modelica.SIunits.Power PinputB;
  output.Modelica.SIunits.Power Pinput;
  //output Modelica.SIunits.Temperature SSTmaxA;
  output.Modelica.SIunits.Temperature SSTminA;
  //output Modelica.SIunits.Temperature SDTmaxA;
  //output Modelica.SIunits.Temperature SDTminA;
  output.Modelica.SIunits.Temperature DGTmaxA;
  // output Modelica.SIunits.Temperature SSTmaxB;
  output.Modelica.SIunits.Temperature SSTminB;
  //output Modelica.SIunits.Temperature SDTmaxB;
  //output Modelica.SIunits.Temperature SDTminB;
  output.Modelica.SIunits.Temperature DGTmaxB;
  output.Modelica.SIunits.Temperature SSTA;
  output.Modelica.SIunits.Temperature SDTA;
  output.Modelica.SIunits.Temperature DGTA;
  output.Modelica.SIunits.Conversions.NonSIunits.AngularVelocity_rpm FanSpdA;
  output.Modelica.SIunits.Conversions.NonSIunits.AngularVelocity_rpm FanSpdB;
  output.Modelica.SIunits.Frequency CmpSpdA;
  output.Modelica.SIunits.Frequency CmpSpdB;
equation
  CoolingCapacityA=oL_modular.BlocA.evapBPHE.summary.Q_flow_ref;
  CoolingCapacityB=oL_modular.BlocB.evapBPHE.summary.Q_flow_ref;
  CoolingCapacity=oL_modular.BlocA.evapBPHE.summary.Q_flow_ref+oL_modular.BlocB.evapBPHE.summary.Q_flow_ref;
  PcompressorA=oL_modular.BlocA.compressor.summary.P_compression;
  PcompressorB=oL_modular.BlocB.compressor.summary.P_compression;
  Pcompressor=oL_modular.BlocA.compressor.summary.P_compression+oL_modular.BlocB.compressor.summary.P_compression;
  PfanA=oL_modular.BlocA.motor.summary.power_shaft;
  PfanB=oL_modular.BlocB.motor.summary.power_shaft;
  Pfan=oL_modular.BlocA.motor.summary.power_shaft+oL_modular.BlocB.motor.summary.power_shaft;
  PinputA=PfanA+PcompressorA;
  PinputB=PfanB+PcompressorB;
  Pinput=Pfan+Pcompressor;
  EERA=CoolingCapacityA/PinputA;
  //EERB=CoolingCapacityB/PinputB;
  EER=CoolingCapacity/Pinput;
  DGTmaxA=423.15;
  SSTminA=274.15;
  //SDTmaxA=SDTmax_varA;
  //SSTmaxA=SSTmax_varA;
  //SDTminA=SDTmin_varA;
  DGTmaxB=423.15;
  SSTminB=274.15;
  //SDTmaxB=SDTmax_varB;
  //SSTmaxB=SSTmax_varB;
  //SDTminB=SDTmin_varB;
  FanSpdA=oL_modular.BlocA.motor.summary.Speed;
  FanSpdB=oL_modular.BlocB.motor.summary.Speed;
  CmpSpdA=oL_modular.BlocA.compressor.summary.Ncomp;
  CmpSpdB=oL_modular.BlocB.compressor.summary.Ncomp;
  SSTA=oL_modular.BlocA.node_suction.summary.Tsat;
  SDTA=oL_modular.BlocA.node_discharge.summary.Tsat;
  DGTA=oL_modular.BlocA.node_discharge.summary.T;
end CL_system_opti;
