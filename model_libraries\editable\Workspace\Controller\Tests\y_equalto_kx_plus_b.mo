within Workspace.Controller.Tests;
model y_equalto_kx_plus_b
  /* size of K matrix is defined below. Don't change or remove this line that is an indicator for python scripts.*/ parameter Integer n=3
    "Length of x";
  parameter Integer m=7
    "Length of y";
  /* size of K matrix is defined above. Don't change or remove this line that is an indicator for python scripts.*//* K matrix is defined below. Don't change or remove this line that is an indicator for python scripts.*/parameter Real K[m,n]={{0.,454.54545455,0.},{0.,0.,-5.26315789},{-0.07777778,0.63636364,0.},{0.,-0.13636364,15.78947368},{1.05555556,0.,0.},{0.,1.27272727,0.},{0.16666667,-1.36363636,157.89473684}}
    "K matrix in y = k * (x - xmin) + b";
  /* K matrix is defined above. Don't change or remove this line that is an indicator for python scripts.*//* b vector is defined below. Don't change or remove this line that is an indicator for python scripts.*/parameter Real b[m]={3.75e+04,3.50e+00,4.75e+01,8.25e+00,5.00e+02,8.50e+01,9.00e+01}
    "b vector in y = k * (x - xmin) + b";
  /* b vector is defined above. Don't change or remove this line that is an indicator for python scripts.*//* xmin vector is defined below. Don't change or remove this line that is an indicator for python scripts.*/parameter Real xmin[n]={50.,30.,0.05}
    "xmin vector in y = k * (x - xmin) + b";
  /* xmin vector is defined above. Don't change or remove this line that is an indicator for python scripts.*/Modelica.Blocks.Interfaces.RealInput x[n]
    annotation (Placement(transformation(extent={{-84,-12},{-60,12}}),iconTransformation(extent={{-56,-8},{-42,6}})));
  Modelica.Blocks.Interfaces.RealOutput y[m]
    annotation (Placement(transformation(extent={{50,-10},{70,10}}),iconTransformation(extent={{44,-8},{58,6}})));
  parameter Real cf_K=1;
algorithm
  y := cf_K*K*(x-xmin)+b;
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false),
      graphics={
        Rectangle(
          extent={{-42,36},{44,-36}},
          lineColor={28,108,200}),
        Text(
          extent={{-32,20},{32,-24}},
          lineColor={28,108,200},
          textString="y= K*x + b")}),
    Diagram(
      coordinateSystem(
        preserveAspectRatio=false)),
    uses(
      Modelica(
        version="3.2.2")));
end y_equalto_kx_plus_b;
