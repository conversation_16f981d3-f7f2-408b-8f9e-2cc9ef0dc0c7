within Workspace.System.HPC.BaseCycle.optimization;
model Unit
  import CoolantCommonMedium=BOLT.InternalLibrary.Media.Coolant.CoolantCommon;
  .Workspace.System.HPC.BaseCycle.Equipement oL_modular(
    CoolantMedium=CoolantMedium,
    BrineConcentration=BrineConcentration,
    Use_Cp_actuator=Use_Cp_actuator,
    EWT=EWT,
    LWT=LWT,
    OAT=OAT)
    annotation (Placement(transformation(extent={{-2.0,24.0},{18.0,44.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Source sourceBrine(
    Vd_fixed=false,
    T_set=EWT,
    p_fixed=true,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration)
    annotation (Placement(transformation(extent={{-4.0,-4.0},{4.0,4.0}},origin={8.0,-6.0},rotation=90.0)));
  .BOLT.BoundaryNode.Coolant.Sink sinkBrine(
    p_fixed=false,
    T_fixed=true,
    T_set=LWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration)
    annotation (Placement(transformation(extent={{-4.0,-4.0},{4.0,4.0}},origin={8.0,64.0},rotation=-90.0)));
  .BOLT.Control.SteadyState.SetpointControl.Actuator EXV_actuatorA(
    setPoint=0.5,
    minBound=0,
    maxBound=1,
    fixed=false)
    annotation (Placement(transformation(extent={{-32.0,32.0},{-52.0,52.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Actuator EXV_actuatorB(
    fixed=false,
    maxBound=1,
    minBound=0,
    setPoint=0.5,
    isOff=is_monobloc)
    annotation (Placement(transformation(extent={{42.0,36.0},{62.0,56.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Actuator Fan_actuatorA(
    fixed=true,
    maxBound=950,
    minBound=0,
    setPoint=720)
    annotation (Placement(transformation(extent={{-50.0,24.0},{-70.0,44.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Actuator Fan_actuatorB(
    setPoint=720,
    minBound=0,
    maxBound=950,
    fixed=true,
    isOff=is_monobloc)
    annotation (Placement(transformation(extent={{50.0,22.0},{70.0,42.0}},origin={0.0,0.0},rotation=0.0)));
  parameter Boolean is_monobloc=false;
  parameter Boolean Use_pump=false;
  parameter Boolean Use_tank=false;
  parameter Boolean Use_filter=false;
  parameter Boolean is0ff=false;
  parameter Boolean isOFFA=false
    annotation (Dialog(group="Cp_control"));
  parameter Boolean isOFFB=false
    annotation (Dialog(group="Cp_control"));
  parameter.Modelica.SIunits.Temperature LWT=280.15;
  parameter.Modelica.SIunits.Temperature EWT=285.15;
  parameter.Modelica.SIunits.Temperature OAT=308.15;
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointControllerA(
    AV_start=100,
    AV_max=140,
    AV_min=30)
    annotation (Placement(transformation(extent={{10.0,-10.0},{-10.0,10.0}},origin={-42.0,-27.0},rotation=-90.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation errorCalculationA(
    setPoint=TargetCapacity,
    measurement=-oL_modular.BlocA.evapBPHE.summary.Q_flow_coolant)
    annotation (Placement(transformation(extent={{10.0,-10.0},{-10.0,10.0}},origin={-42.0,-73.0},rotation=-90.0)));
  parameter Real TargetCapacity=.Workspace.Controller.Capacity_function_heating(
    sinkBrine.T_set-273.15);
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation errorCalculationB(
    measurement=-oL_modular.BlocB.evapBPHE.summary.Q_flow_coolant,
    setPoint=TargetCapacity,
    isOff=is_monobloc)
    annotation (Placement(transformation(extent={{10.0,-10.0},{-10.0,10.0}},origin={54.0,-64.0},rotation=-90.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointControllerB(
    AV_min=30,
    AV_max=140,
    AV_start=100,
    isOff=is_monobloc)
    annotation (Placement(transformation(extent={{10.0,-10.0},{-10.0,10.0}},origin={52.0,-28.0},rotation=-90.0)));
  // Declaration of Coolant Medium Package
  //package CoolantCommonMedium =
  //.BOLT.InternalLibrary.Media.Coolant.CoolantCommon                             "Coolant Medium Package"// annotation ();
  // Declaration of Coolant Medium at Evaporator Circuit
  parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector CoolantMedium=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector.EG
    "Coolant Medium Selection"
    annotation (Dialog(group="Medium"));
  final parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Empty coolantInf=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.getSelector(
    CoolantMedium)
    "Coolant Medium"
    annotation ();
  .BOLT.InternalLibrary.Media.Coolant.CoolantCommon.Temperature FreezTemp=.BOLT.InternalLibrary.Media.Coolant.CoolantCommon.freezeTemp(
    coolantInf,
    BrineConcentration,
    1);
  parameter Real BrineConcentration=0.2
    annotation (Dialog(group="Medium"));
  parameter Boolean Use_Cp_actuator=true;
equation
  connect(EXV_actuatorB.value,oL_modular.EXV_controller_B)
    annotation (Line(points={{47,46},{33,46},{33,36.5},{19,36.5}},color={0,0,127}));
  connect(Fan_actuatorB.value,oL_modular.Fan_controller_B)
    annotation (Line(points={{55,32},{33,32},{33,31.5},{19,31.5}},color={0,0,127}));
  connect(setpointControllerB.actuatorSignal,oL_modular.Compressor_controller_B)
    annotation (Line(points={{52,-17.4},{52,26.5},{19,26.5}},color={0,0,127}));
  connect(setpointControllerA.actuatorSignal,oL_modular.Compressor_controller_A)
    annotation (Line(points={{-42,-16.4},{-42,26.5},{-3,26.5}},color={0,0,127}));
  connect(EXV_actuatorA.value,oL_modular.EXV_controller_A)
    annotation (Line(points={{-37,42},{-20,42},{-20,36.5},{-3,36.5}},color={0,0,127}));
  connect(Fan_actuatorA.value,oL_modular.Fan_controller_A)
    annotation (Line(points={{-55,34},{-29,34},{-29,31.5},{-3,31.5}},color={0,0,127}));
  connect(errorCalculationB.sensor,setpointControllerB.errorSignal)
    annotation (Line(points={{53.199999999999996,-54.2},{53.199999999999996,-46.1},{52,-46.1},{52,-38}},color={28,108,200}));
  connect(errorCalculationA.sensor,setpointControllerA.errorSignal)
    annotation (Line(points={{-42.8,-63.2},{-42.8,-50.1},{-42,-50.1},{-42,-37}},color={28,108,200}));
  connect(sourceBrine.port,oL_modular.coolant_in)
    annotation (Line(points={{8,-2},{8,10.899999999999999},{7.6,10.899999999999999},{7.6,22}},color={0,127,0}));
  connect(oL_modular.coolant_out,sinkBrine.port)
    annotation (Line(points={{7.6,44.2},{7.6,52.1},{8,52.1},{8,60}},color={0,127,0}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end Unit;
