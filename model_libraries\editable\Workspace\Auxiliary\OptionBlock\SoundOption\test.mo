within Workspace.Auxiliary.OptionBlock.SoundOption;
model test
    .Workspace.Auxiliary.OptionBlock.SoundOption.getSelector getSelector annotation(Placement(transformation(extent = {{-17.785641942568795,21.587463292700065},{2.2143580574312054,41.587463292700065}},origin = {0.0,0.0},rotation = 0.0)));
    annotation(Icon(coordinateSystem(preserveAspectRatio = false,extent = {{-100.0,-100.0},{100.0,100.0}}),graphics = {Rectangle(lineColor={0,0,0},fillColor={230,230,230},fillPattern=FillPattern.Solid,extent={{-100.0,-100.0},{100.0,100.0}}),Text(lineColor={0,0,255},extent={{-150,150},{150,110}},textString="%name")}));
end test;
