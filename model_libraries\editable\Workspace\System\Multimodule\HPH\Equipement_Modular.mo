within Workspace.System.Multimodule.HPH;
model Equipement_Modular
  .Workspace.System.HPH.BaseCycles.OL_FW092 BlockA(
    EWT=EWT,
    LWT=LWT,
    OAT=OAT,
    Water_pressure=Water_pressure,
    isOff_ref=isOFF,
    CoolantMedium=CoolantMedium,
    BrineConcentration=BrineConcentration,
    use_Z_in=false,
    selector_Comp=selector_Comp[1],
    capacity_design=capacity_design[1],
    CompVoltage=CompVoltage[1],
    fmax=fmax[1],
    fmin=fmin[1],
    EXV_main=EXV_main[1],
    Dport_coolant_a=Dport_coolant_a[1],
    Dport_coolant_b=Dport_coolant_b[1],
    Dport_ref_a=Dport_ref_a[1],
    Dport_ref_b=Dport_ref_b[1],
    nPlate=nPlate[1],
    selector_geo_BPHE=selector_geo_BPHE[1],
    nCoils=nCoils[1],
    Itube=Itube[1],
    nCir=nCir[1],
    Ntube=Ntube[1],
    Nrow=Nrow[1],
    Ltube=Ltube[1],
    Dotube=Dotube[1],
    Ttube=Ttube[1],
    Ptube=Ptube[1],
    Prow=Prow[1],
    Dfin=Dfin[1],
    Tfin=Tfin[1],
    Fw_fan=Fw_fan[1],
    max_fan_frequency=if OAT < 278.25 then max_fan_frequency[1] else max_fan_frequency2[1],
    fanCurveCoefficientsCooling=fanCurveCoefficientsCooling[1,:],
    fanCurveCoefficientsHeating=fanCurveCoefficientsHeating[1,:],
    Suction_line_diameter=Suction_line_diameter[1],
    Suction_line_length=Suction_line_length[1],
    coil_line_length=coil_line_length[1],
    liquid_line_diameter=liquid_line_diameter[1],
    liquid_line_length=liquid_line_length[1],
    discharge_line_diameter=discharge_line_diameter[1],
    discharge_line_length=discharge_line_length[1],
    EXV_in_line_diameter=EXV_in_line_diameter[1],
    EXV_in_line_length=EXV_in_line_length[1],
    Ac_duct=Ac_duct[1],
    Ka_duct=Ka_duct[1],
    UA_duct=UA_duct[1],
    coil_line_diameter=coil_line_diameter[1],
    selector_pump=selector_pump[1],
    isCoating=isCoating,
    CondFoulingFactor=CondFoulingFactor,
    relative_humidity=relative_humidity,
    PDC_4WV=PDC_4WV[1],
    Heatcap_Tbiv=Heatcap_Tbiv[1],
    FW=FW[1],
    use_Calib=use_Calib,
    Zflow_heatcap=Zflow_heatcap[1],
    Zflow_Ncomp=Zflow_Ncomp[1],
    Zflow_SST2=Zflow_SST2[1],
    Zflow_SST3=Zflow_SST3[1],
    Zpower_DGT=Zpower_DGT[1],
    Zcond_HPH_cst=Zcond_HPH_cst[1],
    Zcond_HPH_heatcap=Zcond_HPH_heatcap[1],
    Zcond_HPH_SST=Zcond_HPH_SST[1],
    Zcond_HPC_SDT=Zcond_HPC_SDT[1],
    Zcond_HPC_cst=Zcond_HPC_cst[1],
    Zevap_HPH_cst=Zevap_HPH_cst[1],
    Zevap_HPH_SST=Zevap_HPH_SST[1],
    Zevap_HPC_cst=Zevap_HPC_cst[1],
    Zevap_HPC_SST=Zevap_HPC_SST[1],
    Zevap_coated_HPH=Zevap_coated_HPH[1],
    Zflow_intercept=Zflow_intercept[1],
    Zflow_SST=Zflow_SST[1],
    Zflow_SDT=Zflow_SDT[1],
    Zpower_intercept=Zpower_intercept[1],
    Zpower_SST=Zpower_SST[1],
    Zpower_SST2=Zpower_SST2[1],
    Zcond_HPH_SDT=Zcond_HPH_SDT[1],
    Zevap_HPC_Coolcap=Zevap_froid_Coolcap[1],
    Zevap_HPH_Coolcap=Zevap_chaud_Coolcap[1],
    Zpower_SDT=Zpower_SDT[1],
    Zpower_Ncomp=Zpower_Ncomp[1],
    Zpower_Heatcap=Zpower_heatcap[1],
    use_Calib=use_Calib,
    isOff=isOFF,
    ssh_setPoint=ssh_setPoint[1],
    SC_setpoint=SC_setpoint[1],
    SC_fixed=SC_fixed[1],
    Mref_fixed=Mref_fixed[1],
    Mref=M_ref[1],
    is_relative_humidity=is_relative_humidity,
    OAT_WB=OAT_WB,
    is_OAT_WB=is_OAT_WB,
    Zcond_HPC_coated=Zcond_HPC_coated[1],Altitude = Altitude,OAT_target_cap = OAT_target_cap[1],Nominal_cap = Nominal_cap[1],cst_target_cap = cst_target_cap[1],Max_max_target_cap = Max_max_target_cap[1],Heatcap_Tbiv_LN_option = Heatcap_Tbiv_LN_option[1],is_ULN_option = is_ULN_option,Nominal_cap_LN = Nominal_cap_LN[1],OAT_target_cap_LN = OAT_target_cap_LN[1],cst_target_cap_LN = cst_target_cap_LN[1],Max_max_target_cap_LN = Max_max_target_cap_LN[1],Zevap_HPH_min = Zevap_HPH_min[1],Zevap_HPH_max = Zevap_HPH_max[1],Zevap_HPC_min = Zevap_HPC_min[1],Zevap_HPC_max = Zevap_HPC_max[1],Zcond_HPH_min = Zcond_HPH_min[1],Zcond_HPH_max = Zcond_HPH_max[1],Zcond_HPC_min = Zcond_HPC_min[1],Zcond_HPC_max = Zcond_HPC_max[1],Zflow_min = Zflow_min[1],Zflow_max = Zflow_max[1],Zpower_min = Zpower_min[1],Zpower_max = Zpower_max[1])
    annotation (Placement(transformation(extent={{-51.41223706314183,42.001104851458585},{-31.41223706314183,62.001104851458585}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.System.HPH.BaseCycles.OL_FW092 BlockB(
    isOff=is_monobloc or isOFF,
    EWT=EWT,
    LWT=LWT,
    OAT=OAT,
    Water_pressure=Water_pressure,
    isOff_ref=is_monobloc or isOFF,
    CoolantMedium=CoolantMedium,
    BrineConcentration=BrineConcentration,
    use_Z_in=false,
    selector_Comp=selector_Comp[2],
    capacity_design=capacity_design[2],
    CompVoltage=CompVoltage[2],
    fmax=fmax[2],
    fmin=fmin[2],
    EXV_main=EXV_main[2],
    Dport_coolant_a=Dport_coolant_a[2],
    Dport_coolant_b=Dport_coolant_b[2],
    Dport_ref_a=Dport_ref_a[2],
    Dport_ref_b=Dport_ref_b[2],
    nPlate=nPlate[2],
    selector_geo_BPHE=selector_geo_BPHE[2],
    nCoils=nCoils[2],
    Itube=Itube[2],
    nCir=nCir[2],
    Ntube=Ntube[2],
    Nrow=Nrow[2],
    Ltube=Ltube[2],
    Dotube=Dotube[2],
    Ttube=Ttube[2],
    Ptube=Ptube[2],
    Prow=Prow[2],
    Dfin=Dfin[2],
    Tfin=Tfin[2],
    Fw_fan=Fw_fan[2],
    max_fan_frequency=if OAT < 278.25 then max_fan_frequency[2] else max_fan_frequency2[2],
    fanCurveCoefficientsCooling=fanCurveCoefficientsCooling[2,:],
    fanCurveCoefficientsHeating=fanCurveCoefficientsHeating[2,:],
    Suction_line_diameter=Suction_line_diameter[2],
    Suction_line_length=Suction_line_length[2],
    coil_line_diameter=coil_line_diameter[2],
    coil_line_length=coil_line_length[2],
    liquid_line_diameter=liquid_line_diameter[2],
    liquid_line_length=liquid_line_length[2],
    discharge_line_diameter=discharge_line_diameter[2],
    discharge_line_length=discharge_line_length[2],
    EXV_in_line_diameter=EXV_in_line_diameter[2],
    EXV_in_line_length=EXV_in_line_length[2],
    Ac_duct=Ac_duct[2],
    Ka_duct=Ka_duct[2],
    UA_duct=UA_duct[2],
    selector_pump=selector_pump[2],
    isCoating=isCoating,
    CondFoulingFactor=CondFoulingFactor,
    relative_humidity=relative_humidity,
    PDC_4WV=PDC_4WV[2],
    Heatcap_Tbiv=Heatcap_Tbiv[2],
    FW=FW[2],
    use_Calib=use_Calib,
    Zflow_heatcap=Zflow_heatcap[2],
    Zflow_Ncomp=Zflow_Ncomp[2],
    Zpower_DGT=Zpower_DGT[2],
    Zcond_HPH_cst=Zcond_HPH_cst[2],
    Zcond_HPH_heatcap=Zcond_HPH_heatcap[2],
    Zcond_HPH_SST=Zcond_HPH_SST[2],
    Zcond_HPC_SDT=Zcond_HPC_SDT[2],
    Zcond_HPC_cst=Zcond_HPC_cst[2],
    Zevap_HPH_cst=Zevap_HPH_cst[2],
    Zevap_HPH_SST=Zevap_HPH_SST[2],
    Zevap_HPC_cst=Zevap_HPC_cst[2],
    Zevap_HPC_SST=Zevap_HPC_SST[2],
    Zevap_coated_HPH=Zevap_coated_HPH[2],
    Zflow_intercept=Zflow_intercept[2],
    Zflow_SST=Zflow_SST[2],
    Zflow_SST2=Zflow_SST2[2],
    Zflow_SST3=Zflow_SST3[2],
    Zflow_SDT=Zflow_SDT[2],
    Zpower_intercept=Zpower_intercept[2],
    Zpower_SST=Zpower_SST[2],
    Zpower_SST2=Zpower_SST2[2],
    Zcond_HPH_SDT=Zcond_HPH_SDT[2],
    Zevap_HPC_Coolcap=Zevap_froid_Coolcap[2],
    Zevap_HPH_Coolcap=Zevap_chaud_Coolcap[2],
    Zpower_SDT=Zpower_SDT[2],
    Zpower_Ncomp=Zpower_Ncomp[2],
    Zpower_Heatcap=Zpower_heatcap[2],
    use_Calib=use_Calib,
    ssh_setPoint=ssh_setPoint[2],
    SC_setpoint=SC_setpoint[2],
    SC_fixed=SC_fixed[2],
    Mref_fixed=Mref_fixed[2],
    Mref=M_ref[2],
    is_relative_humidity=is_relative_humidity,
    OAT_WB=OAT_WB,
    is_OAT_WB=is_OAT_WB,
    Zcond_HPC_coated=Zcond_HPC_coated[2],
    Altitude = Altitude,
    Nominal_cap = Nominal_cap[2],
    OAT_target_cap = OAT_target_cap[2],
    cst_target_cap = cst_target_cap[2],Heatcap_Tbiv_LN_option = Heatcap_Tbiv_LN_option[2],is_ULN_option = is_ULN_option,Nominal_cap_LN = Nominal_cap_LN[2],OAT_target_cap_LN = OAT_target_cap_LN[2],cst_target_cap_LN = cst_target_cap_LN[2],Max_max_target_cap = Max_max_target_cap[2],Max_max_target_cap_LN = Max_max_target_cap_LN[2],Zevap_HPH_min = Zevap_HPH_min[2],Zevap_HPH_max = Zevap_HPH_max[2],Zevap_HPC_min = Zevap_HPC_min[2],Zevap_HPC_max = Zevap_HPC_max[2],Zcond_HPH_min = Zcond_HPH_min[2],Zcond_HPH_max = Zcond_HPH_max[2],Zcond_HPC_min = Zcond_HPC_min[2],Zcond_HPC_max = Zcond_HPC_max[2],Zflow_min = Zflow_min[2],Zflow_max = Zflow_max[2],Zpower_min = Zpower_min[2],Zpower_max = Zpower_max[2])
    annotation (Placement(transformation(extent={{51.99944757427065,41.42963602090205},{31.999447574270647,61.42963602090205}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.Mixer mixer(
    fa_fixed=false,
    isOff_b=is_monobloc or isOFF,
    T_start=LWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff_a=isOFF,
    fa_set=
      if is_monobloc then
        1
      else
        0.5)
    annotation (Placement(transformation(extent={{-10.0,-10.0},{10.0,10.0}},origin={-2.6003915644383007,66.06009079921068},rotation=90.0)));
  parameter Boolean Use_pump=true;
  parameter Boolean Use_fake_pump=false;
  parameter Boolean use_Calib=false;
  parameter Boolean is_monobloc=false
    annotation (Dialog(group="isOff"));
  parameter Boolean isOFF=false
    annotation (Dialog(group="isOff"));
  parameter Boolean isFilter=false
    annotation (Dialog(group="isOff"));
  parameter Boolean isBufferTank=false
    annotation (Dialog(group="isOff"));
  parameter Real Water_pressure=200000;
  parameter Real Max_target_cap = BlockA.Max_target_cap + BlockB.Max_target_cap;
  .BOLT.CoolantMisc.Split split_pumpA(
    mDot_a_start=
      if Use_fake_pump then
        mdot_start
      else
        0.001,
    mDot_b_start=
      if not Use_fake_pump then
        mdot_start
      else
        0.001,
    p_start=Water_pressure,
    T_start=EWT,
    isOff_b=not FakePumpA.isOff or isOFF,
    isOff_a=not Use_fake_pump or isOFF,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    fa_set=
      if Use_pump then
        0
      else
        1)
    annotation (Placement(transformation(extent={{-5.275067375849304,-5.275067375849304},{5.275067375849304,5.275067375849304}},origin={-99.73066594633582,-56.58705980812161},rotation=90.0)));
  .BOLT.CoolantMisc.PumpPoly pumpPolyA(
    T_start=EWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff=not FakePumpA.isOff or isOFF,
    selector=selector_pump[1],
    k_pow=1)
    annotation (Placement(transformation(extent={{8.39339786044772,-8.39339786044772},{-8.39339786044772,8.39339786044772}},origin={-65.08160928199607,-29.698159792787976},rotation=-90.0)));
  .BOLT.CoolantMisc.ReducedPipe filter(
    T_start=EWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    Ka_set=Coef_filter[1]*.Workspace.Auxiliary.Tools.booleanToReal(
      isFilter)+0.0001*(1-.Workspace.Auxiliary.Tools.booleanToReal(
      isFilter)),
    Kb=Coef_filter[2]*.Workspace.Auxiliary.Tools.booleanToReal(
      isFilter)+0,
    isOff=isOFF)
    annotation (Placement(transformation(extent={{-6.386404559420782,-6.386404559420782},{6.386404559420782,6.386404559420782}},origin={-2.74740781513815,-109.98744761987703},rotation=90.0)));
  .BOLT.CoolantMisc.Split split_bloc(
    mDot_a_start=mdot_start,
    mDot_b_start=
      if not is_monobloc then
        mdot_start
      else
        0.001,
    isOff_b=is_monobloc or isOFF,
    T_start=EWT,
    p_start=Water_pressure,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff_a=isOFF,
    fa_set=
      if is_monobloc then
        1
      else
        0.5)
    annotation (Placement(transformation(extent={{-5.275067375849304,-5.275067375849304},{5.275067375849304,5.275067375849304}},origin={-2.0,-79.32622235951571},rotation=90.0)));
  .BOLT.CoolantMisc.Split split_pumpB(
    mDot_a_start=
      if not Use_fake_pump and not is_monobloc then
        mdot_start
      else
        0.001,
    mDot_b_start=
      if Use_fake_pump and not is_monobloc then
        mdot_start
      else
        0.001,
    isOff_b=not Use_fake_pump or is_monobloc or isOFF,
    T_start=EWT,
    p_start=Water_pressure,
    isOff_a=not FakePumpB.isOff or is_monobloc or isOFF,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    fa_set=
      if Use_pump then
        1
      else
        0)
    annotation (Placement(transformation(extent={{-5.275067375849304,-5.275067375849304},{5.275067375849304,5.275067375849304}},origin={97.91362258255033,-57.40960626581417},rotation=90.0)));
  .BOLT.CoolantMisc.PumpPoly pumpPolyB(
    isOff=not FakePumpB.isOff or is_monobloc or isOFF,
    T_start=EWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    selector=selector_pump[2],
    k_pow=1)
    annotation (Placement(transformation(extent={{-8.39339786044772,-8.39339786044772},{8.39339786044772,8.39339786044772}},origin={57.59422308194374,-29.0322955537304},rotation=90.0)));
  .BOLT.InternalLibrary.BuildingBlocks.Coolant.Ports.FluidPort_a coolant_in(
    CoolantMedium=CoolantMedium,
    Xi_set=BrineConcentration)
    annotation (Placement(transformation(extent={{-13.480089871014542,-156.7059464254537},{6.519910128985458,-136.7059464254537}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.InternalLibrary.BuildingBlocks.Coolant.Ports.FluidPort_b coolant_out(
    CoolantMedium=CoolantMedium,
    Xi_set=BrineConcentration)
    annotation (Placement(transformation(extent={{-11.405481404682625,132.6738698914627},{8.594518595317375,152.6738698914627}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-11.900134225592929,101.51074217411269},{8.099865774407071,121.51074217411269}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput EXV_controller_A
    annotation (Placement(transformation(extent={{-95.37224004817512,65.18237406821589},{-103.73623419313878,73.54636821317959}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={-110,25})));
  .Modelica.Blocks.Interfaces.RealInput Fan_controller_A
    annotation (Placement(transformation(extent={{-93.68762778007806,55.21653743265198},{-103.01998396510851,64.54889361768241}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={-110,-25})));
  .Modelica.Blocks.Interfaces.RealInput Compressor_controller_B
    annotation (Placement(transformation(extent={{82.98592386547752,70.50664433143892},{90.7273148605954,78.2480353265568}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={110,-75})));
  .Modelica.Blocks.Interfaces.RealInput EXV_controller_B
    annotation (Placement(transformation(extent={{83.51745205546982,61.75115390963737},{90.84873104047861,69.08243289464616}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={110,25})));
  .Modelica.Blocks.Interfaces.RealInput Fan_controller_B
    annotation (Placement(transformation(extent={{83.18324277938731,53.45614128825156},{91.36331866109131,61.63621716995553}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={110,-25})));
  .Modelica.Blocks.Interfaces.RealInput Compressor_controller_A
    annotation (Placement(transformation(extent={{-94.52303104173927,74.80176781892371},{-103.35490732000406,83.63364409718852}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={-110,-75})));
  .Workspace.Interfaces.MeasurementBus measurementBusA
    annotation (Placement(transformation(extent={{-40.15608500378272,148.13769465476108},{-15.20881689978215,173.08496275876163}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={-80,100})));
  .Workspace.Interfaces.MeasurementBus measurementBusB
    annotation (Placement(transformation(extent={{9.939416093258004,147.5405819008838},{36.08090970501311,173.6820755126389}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={80,100})));
  parameter.Modelica.SIunits.Temperature EWT=285.15;
  parameter.Modelica.SIunits.Temperature LWT=280.15;
  parameter.Modelica.SIunits.Temperature OAT=308.15;
  parameter Real relative_humidity=0.87;
  parameter Boolean is_relative_humidity=false;
  parameter Real OAT_WB=0.87;
  parameter Boolean is_OAT_WB=false;
  .BOLT.BoundaryNode.Coolant.Node node_blocA_out(
    m_flow_start=mdot_start,
    T_start=LWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff=isOFF)
    annotation (Placement(transformation(extent={{-22.619641313530828,45.72354917436189},{-14.619641313530828,53.72354917436189}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Node node_blocB_out(
    m_flow_start=
      if not is_monobloc then
        mdot_start
      else
        0,
    T_start=LWT,
    isOff=is_monobloc or isOFF,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration)
    annotation (Placement(transformation(extent={{17.716900311317985,45.72354917436189},{9.716900311317985,53.72354917436189}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Node node_coolant_in_pumpB(
    m_flow_start=
      if not is_monobloc then
        mdot_start
      else
        0,
    T_start=EWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff=is_monobloc or isOFF)
    annotation (Placement(transformation(extent={{41.77608337530837,-67.90284438952699},{49.77608337530837,-59.902844389527}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Node node_coolant_in_pumpA(
    m_flow_start=mdot_start,
    T_start=EWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff=isOFF)
    annotation (Placement(transformation(extent={{-37.41409050210664,-67.69206959008456},{-45.41409050210664,-59.69206959008456}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Node node_coolant_in_splitbloc(
    m_flow_start=mdot_start,
    T_start=EWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff=isOFF)
    annotation (Placement(transformation(extent={{4.0,-4.0},{-4.0,4.0}},origin={-2.8346941706164603,-94.43853299464625},rotation=-90.0)));
  .BOLT.BoundaryNode.Coolant.Node node_coolant_source(
    m_flow_start=mdot_start,
    T_start=EWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff=isOFF)
    annotation (Placement(transformation(extent={{4.0,-3.999999999999993},{-4.0,3.999999999999993}},origin={-3.401360837283125,-127.20519966131292},rotation=-90.0)));
  // Declaration of Coolant Medium Package
  //package CoolantCommonMedium =
  //.BOLT.InternalLibrary.Media.Coolant.CoolantCommon                             "Coolant Medium Package" annotation ();
  // Declaration of Coolant Medium at Evaporator Circuit
  parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector CoolantMedium=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector.FW
    "Coolant Medium Selection"
    annotation (Dialog(group="Medium"));
  final parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Empty coolantInf=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.getSelector(
    CoolantMedium)
    "Coolant Medium"
    annotation ();
  .BOLT.InternalLibrary.Media.Coolant.CoolantCommon.Temperature FreezTemp=.BOLT.InternalLibrary.Media.Coolant.CoolantCommon.freezeTemp(
    coolantInf,
    BrineConcentration,
    1);
  parameter Real BrineConcentration=0.2
    annotation (Dialog(group="Medium"));
  .BOLT.CoolantMisc.ReducedPipe gaz_separatorA(
    T_start=EWT,
    Ka_set=7,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff=isOFF)
    annotation (Placement(transformation(extent={{-6.076246165826419,-6.076246165826419},{6.076246165826419,6.076246165826419}},origin={-101.26278268171433,33.30635426390258},rotation=90.0)));
  .BOLT.CoolantMisc.ReducedPipe gaz_separatorB(
    T_start=EWT,
    Ka_set=7,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff=is_monobloc or isOFF)
    annotation (Placement(transformation(extent={{-6.076246165826419,-6.076246165826419},{6.076246165826419,6.076246165826419}},origin={97.20855048825722,31.723549174361892},rotation=90.0)));
  .BOLT.CoolantMisc.ReducedPipe PipingA(
    T_start=EWT,
    Ka_set=0.738,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    Kb=2.3384,
    isOff=isOFF)
    annotation (Placement(transformation(extent={{-6.076246165826419,-6.076246165826419},{6.076246165826419,6.076246165826419}},origin={-101.26278268171433,15.306354263902577},rotation=90.0)));
  .BOLT.CoolantMisc.ReducedPipe PipingB(
    T_start=EWT,
    Ka_set=0.738,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff=is_monobloc or isOFF,
    Kb=2.3384)
    annotation (Placement(transformation(extent={{-6.076246165826419,-6.076246165826419},{6.076246165826419,6.076246165826419}},origin={97.57846085003577,13.7235491743619},rotation=90.0)));
  .BOLT.BoundaryNode.Shaft.Source shafA(
    isOff=not FakePumpA.isOff or isOFF,
    use_speed_in=true)
    annotation (Placement(transformation(extent={{-48.2918498052081,-7.32838118682638},{-40.2918498052081,0.6716188131736196}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Shaft.Source shafB(
    isOff=not FakePumpB.isOff or is_monobloc or isOFF,
    use_speed_in=true)
    annotation (Placement(transformation(extent={{41.23789886228211,-6.614264544162268},{33.23789886228211,1.3857354558377324}},origin={0.0,0.0},rotation=0.0)));
  parameter.Modelica.SIunits.Pressure Pdispo=50000;
  .BOLT.BoundaryNode.Coolant.Node node_out(
    T_start=LWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff=isOFF)
    annotation (Placement(transformation(extent={{-4.0,-4.0},{4.0,4.0}},origin={-2.105738743527997,119.69648262111227},rotation=90.0)));
  Real controlledCapacity;
  Real controlledPower;
  parameter.Modelica.SIunits.Power Elec_box_power=160;
  .Modelica.Blocks.Interfaces.RealInput ActuatorPumpUser_A
    annotation (Placement(transformation(extent={{4.666178092515224,-4.666178092515217},{-4.666178092515224,4.666178092515217}},origin={-58.0,24.0},rotation=-90.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={-110,70})));
  .Modelica.Blocks.Interfaces.RealInput ActuatorPumpUser_B
    annotation (Placement(transformation(extent={{4.666178092515224,-4.666178092515217},{-4.666178092515224,4.666178092515217}},origin={38.0,22.0},rotation=-90.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={110,70})));
  parameter.BOLT.InternalLibrary.Compressors.PD_2Port.SubComponents.PD.DataBase.Polynomial.ARI_VS.Selector selector_Comp[2]
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Real capacity_design[2]
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Real CompVoltage[2]
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Real fmax[2]
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Real fmin[2]
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Real M_ref[2]
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter.BOLT.InternalLibrary.Refrigerant.Valves.TwoPort.DataBase.Selector EXV_main[2]
    annotation (Dialog(group="EXV",tab="Unit Characteristics"));
  parameter Real Dport_coolant_a[2]
    annotation (Dialog(group="BPHE",tab="Unit Characteristics"));
  parameter Real Dport_coolant_b[2]
    annotation (Dialog(group="BPHE",tab="Unit Characteristics"));
  parameter Real Dport_ref_a[2]
    annotation (Dialog(group="BPHE",tab="Unit Characteristics"));
  parameter Real Dport_ref_b[2]
    annotation (Dialog(group="BPHE",tab="Unit Characteristics"));
  parameter Integer nPlate[2]
    annotation (Dialog(group="BPHE",tab="Unit Characteristics"));
  parameter Real CondFoulingFactor
    annotation (Dialog(group="BPHE",tab="Unit Characteristics"));
  parameter.BlackBoxLibrary.GeoSelector.GeoSelector_1C_Cond selector_geo_BPHE[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Integer nCoils[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Integer Itube[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Integer nCir[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Integer Ntube[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Integer Nrow[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Ltube[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Dotube[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Ttube[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Ptube[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Prow[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Dfin[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Tfin[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Nominal_cap[2]
    annotation (Dialog(group="Capacity",tab="Unit Characteristics"));
  parameter Real OAT_target_cap[2]
    annotation (Dialog(group="Capacity",tab="Unit Characteristics"));
  parameter Real cst_target_cap[2]
    annotation (Dialog(group="Capacity",tab="Unit Characteristics"));
  parameter Real Nominal_cap_LN[2]
    annotation (Dialog(group="Capacity",tab="Unit Characteristics"));
  parameter Real OAT_target_cap_LN[2]
    annotation (Dialog(group="Capacity",tab="Unit Characteristics"));
  parameter Real cst_target_cap_LN[2]
    annotation (Dialog(group="Capacity",tab="Unit Characteristics"));
  parameter Boolean isCoating
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter.BOLT.InternalLibrary.Coolant.Pumps.DataBase.PumpPoly.Selector selector_pump[2]
    annotation (Dialog(group="Pump",tab="Unit Characteristics"));
  parameter Real[2] Coef_filter
    annotation (Dialog(tab="Unit Characteristics",group="Pump"));
  parameter Real[2] Coef_bufferTank
    annotation (Dialog(tab="Unit Characteristics",group="Pump"));
  parameter Real Fw_fan[2]
    annotation (Dialog(group="Fan",tab="Unit Characteristics"));
  parameter Real max_fan_frequency[2]
    annotation (Dialog(group="Fan",tab="Unit Characteristics"));
  parameter Real max_fan_frequency2[2]
    annotation (Dialog(group="Fan",tab="Unit Characteristics"));
  parameter Real fanCurveCoefficientsCooling[2,9]
    annotation (Dialog(group="Fan",tab="Unit Characteristics"));
  parameter Real fanCurveCoefficientsHeating[2,9]
    annotation (Dialog(group="Fan",tab="Unit Characteristics"));
  parameter Real Suction_line_diameter[2]
    annotation (Dialog(group="Suction Line",tab="Unit Characteristics"));
  parameter Real Suction_line_length[2]
    annotation (Dialog(group="Suction Line",tab="Unit Characteristics"));
  parameter Real coil_line_diameter[2]
    annotation (Dialog(group="Coil Line",tab="Unit Characteristics"));
  parameter Real coil_line_length[2]
    annotation (Dialog(group="Coil Line",tab="Unit Characteristics"));
  parameter Real liquid_line_diameter[2]
    annotation (Dialog(group="Liquid Line",tab="Unit Characteristics"));
  parameter Real liquid_line_length[2]
    annotation (Dialog(group="Liquid Line",tab="Unit Characteristics"));
  parameter Real discharge_line_diameter[2]
    annotation (Dialog(group="Discharge Line",tab="Unit Characteristics"));
  parameter Real discharge_line_length[2]
    annotation (Dialog(group="Discharge Line",tab="Unit Characteristics"));
  parameter Real EXV_in_line_diameter[2]
    annotation (Dialog(group="EXV in Line",tab="Unit Characteristics"));
  parameter Real EXV_in_line_length[2]
    annotation (Dialog(group="EXV in Line",tab="Unit Characteristics"));
  parameter Real Ac_duct[2]
    annotation (Dialog(group="Air Duct",tab="Unit Characteristics"));
  parameter Real Ka_duct[2]
    annotation (Dialog(group="Air Duct",tab="Unit Characteristics"));
  parameter Real UA_duct[2]
    annotation (Dialog(group="Air Duct",tab="Unit Characteristics"));
  parameter Real PDC_4WV[2]
    annotation (Dialog(group="4WV",tab="Unit Characteristics"));
  parameter Integer Heatcap_Tbiv[2]
    annotation (Dialog(group="4WV",tab="Unit Characteristics"));
  parameter Integer Heatcap_Tbiv_LN_option[2]
    annotation (Dialog(group="4WV",tab="Unit Characteristics"));
  parameter Real Freq_degi[2]
    annotation (Dialog(group="4WV",tab="Unit Characteristics"));
  parameter Real max_fan_ULN_heating[2]
    annotation (Dialog(group="Fan",tab="Unit Characteristics"));
  parameter Real max_cp_ULN_heating[2]
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Real max_fan_ULN_cooling[2]
    annotation (Dialog(group="Fan",tab="Unit Characteristics"));
  parameter Real max_cp_ULN_cooling[2]
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Real Zevap_HPH_cst[2]
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_HPH_SST[2]
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_coated_HPH[2]
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_HPH_min[2]
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_HPH_max[2]
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_HPC_cst[2]
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_HPC_SST[2]
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_HPC_min[2]
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_HPC_max[2]
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_chaud_Coolcap[2]
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_froid_Coolcap[2]
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zcond_HPH_cst[2]
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPH_SDT[2]
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPH_heatcap[2]
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPH_SST[2]
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPH_min[2]
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPH_max[2]
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPC_cst[2]
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPC_SDT[2]
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPC_coated[2]
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPC_min[2]
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPC_max[2]
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zflow_intercept[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zflow_SST[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zflow_SDT[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zflow_heatcap[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zflow_Ncomp[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zflow_SST2[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zflow_SST3[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zpower_intercept[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zpower_Ncomp[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zpower_DGT[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zpower_SST[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zpower_SST2[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zpower_SDT[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zpower_heatcap[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zpower_min[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zpower_max[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zflow_min[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zflow_max[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real FW[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Boolean use_bf=false
    annotation (Dialog(tab="BusinessFactor&EN14511"));
  //"if false BF's are set to 1 and controlledCapacity = engineering capacity"    
  parameter Boolean Use_EN=false
    annotation (Dialog(tab="BusinessFactor&EN14511"));
  //"if false controlledCapacity = gross capacity" ;
  parameter Boolean Use_defrost=true
    annotation (Dialog(tab="BusinessFactor&EN14511"));
  parameter Real const_bf_cap=const_bf_cap
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real load_bf_cap=load_bf_cap
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real OAT_bf_cap=OAT_bf_cap
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real OAT2_bf_cap=OAT2_bf_cap
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real OAT3_bf_cap=OAT3_bf_cap
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real const_bf_pow=const_bf_pow
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real load_bf_pow=load_bf_pow
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real OAT_bf_pow=OAT_bf_pow
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real OAT2_bf_pow=OAT2_bf_pow
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real OAT3_bf_pow=OAT3_bf_pow
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real max_bf_cap=max_bf_cap
    "capacity business factor max value"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real min_bf_cap=min_bf_cap
    "capacity business factor min value"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real max_bf_pow=max_bf_pow
    "power business factor max value"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real min_bf_pow=min_bf_pow
    "power business factor min value"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real mdot_start
    annotation (Dialog(group="Pump ",tab="Initialization"));
  inner.BOLT.InternalLibrary.Refrigerant.Aggregation.AggregateStreams_2 systemVariablesPump(
    isOff={not Use_pump,not Use_pump})
    annotation (Placement(transformation(extent={{53.64941913376437,125.42875105810268},{73.64941913376437,145.42875105810268}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.Mixer mixerPumpUserA(
    mDot_a_start=
      if Use_fake_pump then
        mdot_start
      else
        0,
    mDot_b_start=
      if not Use_fake_pump then
        mdot_start
      else
        0,
    p_start=Water_pressure,
    T_start=EWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff_a=not Use_fake_pump or isOFF,
    isOff_b=not FakePumpA.isOff or isOFF,
    fa_set=
      if Use_pump then
        0
      else
        1)
    annotation (Placement(transformation(extent={{-5.319920743950348,-5.319920743950334},{5.319920743950348,5.319920743950334}},origin={-100.77856842376012,-7.854595463337816},rotation=90.0)));
  .BOLT.CoolantMisc.Mixer mixerPumpUserB(
    mDot_a_start=
      if not Use_fake_pump then
        mdot_start
      else
        0,
    mDot_b_start=
      if Use_fake_pump then
        mdot_start
      else
        0,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    fa_set=
      if Use_pump then
        1
      else
        0,
    isOff_b=not Use_fake_pump or is_monobloc or isOFF,
    isOff_a=not FakePumpB.isOff or is_monobloc or isOFF,
    T_start=EWT,
    p_start=Water_pressure)
    annotation (Placement(transformation(extent={{-5.319920743950348,-5.319920743950334},{5.319920743950348,5.319920743950334}},origin={97.5852880366944,-8.222272438398097},rotation=90.0)));
  .Workspace.Auxiliary.EN14511.EN14511_HPC_HPH eN14511(
    integrated_pump=not Use_fake_pump,
    isOffA=isOFF,
    isOffB=is_monobloc or isOFF,
    ie=0.88,
    heating_mode=true,
    use_en=Use_EN and not isOFF)
    annotation (Placement(transformation(extent={{-227.06455898941076,16.703680613446103},{-207.06455898941076,36.7036806134461}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression DPe(
    y=-node_out.summary.p+node_coolant_source.summary.p)
    annotation (Placement(transformation(extent={{-278.07848984415244,41.89446061765005},{-258.68118289523767,61.29176756656482}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression DPi(
    y=FakePumpA.summary.dp)
    annotation (Placement(transformation(extent={{-276.98431235694375,27.631131242611637},{-257.587005408029,47.888438339593655}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression q(
    y=node_out.summary.Vd)
    annotation (Placement(transformation(extent={{-276.3679901734308,12.833522011361872},{-256.97068322451605,33.09082910834393}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression q_air_A_B(
    y=0)
    annotation (Placement(transformation(extent={{-276.3345274906958,-1.4648291807605496},{-256.937220541781,18.792477916221518}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression gross_cap(
    y=BlockA.condBPHE.summary.Q_flow_coolant + BlockB.condBPHE.summary.Q_flow_coolant + pumpPolyA.summary.P_pump + pumpPolyB.summary.P_pump)
    annotation (Placement(transformation(extent={{-276.3345274906958,-14.37047763410581},{-256.937220541781,5.026829314808964}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression gross_pow(
    y=BlockA.systemVariables.summary.pow_total+BlockB.systemVariables.summary.pow_total+pumpPolyA.summary.P_motor+pumpPolyB.summary.P_motor+Elec_box_power*(1-.Workspace.Auxiliary.Tools.booleanToReal(
      isOFF)))
    annotation (Placement(transformation(extent={{-277.15847750160935,-26.97924340650573},{-257.7611705526946,-7.581936457590956}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Auxiliary.Business_Factor.Business_Factors_heating businessFactors(
    use_business_factor=use_bf,
    is_monobloc=is_monobloc,
    Nrow=Nrow[1],
    const_bf_cap=const_bf_cap,
    load_bf_cap=load_bf_cap,
    OAT_bf_cap=OAT_bf_cap,
    OAT2_bf_cap=OAT2_bf_cap,
    OAT3_bf_cap=OAT3_bf_cap,
    const_bf_pow=const_bf_pow,
    load_bf_pow=load_bf_pow,
    OAT_bf_pow=OAT_bf_pow,
    OAT2_bf_pow=OAT2_bf_pow,
    OAT3_bf_pow=OAT3_bf_pow,
    max_bf_cap=max_bf_cap,
    min_bf_cap=min_bf_cap,
    max_bf_pow=max_bf_pow,
    min_bf_pow=min_bf_pow,
    Heatcap_Tbiv = BlockA.Heatcap_Tbiv)
    annotation (Placement(transformation(extent={{-154.32007589408292,24.273761802515814},{-127.36450204947496,51.22933564712377}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression load(
    y= if is_ULN_option == false then defrost_factor.Integrated_gross_heatcap/(Heatcap_Tbiv_tot) else defrost_factor.Integrated_gross_heatcap/(Heatcap_Tbiv_tot_LN_option) )
    annotation (Placement(transformation(extent={{-277.61175249357836,-44.13960696584968},{-258.2144455446636,-24.742300016934905}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Auxiliary.Defrost.Defrost_factor defrost_factor(
    use_DefrostFactor=Use_defrost,
    Nrow=Nrow[1],
    is_monobloc=is_monobloc,
    Frequence_degi=Freq_degi[1])
    annotation (Placement(transformation(extent={{-196.71482185336893,62.96517926041719},{-176.71482185336893,82.96517926041719}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression DPe_air_A_B(
    y=0)
    annotation (Placement(transformation(extent={{-277.35035284000753,55.45501543006122},{-257.95304589109276,74.852322378976}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression RH(
    y=BlockA.sourceAir.summary.RH)
    annotation (Placement(transformation(extent={{-277.38818229732306,87.86961319620329},{-259.9226154440553,105.335180049471}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression OAT_(
    y=BlockA.sourceAir.summary.Tdb)
    annotation (Placement(transformation(extent={{-277.66247417481395,100.67667808128334},{-260.1969073215462,118.14224493455106}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression External_Pressure(
    y=node_out.p)
    annotation (Placement(transformation(extent={{-10.0,-10.0},{10.0,10.0}},origin={-2.0,217.56292075622798},rotation=-90.0)));
  parameter Real[2] ssh_setPoint={5,5}
    "circuit A ssh set point"
    annotation (Dialog(group="Refrigerant"));
  parameter.Modelica.SIunits.TemperatureDifference[2] SC_setpoint={-2,-2}
    annotation (Dialog(group="Refrigerant"));
  parameter Boolean[2] SC_fixed={true,true}
    annotation (Dialog(group="Refrigerant"));
  parameter Boolean[2] Mref_fixed={false,false}
    annotation (Dialog(group="Refrigerant"));
  .BOLT.CoolantMisc.ReducedPipe FakePumpA(
    isOff=not Use_fake_pump or isOFF,
    use_Ka_in=true,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration)
    annotation (Placement(transformation(extent={{7.9306406917366985,-7.9306406917366985},{-7.9306406917366985,7.9306406917366985}},origin={-106.0,-30.0},rotation=-90.0)));
  .BOLT.CoolantMisc.ReducedPipe FakePumpB(
    isOff=not Use_fake_pump or is_monobloc or isOFF,
    use_Ka_in=true,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration)
    annotation (Placement(transformation(extent={{-7.9306406917366985,-7.9306406917366985},{7.9306406917366985,7.9306406917366985}},origin={100.0,-32.0},rotation=90.0)));
  .Modelica.Blocks.Sources.RealExpression P_comp(
    y=BlockA.compressor.summary.P_compression+BlockB.compressor.summary.P_compression)
    annotation (Placement(transformation(extent={{-278.04920234554174,114.52269178602921},{-260.58363549227397,131.98825863929693}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression Ncomp(
    y=BlockA.compressor.summary.Ncomp)
    annotation (Placement(transformation(extent={{-278.32595594873953,126.29938401042978},{-260.86038909547176,143.7649508636975}},origin={0.0,0.0},rotation=0.0)));
  inner.BOLT.GlobalParameters globalParameters(
    RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R290,
    varLevel=.BOLT.InternalLibrary.BuildingBlocks.Types.Var_level.Advanced)
    annotation (Placement(transformation(extent={{80.0,124.0},{100.0,144.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.ReducedPipe buffer_tank(
    isOff=isOFF,
    Ka_set=Coef_bufferTank[1]*.Workspace.Auxiliary.Tools.booleanToReal(
      isBufferTank)+0.0001*(1-.Workspace.Auxiliary.Tools.booleanToReal(
      isBufferTank)),
    Kb=Coef_bufferTank[2]*.Workspace.Auxiliary.Tools.booleanToReal(
      isBufferTank)+0,
    X=BrineConcentration,
    CoolantMedium=CoolantMedium,
    T_start=EWT)
    annotation (Placement(transformation(extent={{-6.386404559420782,-6.386404559420782},{6.386404559420782,6.386404559420782}},origin={-2.29686437207123,102.88360829639835},rotation=90.0)));
  .BOLT.BoundaryNode.Coolant.Node node_buffer_in(
    isOff=isOFF,
    X=BrineConcentration,
    CoolantMedium=CoolantMedium,
    T_start=LWT)
    annotation (Placement(transformation(extent={{-4.0,-4.0},{4.0,4.0}},origin={-2.529353788005114,86.05484504996053},rotation=90.0)));
  parameter Integer Heatcap_Tbiv_tot=
    if is_monobloc then
      BlockA.Heatcap_Tbiv
    else
      BlockA.Heatcap_Tbiv+BlockB.Heatcap_Tbiv;
    
    parameter Integer Heatcap_Tbiv_tot_LN_option=
    if is_monobloc then
      BlockA.Heatcap_Tbiv_LN_option
    else
      BlockA.Heatcap_Tbiv_LN_option+BlockB.Heatcap_Tbiv_LN_option;
  .Modelica.SIunits.PressureDifference InternalPressureDrop=-FakePumpA.summary.dp+(pumpPolyA.summary.dp_pump-node_out.summary.p+node_coolant_source.summary.p)*(1-.Workspace.Auxiliary.Tools.booleanToReal(
    Use_fake_pump))
    "Internal hydraulic pressure drop ";
  .Modelica.SIunits.PressureDifference AvailableStaticPressure=node_out.summary.p-node_coolant_source.summary.p;
  Real BF_pow_out=
    if not isOFF then
      businessFactors.pub_net_pow/businessFactors.net_pow
    else
      1
    "final business factor";
  Real BF_cap_out=
    if not isOFF then
      businessFactors.pub_net_heat_cap/businessFactors.net_heat_cap
    else
      1
    "Final business factor for capacity ";
  Real DF_cap_out=
    if not isOFF then
      defrost_factor.Integrated_net_heatcap/(defrost_factor.Net_Heatcap)
    else
      1
    "final business factor";
  Real DF_pow_out=
    if not isOFF then
      defrost_factor.Integrated_net_power/((defrost_factor.Net_Power))
    else
      1
    "final business factor";
  Real InstantaneousHeatingCapacity=eN14511.inst_net_cap*BF_cap_out
    "Heating capacity without frost factor";
  .Modelica.SIunits.VolumeFlowRate CondFlowRate=
    if not isOFF then
      BF_cap_out*node_coolant_source.summary.Vd
    else
      0
    "outlet flow rate included BF factor correction";
  .Modelica.SIunits.Temperature Cond_ewt=
    if not isOFF then
      BF_cap_out*node_coolant_source.summary.T+(1-BF_cap_out)*node_out.summary.T
    else
      0
    "outlet flow rate included BF factor correction";
  .Modelica.SIunits.Temperature Cond_lwt=
    if not isOFF then
      BF_cap_out*node_out.summary.T+(1-BF_cap_out)*node_coolant_source.summary.T
    else
      0
    "outlet flow rate included BF factor correction";
    parameter Real Altitude = 0;
    parameter Real Max_max_target_cap[2] annotation(Dialog(group = "Capacity",tab = "Unit Characteristics"));
    parameter Real Max_max_target_cap_LN[2] annotation(Dialog(group = "Capacity",tab = "Unit Characteristics"));
    parameter Real Max_max_target_cap_total = BlockA.Max_max_target_capa + BlockB.Max_max_target_capa;
    parameter Boolean is_ULN_option = false;
equation
  controlledCapacity=businessFactors.pub_net_heat_cap;
  controlledPower=businessFactors.pub_net_pow;
  connect(split_pumpB.port_a,pumpPolyB.port_a)
    annotation (Line(points={{95.1705875471087,-52.134538889964865},{95.1705875471087,-44.926847888774006},{57.59422308194374,-44.926847888774006},{57.59422308194374,-37.42569341417812}},color={0,127,0}));
  connect(node_blocB_out.port_b,mixer.port_b)
    annotation (Line(points={{9.716900311317987,49.723549174361885},{2.599608435561697,49.723549174361885},{2.599608435561697,56.06009079921068}},color={0,127,0}));
  connect(node_blocA_out.port_b,mixer.port_a)
    annotation (Line(points={{-14.619641313530828,49.723549174361885},{-7.400391564438303,49.723549174361885},{-7.400391564438303,56.06009079921068}},color={0,127,0}));
  connect(split_bloc.port_b,node_coolant_in_pumpB.port_a)
    annotation (Line(points={{0.7430350354416393,-74.05115498366641},{0.7430350354416393,-63.902844389527},{41.77608337530837,-63.902844389527}},color={0,127,0}));
  connect(node_coolant_in_pumpB.port_b,split_pumpB.port_c)
    annotation (Line(points={{49.77608337530837,-63.902844389527},{98.01912393006732,-63.902844389527},{98.01912393006732,-62.79017498918046}},color={0,127,0}));
  connect(split_bloc.port_a,node_coolant_in_pumpA.port_a)
    annotation (Line(points={{-4.743035035441636,-74.05115498366641},{-4.743035035441636,-63.69206959008456},{-37.41409050210664,-63.69206959008456}},color={0,127,0}));
  connect(node_coolant_in_splitbloc.port_b,split_bloc.port_c)
    annotation (Line(points={{-2.834694170616461,-90.43853299464625},{-2.834694170616461,-84.706791082882},{-1.8944986524830152,-84.706791082882}},color={0,127,0}));
  connect(shafA.flange,pumpPolyA.flange)
    annotation (Line(points={{-40.2918498052081,-3.3283811868263804},{-31.988777703356632,-3.3283811868263804},{-31.988777703356632,-29.698159792787973},{-56.688211421548345,-29.698159792787973}},color={127,0,0}));
  connect(shafB.flange,pumpPolyB.flange)
    annotation (Line(points={{33.23789886228211,-2.6142645441622676},{27.23789886228211,-2.6142645441622676},{27.23789886228211,-29.032295553730396},{49.20082522149602,-29.032295553730396}},color={127,0,0}));
  connect(PipingB.port_b,gaz_separatorB.port_a)
    annotation (Line(points={{97.57846085003577,19.799795340188318},{97.20855048825722,19.799795340188318},{97.20855048825722,25.647303008535474}},color={0,127,0}));
  connect(PipingA.port_b,gaz_separatorA.port_a)
    annotation (Line(points={{-101.26278268171433,21.38260042972899},{-101.26278268171433,27.23010809807615}},color={0,127,0}));
  connect(ActuatorPumpUser_B,shafB.speed_in)
    annotation (Line(points={{38,22},{38,-1.0142645441622675},{41.23789886228211,-1.0142645441622675}},color={0,0,127}));
  connect(ActuatorPumpUser_A,shafA.speed_in)
    annotation (Line(points={{-58,24},{-58,-1.7283811868263803},{-48.2918498052081,-1.7283811868263803}},color={0,0,127}));
  connect(mixerPumpUserA.port_c,PipingA.port_a)
    annotation (Line(points={{-100.77856842376012,-2.3218778896294543},{-100.77856842376012,3.4541151042233507},{-101.26278268171433,3.4541151042233507},{-101.26278268171433,9.230108098076158}},color={0,127,0}));
  connect(mixerPumpUserB.port_c,PipingB.port_a)
    annotation (Line(points={{97.5852880366944,-2.689554864689735},{97.5852880366944,2.4788740719228692},{97.57846085003577,2.4788740719228692},{97.57846085003577,7.647303008535481}},color={0,127,0}));
  connect(pumpPolyA.port_b,mixerPumpUserA.port_b)
    annotation (Line(points={{-65.08160928199607,-21.304761932340256},{-65.08160928199607,-13.174516207288164},{-98.01220963690595,-13.174516207288164}},color={0,127,0}));
  connect(pumpPolyB.port_b,mixerPumpUserB.port_a)
    annotation (Line(points={{57.59422308194374,-20.63889769328268},{57.59422308194374,-13.542193182348445},{95.03172607959823,-13.542193182348445}},color={0,127,0}));
  connect(BlockA.port_b,node_blocA_out.port_a)
    annotation (Line(points={{-29.81223706314183,50.001104851458585},{-22.619641313530828,50.001104851458585},{-22.619641313530828,49.723549174361885}},color={0,127,0}));
  connect(BlockA.actual_FSFanSpd_in,Fan_controller_A)
    annotation (Line(points={{-52.41223706314183,54.001104851458585},{-76.90288730001818,54.001104851458585},{-76.90288730001818,59.88271552516719},{-98.35380587259328,59.88271552516719}},color={0,0,127}));
  connect(BlockA.EXV_controller,EXV_controller_A)
    annotation (Line(points={{-52.41223706314183,59.001104851458585},{-76.50394290903301,59.001104851458585},{-76.50394290903301,69.36437114069773},{-99.55423712065695,69.36437114069773}},color={0,0,127}));
  connect(BlockA.compressor_controller,Compressor_controller_A)
    annotation (Line(points={{-52.41223706314183,62.001104851458585},{-76.18453903904617,62.001104851458585},{-76.18453903904617,79.21770595805611},{-98.93896918087167,79.21770595805611}},color={0,0,127}));
  connect(BlockA.measurementBus,measurementBusA)
    annotation (Line(points={{-30.41223706314183,58.001104851458585},{-27.682450951782435,58.001104851458585},{-27.682450951782435,160.61132870676136}},color={255,204,51}));
  connect(gaz_separatorA.port_b,BlockA.port_a)
    annotation (Line(points={{-101.26278268171433,39.38260042972899},{-101.26278268171433,50.001104851458585},{-52.41223706314183,50.001104851458585}},color={0,127,0}));
  connect(BlockB.port_b,node_blocB_out.port_a)
    annotation (Line(points={{30.399447574270646,49.42963602090205},{17.716900311317985,49.42963602090205},{17.716900311317985,49.723549174361885}},color={0,127,0}));
  connect(BlockB.measurementBus,measurementBusB)
    annotation (Line(points={{30.999447574270647,57.42963602090205},{23.01016289913556,57.42963602090205},{23.01016289913556,160.61132870676136}},color={255,204,51}));
  connect(gaz_separatorB.port_b,BlockB.port_a)
    annotation (Line(points={{97.20855048825722,37.79979534018831},{97.20855048825722,49.42963602090205},{52.99944757427065,49.42963602090205}},color={0,127,0}));
  connect(Fan_controller_B,BlockB.actual_FSFanSpd_in)
    annotation (Line(points={{87.27328072023931,57.546179229103544},{70.19975331193633,57.546179229103544},{70.19975331193633,53.42963602090205},{52.99944757427065,53.42963602090205}},color={0,0,127}));
  connect(EXV_controller_B,BlockB.EXV_controller)
    annotation (Line(points={{87.18309154797421,65.41679340214176},{70.15465872580378,65.41679340214176},{70.15465872580378,58.42963602090205},{52.99944757427065,58.42963602090205}},color={0,0,127}));
  connect(Compressor_controller_B,BlockB.compressor_controller)
    annotation (Line(points={{86.85661936303646,74.37733982899786},{70.2636455748654,74.37733982899786},{70.2636455748654,61.42963602090205},{52.99944757427065,61.42963602090205}},color={0,0,127}));
  connect(split_pumpA.port_b,pumpPolyA.port_a)
    annotation (Line(points={{-96.98763091089418,-51.311992432272305},{-96.98763091089418,-44.55022524213727},{-65.08160928199607,-44.55022524213727},{-65.08160928199607,-38.0915576532357}},color={0,127,0}));
  connect(node_coolant_in_pumpA.port_b,split_pumpA.port_c)
    annotation (Line(points={{-45.41409050210664,-63.69206959008456},{-99.62516459881883,-63.69206959008456},{-99.62516459881883,-61.9676285314879}},color={0,127,0}));
  connect(coolant_in,node_coolant_source.port_a)
    annotation (Line(points={{-3.480089871014542,-146.7059464254537},{-3.480089871014542,-131.20519966131292},{-3.4013608372831245,-131.20519966131292}},color={0,127,0}));
  connect(node_out.port_b,coolant_out)
    annotation (Line(points={{-2.105738743527996,123.69648262111227},{-2.105738743527996,142.6738698914627},{-1.4054814046826252,142.6738698914627}},color={0,127,0}));
  connect(eN14511.DPe,DPe.y)
    annotation (Line(points={{-227.06455898941076,32.903680613446106},{-240.33732796153723,32.903680613446106},{-240.33732796153723,51.593114092107434},{-257.7113175477919,51.593114092107434}},color={0,0,127}));
  connect(eN14511.DPi,DPi.y)
    annotation (Line(points={{-227.06455898941076,27.1036806134461},{-240.60344467994113,27.1036806134461},{-240.60344467994113,37.759784791102646},{-256.6171400605832,37.759784791102646}},color={0,0,127}));
  connect(eN14511.q,q.y)
    annotation (Line(points={{-227.06455898941076,25.303680613446105},{-240.47595937186642,25.303680613446105},{-240.47595937186642,22.962175559852902},{-256.0008178770703,22.962175559852902}},color={0,0,127}));
  connect(eN14511.q_air_A,q_air_A_B.y)
    annotation (Line(points={{-227.06455898941076,23.303680613446105},{-240.4808920940728,23.303680613446105},{-240.4808920940728,8.663824367730484},{-255.96735519433523,8.663824367730484}},color={0,0,127}));
  connect(eN14511.q_air_B,q_air_A_B.y)
    annotation (Line(points={{-227.06455898941076,21.303680613446105},{-240.58993794141745,21.303680613446105},{-240.58993794141745,8.663824367730484},{-255.96735519433523,8.663824367730484}},color={0,0,127}));
  connect(eN14511.inst_gross_cap,gross_cap.y)
    annotation (Line(points={{-227.06455898941076,19.303680613446105},{-240.18208135886297,19.303680613446105},{-240.18208135886297,-4.671824159648423},{-255.96735519433523,-4.671824159648423}},color={0,0,127}));
  connect(eN14511.inst_gross_pow,gross_pow.y)
    annotation (Line(points={{-227.06455898941076,17.303680613446105},{-240.44676722662317,17.303680613446105},{-240.44676722662317,-17.28058993204835},{-256.7913052052488,-17.28058993204835}},color={0,0,127}));
  connect(defrost_factor.Net_Power,eN14511.inst_net_pow)
    annotation (Line(points={{-196.76261593674676,71.10232046711647},{-201.25909327676288,71.10232046711647},{-201.25909327676288,23.703680613446103},{-207.06455898941076,23.703680613446103}},color={0,0,127}));
  connect(defrost_factor.Net_Heatcap,eN14511.inst_net_cap)
    annotation (Line(points={{-196.76261593674676,67.90232046711645},{-201.29710343418134,67.90232046711645},{-201.29710343418134,29.703680613446103},{-207.06455898941076,29.703680613446103}},color={0,0,127}));
  connect(eN14511.DPe_air_A,DPe_air_A_B.y)
    annotation (Line(points={{-227.06455898941076,30.903680613446106},{-240.40246326187017,30.903680613446106},{-240.40246326187017,65.15366890451861},{-256.983180543647,65.15366890451861}},color={0,0,127}));
  connect(eN14511.DPe_air_B,DPe_air_A_B.y)
    annotation (Line(points={{-227.06455898941076,28.903680613446106},{-240.40246326187017,28.903680613446106},{-240.40246326187017,65.15366890451861},{-256.983180543647,65.15366890451861}},color={0,0,127}));
  connect(External_Pressure.y,measurementBusA.External_Pressure)
    annotation (Line(points={{-1.9999999999999976,206.56292075622798},{-1.9999999999999976,183.80566435338068},{-27.682450951782435,183.80566435338068},{-27.682450951782435,160.61132870676136}},color={0,0,127}));
  connect(External_Pressure.y,measurementBusB.External_Pressure)
    annotation (Line(points={{-1.9999999999999976,206.56292075622798},{-1.9999999999999976,183.80566435338068},{23.01016289913556,183.80566435338068},{23.01016289913556,160.61132870676136}},color={0,0,127}));
  connect(split_pumpA.port_a,FakePumpA.port_a)
    annotation (Line(points={{-102.47370098177745,-51.311992432272305},{-102.47370098177745,-46.6213165620045},{-106,-46.6213165620045},{-106,-37.9306406917367}},color={0,127,0}));
  connect(FakePumpA.port_b,mixerPumpUserA.port_a)
    annotation (Line(points={{-106,-22.0693593082633},{-103.33213038085628,-22.0693593082633},{-103.33213038085628,-13.174516207288164}},color={0,127,0}));
  connect(ActuatorPumpUser_A,FakePumpA.Ka_in)
    annotation (Line(points={{-58,24},{-76.19329246796639,24},{-76.19329246796639,-32.85503064902521},{-98.38658493593277,-32.85503064902521}},color={0,0,127}));
  connect(split_pumpB.port_b,FakePumpB.port_a)
    annotation (Line(points={{100.65665761799197,-52.134538889964865},{100.65665761799197,-39.9306406917367},{100,-39.9306406917367}},color={0,127,0}));
  connect(FakePumpB.port_b,mixerPumpUserB.port_b)
    annotation (Line(points={{100,-24.0693593082633},{100,-18.80577624530587},{100.35164682354856,-18.80577624530587},{100.35164682354856,-13.542193182348445}},color={0,127,0}));
  connect(ActuatorPumpUser_B,FakePumpB.Ka_in)
    annotation (Line(points={{38,22},{65.19329246796639,22},{65.19329246796639,-34.85503064902521},{92.38658493593277,-34.85503064902521}},color={0,0,127}));
  connect(defrost_factor.Pcompresseur,P_comp.y)
    annotation (Line(points={{-196.81729748212604,80.07794517623822},{-226.87989451561307,80.07794517623822},{-226.87989451561307,123.25547521266307},{-259.71035714961056,123.25547521266307}},color={0,0,127}));
  connect(defrost_factor.Ncomp,Ncomp.y)
    annotation (Line(points={{-196.71729748212604,78.37794517623823},{-226.98835121245136,78.37794517623823},{-226.98835121245136,135.03216743706363},{-259.98711075280835,135.03216743706363}},color={0,0,127}));
  connect(OAT_.y,defrost_factor.OAT)
    annotation (Line(points={{-259.3236289788829,109.4094615079172},{-227.1177651685609,109.4094615079172},{-227.1177651685609,75.61232046711646},{-196.76261593674676,75.61232046711646}},color={0,0,127}));
  connect(defrost_factor.RH,RH.y)
    annotation (Line(points={{-196.76261593674676,74.10232046711647},{-227.0715429779344,74.10232046711647},{-227.0715429779344,96.60239662283715},{-259.04933710139187,96.60239662283715}},color={0,0,127}));
  connect(gross_pow.y,defrost_factor.Gross_Power)
    annotation (Line(points={{-256.7913052052488,-17.28058993204835},{-201.57240851020507,-17.28058993204835},{-201.57240851020507,69.50232046711645},{-196.76261593674676,69.50232046711645}},color={0,0,127}));
  connect(gross_cap.y,defrost_factor.Gross_Heatcap)
    annotation (Line(points={{-255.96735519433523,-4.671824159648423},{-201.3840394630533,-4.671824159648423},{-201.3840394630533,66.50232046711645},{-196.76261593674676,66.50232046711645}},color={0,0,127}));
  connect(defrost_factor.Integrated_net_heatcap,businessFactors.net_heat_cap)
    annotation (Line(points={{-175.5278858016935,70.06242222833211},{-167.3111495187193,70.06242222833211},{-167.3111495187193,47.6555182382147},{-155.82716495706953,47.6555182382147}},color={0,0,127}));
  connect(defrost_factor.Integrated_net_power,businessFactors.net_pow)
    annotation (Line(points={{-175.57210699176636,74.9408181617577},{-167.29845137529162,74.9408181617577},{-167.29845137529162,44.19791963788415},{-155.79835534190167,44.19791963788415}},color={0,0,127}));
  connect(OAT_.y,businessFactors.OAT)
    annotation (Line(points={{-259.3236289788829,109.4094615079172},{-226.96561286404219,109.4094615079172},{-226.96561286404219,41.08603641956778},{-155.82716495706953,41.08603641956778}},color={0,0,127}));
  connect(defrost_factor.Integrated_gross_power,businessFactors.gross_pow)
    annotation (Line(points={{-175.57210699176636,77.8443386242358},{-167.29070939542828,77.8443386242358},{-167.29070939542828,37.196293591719495},{-155.82716495706953,37.196293591719495}},color={0,0,127}));
  connect(defrost_factor.Integrated_gross_heatcap,businessFactors.gross_heat_cap)
    annotation (Line(points={{-175.5278858016935,72.76795198015424},{-167.2282233991435,72.76795198015424},{-167.2282233991435,33.594646915549696},{-155.97121303290876,33.594646915549696}},color={0,0,127}));
  connect(load.y,businessFactors.load)
    annotation (Line(points={{-257.24458019721783,-34.440953491392285},{-166.49012851176056,-34.440953491392285},{-166.49012851176056,29.591650622971684},{-155.96075347845445,29.591650622971684}},color={0,0,127}));
  connect(node_coolant_source.port_b,filter.port_a)
    annotation (Line(points={{-3.4013608372831263,-123.20519966131293},{-3.4013608372831263,-116.37385217929781},{-2.747407815138151,-116.37385217929781}},color={0,127,0}));
  connect(node_coolant_in_splitbloc.port_a,filter.port_b)
    annotation (Line(points={{-2.834694170616459,-98.43853299464625},{-2.834694170616459,-103.60104306045625},{-2.7474078151381485,-103.60104306045625}},color={0,127,0}));
  connect(mixer.port_c,node_buffer_in.port_a)
    annotation (Line(points={{-2.6003915644382984,76.46009079921069},{-2.6003915644382984,79.25746792458561},{-2.529353788005115,79.25746792458561},{-2.529353788005115,82.05484504996053}},color={0,127,0}));
  connect(node_buffer_in.port_b,buffer_tank.port_a)
    annotation (Line(points={{-2.529353788005113,90.05484504996053},{-2.529353788005113,93.27602439346904},{-2.2968643720712314,93.27602439346904},{-2.2968643720712314,96.49720373697757}},color={0,127,0}));
  connect(buffer_tank.port_b,node_out.port_a)
    annotation (Line(points={{-2.2968643720712287,109.27001285581913},{-2.2968643720712287,112.4832477384657},{-2.105738743527998,112.4832477384657},{-2.105738743527998,115.69648262111227}},color={0,127,0}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={49,49,49},
          fillColor={181,124,237},
          fillPattern=FillPattern.Solid,
          extent={{-100,-119},{100,119}},
          origin={0,-19}),
        Text(
          textString="MONOBLOC",
          origin={0,-14},
          extent={{-100,35},{100,-35}},
          lineColor={237,237,237}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end Equipement_Modular;
