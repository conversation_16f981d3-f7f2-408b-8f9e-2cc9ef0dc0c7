within Workspace.Auxiliary.Calibration;
model CalibrationBlock
  //Input
  .Modelica.Blocks.Interfaces.RealInput SST
    annotation (Placement(transformation(extent={{-87.72283959637137,81.37716040362862},{-68.27716040362863,100.82283959637137}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput SDT
    annotation (Placement(transformation(extent={{-87.72283959637137,66.27716040362861},{-68.27716040362863,85.72283959637139}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput Ncomp
    annotation (Placement(transformation(extent={{-87.72283959637137,20.277160403628628},{-68.27716040362863,39.72283959637137}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput Q_flow
    annotation (Placement(transformation(extent={{-87.72283959637137,-21.722839596371372},{-68.27716040362863,-2.2771604036286277}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput SH
    annotation (Placement(transformation(extent={{-87.72283959637137,6.277160403628628},{-68.27716040362863,25.722839596371372}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput DGT
    annotation (Placement(transformation(extent={{-87.72283959637137,-7.722839596371372},{-68.27716040362863,11.722839596371372}},origin={0.0,0.0},rotation=0.0)));
  //Output
  .Modelica.Blocks.Interfaces.RealOutput Z_Evap_HPC
    annotation (Placement(transformation(extent={{188.0,56.0},{208.0,76.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Z_Evap_HPH
    annotation (Placement(transformation(extent={{188.0,82.0},{208.0,102.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Z_Cond_HPC
    annotation (Placement(transformation(extent={{189.6,-21.070769230769233},{209.6,-1.0707692307692334}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Z_Cond_HPH
    annotation (Placement(transformation(extent={{187.6,4.929230769230767},{207.6,24.929230769230767}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Z_Power
    annotation (Placement(transformation(extent={{189.6,-45.07076923076923},{209.6,-25.07076923076923}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Z_Flow
    annotation (Placement(transformation(extent={{187.6,32.92923076923077},{207.6,52.92923076923077}},origin={0.0,0.0},rotation=0.0)));
  parameter Boolean is_CoatingOption=false
    " true if there is coating on both coils "
    annotation (Dialog(tab="Condenser",group="Coating Option"));
  //Evap_HPC // 
  parameter Real Zevap_HPC_cst
    annotation (Dialog(group="Coefficients ZUev",tab="Evaporator"));
  parameter Real Zevap_HPC_heatcap
    annotation (Dialog(group="Coefficients ZUev",tab="Evaporator"));
  parameter Real Zevap_HPC_SST
    annotation (Dialog(group="Coefficients ZUev",tab="Evaporator"));
  parameter Real Zevap_HPH_cst
    annotation (Dialog(group="Coefficients ZUev",tab="Evaporator"));
  parameter Real Zevap_HPH_heatcap
    annotation (Dialog(group="Coefficients ZUev",tab="Evaporator"));
  parameter Real Zevap_HPH_Ncomp
    annotation (Dialog(group="Coefficients ZUev",tab="Evaporator"));
  parameter Real Zevap_HPH_SST
    annotation (Dialog(group="Coefficients ZUev",tab="Evaporator"));
  //Compressor
  //Z_Pow
  parameter Real Zpower_intercept
    annotation (Dialog(group="Coefficients Z_Power",tab="Compressor"));
  parameter Real Zpower_SH
    annotation (Dialog(group="Coefficients Z_Power",tab="Compressor"));
  parameter Real Zpower_DGT
    annotation (Dialog(group="Coefficients Z_Power",tab="Compressor"));
  parameter Real Zpower_SST
    annotation (Dialog(group="Coefficients Z_Power",tab="Compressor"));
  parameter Real Zpower_Zflow
    annotation (Dialog(group="Coefficients Z_Power",tab="Compressor"));
  //Z_flow
  parameter Real Zflow_intercept
    annotation (Dialog(group="Coefficients Z_Flow",tab="Compressor"));
  parameter Real Zflow_Ncomp
    annotation (Dialog(group="Coefficients Z_Flow",tab="Compressor"));
  parameter Real Zflow_SDT
    annotation (Dialog(group="Coefficients Z_Flow",tab="Compressor"));
  parameter Real Zflow_SST
    annotation (Dialog(group="Coefficients Z_Flow",tab="Compressor"));
  parameter Real Zflow_heatcap
    annotation (Dialog(group="Coefficients Z_Flow",tab="Compressor"));
  //Condenser
  parameter Real Zcond_HPC_cst
    annotation (Dialog(tab="Condenser",group="Condenser"));
  parameter Real Zcond_HPC_DGT
    annotation (Dialog(tab="Condenser",group="Condenser"));
  parameter Real Zcond_HPC_SDT
    annotation (Dialog(tab="Condenser",group="Condenser"));
  parameter Real Zcond_HPC_Ncomp
    annotation (Dialog(tab="Condenser",group="Condenser"));
  parameter Real Zcond_HPH_cst
    annotation (Dialog(tab="Condenser",group="Condenser"));
  parameter Real Zcond_HPH_heatcap
    annotation (Dialog(tab="Condenser",group="Condenser"));
  parameter Real Zcond_HPH_SST
    annotation (Dialog(tab="Condenser",group="Condenser"));
  parameter Real Zcond_HPH_SDT
    annotation (Dialog(tab="Condenser",group="Condenser"));
  parameter Real Zcoated
    annotation (Dialog(tab="Condenser",group="Condenser"));
  //Limits
  parameter Real Z_cond_HPH_min=0.85
    annotation (Dialog(tab="Condenser",group="Limits"));
  parameter Real Z_cond_HPH_max=3
    annotation (Dialog(tab="Condenser",group="Limits"));
  parameter Real Z_cond_HPC_min=0.6
    annotation (Dialog(tab="Condenser",group="Limits"));
  parameter Real Z_cond_HPC_max=1
    annotation (Dialog(tab="Condenser",group="Limits"));
  parameter Real Z_Evap_HPC_min=0.3
    annotation (Dialog(tab="Condenser",group="Limits"));
  parameter Real Z_Evap_HPC_max=1
    annotation (Dialog(tab="Condenser",group="Limits"));
  parameter Real Z_Flow_min=0.85
    annotation (Dialog(tab="Condenser",group="Limits"));
  parameter Real Z_Flow_max=1.075
    annotation (Dialog(tab="Condenser",group="Limits"));
  parameter Real Z_Power_min=0.9
    annotation (Dialog(tab="Condenser",group="Limits"));
  parameter Real Z_Power_max=1.04
    annotation (Dialog(tab="Condenser",group="Limits"));
//Evaporator
equation
  Z_Evap_HPC=min(
    max(
      Zevap_HPC_cst+Zevap_HPC_heatcap*Q_flow+Zevap_HPC_SST*SST,
      Z_Evap_HPC_min),
    Z_Evap_HPC_max);
  Z_Evap_HPH=
    if is_CoatingOption then
      Zcoated
    else
      Zevap_HPH_cst+Zevap_HPH_SST*SST+Zevap_HPH_heatcap*Q_flow+Zevap_HPH_Ncomp*Ncomp;
//Batterie
equation
  Z_Cond_HPC=
    if is_CoatingOption then
      Zcoated
    else
      min(
        max(
          Zcond_HPC_cst+Zcond_HPC_DGT*DGT+Zcond_HPC_SDT*SDT+Zcond_HPC_Ncomp*Ncomp,
          Z_cond_HPC_min),
        Z_cond_HPC_max);
  Z_Cond_HPH=min(
    max(
      Zcond_HPH_cst+Zcond_HPH_heatcap*Q_flow+Zcond_HPH_SST*SST+Zcond_HPH_SDT*SDT,
      Z_cond_HPH_min),
    Z_cond_HPH_max);
  //Compressor
  Z_Flow=min(
    max(
      Zflow_intercept+Zflow_SDT*SDT+Zflow_Ncomp*Ncomp+Zflow_SST*SST+Zflow_heatcap*Q_flow,
      Z_Flow_min),
    Z_Flow_max);
  Z_Power=min(
    max(
      Zpower_intercept+Zpower_SST*SST+Zpower_SH*SH+Zpower_DGT*DGT+Zpower_Zflow*Z_Flow,
      Z_Power_min),
    Z_Power_max);
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          fillColor={32,50,18},
          fillPattern=FillPattern.Solid,
          extent={{-126,-119},{126,119}},
          origin={62,13}),
        Text(
          lineColor={255,255,255},
          extent={{-123,27},{123,-27}},
          textString="CalibrationBlock",
          origin={58,12})}));
end CalibrationBlock;
