within Workspace.System.HPH.BaseCycles.optimization;
model Unit_optimization
  import CoolantCommonMedium=BOLT.InternalLibrary.Media.Coolant.CoolantCommon;
  .Workspace.System.HPH.BaseCycles.Equipement oL_modular(
    EWT=EWT,
    LWT=LWT,
    is_monobloc=is_monobloc,
    Use_pump=Use_pump,
    Use_filter=Use_filter,
    Use_expansion_tank=Use_tank,
    OAT=OAT,
    OAT_WB=OAT_WB,
    isOFFB=isOFFB,
    isOFFA=isOFFA,
    Unit_size=Unit_size,
    BrineConcentration=BrineConcentration,
    CoolantMedium=CoolantMedium,
    Water_pressure=sourceBrine.p_set)
    annotation (Placement(transformation(extent={{-33.935153134661874,3.612627612218235},{-13.935153134661874,23.612627612218233}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Source sourceBrine(
    Vd_fixed=false,
    T_set=EWT,
    p_fixed=true,
    X=BrineConcentration,
    CoolantMedium=CoolantMedium)
    annotation (Placement(transformation(extent={{-4.0,-4.0},{4.0,4.0}},origin={-22.0,-22.0},rotation=90.0)));
  .BOLT.BoundaryNode.Coolant.Sink sinkBrine(
    p_fixed=false,
    T_fixed=true,
    T_set=LWT,
    X=BrineConcentration,
    CoolantMedium=CoolantMedium)
    annotation (Placement(transformation(extent={{-4.0,-4.0},{4.0,4.0}},origin={-20.0,44.0},rotation=-90.0)));
  parameter Boolean is_monobloc=
    if Unit_size == 40 then
      true
    elseif Unit_size == 50 then
      true
    elseif Unit_size == 60 then
      true
    elseif Unit_size == 70 then
      true
    else
      false
    annotation (Dialog(group="Unit_config"));
  parameter Boolean Use_pump=false;
  parameter Boolean Use_tank=false;
  parameter Boolean Use_filter=false;
  parameter Boolean is0ff=false;
  parameter Boolean isOFFA=false
    annotation (Dialog(group="Cp_control"));
  parameter Boolean isOFFB=false
    annotation (Dialog(group="Cp_control"));
  parameter Integer Unit_size=70
    "Stringify values -- IPM Cloud limitation"
    annotation (Dialog(group="Unit_config"));
  parameter.Modelica.SIunits.Temperature LWT=308.15;
  parameter.Modelica.SIunits.Temperature EWT=303.15;
  output.Modelica.SIunits.Power HeatingCapacityA;
  output.Modelica.SIunits.Power HeatingCapacityB;
  output.Modelica.SIunits.Power PcompressorA;
  output.Modelica.SIunits.Power PcompressorB;
  output.Modelica.SIunits.Power Pcompressor;
  output.Modelica.SIunits.Power PfanA;
  output.Modelica.SIunits.Power PfanB;
  output.Modelica.SIunits.Power Pfan;
  output.Modelica.SIunits.Power PinputA;
  output.Modelica.SIunits.Power PinputB;
  output.Modelica.SIunits.Temperature SSTmaxA;
  output.Modelica.SIunits.Temperature SSTminA;
  output.Modelica.SIunits.Temperature SDTmaxA;
  output.Modelica.SIunits.Temperature SDTminA;
  output.Modelica.SIunits.Temperature DGTmaxA;
  output.Modelica.SIunits.Temperature SSTmaxB;
  output.Modelica.SIunits.Temperature SSTminB;
  output.Modelica.SIunits.Temperature SDTmaxB;
  output.Modelica.SIunits.Temperature SDTminB;
  output.Modelica.SIunits.Temperature DGTmaxB;
  output.Modelica.SIunits.Temperature SSTA;
  output.Modelica.SIunits.Temperature SDTA;
  output.Modelica.SIunits.Temperature DGTA;
  .Modelica.SIunits.Conversions.NonSIunits.AngularVelocity_rpm FanSpdA;
  .Modelica.SIunits.Conversions.NonSIunits.AngularVelocity_rpm FanSpdB;
  .Modelica.SIunits.Frequency CmpSpdA;
  .Modelica.SIunits.Frequency CmpSpdB;
  parameter.Modelica.SIunits.Power Target_capacityA=40000;
  parameter.Modelica.SIunits.Power Target_capacityB=60000;
  parameter Real Fan_speedA=720;
  parameter Real Fan_speedB=720;
  parameter Real EXV_OpeningA=0.5;
  parameter Real EXV_OpeningB=0.5;
  parameter.Modelica.SIunits.Frequency FrequencyA=100;
  parameter.Modelica.SIunits.Frequency FrequencyB=100;
  parameter Boolean Use_Cp_actuator=false;
  parameter Boolean Use_Fan_actuator=false;
  parameter.Modelica.SIunits.Temperature OAT=280.15;
  // Declaration of Coolant Medium Package
  //package CoolantCommonMedium =
  //.BOLT.InternalLibrary.Media.Coolant.CoolantCommon                             "Coolant Medium Package" annotation ();
  // Declaration of Coolant Medium at Evaporator Circuit
  parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector CoolantMedium=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector.FW
    "Coolant Medium Selection"
    annotation (Dialog(group="Medium"));
  final parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Empty coolantInf=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.getSelector(
    CoolantMedium)
    "Coolant Medium"
    annotation ();
  .BOLT.InternalLibrary.Media.Coolant.CoolantCommon.Temperature FreezTemp=.BOLT.InternalLibrary.Media.Coolant.CoolantCommon.freezeTemp(
    coolantInf,
    BrineConcentration,
    1);
  parameter Real BrineConcentration=0.2
    annotation (Dialog(group="Medium"));
  //Below output are  for Pyomo optimization purpose
  output Real COP;
  output Real COPA;
  output.Modelica.SIunits.Power HeatingCapacity;
  output.Modelica.SIunits.Power Pinput;
  .Modelica.SIunits.Temperature SDTmax_varA=Table_SDT_maxA.y;
  .Modelica.SIunits.Temperature SDTmin_varA=Table_SDT_minA.y[1];
  .Modelica.SIunits.Temperature SSTmax_varA=Table_SST_maxA.y[1];
  .Modelica.SIunits.TemperatureDifference SH_sp_varA=Table_ssh_setpointA.y[1];
  .Modelica.SIunits.Temperature SDTmax_varB=Table_SDT_maxB.y;
  .Modelica.SIunits.Temperature SDTmin_varB=Table_SDT_minB.y[1];
  .Modelica.SIunits.Temperature SSTmax_varB=Table_SST_maxB.y[1];
  .Modelica.SIunits.TemperatureDifference SH_sp_varB=Table_ssh_setpointB.y[1];
  .Modelica.Blocks.Tables.CombiTable1D Table_ssh_setpointA(
    table={{243.15,5},{278.15,5},{283.15,10},{298.15,10}})
    annotation (Placement(transformation(extent={{142.72507714844852,31.426631037438923},{162.72545714844853,51.42662103743892}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Tables.CombiTable2D Table_SDT_maxA(
    table={{0,30,50,100,120,140},{243.15,333.15,333.15,333.15,333.15,333.15},{258.15,355.15,355.15,355.15,355.15,343.15},{273.14,355.15,355.15,355.15,355.15,343.15},{273.16,333.15,355.15,355.15,343.15,333.15},{288.15,333.15,355.15,355.15,343.15,333.15},{293.15,333.15,349.15,349.15,338.15,333.15},{298.155,333.15,343.15,343.15,338.15,333.15}})
    annotation (Placement(transformation(extent={{142.72517714844852,1.4270310374389226},{162.7254771484485,21.428031037438927}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Tables.CombiTable1D Table_SST_maxA(
    table={{30,288.15},{50,298.15},{100,298.15},{120,293.15},{140,288.15}})
    annotation (Placement(transformation(extent={{142.72557714844854,-28.57436896256108},{162.72496714844854,-8.57346896256108}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Tables.CombiTable1D Table_SDT_minA(
    table={{243.15,283.15},{273.15,283.15},{298.15,303.15}})
    annotation (Placement(transformation(extent={{142.72587714844852,-58.573368962561084},{162.72499714844852,-38.573568962561076}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression SST_A(
    y=oL_modular.BlocA.node_suction.summary.Tsat)
    annotation (Placement(transformation(extent={{62.72527714844853,11.42693103743892},{82.72527714844853,31.42723103743893}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression freq_A(
    y=oL_modular.BlocA.compressor.summary.Ncomp)
    annotation (Placement(transformation(extent={{62.72507714844852,-28.573068962561067},{82.72457714844853,-8.57276896256107}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Tables.CombiTable1D Table_ssh_setpointB(
    table={{243.15,5},{278.15,5},{283.15,10},{298.15,10}})
    annotation (Placement(transformation(extent={{262.72517714844855,31.426831037438916},{282.7252771484485,51.428651037438925}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Tables.CombiTable2D Table_SDT_maxB(
    table={{0,30,50,100,120,140},{243.15,333.15,333.15,333.15,333.15,333.15},{258.15,355.15,355.15,355.15,355.15,343.15},{273.14,355.15,355.15,355.15,355.15,343.15},{273.16,333.15,355.15,355.15,343.15,333.15},{288.15,333.15,355.15,355.15,343.15,333.15},{293.15,333.15,349.15,349.15,338.15,333.15},{298.155,333.15,343.15,343.15,338.15,333.15}})
    annotation (Placement(transformation(extent={{262.7253771484485,1.4265310374389202},{282.7252771484485,21.42743103743892}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Tables.CombiTable1D Table_SST_maxB(
    table={{30,288.15},{50,298.15},{100,298.15},{120,293.15},{140,288.15}})
    annotation (Placement(transformation(extent={{262.72517714844855,-28.573768962561076},{282.7252771484485,-8.57266896256107}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Tables.CombiTable1D Table_SDT_minB(
    table={{243.15,283.15},{273.15,283.15},{298.15,303.15}})
    annotation (Placement(transformation(extent={{262.7246771484485,-58.57336896256107},{282.72527714844847,-38.57356896256106}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression SST_B(
    y=oL_modular.BlocB.node_suction.summary.Tsat)
    annotation (Placement(transformation(extent={{182.7253771484485,11.42663103743892},{202.72617714844853,31.426331037438917}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression freq_B(
    y=oL_modular.BlocB.compressor.summary.Ncomp)
    annotation (Placement(transformation(extent={{182.72557714844856,-28.573368962561077},{202.72477714844854,-8.573068962561068}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Controller.Optimization.Controller_Opt cpControlA(
    useActuator=Use_Cp_actuator,
    isOff=isOFFA,
    gain=1/70000,
    measurement=oL_modular.BlocA.condBPHE.summary.Q_flow_coolant,
    setPoint=Target_capacityA,
    AV_setPoint=FrequencyA,
    AV_min=30,
    AV_max=140)
    annotation (Placement(transformation(extent={{-79.67908766194945,-60.0},{-59.67908766194945,-40.0}},rotation=0.0,origin={0.0,0.0})));
  .Workspace.Controller.Optimization.Controller_Opt cpControlB(
    useActuator=Use_Cp_actuator,
    isOff=isOFFB or is_monobloc,
    gain=1/70000,
    measurement=oL_modular.BlocB.condBPHE.summary.Q_flow_coolant,
    setPoint=Target_capacityB,
    AV_setPoint=FrequencyB,
    AV_min=30,
    AV_max=140)
    annotation (Placement(transformation(extent={{44.84471805364276,-49.03413223691609},{24.844718053642758,-29.034132236916093}},rotation=0.0,origin={0.0,0.0})));
  .Workspace.Controller.Optimization.Controller_Opt fanControlA(
    useActuator=Use_Fan_actuator,
    isOff=isOFFA,
    gain=1,
    measurement=oL_modular.BlocA.fanCurve.summary.speed,
    setPoint=Fan_speedA,
    AV_setPoint=Fan_speedA,
    AV_min=0,
    AV_max=950)
    annotation (Placement(transformation(extent={{-80,-20},{-60,0}})));
  .Workspace.Controller.Optimization.Controller_Opt fanControlB(
    useActuator=Use_Fan_actuator,
    isOff=isOFFB or is_monobloc,
    gain=1,
    measurement=oL_modular.BlocB.fanCurve.summary.speed,
    setPoint=Fan_speedB,
    AV_setPoint=Fan_speedB,
    AV_min=0,
    AV_max=950)
    annotation (Placement(transformation(extent={{43.8771414863118,-15.162117163345318},{23.8771414863118,4.837882836654682}},rotation=0.0,origin={0.0,0.0})));
  .Workspace.Controller.Optimization.Controller_Opt EXVControlA(
    useActuator=false,
    isOff=isOFFA,
    gain=-0.1,
    measurement=oL_modular.BlocA.node_suction.summary.dTsh,
    setPoint=SH_sp_varA,
    AV_setPoint=EXV_OpeningA,
    AV_min=0.01,
    AV_max=1,
    PIController(
      AV_start=0.5))
    annotation (Placement(transformation(extent={{-80,20},{-60,40}})));
  .Workspace.Controller.Optimization.Controller_Opt EXVControlB(
    useActuator=false,
    isOff=isOFFB or is_monobloc,
    gain=-0.1,
    measurement=oL_modular.BlocB.node_suction.summary.dTsh,
    setPoint=SH_sp_varB,
    AV_setPoint=EXV_OpeningB,
    AV_min=0.01,
    AV_max=1,
    PIController(
      AV_start=0.5))
    annotation (Placement(transformation(extent={{41.29532412236956,19.354948955112704},{21.295324122369557,39.354948955112704}},rotation=0.0,origin={0.0,0.0})));
  parameter.Modelica.SIunits.Temperature OAT_WB=279.15;
equation
  HeatingCapacityA=oL_modular.BlocA.condBPHE.summary.Q_flow_coolant;
  HeatingCapacityB=oL_modular.BlocB.condBPHE.summary.Q_flow_coolant;
  HeatingCapacity=oL_modular.BlocA.condBPHE.summary.Q_flow_coolant+oL_modular.BlocB.condBPHE.summary.Q_flow_coolant;
  PcompressorA=oL_modular.BlocA.compressor.summary.P_compression;
  PcompressorB=oL_modular.BlocB.compressor.summary.P_compression;
  Pcompressor=oL_modular.BlocA.compressor.summary.P_compression+oL_modular.BlocB.compressor.summary.P_compression;
  PfanA=oL_modular.BlocA.motor.summary.power_shaft;
  PfanB=oL_modular.BlocB.motor.summary.power_shaft;
  Pfan=oL_modular.BlocA.motor.summary.power_shaft+oL_modular.BlocB.motor.summary.power_shaft;
  PinputA=PfanA+PcompressorA;
  PinputB=PfanB+PcompressorB;
  Pinput=Pfan+Pcompressor;
  COPA=HeatingCapacityA/PinputA;
  //EERB=CoolingCapacityB/PinputB;
  COP=HeatingCapacity/Pinput;
  DGTmaxA=423.15;
  SSTminA=243.15;
  SDTmaxA=SDTmax_varA;
  SSTmaxA=SSTmax_varA;
  SDTminA=SDTmin_varA;
  DGTmaxB=423.15;
  SSTminB=243.15;
  SDTmaxB=SDTmax_varB;
  SSTmaxB=SSTmax_varB;
  SDTminB=SDTmin_varB;
  FanSpdA=oL_modular.BlocA.motor.summary.Speed;
  FanSpdB=oL_modular.BlocB.motor.summary.Speed;
  CmpSpdA=oL_modular.BlocA.compressor.summary.Ncomp;
  CmpSpdB=oL_modular.BlocB.compressor.summary.Ncomp;
  SSTA=oL_modular.BlocA.node_suction.summary.Tsat;
  SDTA=oL_modular.BlocA.node_discharge.summary.Tsat;
  DGTA=oL_modular.BlocA.node_discharge.summary.T;
  connect(EXVControlA.controllerAV,oL_modular.EXV_controller_A)
    annotation (Line(points={{-59,30},{-46,30},{-46,14.5},{-33,14.5}},color={0,0,127}));
  connect(fanControlA.controllerAV,oL_modular.Fan_controller_A)
    annotation (Line(points={{-59,-10},{-46.96757656733094,-10},{-46.96757656733094,11.112627612218233},{-34.935153134661874,11.112627612218233}},color={0,0,127}));
  connect(cpControlA.controllerAV,oL_modular.Compressor_controller_A)
    annotation (Line(points={{-58.67908766194945,-50},{-46.96757656733094,-50},{-46.96757656733094,6.112627612218233},{-34.935153134661874,6.112627612218233}},color={0,0,127}));
  connect(fanControlB.controllerAV,oL_modular.Fan_controller_B)
    annotation (Line(points={{22.8771414863118,-5.162117163345318},{4.970994175824963,-5.162117163345318},{4.970994175824963,11.112627612218233},{-12.935153134661874,11.112627612218233}},color={0,0,127}));
  connect(oL_modular.EXV_controller_B,EXVControlB.controllerAV)
    annotation (Line(points={{-12.935153134661874,16.112627612218233},{3.6800854938538414,16.112627612218233},{3.6800854938538414,29.354948955112704},{20.295324122369557,29.354948955112704}},color={0,0,127}));
  connect(cpControlB.controllerAV,oL_modular.Compressor_controller_B)
    annotation (Line(points={{23.844718053642758,-39.03413223691609},{5.454782459490442,-39.03413223691609},{5.454782459490442,6.112627612218233},{-12.935153134661874,6.112627612218233}},color={0,0,127}));
equation
  connect(SST_A.y,Table_ssh_setpointA.u[1])
    annotation (Line(points={{83.72527714844853,21.427131037438926},{102.72527714844853,21.427131037438926},{102.72527714844853,41.42663103743892},{140.72527714844853,41.42663103743892}},color={0,0,127}));
  connect(SST_A.y,Table_SDT_minA.u[1])
    annotation (Line(points={{83.72527714844853,21.427131037438926},{102.72527714844853,21.427131037438926},{102.72527714844853,-48.57346896256108},{140.72627714844853,-48.57346896256108}},color={0,0,127}));
  connect(SST_A.y,Table_SDT_maxA.u1)
    annotation (Line(points={{83.72527714844853,21.427131037438926},{122.72527714844853,21.427131037438926},{122.72527714844853,17.42783103743892},{140.72527714844853,17.42783103743892}},color={0,0,127}));
  connect(freq_A.y,Table_SDT_maxA.u2)
    annotation (Line(points={{83.72427714844852,-18.572868962561074},{122.72527714844853,-18.572868962561074},{122.72527714844853,5.427231037438922},{140.72527714844853,5.427231037438922}},color={0,0,127}));
  connect(freq_A.y,Table_SST_maxA.u[1])
    annotation (Line(points={{83.72427714844852,-18.572868962561074},{140.72527714844853,-18.573918962561077}},color={0,0,127}));
  connect(SST_B.y,Table_ssh_setpointB.u[1])
    annotation (Line(points={{203.7262771484485,21.426431037438924},{222.72527714844853,21.426431037438924},{222.72527714844853,41.42773103743892},{260.7252771484485,41.42773103743892}},color={0,0,127}));
  connect(SST_B.y,Table_SDT_minB.u[1])
    annotation (Line(points={{203.7262771484485,21.426431037438924},{222.72527714844853,21.426431037438924},{222.72527714844853,-48.57346896256108},{260.72427714844855,-48.57346896256108}},color={0,0,127}));
  connect(SST_B.y,Table_SDT_maxB.u1)
    annotation (Line(points={{203.7262771484485,21.426431037438924},{242.72527714844853,21.426431037438924},{242.72527714844853,17.427231037438922},{260.7252771484485,17.427231037438922}},color={0,0,127}));
  connect(freq_B.y,Table_SST_maxB.u[1])
    annotation (Line(points={{203.72427714844855,-18.573168962561077},{260.7252771484485,-18.573168962561077}},color={0,0,127}));
  connect(freq_B.y,Table_SDT_maxB.u2)
    annotation (Line(points={{203.72427714844855,-18.573168962561077},{242.72527714844853,-18.573168962561077},{242.72527714844853,5.426731037438927},{260.7252771484485,5.426731037438927}},color={0,0,127}));
equation
  connect(oL_modular.coolant_out,sinkBrine.port)
    annotation (Line(points={{-21.6,21.6},{-21.6,40},{-20,40}},color={0,127,0}));
  connect(sourceBrine.port,oL_modular.coolant_in)
    annotation (Line(points={{-22,-18},{-22,-10.5},{-21.8,-10.5},{-21.8,-3}},color={0,127,0}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end Unit_optimization;
