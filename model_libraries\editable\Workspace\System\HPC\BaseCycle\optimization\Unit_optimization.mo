within Workspace.System.HPC.BaseCycle.optimization;
model Unit_optimization
  import CoolantCommonMedium=BOLT.InternalLibrary.Media.Coolant.CoolantCommon;
  .Workspace.System.HPC.BaseCycle.Equipement oL_modular(
    is_monobloc=is_monobloc,
    Use_pump=Use_pump,
    Use_filter=Use_filter,
    Use_expansion_tank=Use_tank,
    EWT=EWT,
    LWT=LWT,
    OAT=OAT,
    isOFFA=isOFFA,
    isOFFB=isOFFB,
    Unit_size=Unit_size,
    CoolantMedium=CoolantMedium,
    BrineConcentration=BrineConcentration,
    Water_pressure=sourceBrine.p_set)
    annotation (Placement(transformation(extent={{-4,48},{36,88}},origin={-16,-68},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Source sourceBrine(
    Vd_fixed=false,
    T_set=EWT,
    p_fixed=true,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration)
    annotation (Placement(transformation(extent={{-10,-10},{10,10}},origin={0,-80},rotation=90.0)));
  .BOLT.BoundaryNode.Coolant.Sink sinkBrine(
    p_fixed=false,
    T_fixed=true,
    T_set=LWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration)
    annotation (Placement(transformation(extent={{-10,-10},{10,10}},origin={0,80},rotation=-90.0)));
  parameter Boolean is_monobloc=
    if Unit_size == 40 then
      true
    elseif Unit_size == 50 then
      true
    elseif Unit_size == 60 then
      true
    elseif Unit_size == 70 then
      true
    else
      false
    annotation (Dialog(group="Unit_config"));
  parameter Boolean Use_pump=false;
  parameter Boolean Use_tank=false;
  parameter Boolean Use_filter=false;
  parameter Boolean is0ff=false;
  parameter Boolean isOFFA=false
    annotation (Dialog(group="Cp_control"));
  parameter Boolean isOFFB=false
    annotation (Dialog(group="Cp_control"));
  parameter Integer Unit_size=60
    "Stringify values -- IPM Cloud limitation"
    annotation (Dialog(group="Unit_config"));
  // Declaration of Coolant Medium Package
  //package CoolantCommonMedium =
  //.BOLT.InternalLibrary.Media.Coolant.CoolantCommon                             "Coolant Medium Package" annotation ();
  // Declaration of Coolant Medium at Evaporator Circuit
  parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector CoolantMedium=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector.FW
    "Coolant Medium Selection"
    annotation (Dialog(group="Medium"));
  final parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Empty coolantInf=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.getSelector(
    CoolantMedium)
    "Coolant Medium"
    annotation ();
  .BOLT.InternalLibrary.Media.Coolant.CoolantCommon.Temperature FreezTemp=.BOLT.InternalLibrary.Media.Coolant.CoolantCommon.freezeTemp(
    coolantInf,
    BrineConcentration,
    1);
  parameter Real BrineConcentration=0.2
    annotation (Dialog(group="Medium"));
  output Real EERA;
  //output Real EERB;
  output Real EER;
  parameter.Modelica.SIunits.Temperature LWT=280.15;
  parameter.Modelica.SIunits.Temperature EWT=285.15;
  output.Modelica.SIunits.Power CoolingCapacityA;
  output.Modelica.SIunits.Power CoolingCapacityB;
  output.Modelica.SIunits.Power CoolingCapacity;
  output.Modelica.SIunits.Power PcompressorA;
  output.Modelica.SIunits.Power PcompressorB;
  output.Modelica.SIunits.Power Pcompressor;
  output.Modelica.SIunits.Power PfanA;
  output.Modelica.SIunits.Power PfanB;
  output.Modelica.SIunits.Power Pfan;
  output.Modelica.SIunits.Power PinputA;
  output.Modelica.SIunits.Power PinputB;
  output.Modelica.SIunits.Power Pinput;
  output.Modelica.SIunits.Temperature SSTmaxA;
  output.Modelica.SIunits.Temperature SSTminA;
  output.Modelica.SIunits.Temperature SDTmaxA;
  output.Modelica.SIunits.Temperature SDTminA;
  output.Modelica.SIunits.Temperature DGTmaxA;
  output.Modelica.SIunits.Temperature SSTmaxB;
  output.Modelica.SIunits.Temperature SSTminB;
  output.Modelica.SIunits.Temperature SDTmaxB;
  output.Modelica.SIunits.Temperature SDTminB;
  output.Modelica.SIunits.Temperature DGTmaxB;
  output.Modelica.SIunits.Temperature SSTA;
  output.Modelica.SIunits.Temperature SDTA;
  output.Modelica.SIunits.Temperature DGTA;
  .Modelica.SIunits.Conversions.NonSIunits.AngularVelocity_rpm FanSpdA;
  .Modelica.SIunits.Conversions.NonSIunits.AngularVelocity_rpm FanSpdB;
  .Modelica.SIunits.Frequency CmpSpdA;
  .Modelica.SIunits.Frequency CmpSpdB;
  parameter.Modelica.SIunits.Power Target_capacityA=40000;
  parameter.Modelica.SIunits.Power Target_capacityB=60000;
  .Modelica.Blocks.Tables.CombiTable1D Table_ssh_setpointA(
    table={{243.15,5},{278.15,5},{283.15,10},{298.15,10}})
    annotation (Placement(transformation(extent={{-28.6672,-17.5},{-8.66682,2.49999}},origin={248.667,77.5},rotation=0.0)));
  .Modelica.Blocks.Tables.CombiTable2D Table_SDT_maxA(
    table={{0,30,50,100,120,140},{243.15,333.15,333.15,333.15,333.15,333.15},{258.15,355.15,355.15,355.15,355.15,343.15},{273.14,355.15,355.15,355.15,355.15,343.15},{273.16,333.15,355.15,355.15,343.15,333.15},{288.15,333.15,355.15,355.15,343.15,333.15},{293.15,333.15,349.15,349.15,338.15,333.15},{298.155,333.15,343.15,343.15,338.15,333.15}})
    annotation (Placement(transformation(extent={{-28.6671,-51.5026},{-8.6668,-31.5016}},origin={248.667,81.503},rotation=0.0)));
  .Modelica.Blocks.Tables.CombiTable1D Table_SST_maxA(
    table={{30,288.15},{50,298.15},{100,298.15},{120,293.15},{140,288.15}})
    annotation (Placement(transformation(extent={{-28.6657,-81.503},{-8.66631,-61.5021}},origin={248.666,81.502},rotation=0.0)));
  .Modelica.Blocks.Tables.CombiTable1D Table_SDT_minA(
    table={{243.15,283.15},{273.15,283.15},{298.15,303.15}})
    annotation (Placement(transformation(extent={{-28.6654,-112.173},{-8.66628,-92.1732}},origin={248.666,82.173},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression SST_A(
    y=oL_modular.BlocA.node_suction.summary.Tsat)
    annotation (Placement(transformation(extent={{-104.668,-63.6677},{-84.668,-43.6674}},origin={244.668,103.668},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression freq_A(
    y=oL_modular.BlocA.compressor.summary.Ncomp)
    annotation (Placement(transformation(extent={{-56.6652,-69.6677},{-36.6657,-49.6674}},origin={196.665,69.668},rotation=0.0)));
  .Modelica.SIunits.Temperature SDTmax_varA=Table_SDT_maxA.y;
  .Modelica.SIunits.Temperature SDTmin_varA=Table_SDT_minA.y[1];
  .Modelica.SIunits.Temperature SSTmax_varA=Table_SST_maxA.y[1];
  .Modelica.SIunits.TemperatureDifference SH_sp_varA=Table_ssh_setpointA.y[1];
  .Modelica.SIunits.Temperature SDTmax_varB=Table_SDT_maxB.y;
  .Modelica.SIunits.Temperature SDTmin_varB=Table_SDT_minB.y[1];
  .Modelica.SIunits.Temperature SSTmax_varB=Table_SST_maxB.y[1];
  .Modelica.SIunits.TemperatureDifference SH_sp_varB=Table_ssh_setpointB.y[1];
  .Modelica.Blocks.Tables.CombiTable1D Table_ssh_setpointB(
    table={{243.15,5},{278.15,5},{283.15,10},{298.15,10}})
    annotation (Placement(transformation(extent={{90.8349,-17.8558},{110.835,2.14602}},origin={249.165,77.856},rotation=0.0)));
  .Modelica.Blocks.Tables.CombiTable2D Table_SDT_maxB(
    table={{0,30,50,100,120,140},{243.15,333.15,333.15,333.15,333.15,333.15},{258.15,355.15,355.15,355.15,355.15,343.15},{273.14,355.15,355.15,355.15,355.15,343.15},{273.16,333.15,355.15,355.15,343.15,333.15},{288.15,333.15,355.15,355.15,343.15,333.15},{293.15,333.15,349.15,349.15,338.15,333.15},{298.155,333.15,343.15,343.15,338.15,333.15}})
    annotation (Placement(transformation(extent={{86.3241,-50.4191},{106.324,-30.4182}},origin={253.676,80.419},rotation=0.0)));
  .Modelica.Blocks.Tables.CombiTable1D Table_SST_maxB(
    table={{30,288.15},{50,298.15},{100,298.15},{120,293.15},{140,288.15}})
    annotation (Placement(transformation(extent={{87.3319,-79.1294},{107.332,-59.1283}},origin={252.668,79.129},rotation=0.0)));
  .Modelica.Blocks.Tables.CombiTable1D Table_SDT_minB(
    table={{243.15,283.15},{273.15,283.15},{298.15,303.15}})
    annotation (Placement(transformation(extent={{86.3334,-111.087},{106.334,-91.0872}},origin={253.666,81.087},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression SST_B(
    y=oL_modular.BlocB.node_suction.summary.Tsat)
    annotation (Placement(transformation(extent={{15.8341,-56.395},{35.8349,-36.3953}},origin={244.166,96.395},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression freq_B(
    y=oL_modular.BlocB.compressor.summary.Ncomp)
    annotation (Placement(transformation(extent={{53.3313,-69.126},{73.3305,-49.1257}},origin={206.669,69.126},rotation=0.0)));
  parameter Real Fan_speedA=720;
  parameter Real Fan_speedB=720;
  parameter Real EXV_OpeningA=0.5;
  parameter Real EXV_OpeningB=0.5;
  parameter.Modelica.SIunits.Frequency FrequencyA=100;
  parameter.Modelica.SIunits.Frequency FrequencyB=100;
  parameter Boolean Use_Cp_actuator=false;
  parameter Boolean Use_Fan_actuator=false;
  parameter.Modelica.SIunits.Temperature OAT=308.15;
  .Workspace.Controller.Optimization.Controller_Opt cpControlA(
    useActuator=Use_Cp_actuator,
    isOff=isOFFA,
    gain=1/70000,
    measurement=oL_modular.BlocA.evapBPHE.summary.Q_flow_ref,
    setPoint=Target_capacityA,
    AV_setPoint=FrequencyA,
    AV_min=30,
    AV_max=140)
    annotation (Placement(transformation(extent={{-80,-60},{-60,-40}})));
  .Workspace.Controller.Optimization.Controller_Opt cpControlB(
    useActuator=Use_Cp_actuator,
    isOff=isOFFB or is_monobloc,
    gain=1/70000,
    measurement=oL_modular.BlocB.evapBPHE.summary.Q_flow_ref,
    setPoint=Target_capacityB,
    AV_setPoint=FrequencyB,
    AV_min=30,
    AV_max=140)
    annotation (Placement(transformation(extent={{80,-60},{60,-40}})));
  .Workspace.Controller.Optimization.Controller_Opt fanControlA(
    useActuator=Use_Fan_actuator,
    isOff=isOFFA,
    gain=1,
    measurement=oL_modular.BlocA.fanCurve.summary.speed,
    setPoint=Fan_speedA,
    AV_setPoint=Fan_speedA,
    AV_min=0,
    AV_max=950)
    annotation (Placement(transformation(extent={{-80,-20},{-60,0}})));
  .Workspace.Controller.Optimization.Controller_Opt fanControlB(
    useActuator=Use_Fan_actuator,
    isOff=isOFFB or is_monobloc,
    gain=1,
    measurement=oL_modular.BlocB.fanCurve.summary.speed,
    setPoint=Fan_speedB,
    AV_setPoint=Fan_speedB,
    AV_min=0,
    AV_max=950)
    annotation (Placement(transformation(extent={{80,-20},{60,0}})));
  .Workspace.Controller.Optimization.Controller_Opt EXVControlA(
    useActuator=false,
    isOff=isOFFA,
    gain=-0.1,
    measurement=oL_modular.BlocA.node_suction.summary.dTsh,
    setPoint=SH_sp_varA,
    AV_setPoint=EXV_OpeningA,
    AV_min=0.01,
    AV_max=1,
    PIController(
      AV_start=0.5))
    annotation (Placement(transformation(extent={{-80,20},{-60,40}})));
  .Workspace.Controller.Optimization.Controller_Opt EXVControlA1(
    useActuator=false,
    isOff=isOFFB or is_monobloc,
    gain=-0.1,
    measurement=oL_modular.BlocB.node_suction.summary.dTsh,
    setPoint=SH_sp_varB,
    AV_setPoint=EXV_OpeningB,
    AV_min=0.01,
    AV_max=1,
    PIController(
      AV_start=0.5))
    annotation (Placement(transformation(extent={{80,20},{60,40}})));
equation
  CoolingCapacityA=oL_modular.BlocA.evapBPHE.summary.Q_flow_ref;
  CoolingCapacityB=oL_modular.BlocB.evapBPHE.summary.Q_flow_ref;
  CoolingCapacity=oL_modular.BlocA.evapBPHE.summary.Q_flow_ref+oL_modular.BlocB.evapBPHE.summary.Q_flow_ref;
  PcompressorA=oL_modular.BlocA.compressor.summary.P_compression;
  PcompressorB=oL_modular.BlocB.compressor.summary.P_compression;
  Pcompressor=oL_modular.BlocA.compressor.summary.P_compression+oL_modular.BlocB.compressor.summary.P_compression;
  PfanA=oL_modular.BlocA.motor.summary.power_shaft;
  PfanB=oL_modular.BlocB.motor.summary.power_shaft;
  Pfan=oL_modular.BlocA.motor.summary.power_shaft+oL_modular.BlocB.motor.summary.power_shaft;
  PinputA=PfanA+PcompressorA;
  PinputB=PfanB+PcompressorB;
  Pinput=Pfan+Pcompressor;
  EERA=CoolingCapacityA/PinputA;
  //EERB=CoolingCapacityB/PinputB;
  EER=CoolingCapacity/Pinput;
  DGTmaxA=423.15;
  SSTminA=274.15;
  SDTmaxA=SDTmax_varA;
  SSTmaxA=SSTmax_varA;
  SDTminA=SDTmin_varA;
  DGTmaxB=423.15;
  SSTminB=274.15;
  SDTmaxB=SDTmax_varB;
  SSTmaxB=SSTmax_varB;
  SDTminB=SDTmin_varB;
  FanSpdA=oL_modular.BlocA.motor.summary.Speed;
  FanSpdB=oL_modular.BlocB.motor.summary.Speed;
  CmpSpdA=oL_modular.BlocA.compressor.summary.Ncomp;
  CmpSpdB=oL_modular.BlocB.compressor.summary.Ncomp;
  SSTA=oL_modular.BlocA.node_suction.summary.Tsat;
  SDTA=oL_modular.BlocA.node_discharge.summary.Tsat;
  DGTA=oL_modular.BlocA.node_discharge.summary.T;
  connect(SST_A.y,Table_ssh_setpointA.u[1])
    annotation (Line(points={{161,50.0005},{180,50.0005},{180,70},{218,70}},color={0,0,127}));
  connect(SST_A.y,Table_SDT_minA.u[1])
    annotation (Line(points={{161,50.0005},{180,50.0005},{180,-20.0001},{218.001,-20.0001}},color={0,0,127}));
  connect(SST_A.y,Table_SDT_maxA.u1)
    annotation (Line(points={{161,50.0005},{200,50.0005},{200,46.0012},{218,46.0012}},color={0,0,127}));
  connect(freq_A.y,Table_SDT_maxA.u2)
    annotation (Line(points={{160.999,10.0005},{200,10.0005},{200,34.0006},{218,34.0006}},color={0,0,127}));
  connect(freq_A.y,Table_SST_maxA.u[1])
    annotation (Line(points={{160.999,10.0005},{218,9.99945}},color={0,0,127}));
  connect(SST_B.y,Table_ssh_setpointB.u[1])
    annotation (Line(points={{281.001,49.9998},{300,49.9998},{300,70.0011},{338,70.0011}},color={0,0,127}));
  connect(SST_B.y,Table_SDT_minB.u[1])
    annotation (Line(points={{281.001,49.9998},{300,49.9998},{300,-20.0001},{337.999,-20.0001}},color={0,0,127}));
  connect(SST_B.y,Table_SDT_maxB.u1)
    annotation (Line(points={{281.001,49.9998},{320,49.9998},{320,46.0006},{338,46.0006}},color={0,0,127}));
  connect(freq_B.y,Table_SST_maxB.u[1])
    annotation (Line(points={{280.999,10.0002},{296,10.0002},{296,10},{310,10},{310,10.0002},{338,10.0002}},color={0,0,127}));
  connect(freq_B.y,Table_SDT_maxB.u2)
    annotation (Line(points={{280.999,10.0002},{320,10.0002},{320,34.0001},{338,34.0001}},color={0,0,127}));
  connect(sourceBrine.port,oL_modular.coolant_in)
    annotation (Line(points={{0,-70},{0,-50},{0,-28.1645},{-0.88227,-28.1645}},color={0,127,0}));
  connect(sinkBrine.port,oL_modular.coolant_out)
    annotation (Line(points={{0,70},{0,46},{0,20.4},{-0.8,20.4}},color={0,127,0}));
  connect(cpControlA.controllerAV,oL_modular.Compressor_controller_A)
    annotation (Line(points={{-59,-50},{-40,-50},{-40,-15},{-22,-15}},color={0,0,127}));
  connect(cpControlB.controllerAV,oL_modular.Compressor_controller_B)
    annotation (Line(points={{59,-50},{40,-50},{40,-15},{22,-15}},color={0,0,127}));
  connect(fanControlA.controllerAV,oL_modular.Fan_controller_A)
    annotation (Line(points={{-59,-10},{-50,-10},{-50,-5},{-22,-5}},color={0,0,127}));
  connect(fanControlB.controllerAV,oL_modular.Fan_controller_B)
    annotation (Line(points={{59,-10},{50,-10},{50,-5},{22,-5}},color={0,0,127}));
  connect(EXVControlA.controllerAV,oL_modular.EXV_controller_A)
    annotation (Line(points={{-59,30},{-40,30},{-40,5},{-22,5}},color={0,0,127}));
  connect(EXVControlA1.controllerAV,oL_modular.EXV_controller_B)
    annotation (Line(points={{59,30},{40,30},{40,5},{22,5}},color={0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end Unit_optimization;
