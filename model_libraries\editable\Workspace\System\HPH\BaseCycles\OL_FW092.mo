within Workspace.System.HPH.BaseCycles;
model OL_FW092
  import Booltoreal=Workspace.Auxiliary.Tools.booleanToReal;
  parameter Boolean isOff=false
    "Set true to turn off";
  parameter Boolean use_Z_in=true
    "Set false to enable calibration capability";
  parameter.Modelica.SIunits.Conversions.NonSIunits.AngularVelocity_rpm max_fan_rpm=720
    "maximum fan speed";
  parameter.Modelica.SIunits.Frequency max_motor_frequency=max_fan_rpm/max_fan_rpm_STD*max_motor_frequency_STD
    "maximum motor frequency";
  .BOLT.Compressor.PD.CompVS_2P compressor(
    RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R290,
    isLoopBreaker=true,
    isOff=isOff_ref,
    selector_CompARIS=selector_Comp,
    x_in_start=1.02,
    x_out_start=1.15,
    use_z_flow_suc_expression=true,
    Z_flow_suc_expression=
      if use_Calib then
        calibrationBlock.Z_Flow
      else
        compressor.Z_flow_suc,
    use_z_power_expression=true,
    Z_power_expression=
      if use_Calib then
        calibrationBlock.Z_Power
      else
        compressor.Z_power,
    Fw=
      if use_Calib then
        FW
      else
        0.92)
    annotation (Placement(transformation(extent={{83.05605110166243,-65.1377118481416},{111.05605110166243,-37.137711848141606}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput EXV_controller
    annotation (Placement(transformation(extent={{-74.49864370555972,-3.295745471720096},{-62.79694614261919,8.405952091220428}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={-110,70})));
  .Modelica.Blocks.Interfaces.RealInput compressor_controller
    annotation (Placement(transformation(extent={{91.84051667472913,-33.16829003549446},{100.66486520374089,-24.343941506482714}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={-110,100})));
  .BOLT.BoundaryNode.Coolant.Node node_sinkBrine(
    isOff=isOff,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    T_start=LWT)
    annotation (Placement(transformation(extent={{4.0,-4.0},{-4.0,4.0}},origin={43.85284874234604,76.25558695919354},rotation=-180.0)));
  .BOLT.BoundaryNode.Coolant.Node node_sourcebrine(
    isOff=isOff,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    T_start=EWT)
    annotation (Placement(transformation(extent={{4.0,-4.0},{-4.0,4.0}},origin={-6.194445963644725,76.98613348133698},rotation=-180.0)));
  .BOLT.InternalLibrary.BuildingBlocks.Coolant.Ports.FluidPort_a port_a(
    CoolantMedium=CoolantMedium,
    Xi_set=BrineConcentration)
    annotation (Placement(transformation(extent={{-4.35124695223881,-4.351246952238824},{4.35124695223881,4.351246952238824}},origin={-19.838251179316302,99.28484223121312},rotation=90.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={-110,-20})));
  .BOLT.InternalLibrary.BuildingBlocks.Coolant.Ports.FluidPort_b port_b(
    CoolantMedium=CoolantMedium,
    Xi_set=BrineConcentration)
    annotation (Placement(transformation(extent={{-4.787170613544987,-4.787170613544987},{4.787170613544987,4.787170613544987}},origin={52.27665269609747,98.44526814222617},rotation=-180.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={116,-20})));
  .Modelica.Blocks.Interfaces.RealInput actual_FSFanSpd_in
    annotation (Placement(transformation(extent={{-3.228709177677075,-3.228709177677075},{3.228709177677075,3.228709177677075}},origin={-59.750446216281205,-92.************},rotation=-90.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={-110,20})));
  .BOLT.Condenser.RefCoolant.CondBPHE_MVB condBPHE(
    RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R290,
    isOff=isOff,
    T_a_start_coolant=EWT,
    T_b_start_coolant=LWT,
    p_start_coolant=Water_pressure,
    x_a_start=1.15,
    x_b_start=-0.1,
    counterFlow=true,
    isOff_ref=isOff_ref,
    capacity_ref_fixed=false,
    capacity_ref=40000,
    X=BrineConcentration,
    CoolantMedium=CoolantMedium,
    Dport_coolant_a=Dport_coolant_a,
    Dport_coolant_b=Dport_coolant_b,
    Dport_ref_a=Dport_ref_a,
    Dport_ref_b=Dport_ref_b,
    nPlate=nPlate,
    R_foul=CondFoulingFactor,
    k_CoolCap=0,
    k_HeatCap=1,
    selectGeo=selector_geo_BPHE,
    Z_UA=1.408,
    use_Z_UA_expression=true,
    Z_UA_expression=
      if use_Calib then
        calibrationBlock.Z_Cond_HPH
      else
        1)
    annotation (Placement(transformation(extent={{-10.0,13.000000000000004},{10.0,-13.000000000000004}},origin={16.25269093923501,59.16428751094719},rotation=-180.0)));
  .BOLT.Evaporator.RefAir.EvapAir1_MVB evapAir1_MVB(
    RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R290,
    isOff=isOff_ref,
    nCoils=nCoils,
    userDefined_geo(
      Itube=Itube,
      nCir=nCir,
      NRow=Nrow,
      LTube=Ltube,
      Dotube=Dotube,
      Ttube=Ttube,
      Ptube=Ptube,
      PRow=Prow,
      Dfin=Dfin,
      Tfin=Tfin,
      NTube=Ntube),
    x_b_start=1.02,
    k_CoolCap=1,
    k_HeatCap=0,
    Z_Uev=0.95,
    x_a_start=0.2,
    m_flow_start_air=sourceAir.Vds_flow_set*1.204,
    T_a_start=sourceAir.Tdb_set,
    T_b_start=sourceAir.Tdb_set-5,
    phi_a_start=0.87,
    use_Z_Uev_expression=true,
    Z_Uev_expression=
      if use_Calib then
        calibrationBlock.Z_Evap_HPH
      else
        1,isOffRef = isOff_ref,isOffAir = isOff_ref)
    annotation (Placement(transformation(extent={{10.0,-12.0},{-10.0,12.0}},origin={-55.74730906076499,-54.756115770988586},rotation=-180.0)));
  parameter.Modelica.SIunits.Temperature EWT=285.15;
  parameter.Modelica.SIunits.Temperature LWT=280.15;
  parameter.Modelica.SIunits.Temperature OAT=308.15;
  parameter Real relative_humidity=0.87;
  parameter Boolean is_relative_humidity=false;
  parameter Real OAT_WB=6;
  parameter Boolean is_OAT_WB=false;
  parameter Boolean isOff_ref=false;
  .BOLT.BoundaryNode.Air.Node node_out_coil_air(
    isOff=isOff_ref)
    annotation (Placement(transformation(extent={{-4.0,-4.0},{4.0,4.0}},origin={-74.52370337243701,-59.11316621122765},rotation=-180.0)));
  .BOLT.BoundaryNode.Air.Node node_out_duct_air(
    isOff=isOff_ref)
    annotation (Placement(transformation(extent={{-4.0,-4.0},{4.0,4.0}},origin={-99.19257992017211,-66.6190674430411},rotation=-90.0)));
  parameter Boolean Use_EN=false;
  parameter Boolean use_Calib;
  parameter.BOLT.InternalLibrary.Compressors.PD_2Port.SubComponents.PD.DataBase.Polynomial.ARI_VS.Selector selector_Comp
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Real capacity_design
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Real CompVoltage
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Real fmax
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Real fmin
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter.BOLT.InternalLibrary.Refrigerant.Valves.TwoPort.DataBase.Selector EXV_main
    annotation (Dialog(group="EXV",tab="Unit Characteristics"));
  parameter Real Dport_coolant_a
    annotation (Dialog(group="BPHE",tab="Unit Characteristics"));
  parameter Real Dport_coolant_b
    annotation (Dialog(group="BPHE",tab="Unit Characteristics"));
  parameter Real Dport_ref_a
    annotation (Dialog(group="BPHE",tab="Unit Characteristics"));
  parameter Real Dport_ref_b
    annotation (Dialog(group="BPHE",tab="Unit Characteristics"));
  parameter Integer nPlate
    annotation (Dialog(group="BPHE",tab="Unit Characteristics"));
  parameter Real CondFoulingFactor
    annotation (Dialog(group="BPHE",tab="Unit Characteristics"));
  parameter.BlackBoxLibrary.GeoSelector.GeoSelector_1C_Cond selector_geo_BPHE
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Integer nCoils
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Integer Itube
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Integer nCir
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Integer Ntube
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Integer Nrow
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Ltube
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Dotube
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Ttube
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Ptube
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Prow
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Dfin
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Tfin
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Boolean isCoating
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter.BOLT.InternalLibrary.Coolant.Pumps.DataBase.PumpPoly.Selector selector_pump
    annotation (Dialog(group="Pump",tab="Unit Characteristics"));
  parameter Real Fw_fan
    annotation (Dialog(group="Fan",tab="Unit Characteristics"));
  parameter Real max_fan_frequency
    annotation (Dialog(group="Fan",tab="Unit Characteristics"));
  parameter Real[9] fanCurveCoefficientsCooling
    annotation (Dialog(group="Fan",tab="Unit Characteristics"));
  parameter Real[9] fanCurveCoefficientsHeating
    annotation (Dialog(group="Fan",tab="Unit Characteristics"));
  parameter Real Suction_line_diameter
    annotation (Dialog(group="Suction Line",tab="Unit Characteristics"));
  parameter Real Suction_line_length
    annotation (Dialog(group="Suction Line",tab="Unit Characteristics"));
  parameter Real coil_line_diameter
    annotation (Dialog(group="Coil Line",tab="Unit Characteristics"));
  parameter Real coil_line_length
    annotation (Dialog(group="Coil Line",tab="Unit Characteristics"));
  parameter Real liquid_line_diameter
    annotation (Dialog(group="Liquid Line",tab="Unit Characteristics"));
  parameter Real liquid_line_length
    annotation (Dialog(group="Liquid Line",tab="Unit Characteristics"));
  parameter Real discharge_line_diameter
    annotation (Dialog(group="Discharge Line",tab="Unit Characteristics"));
  parameter Real discharge_line_length
    annotation (Dialog(group="Discharge Line",tab="Unit Characteristics"));
  parameter Real EXV_in_line_diameter
    annotation (Dialog(group="EXV in Line",tab="Unit Characteristics"));
  parameter Real EXV_in_line_length
    annotation (Dialog(group="EXV in Line",tab="Unit Characteristics"));
  parameter Real Ac_duct
    annotation (Dialog(group="Air Duct",tab="Unit Characteristics"));
  parameter Real Ka_duct
    annotation (Dialog(group="Air Duct",tab="Unit Characteristics"));
  parameter Real UA_duct
    annotation (Dialog(group="Air Duct",tab="Unit Characteristics"));
  parameter Real PDC_4WV
    annotation (Dialog(group="4WV",tab="Unit Characteristics"));
  parameter Integer Heatcap_Tbiv
    annotation (Dialog(group="",tab="Unit Characteristics"));
  parameter Integer Heatcap_Tbiv_LN_option
    annotation (Dialog(group="",tab="Unit Characteristics"));
  .BOLT.BoundaryNode.Refrigerant.Node node_4WV_LP_in(
    isOff=isOff_ref,
    RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R290)
    annotation (Placement(transformation(extent={{16.565413068221034,-54.68000117835085},{24.565413068221034,-46.68000117835085}},origin={0.0,0.0},rotation=0.0)));
  parameter Real Zevap_HPH_cst
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_HPH_SST
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_coated_HPH
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_HPH_min
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_HPH_max
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_HPC_cst
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_HPC_SST
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_HPC_min
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_HPC_max
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zcond_HPH_cst
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPH_SDT
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPH_heatcap
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPH_SST
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPH_min
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPH_max
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPC_cst
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPC_SDT
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPC_coated
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPC_min
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPC_max
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zflow_intercept
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zflow_SST
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zflow_SST2
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zflow_SST3
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zflow_SDT
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zflow_heatcap
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zflow_Ncomp
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zflow_min
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zflow_max
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zpower_intercept
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zpower_DGT
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zpower_SST
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zpower_SST2
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zpower_SDT
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zpower_Ncomp
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zpower_Heatcap
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zpower_min
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zpower_max
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real FW
    annotation (Dialog(group="compressor",tab="Calibration"));
  .Workspace.Auxiliary.Calibration.CalibrationBlock_FW092 calibrationBlock(
    Zpower_intercept=Zpower_intercept,
    Zpower_DGT=Zpower_DGT,
    Zpower_Heatcap=Zpower_Heatcap,
    Zpower_SST=Zpower_SST,
    Zpower_SDT=Zpower_SDT,
    Zflow_intercept=Zflow_intercept,
    Zflow_SDT=Zflow_SDT,
    Zflow_SST=Zflow_SST,
    Zflow_heatcap=Zflow_heatcap,
    Zevap_HPC_cst=Zevap_HPC_cst,
    Zevap_HPC_Coolcap=Zevap_HPC_Coolcap,
    Zevap_HPC_SST=Zevap_HPC_SST,
    Zevap_HPH_cst=Zevap_HPH_cst,
    Zevap_HPH_SST=Zevap_HPH_SST,
    Zcond_HPC_cst=Zcond_HPC_cst,
    Zcond_HPC_SDT=Zcond_HPC_SDT,
    Zcond_HPH_cst=Zcond_HPH_cst,
    Zcond_HPH_heatcap=Zcond_HPH_heatcap,
    Zcond_HPH_SST=Zcond_HPH_SST,
    Zcond_HPH_SDT=Zcond_HPH_SDT,
    Zpower_Ncomp=Zpower_Ncomp,
    is_CoatingOption=isCoating,
    Zevap_HPH_coated=Zevap_coated_HPH,
    Zcond_HPCcoated=Zcond_HPC_coated,Zevap_HPC_Coolcap = Zevap_HPC_Coolcap,Zevap_HPH_Coolcap = Zevap_HPH_Coolcap,Zflow_Ncomp = Zflow_Ncomp,Zpower_SST2 = Zpower_SST2,Zflow_SST2 = Zflow_SST2,Zflow_SST3 = Zflow_SST3,Z_cond_HPH_min = Zcond_HPH_min,Z_cond_HPH_max = Zcond_HPH_max,Z_cond_HPC_min = Zcond_HPC_min,Z_cond_HPC_max = Zcond_HPC_max,Z_Evap_HPC_max = Zevap_HPC_max,Z_Evap_HPC_min = Zevap_HPC_min,Z_Evap_HPH_min = Zevap_HPH_min,Z_Evap_HPH_max = Zevap_HPH_max,Z_Flow_min = Zflow_min,Z_Flow_max = Zflow_max,Z_Power_min = Zpower_min,Z_Power_max = Zpower_max)
    annotation (Placement(transformation(extent={{-81.23462181004618,91.93828976683795},{-57.56037333272627,115.61253824415786}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression SST(
    y=node_suction.summary.Tsat)
    annotation (Placement(transformation(extent={{-125.34460057609186,117.09605568016005},{-113.71961068323728,128.72104557301466}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression SDT(
    y=node_discharge.summary.Tsat)
    annotation (Placement(transformation(extent={{-125.34460057609186,107.56767897592525},{-113.71961068323728,119.19266886877986}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression Q_flow(
    y=condBPHE.summary.Q_flow_coolant)
    annotation (Placement(transformation(extent={{-124.28573834484928,86.78912994457673},{-112.66074845199469,100.62583025577754}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression Ncomp(
    y=compressor.summary.Ncomp)
    annotation (Placement(transformation(extent={{-124.28573834484928,98.21785437565362},{-112.66074845199469,109.8428442685082}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression SH(
    y=node_suction.summary.dTsh)
    annotation (Placement(transformation(extent={{-124.28573834484928,77.556344144431},{-112.66074845199469,89.25669995989833}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression DGT(
    y=node_discharge.summary.T)
    annotation (Placement(transformation(extent={{-125.8124949464273,70.14982209226633},{-114.1875050535727,81.85017790773367}},origin={0.0,0.0},rotation=0.0)));
  parameter Real Water_pressure=200000
    annotation (Dialog(group="Medium"));
  parameter Real ssh_setPoint=5
    "circuit A ssh set point"
    annotation (Dialog(group="Refrigerant"));
  parameter.Modelica.SIunits.TemperatureDifference SC_setpoint=-2
    annotation (Dialog(group="Refrigerant"));
  parameter Boolean SC_fixed=true
    annotation (Dialog(group="Refrigerant"));
  parameter Boolean Mref_fixed=false
    annotation (Dialog(group="Refrigerant"));
  parameter.Modelica.SIunits.Mass Mref=5
    annotation (Dialog(group="Refrigerant"));
  .Modelica.Blocks.Sources.RealExpression P_compressor(
    y=compressor.summary.P_compression)
    annotation (Placement(transformation(extent={{-125.8124949464273,62.149822092266334},{-114.1875050535727,73.85017790773367}},origin={0.0,0.0},rotation=0.0)));
    parameter Real Zevap_HPH_Coolcap annotation(Dialog(group = "evaporator",tab = "Calibration"));
    parameter Real Zevap_HPC_Coolcap annotation(Dialog(group = "evaporator",tab = "Calibration"));
    .Workspace.Auxiliary.Altitude_to_Pressure altitude_to_Pressure(altitude = Altitude) annotation(Placement(transformation(extent = {{-9.087204427195964,-94.7573369578738},{10.912795572804036,-74.7573369578738}},origin = {0,0},rotation = 0)));
    parameter Real Altitude = 0;
    parameter Real Nominal_cap = 38000 annotation(Dialog(tab = "Unit Characteristics"));
    parameter Real OAT_target_cap annotation(Dialog(tab = "Unit Characteristics"));
    parameter Real cst_target_cap annotation(Dialog(tab = "Unit Characteristics"));
    parameter Real Nominal_cap_LN = 38000 annotation(Dialog(tab = "Unit Characteristics"));
    parameter Real OAT_target_cap_LN annotation(Dialog(tab = "Unit Characteristics"));
    parameter Real cst_target_cap_LN annotation(Dialog(tab = "Unit Characteristics"));
    parameter Real Max_target_cap = if not is_ULN_option then (if OAT <= 280.65 then Nominal_cap * (1 - Booltoreal(isOff)) else (OAT_target_cap * OAT + cst_target_cap) * (1 - Booltoreal(isOff))) else (if OAT <= 280.65 then Nominal_cap_LN * (1 - Booltoreal(isOff)) else (OAT_target_cap_LN * OAT + cst_target_cap_LN) * (1 - Booltoreal(isOff))) annotation(Dialog());
    parameter Real Max_max_target_capa = if not is_ULN_option then Max_max_target_cap * (1 - Booltoreal(isOff)) else Max_max_target_cap_LN * (1 - Booltoreal(isOff)) annotation(Dialog(tab = "Unit Characteristics")); 
    parameter Real Max_max_target_cap;
    parameter Real Max_max_target_cap_LN;
    parameter Boolean is_ULN_option = false;
protected
  parameter.Modelica.SIunits.Frequency max_motor_frequency_STD=39.5
    "maximum motor frequency when not low noise fan";
  parameter.Modelica.SIunits.Conversions.NonSIunits.AngularVelocity_rpm max_fan_rpm_STD=720
    "maximum fan speed when not low noise fan";
public
  .BOLT.BoundaryNode.Refrigerant.Node node_discharge(
    RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R290,
    isOff=isOff_ref)
    annotation (Placement(transformation(extent={{118.25269093923501,-54.756115770988586},{126.25269093923501,-46.756115770988586}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Refrigerant.Node node_suction(
    dTsh_fixed=false,
    RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R290,
    isOff=isOff_ref)
    annotation (Placement(transformation(extent={{68.25269093923501,-54.756115770988586},{76.25269093923501,-46.756115770988586}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Refrigerant.Node node_valveout(
    RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R290,
    isOff=isOff_ref)
    annotation (Placement(transformation(extent={{-4.0,-4.0},{4.0,4.0}},origin={-83.74730906076499,-36.756115770988586},rotation=-90.0)));
  .BOLT.Valve.Refrigerant.ValveEXV valveEXV(
    RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R290,
    Tsat_a_start=globalParameters.Tsat_cond_start-1,
    use_actuator_in=true,
    isOff=isOff_ref,
    selector_flowData=EXV_main)
    annotation (Placement(transformation(extent={{-6.0,-6.0},{6.0,6.0}},origin={-83.74730906076499,3.243884229011414},rotation=-90.0)));
  inner.BOLT.GlobalParameters globalParameters(
    T_ambient=OAT,
    RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R290,
    Tsat_cond_start=max(
      299.15,
      LWT+2),
    varLevel=BOLT.InternalLibrary.BuildingBlocks.Types.Var_level.Advanced,
    capacity_design=capacity_design,
    capacity_start=globalParameters.capacity_design/(2),
    Tsat_evap_start=min(
      292.15,
      OAT-12),
    CondCoolantMedium=CoolantMedium,
    Cond_X=BrineConcentration,p_ambient = altitude_to_Pressure.p)
    annotation (Placement(transformation(extent={{30.547487767549605,-96.75611577098859},{50.547487767549605,-76.75611577098859}},origin={0.0,0.0},rotation=0.0)));
  inner.BOLT.InternalLibrary.Refrigerant.Aggregation.AggregateStreams_2 systemVariables(
    fan_ducted=false,
    integrated_pump_in=false,
    nStreams=1,
    isOff={isOff},
    mRef_set={Mref},
    mRef_fixed={Mref_fixed})
    annotation (Placement(transformation(extent={{64.74943843896182,-96.75611577098859},{84.74943843896182,-76.75611577098859}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Air.Source sourceAir(
    Vds_flow_fixed=false,
    Vds_flow_set=2.5,
    pg_fixed=true,
    Tdb_set=OAT,
    RH_set=relative_humidity,
    isOff=isOff_ref,
    RH_fixed=is_relative_humidity,
    Twb_fixed=is_OAT_WB,
    Twb_set=OAT_WB,
    Vd_flow_set=2.5)
    annotation (Placement(transformation(extent={{5.434418992295633,-5.434418992295605},{-5.434418992295633,5.434418992295605}},origin={-32.77822408420735,-100.59080179895189},rotation=-90.0)));
  .BOLT.BoundaryNode.Air.Sink sinkAir(
    Vds_flow_fixed=false,
    pg_fixed=true,
    Vds_flow_set=4.2,
    isOff=isOff_ref)
    annotation (Placement(transformation(extent={{-4.776077345930645,-4.776077345930673},{4.776077345930645,4.776077345930673}},origin={-99.99782666257843,-101.05959061977435},rotation=90.0)));
  .BOLT.BoundaryNode.Refrigerant.Node node_condout(
    RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R290,
    dTsh_fixed=false,
    dTsh_set=-5,
    isOff=isOff_ref)
    annotation (Placement(transformation(extent={{-23.74730906076499,55.243884229011414},{-31.74730906076499,63.243884229011414}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Mechanical.Motor motor(
    selector_AC=.BOLT.InternalLibrary.Mechanicals.Motor.DataBase.MotorCurveMap.AC_motor.Selector.LEROYSOMER_4860698,
    shaftSpeed_set=720,
    MotorHz=37.5,
    MotorHz_fixed=false,
    use_scaling_motor_in=false,
    use_shaftSpeed_in=true,
    use_frequency_in=false,
    isOff=isOff_ref,
    Mtrfreq_start=37.5,
    FanMotType=.BOLT.InternalLibrary.Mechanicals.Motor.Interfaces.FanMotType.EC_FanMot,
    selector_EC=.BOLT.InternalLibrary.Mechanicals.Motor.DataBase.MotorCurveMap.EC_motor.Selector.EBM_M3G150_IF25_A5,
    Z_power=0.85)
    annotation (Placement(transformation(extent={{-4.561700292289942,-5.017870321518927},{4.561700292289942,5.017870321518927}},origin={-66.74763294083888,-76.23458940809138},rotation=90.0)));
  .BOLT.AirMisc.FanCurve fanCurve(
    selector_curve=BOLT.InternalLibrary.Air.Fans.DataBase.Selector.ZN080_ZIQ,
    nStage=0.5,
    dp_start=10,
    isOff=isOff_ref,
    userDefined_curve(
      dp_curve={{0},{43},{81},{115},{147},{173},{200},{224},{224},{224},{224}},
      V_curve={{6.66},{6.3},{5.95},{5.6},{5.23},{4.86},{4.5},{4.11},{4.11},{4.11},{4.11}},
      P_curve={{1360},{1487},{1580},{1656},{1711},{1749},{1785},{1823},{1823},{1823},{1823}}),
    Fw=Fw_fan)
    annotation (Placement(transformation(extent={{-6.848415197744316,-6.848415197744345},{6.848415197744316,6.848415197744345}},origin={-99.60828450777458,-83.53142802447547},rotation=-90.0)));
  .BOLT.RefMisc.ReducedPipe liquid_line(
    RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R290,
    x_a_start=-0.1,
    isOff=isOff_ref,
    Di=EXV_in_line_diameter,
    length=EXV_in_line_length,
    Tsat_a_start=globalParameters.Tsat_cond_start-1,
    use_k_dp=true)
    annotation (Placement(transformation(extent={{-6.0,-6.599999999999994},{6.0,6.599999999999994}},origin={-51.74730906076499,59.243884229011414},rotation=-180.0)));
  .BOLT.RefMisc.ReducedPipe suction_line(
    RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R290,
    Tsat_a_start=globalParameters.Tsat_evap_start,
    x_a_start=1.02,
    isOff=isOff_ref,
    k_dp=0.1,
    Di=Suction_line_diameter,
    length=Suction_line_length,
    use_k_dp=true)
    annotation (Placement(transformation(extent={{46.28921596171128,-57.35611577098859},{58.28921596171128,-44.156115770988585}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Refrigerant.Node node_evapout(
    RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R290,
    isOff=isOff_ref)
    annotation (Placement(transformation(extent={{-28.960810631939943,-56.34057949556903},{-20.960810631939943,-48.34057949556903}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Refrigerant.Node node_EXV_in(
    RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R290,
    dTsh_fixed=SC_fixed,
    dTsh_set=SC_setpoint,
    isOff=isOff_ref)
    annotation (Placement(transformation(extent={{-69.74730906076499,55.243884229011414},{-77.74730906076499,63.243884229011414}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Valve.Refrigerant.ReversingValve reversingValve(
    use_k_dp=false,
    selector_valveData=.BOLT.InternalLibrary.Refrigerant.Valves.FourPort.DataBase.Selector.userDefined,
    use_leak=false,
    RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R290,
    Tsat_high_start=globalParameters.Tsat_cond_start,
    Tsat_low_start=globalParameters.Tsat_evap_start,
    isOff=isOff_ref,
    redeclare replaceable.BOLT.Valve.Refrigerant.UserDefinedBase.ReversingValve_valveData userDefined_valveData(
      m_flow_leak_ref=1.56e-4,
      dp_low_ref=PDC_4WV))
    annotation (Placement(transformation(extent={{-10.0,10.0},{10.0,-10.0}},origin={66.25269093923495,-6.756115770988586},rotation=90.0)));
  .BOLT.BoundaryNode.Refrigerant.Node node_condin(
    RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R290,
    isOff=isOff_ref)
    annotation (Placement(transformation(extent={{-4.0,-4.0},{4.0,4.0}},origin={65.31890544940282,40.79055617906006},rotation=90.0)));
  .BOLT.RefMisc.ReducedPipe Coil_line(
    RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R290,
    Tsat_a_start=globalParameters.Tsat_evap_start,
    x_a_start=1.02,
    isOff=isOff_ref,
    Di=coil_line_diameter,
    length=coil_line_length,
    use_k_dp=true)
    annotation (Placement(transformation(extent={{6.0,-6.599999999999994},{-6.0,6.599999999999994}},origin={-2.3768191844675357,-50.99978661855715},rotation=-180.0)));
  .BOLT.BoundaryNode.Refrigerant.Node node_4WV_LP_out(
    RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R290,
    isOff=isOff_ref)
    annotation (Placement(transformation(extent={{-4.0,-4.0},{4.0,4.0}},origin={42.25269093923501,-22.352046362499138},rotation=-90.0)));
  .BOLT.AirMisc.Duct duct(
    dp_start(
      displayUnit="Pa")=10,
    Ac=Ac_duct,
    Ka=Ka_duct,
    isOff=isOff_ref,
    UA=UA_duct,
    T_start=sourceAir.Tdb_set-5,
    duct(
      Tdb_bounds(
        min=243.15)))
    annotation (Placement(transformation(extent={{-4.8778762792247505,-4.8778762792247505},{4.8778762792247505,4.8778762792247505}},origin={-89.75237599834875,-58.385877423468294},rotation=-180.0)));
  .Workspace.Interfaces.MeasurementBus measurementBus
    annotation (Placement(transformation(extent={{157.7617913012714,43.74444417420516},{184.6401372707166,70.6227901436503}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={110,60})));
  .Modelica.Blocks.Sources.RealExpression dT_ssh(
    y=node_suction.summary.dTsh)
    annotation (Placement(transformation(extent={{108.11942643294066,85.44158741619184},{120.26648328945049,97.58864427270161}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_sdt(
    y=node_discharge.summary.Tsat)
    annotation (Placement(transformation(extent={{108.18382182713961,73.9531457577405},{120.33087868364939,86.10020261425028}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_dgt(
    y=node_discharge.summary.T)
    annotation (Placement(transformation(extent={{108.18382182713961,56.94726615862679},{120.33087868364939,69.09432301513657}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_sst(
    y=node_suction.summary.Tsat)
    annotation (Placement(transformation(extent={{107.9017933841,65.45020595818362},{120.04885024060981,77.59726281469345}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_ewt(
    y=node_sourcebrine.summary.T)
    annotation (Placement(transformation(extent={{108.18382182713961,48.72635480210948},{120.33087868364939,60.87341165861929}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_lwt(
    y=node_sinkBrine.summary.T)
    annotation (Placement(transformation(extent={{108.18382182713961,41.15609224516402},{120.33087868364939,53.303149101673824}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_oat(
    y=sourceAir.Tdb_set)
    annotation (Placement(transformation(extent={{107.9711255960909,32.65315244560716},{120.11818245260068,44.80020930211697}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression compressor_frequency(
    y=compressor.summary.Ncomp)
    annotation (Placement(transformation(extent={{108.15437441657834,22.731215549630747},{120.30143127308814,34.87827240614055}},origin={0.0,0.0},rotation=0.0)));
  // Declaration of Coolant Medium Package
  //package CoolantCommonMedium =
  //.BOLT.InternalLibrary.Media.Coolant.CoolantCommon                             "Coolant Medium Package" annotation ();
  // Declaration of Coolant Medium at Evaporator Circuit
  parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector CoolantMedium=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector.FW
    "Coolant Medium Selection"
    annotation (Dialog(group="Medium"));
  final parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Empty coolantInf=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.getSelector(
    CoolantMedium)
    "Coolant Medium"
    annotation ();
  .BOLT.InternalLibrary.Media.Coolant.CoolantCommon.Temperature FreezTemp=.BOLT.InternalLibrary.Media.Coolant.CoolantCommon.freezeTemp(
    coolantInf,
    BrineConcentration,
    1);
  .Modelica.SIunits.VolumeFlowRate AirFlow = if isOff then 0 else sourceAir.summary.Vd_flow;
  parameter Real BrineConcentration=0.2
    annotation (Dialog(group="Medium"));
equation
  connect(valveEXV.port_b,node_valveout.port_a)
    annotation (Line(points={{-83.74730906076499,-2.756115770988586},{-83.74730906076499,-32.756115770988586}},color={255,0,0}));
  connect(motor.ShaftOut,fanCurve.flange)
    annotation (Line(points={{-71.76550326235783,-77.42063148408677},{-82.26268628619405,-77.42063148408677},{-82.26268628619405,-83.53142802447547},{-92.75986931003024,-83.53142802447547}},color={127,0,0}));
  connect(suction_line.port_b,node_suction.port_a)
    annotation (Line(points={{58.289215961711285,-50.756115770988586},{68.25269093923501,-50.756115770988586}},color={255,0,0}));
  connect(reversingValve.port_S,node_4WV_LP_out.port_a)
    annotation (Line(points={{57.25269093923497,-6.756115770988586},{42.25269093923501,-6.756115770988586},{42.25269093923501,-18.352046362499145}},color={255,0,0}));
  connect(node_4WV_LP_out.port_b,suction_line.port_a)
    annotation (Line(points={{42.25269093923501,-26.352046362499145},{42.25269093923501,-50.756115770988586},{46.289215961711285,-50.756115770988586}},color={255,0,0}));
  connect(EXV_controller,valveEXV.openCommand)
    annotation (Line(points={{-68.64779492408945,2.555103309750166},{-68.64779492408945,3.243884229011414},{-79.84730906076499,3.243884229011414}},color={0,0,127}));
  connect(compressor_controller,compressor.N_speed)
    annotation (Line(points={{96.25269093923501,-28.756115770988586},{96.25269093923501,-21.124194227927816},{87.53605110166245,-21.124194227927816},{87.53605110166245,-41.33771184814161}},color={0,0,127}));
  connect(node_suction.port_b,compressor.port_a)
    annotation (Line(points={{76.25269093923501,-50.756115770988586},{83.05605110166245,-50.756115770988586},{83.05605110166245,-51.137711848141606}},color={255,0,0}));
  connect(compressor.port_b,node_discharge.port_a)
    annotation (Line(points={{111.05605110166245,-51.137711848141606},{115.20269093923501,-51.137711848141606},{115.20269093923501,-50.756115770988586},{118.25269093923501,-50.756115770988586}},color={255,0,0}));
  connect(sinkAir.port,fanCurve.port_b)
    annotation (Line(points={{-99.99782666257843,-96.28351327384371},{-99.60828450777458,-96.28351327384371},{-99.60828450777458,-90.37984322221979}},color={0,0,255}));
  connect(dT_ssh.y,measurementBus.dT_ssh)
    annotation (Line(points={{120.874,91.5151},{152.465,91.5151},{152.465,57.2508},{171.268,57.2508}},color={0,0,127}));
  connect(T_sdt.y,measurementBus.T_sdt)
    annotation (Line(points={{120.938,80.0267},{152.114,80.0267},{152.114,57.2508},{171.268,57.2508}},color={0,0,127}));
  connect(T_sst.y,measurementBus.T_sst)
    annotation (Line(points={{120.656,71.5237},{152.114,71.5237},{152.114,57.2508},{171.268,57.2508}},color={0,0,127}));
  connect(T_dgt.y,measurementBus.T_dgt)
    annotation (Line(points={{120.938,63.0208},{152.114,63.0208},{152.114,57.2508},{171.268,57.2508}},color={0,0,127}));
  connect(T_ewt.y,measurementBus.T_ewt)
    annotation (Line(points={{120.938,54.7999},{152.114,54.7999},{152.114,57.2508},{171.268,57.2508}},color={0,0,127}));
  connect(T_lwt.y,measurementBus.T_lwt)
    annotation (Line(points={{120.938,47.2296},{152.114,47.2296},{152.114,57.2508},{171.268,57.2508}},color={0,0,127}));
  connect(T_oat.y,measurementBus.T_oat)
    annotation (Line(points={{120.726,38.7267},{152.114,38.7267},{152.114,57.2508},{171.268,57.2508}},color={0,0,127}));
  connect(compressor_frequency.y,measurementBus.compressorFrequency)
    annotation (Line(points={{120.909,28.8047},{152.541,28.8047},{152.541,57.2508},{171.268,57.2508}},color={0,0,127}));
  connect(node_out_coil_air.port_b,duct.port_a)
    annotation (Line(points={{-78.52370337243704,-59.113166211227636},{-84.874499719124,-59.113166211227636},{-84.874499719124,-58.385877423468294}},color={0,0,255}));
  connect(duct.port_b,node_out_duct_air.port_a)
    annotation (Line(points={{-94.6302522775735,-58.385877423468294},{-99.19257992017214,-58.385877423468294},{-99.19257992017214,-62.61906744304106}},color={0,0,255}));
  connect(node_out_duct_air.port_b,fanCurve.port_a)
    annotation (Line(points={{-99.19257992017214,-70.61906744304106},{-99.19257992017214,-76.68301282673116},{-99.60828450777458,-76.68301282673116}},color={0,0,255}));
  connect(node_discharge.port_b,reversingValve.port_D)
    annotation (Line(points={{126.25269093923501,-50.756115770988586},{132.252690939235,-50.756115770988586},{132.252690939235,-6.756115770988586},{75.25269093923497,-6.756115770988586}},color={255,0,0}));
  connect(node_evapout.port_b,Coil_line.port_a)
    annotation (Line(points={{-20.960810631939943,-52.34057949556903},{-14.668814908203743,-52.34057949556903},{-14.668814908203743,-50.999786618557145},{-8.376819184467536,-50.999786618557145}},color={255,0,0}));
  connect(Coil_line.port_b,node_4WV_LP_in.port_a)
    annotation (Line(points={{3.6231808155324643,-50.999786618557145},{9.579483336007563,-50.999786618557145},{9.579483336007563,-50.68000117835085},{16.56541306822103,-50.68000117835085}},color={255,0,0}));
  connect(node_4WV_LP_in.port_b,reversingValve.port_E)
    annotation (Line(points={{24.56541306822103,-50.68000117835085},{24.56541306822103,-41.42246898581743},{66.25269093923497,-41.42246898581743},{66.25269093923497,-15.756115770988586}},color={255,0,0}));
  connect(node_condout.port_b,liquid_line.port_a)
    annotation (Line(points={{-31.74730906076499,59.243884229011414},{-45.74730906076499,59.243884229011414}},color={255,0,0}));
  connect(node_EXV_in.port_b,valveEXV.port_a)
    annotation (Line(points={{-77.74730906076499,59.243884229011414},{-83.74730906076499,59.243884229011414},{-83.74730906076499,9.243884229011414}},color={255,0,0}));
  connect(liquid_line.port_b,node_EXV_in.port_a)
    annotation (Line(points={{-57.74730906076499,59.243884229011414},{-69.74730906076499,59.243884229011414}},color={255,0,0}));
  connect(sourceAir.port,evapAir1_MVB.port_a_air)
    annotation (Line(points={{-32.77822408420735,-95.15638280665625},{-32.77822408420735,-59.35611577098858},{-46.54730906076499,-59.35611577098858}},color={0,0,255}));
  connect(evapAir1_MVB.port_b_air,node_out_coil_air.port_a)
    annotation (Line(points={{-65.147309060765,-59.35611577098859},{-65.147309060765,-59.113166211227636},{-70.52370337243704,-59.113166211227636}},color={0,0,255}));
  connect(node_valveout.port_b,evapAir1_MVB.port_a_ref)
    annotation (Line(points={{-83.74730906076499,-40.756115770988586},{-83.74730906076499,-54.55611577098858},{-65.34730906076499,-54.55611577098858}},color={255,0,0}));
  connect(evapAir1_MVB.port_b_ref,node_evapout.port_a)
    annotation (Line(points={{-46.54730906076499,-54.55611577098858},{-40.31219537399572,-54.55611577098858},{-40.31219537399572,-52.34057949556903},{-28.960810631939943,-52.34057949556903}},color={255,0,0}));
  connect(condBPHE.port_ref_b,node_condout.port_a)
    annotation (Line(points={{6.352690939235007,59.06428751094718},{-23.74730906076499,59.06428751094718},{-23.74730906076499,59.243884229011414}},color={255,0,0}));
  connect(node_sourcebrine.port_b,condBPHE.port_coolant_a)
    annotation (Line(points={{-2.194445963644725,76.98613348133698},{2.866850807318116,76.98613348133698},{2.866850807318116,65.46428751094719},{6.352690939235007,65.46428751094719}},color={0,127,0}));
  connect(condBPHE.port_coolant_b,node_sinkBrine.port_a)
    annotation (Line(points={{26.15269093923501,65.46428751094719},{33.812877391400136,65.46428751094719},{33.812877391400136,76.25558695919356},{39.85284874234604,76.25558695919356}},color={0,127,0}));
  connect(port_a,node_sourcebrine.port_a)
    annotation (Line(points={{-19.838251179316302,99.28484223121312},{-19.838251179316302,76.98613348133698},{-10.194445963644725,76.98613348133698}},color={0,127,0}));
  connect(node_sinkBrine.port_b,port_b)
    annotation (Line(points={{47.85284874234604,76.25558695919354},{52.27665269609748,76.25558695919354},{52.27665269609748,98.44526814222617}},color={0,127,0}));
  connect(motor.speed_in,actual_FSFanSpd_in)
    annotation (Line(points={{-63.782527750850434,-81.11560872084162},{-63.782527750850434,-86.84729429428181},{-59.750446216281205,-86.84729429428181},{-59.750446216281205,-92.************}},color={0,0,127}));
  connect(reversingValve.port_C,node_condin.port_a)
    annotation (Line(points={{66.25269093923497,2.243884229011414},{66.25269093923497,36.79055617906006},{65.31890544940282,36.79055617906006}},color={255,0,0}));
  connect(node_condin.port_b,condBPHE.port_ref_a)
    annotation (Line(points={{65.31890544940282,44.79055617906006},{65.31890544940282,59.06428751094718},{26.35269093923501,59.06428751094718}},color={255,0,0}));
  connect(calibrationBlock.SH,SH.y)
    annotation (Line(points={{-78.630454477541,105.6693538836835},{-89.42362739009465,105.6693538836835},{-89.42362739009465,83.40652205216466},{-112.07949895735196,83.40652205216466}},color={0,0,127}));
  connect(calibrationBlock.DGT,DGT.y)
    annotation (Line(points={{-78.630454477541,100.93450418821952},{-89.39883332432937,100.93450418821952},{-89.39883332432937,76},{-113.60625555892997,76}},color={0,0,127}));
  connect(calibrationBlock.Q_flow,Q_flow.y)
    annotation (Line(points={{-78.630454477541,102.3549590968587},{-89.12121718000444,102.3549590968587},{-89.12121718000444,93.70748010017714},{-112.07949895735196,93.70748010017714}},color={0,0,127}));
  connect(calibrationBlock.Ncomp,Ncomp.y)
    annotation (Line(points={{-78.630454477541,107.32655127709589},{-90.44385201224343,107.32655127709589},{-90.44385201224343,104.03034932208091},{-112.07949895735196,104.03034932208091}},color={0,0,127}));
  connect(calibrationBlock.SDT,SDT.y)
    annotation (Line(points={{-78.630454477541,112.77162842687947},{-88.27878760390554,112.77162842687947},{-88.27878760390554,113.38017392235255},{-113.13836118859454,113.38017392235255}},color={0,0,127}));
  connect(calibrationBlock.SST,SST.y)
    annotation (Line(points={{-78.630454477541,114.55903418691713},{-88.63498070999715,114.55903418691713},{-88.63498070999715,122.90855062658736},{-113.13836118859454,122.90855062658736}},color={0,0,127}));
    connect(calibrationBlock.P_compressor,P_compressor.y) annotation(Line(points = {{-78.6080429102187,99.14116198804345},{-86.55830589269948,99.14116198804345},{-86.55830589269948,68},{-113.60625555892997,68}},color = {0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          fillColor={117,117,117},
          fillPattern=FillPattern.Solid,
          extent={{-103,-104},{103,104}},
          origin={3,4}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end OL_FW092;
