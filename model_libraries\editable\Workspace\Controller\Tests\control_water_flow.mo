within Workspace.Controller.Tests;
model control_water_flow
  parameter.Modelica.SIunits.HeatFlowRate TargetCoolingCapacity=215000;
  parameter Real Gain=-0.0001;
  parameter Real Gain_LWT=0.01;
  parameter.Modelica.SIunits.Temperature Heating_Temp=313.15;
  parameter.Modelica.SIunits.Temperature LWT_sp=308.15;
  parameter.Modelica.SIunits.Temperature EWT_sp=303.15;
  output.Modelica.SIunits.HeatFlowRate TotalCapacity;
  .BOLT.CoolantMisc.ReducedPipe PdC_Cool_3(
    Ka_set=0.01,
    use_Ka_in=true)
    annotation (Placement(transformation(extent={{25.40576483308562,-19.40576483308562},{14.59423516691438,-8.59423516691438}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.ReducedPipe PdC_Cool_4(
    Ka_set=0.01,
    use_Ka_in=true)
    annotation (Placement(transformation(extent={{24.873681474297932,-40.87368147429793},{15.126318525702068,-31.126318525702068}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Source MainInlet(
    Vd_set=WaterFlow_sp,
    T_set=EWT_sp,
    Vd_fixed=false,
    p_fixed=true,
    p_set=100000)
    annotation (Placement(transformation(extent={{-104.0,-2.0},{-96.0,6.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.ReducedPipe PdC_Cool_1(
    Ka_set=0.01,
    use_Ka_in=true)
    annotation (Placement(transformation(extent={{24.723607357042866,35.276392642957134},{15.276392642957134,44.723607357042866}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation errorCalculation_2(
    measurement=TotalCapacity,
    setPoint=TargetCoolingCapacity,
    gain=Gain)
    annotation (Placement(transformation(extent={{59.0,64.0},{51.0,72.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.ReducedPipe PdC_LWT_3(
    Ka_set=0.01,
    use_Ka_in=true)
    annotation (Placement(transformation(extent={{-24.0,-16.0},{-16.0,-24.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.ReducedPipe PdC_LWT_2(
    Ka_set=0.01,
    use_Ka_in=true)
    annotation (Placement(transformation(extent={{-24.0,8.0},{-16.0,16.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.ReducedPipe PdC_Cool_2(
    Ka_set=0.01,
    use_Ka_in=true)
    annotation (Placement(transformation(extent={{24.98029306711596,15.019706932884034},{15.019706932884041,24.980293067115966}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointController(
    AV_min=0.0001,
    AV_start=10,
    AV_max=100)
    annotation (Placement(transformation(extent={{41.0,64.0},{33.0,72.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.ReducedPipe PdC_LWT_4(
    Ka_set=0.01,
    use_Ka_in=true)
    annotation (Placement(transformation(extent={{-24.391971779300885,-39.60802822069911},{-15.608028220699115,-48.39197177930089}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.ReducedPipe PdC_LWT_1(
    Ka_set=0.01,
    use_Ka_in=true)
    annotation (Placement(transformation(extent={{-24.0,30.0},{-16.0,38.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Node In4
    annotation (Placement(transformation(extent={{-44.0,-48.0},{-36.0,-40.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Node Out2
    annotation (Placement(transformation(extent={{36.0,8.0},{44.0,16.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Node Out1
    annotation (Placement(transformation(extent={{36.0,30.0},{44.0,38.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Sink MainOutlet(
    p_set=0)
    annotation (Placement(transformation(extent={{121.8704720087815,-3.517014270032919},{113.8704720087815,4.482985729967081}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.Split split_1
    annotation (Placement(transformation(extent={{-64.0,24.0},{-56.0,32.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Node Out3
    annotation (Placement(transformation(extent={{36.0,-24.0},{44.0,-16.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Node In3
    annotation (Placement(transformation(extent={{-44.0,-24.0},{-36.0,-16.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.Split split_0
    annotation (Placement(transformation(extent={{-66.0,-24.0},{-58.0,-16.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.Mixer mixer_1
    annotation (Placement(transformation(extent={{56.498205275669434,22.498205275669438},{67.50179472433057,33.501794724330566}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Node In2
    annotation (Placement(transformation(extent={{-44.0,6.0},{-36.0,14.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Node In1
    annotation (Placement(transformation(extent={{-44.0,30.0},{-36.0,38.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.Mixer mixer
    annotation (Placement(transformation(extent={{74.18604996259316,-5.813950037406841},{85.81395003740684,5.813950037406841}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.Split split
    annotation (Placement(transformation(extent={{-84.0,-2.0},{-76.0,6.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.Mixer mixer_0
    annotation (Placement(transformation(extent={{54.94619260769116,-37.05380739230884},{65.05380739230884,-26.94619260769116}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Node Out4
    annotation (Placement(transformation(extent={{36.0,-48.0},{44.0,-40.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.HeatExchanger.CoolantCoolant.BriBriHX_Reduced briBriHX_Reduced_1(
    UA=15000)
    annotation (Placement(transformation(extent={{8.046560632732016,46.04656063273202},{-8.046560632732016,29.953439367267983}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.HeatExchanger.CoolantCoolant.BriBriHX_Reduced briBriHX_Reduced_2(
    UA=12000)
    annotation (Placement(transformation(extent={{8.287198343686516,24.287198343686516},{-8.287198343686516,7.712801656313484}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.HeatExchanger.CoolantCoolant.BriBriHX_Reduced briBriHX_Reduced_3(
    UA=10000)
    annotation (Placement(transformation(extent={{7.604660789993614,-8.395339210006387},{-7.604660789993614,-23.604660789993613}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.HeatExchanger.CoolantCoolant.BriBriHX_Reduced briBriHX_Reduced_4(
    UA=11000)
    annotation (Placement(transformation(extent={{7.920628599030769,-32.07937140096923},{-7.920628599030769,-47.92062859903077}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Source source_1(
    T_set=Heating_Temp,
    p_set=340000,
    Vd_fixed=false,
    p_fixed=true)
    annotation (Placement(transformation(extent={{44.0,48.0},{36.0,56.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Source source_2(
    T_set=Heating_Temp,
    p_set=340000,
    Vd_fixed=false,
    p_fixed=true)
    annotation (Placement(transformation(extent={{44.0,20.0},{36.0,28.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Source source_4(
    T_set=Heating_Temp,
    p_set=340000,
    Vd_fixed=false,
    p_fixed=true)
    annotation (Placement(transformation(extent={{44.0,-36.0},{36.0,-28.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Source source_3(
    T_set=Heating_Temp,
    p_set=340000,
    Vd_fixed=false,
    p_fixed=true)
    annotation (Placement(transformation(extent={{44.0,-14.0},{36.0,-6.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Sink sink1
    annotation (Placement(transformation(extent={{-24.0,42.0},{-16.0,50.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Sink sink2
    annotation (Placement(transformation(extent={{-24.0,16.0},{-16.0,24.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Sink sink3
    annotation (Placement(transformation(extent={{-24.0,-16.0},{-16.0,-8.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Sink sink4
    annotation (Placement(transformation(extent={{-24.0,-40.0},{-16.0,-32.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointController_lwt1(
    AV_max=1000,
    AV_start=10,
    AV_min=0.0001)
    annotation (Placement(transformation(extent={{-66.0,60.0},{-58.0,68.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation errorCalculation_lwt1(
    gain=Gain_LWT,
    setPoint=LWT_sp,
    measurement=Out1.summary.T)
    annotation (Placement(transformation(extent={{-92.0,60.0},{-84.0,68.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointController_lwt(
    AV_min=0.0001,
    AV_start=10,
    AV_max=1000)
    annotation (Placement(transformation(extent={{-67.0,42.0},{-59.0,50.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation errorCalculation_lwt(
    measurement=Out2.summary.T,
    setPoint=LWT_sp,
    gain=Gain_LWT)
    annotation (Placement(transformation(extent={{-92.0,42.0},{-84.0,50.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointController_lwt2(
    AV_min=0.0001,
    AV_start=10,
    AV_max=1000)
    annotation (Placement(transformation(extent={{-58.75,-61.0},{-50.75,-53.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation errorCalculation_lwt2(
    measurement=Out3.summary.T,
    setPoint=LWT_sp,
    gain=Gain_LWT)
    annotation (Placement(transformation(extent={{-84.75,-61.0},{-76.75,-53.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointController_lwt3(
    AV_max=1000,
    AV_start=10,
    AV_min=0.0001)
    annotation (Placement(transformation(extent={{-59.75,-79.0},{-51.75,-71.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation errorCalculation_lwt3(
    gain=Gain_LWT,
    setPoint=LWT_sp,
    measurement=Out4.summary.T)
    annotation (Placement(transformation(extent={{-84.75,-79.0},{-76.75,-71.0}},origin={0.0,0.0},rotation=0.0)));
  parameter.Modelica.SIunits.VolumeFlowRate WaterFlow_sp=0.02;
  parameter Real Gain_FlowRate=10;
equation
  TotalCapacity=briBriHX_Reduced_1.summary.Q_flow_H+briBriHX_Reduced_2.summary.Q_flow_H+briBriHX_Reduced_3.summary.Q_flow_H+briBriHX_Reduced_4.summary.Q_flow_H;
  connect(MainInlet.port,split.port_c)
    annotation (Line(points={{-96,2},{-90.03999999999999,2},{-90.03999999999999,1.92},{-84.08,1.92}},color={0,127,0}));
  connect(split.port_a,split_1.port_c)
    annotation (Line(points={{-76,4.08},{-70,4.08},{-70,16},{-70,16},{-70,27.92},{-64.08,27.92}},color={0,127,0}));
  connect(split_1.port_a,In1.port_a)
    annotation (Line(points={{-56,30.08},{-50,30.08},{-50,34},{-44,34}},color={0,127,0}));
  connect(In1.port_b,PdC_LWT_1.port_a)
    annotation (Line(points={{-36,34},{-24,34}},color={0,127,0}));
  connect(PdC_LWT_1.port_b,briBriHX_Reduced_1.Bri_H_in)
    annotation (Line(points={{-16,34},{-11.68800695666884,34},{-11.68800695666884,34.244938371391726},{-7.3760139133376805,34.244938371391726}},color={0,127,0}));
  connect(briBriHX_Reduced_1.Bri_H_out,Out1.port_a)
    annotation (Line(points={{7.17484989751938,34.177883699452295},{21.58742494875969,34.177883699452295},{21.58742494875969,34},{36,34}},color={0,127,0}));
  connect(Out1.port_b,mixer_1.port_a)
    annotation (Line(points={{44,34},{50.24910263783472,34},{50.24910263783472,30.640861467678672},{56.498205275669434,30.640861467678672}},color={0,127,0}));
  connect(mixer.port_c,MainOutlet.port)
    annotation (Line(points={{86.04650803890311,0},{113.8704720087815,0},{113.8704720087815,0.4829857299670808}},color={0,127,0}));
  connect(mixer_1.port_c,mixer.port_a)
    annotation (Line(points={{67.7218665133038,28},{73.7218665133038,28},{73.7218665133038,15.395348008977642},{68.18604996259316,15.395348008977642},{68.18604996259316,2.790696017955284},{74.18604996259316,2.790696017955284}},color={0,127,0}));
  connect(split.port_b,split_0.port_c)
    annotation (Line(points={{-76,-0.08000000000000007},{-70,-0.08000000000000007},{-70,-10.079999999999998},{-72,-10.079999999999998},{-72,-20.08},{-66.08,-20.08}},color={0,127,0}));
  connect(split_0.port_a,In3.port_a)
    annotation (Line(points={{-58,-17.92},{-51,-17.92},{-51,-20},{-44,-20}},color={0,127,0}));
  connect(In3.port_b,PdC_LWT_3.port_a)
    annotation (Line(points={{-36,-20},{-24,-20}},color={0,127,0}));
  connect(PdC_LWT_3.port_b,briBriHX_Reduced_3.Bri_H_in)
    annotation (Line(points={{-16,-20},{-11.485469528747073,-20},{-11.485469528747073,-19.54884170199702},{-6.970939057494145,-19.54884170199702}},color={0,127,0}));
  connect(briBriHX_Reduced_3.Bri_H_out,Out3.port_a)
    annotation (Line(points={{6.780822537744305,-19.612213875246965},{21.390411268872153,-19.612213875246965},{21.390411268872153,-20},{36,-20}},color={0,127,0}));
  connect(Out3.port_b,mixer_0.port_a)
    annotation (Line(points={{44,-20},{49.47309630384558,-20},{49.47309630384558,-29.57417245169176},{54.94619260769116,-29.57417245169176}},color={0,127,0}));
  connect(mixer_0.port_c,mixer.port_b)
    annotation (Line(points={{65.2559596880012,-32},{71.2559596880012,-32},{71.2559596880012,-17.51162700972578},{68.18604996259316,-17.51162700972578},{68.18604996259316,-3.0232540194515574},{74.18604996259316,-3.0232540194515574}},color={0,127,0}));
  connect(split_0.port_b,In4.port_a)
    annotation (Line(points={{-58,-22.08},{-51,-22.08},{-51,-44},{-44,-44}},color={0,127,0}));
  connect(In4.port_b,PdC_LWT_4.port_a)
    annotation (Line(points={{-36,-44},{-24.391971779300885,-44}},color={0,127,0}));
  connect(PdC_LWT_4.port_b,briBriHX_Reduced_4.Bri_H_in)
    annotation (Line(points={{-15.608028220699115,-44},{-11.43430221823866,-44},{-11.43430221823866,-43.69629334621436},{-7.2605762157782054,-43.69629334621436}},color={0,127,0}));
  connect(briBriHX_Reduced_4.Bri_H_out,Out4.port_a)
    annotation (Line(points={{7.062560500802436,-43.762298584539614},{21.531280250401217,-43.762298584539614},{21.531280250401217,-44},{36,-44}},color={0,127,0}));
  connect(Out4.port_b,mixer_0.port_b)
    annotation (Line(points={{44,-44},{49.47309630384558,-44},{49.47309630384558,-34.6279798440006},{54.94619260769116,-34.6279798440006}},color={0,127,0}));
  connect(source_4.port,PdC_Cool_4.port_a)
    annotation (Line(points={{36,-32},{30.436840737148966,-32},{30.436840737148966,-36},{24.873681474297932,-36}},color={0,127,0}));
  connect(PdC_Cool_4.port_b,briBriHX_Reduced_4.Bri_L_in)
    annotation (Line(points={{15.126318525702068,-36},{11.061436894089624,-36},{11.061436894089624,-37.095769513688715},{6.99655526247718,-37.095769513688715}},color={0,127,0}));
  connect(briBriHX_Reduced_4.Bri_L_out,sink4.port)
    annotation (Line(points={{-7.3925866924287185,-37.095769513688715},{-11.69629334621436,-37.095769513688715},{-11.69629334621436,-36},{-16,-36}},color={0,127,0}));
  connect(briBriHX_Reduced_3.Bri_L_out,sink3.port)
    annotation (Line(points={{-7.097683403994039,-13.211624377002341},{-11.54884170199702,-13.211624377002341},{-11.54884170199702,-12},{-16,-12}},color={0,127,0}));
  connect(PdC_Cool_3.port_b,briBriHX_Reduced_3.Bri_L_in)
    annotation (Line(points={{14.59423516691438,-14},{10.655842765704369,-14},{10.655842765704369,-13.211624377002341},{6.717450364494359,-13.211624377002341}},color={0,127,0}));
  connect(source_3.port,PdC_Cool_3.port_a)
    annotation (Line(points={{36,-10},{30.70288241654281,-10},{30.70288241654281,-14},{25.40576483308562,-14}},color={0,127,0}));
  connect(split_1.port_b,In2.port_a)
    annotation (Line(points={{-56,25.92},{-50,25.92},{-50,10},{-44,10}},color={0,127,0}));
  connect(In2.port_b,PdC_LWT_2.port_a)
    annotation (Line(points={{-36,10},{-30,10},{-30,12},{-24,12}},color={0,127,0}));
  connect(PdC_LWT_2.port_b,briBriHX_Reduced_2.Bri_H_in)
    annotation (Line(points={{-16,12},{-11.79829924085632,12},{-11.79829924085632,12.132640772946292},{-7.59659848171264,12.132640772946292}},color={0,127,0}));
  connect(briBriHX_Reduced_2.Bri_H_out,Out2.port_a)
    annotation (Line(points={{7.389418523120478,12.063580786748904},{21.694709261560238,12.063580786748904},{21.694709261560238,12},{36,12}},color={0,127,0}));
  connect(Out2.port_b,mixer_1.port_b)
    annotation (Line(points={{44,12},{50.24910263783472,12},{50.24910263783472,25.139066743348106},{56.498205275669434,25.139066743348106}},color={0,127,0}));
  connect(source_2.port,PdC_Cool_2.port_a)
    annotation (Line(points={{36,24},{30.49014653355798,24},{30.49014653355798,20},{24.98029306711596,20}},color={0,127,0}));
  connect(PdC_Cool_2.port_b,briBriHX_Reduced_2.Bri_L_in)
    annotation (Line(points={{15.019706932884041,20},{11.170032734903565,20},{11.170032734903565,19.038639392685056},{7.32035853692309,19.038639392685056}},color={0,127,0}));
  connect(briBriHX_Reduced_2.Bri_L_out,sink2.port)
    annotation (Line(points={{-7.734718454107416,19.038639392685056},{-11.867359227053708,19.038639392685056},{-11.867359227053708,20},{-16,20}},color={0,127,0}));
  connect(sink1.port,briBriHX_Reduced_1.Bri_L_out)
    annotation (Line(points={{-16,46},{-11.755061628608274,46},{-11.755061628608274,40.950405565335075},{-7.510123257216547,40.950405565335075}},color={0,127,0}));
  connect(briBriHX_Reduced_1.Bri_L_in,PdC_Cool_1.port_b)
    annotation (Line(points={{7.107795225579947,40.950405565335075},{11.737940452267278,40.950405565335075},{11.737940452267278,40},{15.276392642957134,40}},color={0,127,0}));
  connect(PdC_Cool_1.port_a,source_1.port)
    annotation (Line(points={{24.723607357042866,40},{31.815957160522697,40},{31.815957160522697,52},{36,52}},color={0,127,0}));
  connect(setpointController.actuatorSignal,PdC_Cool_1.Ka_in)
    annotation (Line(points={{32.76,68},{21.700498648535433,68},{21.700498648535433,44.534663062761155}},color={0,0,127}));
  connect(setpointController.actuatorSignal,PdC_Cool_2.Ka_in)
    annotation (Line(points={{32.76,68},{21.792905504161745,68},{21.792905504161745,24.78108134443133}},color={0,0,127}));
  connect(setpointController.actuatorSignal,PdC_Cool_3.Ka_in)
    annotation (Line(points={{32.76,68},{21.946075339910823,68},{21.946075339910823,-8.810465760237804}},color={0,0,127}));
  connect(errorCalculation_2.sensor,setpointController.errorSignal)
    annotation (Line(points={{51.08,67.68},{45,67.68},{45,68},{41,68}},color={28,108,200}));
  connect(setpointController.actuatorSignal,PdC_Cool_4.Ka_in)
    annotation (Line(points={{32.76,68},{21.754525330747256,68},{21.754525330747256,-31.321265784673983}},color={0,0,127}));
  connect(errorCalculation_lwt1.sensor,setpointController_lwt1.errorSignal)
    annotation (Line(points={{-84.08,63.68000000000001},{-73,63.68000000000001},{-73,64},{-66,64}},color={28,108,200}));
  connect(setpointController_lwt1.actuatorSignal,PdC_LWT_1.Ka_in)
    annotation (Line(points={{-57.76,64},{-21.44,64},{-21.44,37.84}},color={0,0,127}));
  connect(errorCalculation_lwt.sensor,setpointController_lwt.errorSignal)
    annotation (Line(points={{-84.08,45.68},{-74,45.68},{-74,46},{-67,46}},color={28,108,200}));
  connect(setpointController_lwt.actuatorSignal,PdC_LWT_2.Ka_in)
    annotation (Line(points={{-58.76,46},{-21.44,46},{-21.44,15.84}},color={0,0,127}));
  connect(errorCalculation_lwt2.sensor,setpointController_lwt2.errorSignal)
    annotation (Line(points={{-76.83,-57.31999999999999},{-65.75,-57.31999999999999},{-65.75,-57},{-58.75,-57}},color={28,108,200}));
  connect(errorCalculation_lwt3.sensor,setpointController_lwt3.errorSignal)
    annotation (Line(points={{-76.83,-75.32},{-66.75,-75.32},{-66.75,-75},{-59.75,-75}},color={28,108,200}));
  connect(setpointController_lwt2.actuatorSignal,PdC_LWT_3.Ka_in)
    annotation (Line(points={{-50.51,-57},{-21.44,-57},{-21.44,-23.84}},color={0,0,127}));
  connect(setpointController_lwt3.actuatorSignal,PdC_LWT_4.Ka_in)
    annotation (Line(points={{-51.51,-75},{-21.58110984054832,-75},{-21.58110984054832,-48.21629290812885}},color={0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end control_water_flow;
