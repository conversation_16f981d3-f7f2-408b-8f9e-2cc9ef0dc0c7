within Workspace.System.Multimodule.HPC.R290;
model CL_61AQ_Modular
  extends.Workspace.Controller.StateMachine.StateMachine61AQ;
  extends.Workspace.System.Multimodule.HPC.System_61AQ_Modular(
    IsOFF1=not StateMachine.currentMode.Module_1,
    IsOFF2=not StateMachine.currentMode.Module_2,
    IsOFF3=not StateMachine.currentMode.Module_3,
    IsOFF4=not StateMachine.currentMode.Module_4,
    choiceBlock(
      Number_of_modules=1,
      Pump_selector=Workspace.Auxiliary.OptionBlock.PumpOption.Pump_Option.STANDARD,
      Module_2_selector=.Workspace.Auxiliary.OptionBlock.Record_ModuleBase.Module_selector.Module_120,
      Module_3_selector=.Workspace.Auxiliary.OptionBlock.Record_ModuleBase.Module_selector.Module_120,
      Module_4_selector=.Workspace.Auxiliary.OptionBlock.Record_ModuleBase.Module_selector.Module_060,
      Module_1_selector=Workspace.Auxiliary.OptionBlock.Record_ModuleBase.Module_selector.Module_050,
      Filter_selector=Workspace.Auxiliary.OptionBlock.FilterOption.Filter_selector.YES,SoundOption_selector = Workspace.Auxiliary.OptionBlock.SoundOption.Selector.LOW_NOISE),
    ECAT(
      TargetCoolingCapacity_W(
        setPoint=600000),
      ExternalSystemPressureDrop_Pa(
        setPoint=0),AmbientAirDBTemp_K(setPoint = 308.15),EvapBrineEWT_K(setPoint = 296.15),EvapBrineLWT_K(setPoint = 291.15)),
    Module_1(
      Module(
        mdot_start=min(
          1/((1+Workspace.Auxiliary.Tools.booleanToReal(
            IsOFF2)+Workspace.Auxiliary.Tools.booleanToReal(
            IsOFF3)+Workspace.Auxiliary.Tools.booleanToReal(
            IsOFF4)))*TargetCapacity,
          Module_1.Module.capacity_design[1])/((((((4180*(Module_1.Module.EWT-Module_1.Module.LWT))))))))),
    Module_2(
      Module(
        mdot_start=min(
          1/((1+Workspace.Auxiliary.Tools.booleanToReal(
            IsOFF2)+Workspace.Auxiliary.Tools.booleanToReal(
            IsOFF3)+Workspace.Auxiliary.Tools.booleanToReal(
            IsOFF4)))*TargetCapacity,
          Module_2.Module.capacity_design[1])/((((((4180*(Module_2.Module.EWT-Module_2.Module.LWT))))))))),
    Module_3(
      Module(
        mdot_start=min(
          1/((1+Workspace.Auxiliary.Tools.booleanToReal(
            IsOFF2)+Workspace.Auxiliary.Tools.booleanToReal(
            IsOFF3)+Workspace.Auxiliary.Tools.booleanToReal(
            IsOFF4)))*TargetCapacity,
          Module_3.Module.capacity_design[1])/((((((4180*(Module_3.Module.EWT-Module_3.Module.LWT))))))))),
    Module_4(
      Module(
        mdot_start=min(
          1/((1+Workspace.Auxiliary.Tools.booleanToReal(
            IsOFF2)+Workspace.Auxiliary.Tools.booleanToReal(
            IsOFF3)+Workspace.Auxiliary.Tools.booleanToReal(
            IsOFF4)))*TargetCapacity,
          Module_4.Module.capacity_design[1])/((((((4180*(Module_4.Module.EWT-Module_4.Module.LWT))))))))),
    node_out(
      p_fixed=false),use_bf = true);
  import Freq_Cooling_Transition_1_2=Workspace.Controller.Components.Functions.Freq_Cooling_Transition_1_2;
  import Freq_Cooling_Transition_2_3=Workspace.Controller.Components.Functions.Freq_Cooling_Transition_2_3;
  import Freq_Cooling_Transition_3_4=Workspace.Controller.Components.Functions.Freq_Cooling_Transition_3_4;
  Real FreqTransition_1_2;
  Real FreqTransition_2_3;
  Real FreqTransition_3_4;
  Integer Stateid;
  parameter Boolean useStatePrediction = true;
  parameter Real StatePrediction_offset=16;
equation
  // Transition variables equations
  Stateid=Workspace.Controller.Components.Functions.State_prediction(
    Module_1.CompressorFreq,
    choiceBlock.Number_of_modules,
    FreqTransition_1_2,
    FreqTransition_2_3,
    FreqTransition_3_4,
    Module_1.Module.capacity_design[1]+Module_1.Module.capacity_design[2],
    Module_2.Module.capacity_design[1]+Module_2.Module.capacity_design[2],
    Module_3.Module.capacity_design[1]+Module_3.Module.capacity_design[2],
    Module_4.Module.capacity_design[1]+Module_4.Module.capacity_design[2],
    ECAT.TargetCoolingCapacity_W.setPoint,
    ECAT.PubCoolingCapacity_W.value,
    StateMachine.currentMode.Module_2 or not useStatePrediction,
    StatePrediction_offset);
  (FreqTransition_1_2,Module_2_ON)=.Workspace.Controller.Components.Functions.Freq_Cooling_Transition_1_2(
    node_out.T,
    ECAT.AmbientAirDBTemp_K.setPoint,
    Module_1.CompressorFreq,
    ECAT.TargetCoolingCapacity_W.setPoint,
    total_capacity,
    choiceBlock.Number_of_modules);
  (FreqTransition_2_3,Module_3_ON)=.Workspace.Controller.Components.Functions.Freq_Cooling_Transition_2_3(
    node_out.T,
    ECAT.AmbientAirDBTemp_K.setPoint,
    Module_2.CompressorFreq,
    ECAT.TargetCoolingCapacity_W.setPoint,
    total_capacity,
    choiceBlock.Number_of_modules);
  (FreqTransition_3_4,Module_4_ON)=.Workspace.Controller.Components.Functions.Freq_Cooling_Transition_3_4(
    node_out.T,
    ECAT.AmbientAirDBTemp_K.setPoint,
    Module_3.CompressorFreq,
    ECAT.TargetCoolingCapacity_W.setPoint,
    total_capacity,
    choiceBlock.Number_of_modules);
end CL_61AQ_Modular;
