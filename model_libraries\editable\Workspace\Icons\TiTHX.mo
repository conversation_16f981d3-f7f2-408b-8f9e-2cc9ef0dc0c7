within Workspace.Icons;
model TiTHX
  "Icon for refrigerant to refrigerant heat exchanger"
  annotation (
    Diagram(
      graphics),
    Icon(
      graphics={
        Rectangle(
          extent={{-100,40},{100,-40}},
          lineColor={255,0,0},
          fillColor={255,0,0},
          fillPattern=FillPattern.HorizontalCylinder),
        Line(
          points={{-100,-60},{-100,60},{-80,60},{-80,100},{-40,100},{-40,60},{100,60},{100,-60},{80,-60},{80,-100},{40,-100},{40,-60},{-100,-60}},
          color={255,0,0},
          smooth=Smooth.None,
          thickness=0.5),
        Line(
          points={{-60,74},{-60,60}},
          color={255,0,0},
          thickness=0.5,
          smooth=Smooth.None),
        Polygon(
          points={{-60,80},{-66,72},{-54,72},{-60,80}},
          lineColor={255,0,0},
          lineThickness=0.5,
          fillPattern=FillPattern.HorizontalCylinder,
          smooth=Smooth.None,
          fillColor={255,0,0}),
        Polygon(
          points={{60,-60},{54,-68},{66,-68},{60,-60}},
          lineColor={255,0,0},
          lineThickness=0.5,
          fillPattern=FillPattern.HorizontalCylinder,
          smooth=Smooth.None,
          fillColor={255,0,0}),
        Line(
          points={{60,-68},{60,-82}},
          color={255,0,0},
          thickness=0.5,
          smooth=Smooth.None),
        Polygon(
          points={{66,10},{66,-8},{84,2},{66,10}},
          lineColor={255,255,0},
          lineThickness=0.5,
          fillPattern=FillPattern.VerticalCylinder,
          smooth=Smooth.None,
          fillColor={255,255,0}),
        Line(
          points={{-78,0},{66,0}},
          color={255,255,0},
          thickness=0.5,
          smooth=Smooth.None)}),
    Documentation(
      revisions="<html>
<p>Created by Donna Huang, UTRC (China), Dec. 30, 2010</p>
</html>"));
end TiTHX;
