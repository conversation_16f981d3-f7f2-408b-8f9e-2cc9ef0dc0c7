within Workspace.System.HPH.BaseCycles.optimization;
model CL_system_ECAT
  import CoolantCommonMedium=BOLT.InternalLibrary.Media.Coolant.CoolantCommon;
  extends.Workspace.Controller.CL_control_system_heating_zenith(
    controllerSettings_crkA(
      Capacity_setpoint=TargetCapacityA,
      coefficients=oL_modular.BlocA.choiceBlock_HPH.Unit.fanCurveCoefficientsHeating),
    controllerSettings_crkB(
      Capacity_setpoint=TargetCapacityB,
      coefficients=oL_modular.BlocB.choiceBlock_HPH.Unit.fanCurveCoefficientsHeating),
    controller_crkA(
      isOff=isOFFA,
      isOffSDTmin_fan=false,
      isOffSDTmax_fan=false,
      isOffDGTmax_fan=false,
      isOffSSTmin_EXV=false,
      isOffSSTmax_EXV=false,
      isOffDSHmin_EXV=false,
      isOffDGTmax_EXV=false,
      isOffSSTmin_comp=false,
      isOffSDTmax_comp=false,
      isOffDGTmax_comp=false,
      manualOff_fan=false,
      frq_fan_sp_manual=720,
      manualOff_compressor_crkA=false,
      frq_comp_sp_manual_crkA=140,
      manualOff_compressor_crkB=false,
      frq_comp_sp_manual_crkB=140,
      Fan_MaxFrequency=720,
      completeCompressorControl_base(
        compressorControl(
          gain_Cp_speed=0.01),
        Nominal_capacity=oL_modular.BlocA.choiceBlock_HPH.Unit.capacity_design,
        Capacity_setpoint=TargetCapacityA)),
    controller_crkB(
      isOff=is_monobloc or isOFFB,
      isOffSDTmin_fan=false,
      isOffSDTmax_fan=false,
      isOffDGTmax_fan=false,
      isOffSSTmin_EXV=false,
      isOffSSTmax_EXV=false,
      isOffDSHmin_EXV=false,
      isOffDGTmax_EXV=false,
      isOffSSTmin_comp=false,
      isOffSDTmax_comp=false,
      isOffDGTmax_comp=false,
      manualOff_fan=false,
      frq_fan_sp_manual=720,
      manualOff_compressor_crkA=false,
      manualOff_compressor_crkB=false,
      frq_comp_sp_manual_crkA=140,
      frq_comp_sp_manual_crkB=140,
      Fan_MaxFrequency=controllerSettings_crkB.maxfanfreq,
      completeCompressorControl_base(
        Nominal_capacity=oL_modular.BlocB.choiceBlock_HPH.Unit.capacity_design,
        Capacity_setpoint=TargetCapacityB)));
  .Workspace.System.HPH.BaseCycles.Equipement oL_modular(
    Unit_size=Unit_size,
    CoolantMedium=CoolantMedium,
    isOFFB=isOFFB,
    isOFFA=isOFFA,
    BlocB,
    OAT=OAT,
    LWT=LWT,
    EWT=EWT,
    BlocA(
      source_air(
        Twb_fixed=false,
        RH_fixed=ECAT.AmbientAirRH_nd.fixed,
        RH_set=ECAT.AmbientAirRH_nd.setPoint,
        Tdb_fixed=ECAT.AmbientAirDBTemp_K.fixed),
      condBPHE(
        R_foul=ECAT.CondFoulingFactor_m2KW.setPoint)),
    BrineConcentration=BrineConcentration,
    Use_pump=Use_pump,
    OAT_WB=OAT_WB,
    Use_EN=Use_EN)
    annotation (Placement(transformation(extent={{-10.276922540313784,-8.730145088921342},{9.723077459686216,11.269854911078658}},origin={0.0,0.0},rotation=0.0)));
  // Declaration of Coolant Medium Package
  //package CoolantCommonMedium =
  // .BOLT.InternalLibrary.Media.Coolant.CoolantCommon "Coolant Medium Package" annotation ();
  // Declaration of Coolant Medium at Evaporator Circuit
  parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector CoolantMedium=ECAT.CondBrineType_nd
    "Coolant Medium Selection"
    annotation (Dialog(group="Medium"));
  final parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Empty coolantInf=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.getSelector(
    CoolantMedium)
    "Coolant Medium"
    annotation ();
  .BOLT.InternalLibrary.Media.Coolant.CoolantCommon.Temperature FreezTemp=.BOLT.InternalLibrary.Media.Coolant.CoolantCommon.freezeTemp(
    coolantInf,
    BrineConcentration,
    1);
  parameter Real BrineConcentration=ECAT.CondBrineConcentration_nd.setPoint
    annotation (Dialog(group="Medium"));
  .BOLT.BoundaryNode.Coolant.Source sourceBrine(
    Vd_fixed=ECAT.CondBrineFlowRate_m3s.fixed,
    T_set=EWT,
    p_fixed=true,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    T_fixed=ECAT.CondBrineEWT_K.fixed,
    Vd_set=ECAT.CondBrineFlowRate_m3s.setPoint,
    p_set=2e5)
    annotation (Placement(transformation(extent={{-4.0,-4.0},{4.0,4.0}},origin={0.0,-42.0},rotation=90.0)));
  .BOLT.BoundaryNode.Coolant.Sink sinkBrine(
    p_fixed=true,
    T_fixed=ECAT.CondBrineLWT_K.fixed,
    T_set=LWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    p_set=sourceBrine.p_set)
    annotation (Placement(transformation(extent={{-4.0,-4.0},{4.0,4.0}},origin={0.0,42.0},rotation=-90.0)));
  parameter Boolean Use_pump=false
    annotation (Dialog(group="Unit_config"));
  parameter Integer Unit_size=100
    "Stringify values -- IPM Cloud limitation"
    annotation (Dialog(group="Unit_config"));
  parameter.Modelica.SIunits.Temperature LWT=ECAT.CondBrineLWT_K.setPoint
    annotation (Dialog(group="Conditions"));
  parameter.Modelica.SIunits.Temperature EWT=ECAT.CondBrineEWT_K.setPoint
    annotation (Dialog(group="Conditions"));
  parameter.Modelica.SIunits.Temperature OAT=ECAT.HeatingAmbientAirDBTemp_K.setPoint
    annotation (Dialog(group="Conditions"));
  parameter.Modelica.SIunits.Temperature OAT_WB=OAT-1
    annotation (Dialog(group="Conditions"));
  parameter.Modelica.SIunits.Power TargetCapacityA=ECAT.TargetHeatingCapacity_W.setPoint/2
    annotation (Dialog(group="Cp_control"));
  parameter.Modelica.SIunits.Power TargetCapacityB=ECAT.TargetHeatingCapacity_W.setPoint/2
    annotation (Dialog(group="Cp_control"));
  parameter Boolean is_monobloc=
    if Unit_size == 40 then
      true
    elseif Unit_size == 50 then
      true
    elseif Unit_size == 60 then
      true
    elseif Unit_size == 70 then
      true
    else
      false
    annotation (Dialog(group="Unit_config"));
  parameter Boolean isOFFB=false
    annotation (Dialog(group="Cp_control"));
  parameter Boolean isOFFA=false
    annotation (Dialog(group="Cp_control"));
  parameter Boolean Use_EN=false;
  .Workspace.Auxiliary.ECAT_Zenith.ECATBase ECAT(
    nbrCircuit=
      if is_monobloc then
        1
      else
        2,
    EvapBrineType_nd=CoolantMedium,
    CondBrineType_nd=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector.FW,
    RefrigerantCharge_kg(),
    CondCoilAirPressDrop_Pa(
      value={oL_modular.BlocA.evapAir1_MVB.summary.dPa,oL_modular.BlocB.evapAir1_MVB.summary.dPa}),
    RefrigerantSST_K(
      value={oL_modular.BlocA.node_suction.Tsat,oL_modular.BlocB.node_suction.Tsat}),
    RefrigerantSDT_K(
      value={oL_modular.BlocA.node_discharge.Tsat,oL_modular.BlocB.node_discharge.Tsat}),
    RefrigerantSET_K(
      value={oL_modular.BlocA.node_evapout.Tsat,oL_modular.BlocB.node_evapout.Tsat}),
    RefrigerantSCT_K(
      value={oL_modular.BlocA.node_condin.Tsat,oL_modular.BlocB.node_condin.Tsat}),
    RefrigerantDGT_K(
      value={oL_modular.BlocA.node_discharge.T,oL_modular.BlocB.node_discharge.T}),
    SuctionSuperheat_K(
      value={oL_modular.BlocA.node_suction.dTsh,oL_modular.BlocB.node_suction.dTsh}),
    CondSubcooling_K(
      value={oL_modular.BlocA.node_EXV_in.dTsh,oL_modular.BlocB.node_EXV_in.dTsh}),
    DischargeSuperheat_K(
      value={oL_modular.BlocA.node_discharge.dTsh,oL_modular.BlocB.node_discharge.dTsh}),
    CondFanAirflowRate_m3s(
      value={2*oL_modular.BlocA.source_air.Vd_flow,2*oL_modular.BlocB.source_air.Vd_flow}),
    CompressorPower_W(
      value={oL_modular.BlocA.compressor.summary.P_compression,oL_modular.BlocB.compressor.summary.P_compression}),
    FanPower_W(
      value={oL_modular.BlocA.motor.summary.power_VFD,oL_modular.BlocB.motor.summary.power_VFD}),
    CompressorFrequency_Hz(
      value={oL_modular.BlocA.compressor.summary.Ncomp,oL_modular.BlocB.compressor.summary.Ncomp}),
    CompressorSpeed_rpm(
      value={oL_modular.BlocA.compressor.summary.Ncomp*60,oL_modular.BlocB.compressor.summary.Ncomp*60}),
    CondBrineLWT_K(
      value=sinkBrine.T,
      setPoint=308.15,
      fixed=true),
    CondBrineEWT_K(
      value=sourceBrine.T,
      setPoint=303.15,
      fixed=true),
    EvapBrineFlowRate_m3s(
      value=-1),
    EvapBrineLWT_K(
      value=-1,
      setPoint=7+273.15,
      fixed=true),
    EvapBrineEWT_K(
      value=-1,
      setPoint=12+273.15,
      fixed=true),
    TotalRefrigerantCharge_kg(
      value=oL_modular.BlocA.choiceBlock_HPH.Unit.Design_mRef_set_A*2),
    EvapPumpSpeed_rpm(
      value=-1),
    PubUnitPower_W(
      value=oL_modular.controlledPower),
    PubCoolingCapacity_W(
      value=-1),
    PubHeatingCapacity_W(
      value=oL_modular.controlledHeating),
    CondEN14511PumpPowerCorrectionWithPump_W(
      value=-1),
    CondEN14511PumpPowerCorrectionWithoutPump_W(
      value=-1),
    PubHeatingCapacityInstantaneous_W(
      value=-1),
    TotalCompressorPower_W(
      value=sum(
        ECAT.CompressorPower_W.value)),
    TotalFanPower_W(
      value=sum(
        ECAT.FanPower_W.value)),
    CondPumpPower_W(
      value=oL_modular.pumpPolyA.summary.P_motor),
    EvapPumpPower_W(
      value=-1),
    CondBrineIntPressDrop_Pa(
      value=oL_modular.BlocA.condBPHE.summary.dp_coolant),
    EvapPumpTotalHead_m(
      value=-1),
    CondBrineDensity_kgm3(
      value=1/sinkBrine.v),
    EvapBrineIntPressDrop_Pa(
      value=-1),
    CondPumpTotalHead_m(
      value=-1),
    EvapBrineDensity_kgm3(
      value=-1),
    CondBrineVelocity_mps(
      value=-1),
    EvapBrineVelocity_mps(
      value=-1),
    AmbientAirDBTemp_K(
      setPoint=280.15,
      value=oL_modular.BlocA.source_air.summary.Tdb),
    RefrigerantCharge_kg(
      value=oL_modular.BlocA.systemVariables.summary.mRef+oL_modular.BlocB.systemVariables.summary.mRef,
      setPoint={1.59,1.59},
      fixed={false,false}),
    CondBrineConcentration_nd(
      setPoint=0.40),
    HeatingAmbientAirDBTemp_K(
      setPoint=280.15,
      value=oL_modular.BlocA.source_air.summary.Tdb),
    TargetCoolingCapacity_W(
      setPoint=1),
    TargetHeatingCapacity_W(
      setPoint=50000),
    RefrigerantType_nd=.BOLT.InternalLibrary.ECATBlock.Types.RefrigerantTypes.R32,
    StageNum_nd(
      value=-1,
      setPoint=6,
      fixed=false),
    CondFoulingFactor_m2KW(
      setPoint=0),
    ExternalSystemKa_kPas2L2(
      value=-1,
      setPoint=0.3,
      fixed=false),
    ExternalSystemPressureDrop_Pa(
      value=-1,
      setPoint=1e5,
      fixed=false),
    CondPumpSpeed_rpm(
      value=oL_modular.pumpPolyB.summary.speed,
      setPoint=2100),
    AmbientAirRH_nd(
      setPoint=0.87),
    Altitude_m(
      setPoint=0),
    CondBrineFlowRate_m3s(
      setPoint=0.01,
      fixed=false,
      value=sourceBrine.Vd),
    HeatingAmbientAirWBTemp_K(
      value=279.15),
    CondHighStaticFanExtPressDrop_Pa(
      value=-1),
    EvapHighStaticFanExtPressDrop_Pa(
      value=-1,
      setPoint=160,
      fixed=false),
    HighStaticFanExternalKa_kPas2L2(
      fixed={true,true},
      setPoint={31.45,31.45},
      value={-1,-1}),
    CondPumpSpeed_rpm_2(
      value=oL_modular.pumpPolyB.summary.speed,
      setPoint=2100),
    CondPumpPower_W_2(
      value=oL_modular.pumpPolyB.summary.P_motor),
    TotalOilCharge_kg(
      value=oL_modular.BlocA.choiceBlock_HPH.Unit.Oil_charge+oL_modular.BlocB.choiceBlock_HPH.Unit.Oil_charge))
    annotation (Placement(transformation(extent={{-82.0,70.0},{-62.0,90.0}},origin={0.0,0.0},rotation=0.0)));
  parameter.Modelica.SIunits.Pressure Pdispo=50000;
equation
  connect(sourceBrine.port,oL_modular.coolant_in)
    annotation (Line(points={{8.881784197001252e-16,-38},{8.881784197001252e-16,-25.041135126968296},{-0.07692254031378376,-25.041135126968296},{-0.07692254031378376,-13.730145088921342}},color={0,127,0}));
  connect(oL_modular.coolant_out,sinkBrine.port)
    annotation (Line(points={{0.1647960636327931,10.953292118971802},{0.1647960636327931,25.1},{8.881784197001252e-16,25.1},{8.881784197001252e-16,38}},color={0,127,0}));
  connect(controller_crkA.fan,oL_modular.Fan_controller_A)
    annotation (Line(points={{-48.4,6.000596956339052},{-29.7,6.000596956339052},{-29.7,-1.2301450889213417},{-11.276922540313784,-1.2301450889213417}},color={0,0,127}));
  connect(controller_crkA.exv,oL_modular.EXV_controller_A)
    annotation (Line(points={{-48.4,3.699850760915237},{-29.7,3.699850760915237},{-29.7,3.7698549110786583},{-11.276922540313784,3.7698549110786583}},color={0,0,127}));
  connect(controller_crkA.compressor,oL_modular.Compressor_controller_A)
    annotation (Line(points={{-48.4,-3.9001492390847634},{-29.7,-3.9001492390847634},{-29.7,-6.230145088921342},{-11.276922540313784,-6.230145088921342}},color={0,0,127}));
  connect(controller_crkB.fan,oL_modular.Fan_controller_B)
    annotation (Line(points={{50.4,8.000596956339052},{30.7,8.000596956339052},{30.7,-1.2301450889213417},{10.723077459686216,-1.2301450889213417}},color={0,0,127}));
  connect(controller_crkB.exv,oL_modular.EXV_controller_B)
    annotation (Line(points={{50.4,5.699850760915237},{30.7,5.699850760915237},{30.7,3.7698549110786583},{10.723077459686216,3.7698549110786583}},color={0,0,127}));
  connect(controller_crkB.compressor,oL_modular.Compressor_controller_B)
    annotation (Line(points={{50.4,-1.9001492390847634},{30.7,-1.9001492390847634},{30.7,-6.230145088921342},{10.723077459686216,-6.230145088921342}},color={0,0,127}));
  connect(controllerSettings_crkA.measurementBus,oL_modular.measurementBusA)
    annotation (Line(points={{-70.44724010515856,39.45699314828894},{-40.862081322736174,39.45699314828894},{-40.862081322736174,7.269854911078658},{-11.276922540313784,7.269854911078658}},color={255,204,51}));
  connect(controllerSettings_crkB.measurementBus,oL_modular.measurementBusB)
    annotation (Line(points={{68.85895184021807,42.3215877919139},{39.791014649952146,42.3215877919139},{39.791014649952146,7.269854911078658},{10.723077459686216,7.269854911078658}},color={255,204,51}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end CL_system_ECAT;
