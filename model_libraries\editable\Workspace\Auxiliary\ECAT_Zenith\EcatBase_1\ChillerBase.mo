within Workspace.Auxiliary.ECAT_Zenith.EcatBase_1;
model ChillerBase
  import SI=Modelica.SIunits;
  //  parameter String Version="1.0" "ECAT interface version";
  parameter Real Version=1.1;
  //"1.0" "ECAT interface version";                //RRH 2019/05/28 value was 1
  parameter Integer nbrCircuit=1;
  //RRH 2019/05/30 made this a variable
  parameter BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector EvapBrineType_nd=BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector.FW;
  parameter BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector CondBrineType_nd=BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector.FW;
  parameter BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector InternalLoopBrineType_nd=BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector.FW;
  parameter Types.RefrigerantTypes RefrigerantType_nd=Types.RefrigerantTypes.R134a;
  //  parameter Real EvapBrineConcentration_nd = 0;
  //  parameter Real CondBrineConcentration_nd = 0;
  //  parameter Real EvapFoulingFactor_m2KW = 0;
  //  parameter Real CondFoulingFactor_m2KW = 0;
  //  parameter SI.Height Altitude_m  = 0;
  replaceable ECATVariable RefrigerantCharge_kg[nbrCircuit](
    redeclare final type VariableType=SI.Mass)
    annotation (Dialog,Placement(transformation(extent={{-94,-102},{-76,-94}})));
  replaceable ECATVariable CondCoilAirPressDrop_Pa[nbrCircuit](
    redeclare final type VariableType=SI.PressureDifference)
    annotation (Dialog,Placement(transformation(extent={{2,-46},{20,-38}})));
  replaceable ECATVariable RefrigerantSST_K[nbrCircuit](
    redeclare final type VariableType=SI.Temperature)
    annotation (Dialog,Placement(transformation(extent={{-46,-60},{-28,-52}})));
  replaceable ECATVariable RefrigerantSDT_K[nbrCircuit](
    redeclare final type VariableType=SI.Temperature)
    annotation (Dialog,Placement(transformation(extent={{-22,-60},{-4,-52}})));
  replaceable ECATVariable RefrigerantSET_K[nbrCircuit](
    redeclare final type VariableType=SI.Temperature)
    annotation (Dialog,Placement(transformation(extent={{-70,-60},{-52,-52}})));
  replaceable ECATVariable RefrigerantSCT_K[nbrCircuit](
    redeclare final type VariableType=SI.Temperature)
    annotation (Dialog,Placement(transformation(extent={{-94,-60},{-76,-52}})));
  replaceable ECATVariable RefrigerantDGT_K[nbrCircuit](
    redeclare final type VariableType=SI.Temperature)
    annotation (Dialog,Placement(transformation(extent={{-22,-46},{-4,-38}})));
  //RRH 2019/05/28 added; check Placement values
  replaceable ECATVariable SuctionSuperheat_K[nbrCircuit](
    redeclare final type VariableType=SI.TemperatureDifference)
    annotation (Dialog,Placement(transformation(extent={{-94,-74},{-76,-66}})));
  replaceable ECATVariable CondSubcooling_K[nbrCircuit](
    redeclare final type VariableType=SI.TemperatureDifference)
    annotation (Dialog,Placement(transformation(extent={{-70,-74},{-52,-66}})));
  replaceable ECATVariable DischargeSuperheat_K[nbrCircuit](
    redeclare final type VariableType=SI.TemperatureDifference)
    annotation (Dialog,Placement(transformation(extent={{-22,-46},{-4,-38}})));
  replaceable ECATVariable CompressorSpeed_rpm[nbrCircuit]
    annotation (Dialog,Placement(transformation(extent={{-94,-88},{-76,-80}})));
  replaceable ECATVariable CompressorFrequency_Hz[nbrCircuit](
    redeclare final type VariableType=SI.Frequency)
    annotation (Dialog,Placement(transformation(extent={{-70,-88},{-52,-80}})));
  replaceable ECATVariable FanFrequency_Hz[nbrCircuit](
    redeclare final type VariableType=SI.Frequency)
    annotation (Dialog,Placement(transformation(extent={{-70,-88},{-52,-80}})));
  replaceable ECATVariable FanSpeed_rpm[nbrCircuit]
    annotation (Dialog,Placement(transformation(extent={{-94,-88},{-76,-80}})));
  replaceable ECATVariable ElecFanBoxFrequency_Hz[nbrCircuit](
    redeclare final type VariableType=SI.Frequency)
    annotation (Dialog,Placement(transformation(extent={{-70,-88},{-52,-80}})));
  replaceable ECATVariable CondPumpSpeed_Hz[nbrCircuit](
    redeclare final type VariableType=SI.Frequency)
    annotation (Dialog,Placement(transformation(extent={{-70,-88},{-52,-80}})));
  replaceable ECATVariable CondPumpSpeed_rpm[nbrCircuit]
    annotation (Dialog,Placement(transformation(extent={{-94,-88},{-76,-80}})));
  replaceable ECATVariable EvapPumpSpeed_Hz[nbrCircuit](
    redeclare final type VariableType=SI.Frequency)
    annotation (Dialog,Placement(transformation(extent={{-70,-88},{-52,-80}})));
  replaceable ECATVariable EvapPumpSpeed_rpm[nbrCircuit]
    annotation (Dialog,Placement(transformation(extent={{-94,-88},{-76,-80}})));
  replaceable ECATVariable CondFanAirflowRate_m3s[nbrCircuit](
    redeclare final type VariableType=SI.VolumeFlowRate)
    annotation (Dialog,Placement(transformation(extent={{-46,-46},{-28,-38}})));
  replaceable ECATVariable CompressorPower_W[nbrCircuit](
    redeclare final type VariableType=SI.Power)
    annotation (Dialog,Placement(transformation(extent={{-94,-46},{-76,-38}})));
  //RRH 2019/05/28 was GrossCompressorPower_W[]
  replaceable ECATVariable FanPower_W[nbrCircuit](
    redeclare final type VariableType=SI.Power)
    annotation (Dialog,Placement(transformation(extent={{-70,-46},{-52,-38}})));
  //RRH 2019/05/28 was GrossFanPower_W[]
  replaceable ECATVariable Altitude_m(
    redeclare final type VariableType=SI.Height)
    annotation (Dialog,Placement(transformation(extent={{28,72},{46,80}})));
  replaceable ECATVariable AmbientAirDBTemp_K(
    redeclare type VariableType=SI.Temperature)
    "Ambient air DB temperature"
    annotation (Dialog,Placement(transformation(extent={{-22,86},{-2,94}})));
  replaceable ECATVariable AmbientAirRH_nd
    "Ambient air relative humidity"
    annotation (Dialog,Placement(transformation(extent={{4,86},{22,94}})));
  replaceable ECATVariable CondBrineConcentration_nd
    annotation (Dialog,Placement(transformation(extent={{-70,72},{-52,80}})));
  replaceable ECATVariable CondBrineLWT_K(
    redeclare final type VariableType=SI.Temperature)
    annotation (Dialog,Placement(transformation(extent={{28,86},{46,94}})));
  replaceable ECATVariable CondBrineEWT_K(
    redeclare type VariableType=SI.Temperature)
    annotation (Dialog,Placement(transformation(extent={{52,86},{70,94}})));
  replaceable ECATVariable CondBrineFlowRate_m3s(
    redeclare type VariableType=SI.VolumeFlowRate)
    annotation (Dialog,Placement(transformation(extent={{76,86},{94,94}})));
  replaceable ECATVariable CondFoulingFactor_m2KW(
    redeclare final type VariableType=SI.ThermalInsulance)
    annotation (Dialog,Placement(transformation(extent={{-76,72},{-94,80}})));
  replaceable ECATVariable EvapBrineConcentration_nd
    annotation (Dialog,Placement(transformation(extent={{-22,72},{-2,80}})));
  replaceable ECATVariable EvapFoulingFactor_m2KW(
    redeclare final type VariableType=SI.ThermalInsulance)
    annotation (Dialog,Placement(transformation(extent={{-46,72},{-28,80}})));
  replaceable ECATVariable EvapBrineLWT_K(
    redeclare final type VariableType=SI.Temperature)
    annotation (Dialog,Placement(transformation(extent={{-94,86},{-76,94}})));
  replaceable ECATVariable EvapBrineEWT_K(
    redeclare type VariableType=SI.Temperature)
    annotation (Dialog,Placement(transformation(extent={{-70,86},{-52,94}})));
  replaceable ECATVariable EvapBrineFlowRate_m3s(
    redeclare type VariableType=SI.VolumeFlowRate,
    fixed=false)
    annotation (Dialog,Placement(transformation(extent={{-46,86},{-28,94}})));
  replaceable ECATVariable TargetCoolingCapacity_W(
    redeclare type VariableType=SI.Power)
    "Target cooling capacity"
    annotation (Dialog,Placement(transformation(extent={{4,72},{22,80}})));
  replaceable ECATVariable TargetHeatingCapacity_W(
    redeclare type VariableType=SI.Power)
    "Target heating capacity"
    annotation (Dialog,Placement(transformation(extent={{4,72},{22,80}})));
  /*
ECAT.CompressorPower_W        W        array?                        //RRH 2019/05/28 was GrossCompressorPower_W
ECAT.FanPower_W        W        array?                                        //RRH 2019/05/28 was GrossFanPower_W
ECAT.CondCoilAirPressDrop_Pa        Pa        array?
ECAT.RefrigerantSST_K        K        array
ECAT.RefrigerantSDT_K        K        array
ECAT.RefrigerantSET_K        K        array
ECAT.RefrigerantSCT_K        K        array
ECAT.SuctionSuperheat_K        K        array
ECAT.CondSubcooling_K        K        array
ECAT.DischargeSuperheat_K        K        array
ECAT.CompressorSpeed_rpm        rpm        array
ECAT.CompressorFrequency_Hz        Hz        array
ECAT.CondFanAirflowRate_m3s        m3/s        array
ECAT.RefrigerantDGT_K        K        array
ECAT.RefrigerantCharge_kg        kg        array

*/replaceable ECATVariable TotalRefrigerantCharge_kg(
    redeclare final type VariableType=SI.Mass)
    annotation (Dialog,Placement(transformation(extent={{52,52},{70,60}})));
  replaceable ECATVariable TotalOilCharge_kg(
    redeclare final type VariableType=SI.Mass)
    annotation (Dialog,Placement(transformation(extent={{76,52},{92,60}})));
  replaceable ECATVariable PubUnitPower_W(
    redeclare final type VariableType=SI.Power)
    annotation (Dialog,Placement(transformation(extent={{-94,8},{-76,16}})));
  replaceable ECATVariable PubCoolingCapacity_W(
    redeclare final type VariableType=SI.Power)
    annotation (Dialog,Placement(transformation(extent={{-70,8},{-52,16}})));
  replaceable ECATVariable PubHeatingCapacity_W(
    redeclare final type VariableType=SI.Power)
    annotation (Dialog,Placement(transformation(extent={{-46,8},{-28,16}})));
  replaceable ECATVariable TotalCompressorPower_W(
    redeclare final type VariableType=SI.Power)
    annotation (Dialog,Placement(transformation(extent={{-22,8},{-4,16}})));
  //RRH 2019/05/28 was PubCompressorPower_W
  replaceable ECATVariable TotalFanPower_W(
    redeclare final type VariableType=SI.Power)
    annotation (Dialog,Placement(transformation(extent={{2,8},{22,16}})));
  //RRH 2019/05/28 was PubFanPower_W
  replaceable ECATVariable EvapPumpPower_W(
    redeclare final type VariableType=SI.Power)
    annotation (Dialog,Placement(transformation(extent={{-70,52},{-52,60}})));
  //RRH 2019/05/28 was GrossEvapPumpPower_W
  replaceable ECATVariable CondPumpPower_W(
    redeclare final type VariableType=SI.Power)
    annotation (Dialog,Placement(transformation(extent={{-46,52},{-28,60}})));
  //RRH 2019/05/28 was GrossCondPumpPower_W
  replaceable ECATVariable EvapBrineIntPressDrop_Pa(
    redeclare final type VariableType=SI.PressureDifference)
    annotation (Dialog,Placement(transformation(extent={{-94,-12},{-76,-4}})));
  replaceable ECATVariable EvapPumpTotalHead_m(
    redeclare final type VariableType=SI.Height)
    annotation (Dialog,Placement(transformation(extent={{-70,-12},{-52,-4}})));
  replaceable ECATVariable EvapBrineDensity_kgm3(
    redeclare final type VariableType=SI.Density)
    annotation (Dialog,Placement(transformation(extent={{-46,-12},{-28,-4}})));
  replaceable ECATVariable CondBrineIntPressDrop_Pa(
    redeclare final type VariableType=SI.PressureDifference)
    annotation (Dialog,Placement(transformation(extent={{-22,-12},{-4,-4}})));
  replaceable ECATVariable CondPumpTotalHead_m(
    redeclare final type VariableType=SI.Height)
    annotation (Dialog,Placement(transformation(extent={{2,-12},{22,-4}})));
  replaceable ECATVariable CondBrineDensity_kgm3(
    redeclare final type VariableType=SI.Density)
    annotation (Dialog,Placement(transformation(extent={{28,-12},{46,-4}})));
  replaceable ECATVariable EvapBrineVelocity_mps(
    redeclare final type VariableType=SI.Velocity)
    annotation (Dialog,Placement(transformation(extent={{-94,-26},{-76,-18}})));
  replaceable ECATVariable CondBrineVelocity_mps(
    redeclare final type VariableType=SI.Velocity)
    annotation (Dialog,Placement(transformation(extent={{-70,-26},{-52,-18}})));
  replaceable ECATVariable StageNum_nd
    annotation (Dialog,Placement(transformation(extent={{-70,-26},{-52,-18}})));
  //RRH 2019/05/30 added
  replaceable output TableGenOutputs extraSettings
    annotation (Placement(transformation(extent={{16,-98},{52,-78}})),Dialog);
  replaceable ECATVariable HeatingAmbientAirDBTemp_K(
    redeclare type VariableType=SI.Temperature)
    "Heating Ambient air DB temperature"
    annotation (Dialog,Placement(transformation(extent={{-22,52},{-2,60}})));
  replaceable ECATVariable EN14511PumpPowerCorrectionWithPump_W(
    redeclare final type VariableType=SI.Power)
    annotation (Dialog,Placement(transformation(extent={{28,8},{48,16}})));
  replaceable ECATVariable CondEN14511PumpPowerCorrectionWithPump_W(
    redeclare final type VariableType=SI.Power)
    annotation (Dialog,Placement(transformation(extent={{28,8},{48,16}})));
  replaceable ECATVariable EN14511PumpPowerCorrectionWithoutPump_W(
    redeclare final type VariableType=SI.Power)
    annotation (Dialog,Placement(transformation(extent={{54,8},{74,16}})));
  replaceable ECATVariable CondEN14511PumpPowerCorrectionWithoutPump_W(
    redeclare final type VariableType=SI.Power)
    annotation (Dialog,Placement(transformation(extent={{54,8},{74,16}})));
  replaceable ECATVariable ExternalSystemKa_kPas2L2
    annotation (Dialog,Placement(transformation(extent={{52,72},{70,80}})));
  replaceable ECATVariable CondExternalSystemKa_kPas2L2
    annotation (Dialog,Placement(transformation(extent={{52,72},{70,80}})));
  replaceable ECATVariable ExternalSystemPressureDrop_Pa(
    redeclare final type VariableType=SI.PressureDifference)
    annotation (Dialog,Placement(transformation(extent={{76,72},{94,80}})));
  replaceable ECATVariable CondExternalSystemPressureDrop_Pa(
    redeclare final type VariableType=SI.PressureDifference)
    annotation (Dialog,Placement(transformation(extent={{76,72},{94,80}})));
  replaceable ECATVariable IsMinimalCapacity(
    redeclare final type VariableType=Real,
    value=0)
    annotation (Dialog,Placement(transformation(extent={{76,72},{94,80}})));
  replaceable ECATVariable IsCoolingMode(
    redeclare final type VariableType=Real)
    annotation (Dialog,Placement(transformation(extent={{76,72},{94,80}})));
  replaceable ECATVariable PubHeatingCapacityInstantaneous_W(
    redeclare final type VariableType=SI.Power)
    annotation (Dialog,Placement(transformation(extent={{80,8},{98,16}})));
  replaceable ECATVariable HeatingAmbientAirWBTemp_K(
    redeclare type VariableType=SI.Temperature)
    "Heating Ambient air WB temperature"
    annotation (Dialog,Placement(transformation(extent={{4,52},{24,60}})));
  replaceable ECATVariable CondHighStaticFanExtPressDrop_Pa(
    redeclare final type VariableType=SI.PressureDifference)
    annotation (Dialog,Placement(transformation(extent={{52,-12},{70,-4}})));
  replaceable ECATVariable EvapHighStaticFanExtPressDrop_Pa(
    redeclare final type VariableType=SI.PressureDifference)
    annotation (Dialog,Placement(transformation(extent={{76,-12},{94,-4}})));
  replaceable ECATVariable HighStaticFanExternalKa_kPas2L2[nbrCircuit]
    annotation (Dialog,Placement(transformation(extent={{26,-46},{42,-38}})));
  replaceable ECATVariable CondCoilHeatingCapacity_W[nbrCircuit](
    redeclare final type VariableType=SI.Power)
    annotation (Dialog,Placement(transformation(extent={{-46,-26},{-28,-18}})));
  replaceable ECATVariable CoolantFreezingTemp_K(
    redeclare final type VariableType=SI.Temperature)
    annotation (Dialog,Placement(transformation(extent={{2,-60},{20,-52}})));
  replaceable ECATVariable InternalLoopBrineConcentration_nd
    annotation (Dialog,Placement(transformation(extent={{0,0},{0,0}})));
  replaceable ECATVariable AdditionalAirComponentNominalPressureDrop_Pa(
    redeclare final type VariableType=SI.PressureDifference)
    annotation (Dialog,Placement(transformation(extent={{48,-46},{66,-38}})));
  replaceable ECATVariable AdditionalAirComponentNominalFlowRate_m3s(
    redeclare final type VariableType=SI.VolumeFlowRate)
    annotation (Dialog,Placement(transformation(extent={{72,-46},{90,-38}})));
  annotation (
    Evaluate=true,
    Icon(
      coordinateSystem(
        preserveAspectRatio=false)),
    Diagram(
      coordinateSystem(
        preserveAspectRatio=false)),
    defaultComponentName="ECAT");
//       replaceable output TableGenOutputs summary
//      annotation (Placement(transformation(extent={{-20,-96},{24,-58}})),  Dialog);
//       replaceable output TableGenOutputs businessFactorsSummary
//      annotation (Placement(transformation(extent={{62,-98},{98,-78}})),   Dialog);
//       replaceable ECATVariable PubNetUnitPower_W
//      (redeclare final type VariableType = SI.Power)
//      annotation (Dialog, Placement(transformation(extent={{28,8},{46,16}})));
//       replaceable ECATVariable PubNetCoolingCapacity_W
//      (redeclare final type VariableType = SI.Power)
//      annotation (Dialog, Placement(transformation(extent={{52,8},{70,16}})));
//       replaceable ECATVariable PubNetHeatingCapacity_W
//      (redeclare final type VariableType = SI.Power)
//      annotation (Dialog, Placement(transformation(extent={{76,8},{94,16}})));
//       replaceable ECATVariable GrossUnitPower_W
//      (redeclare final type VariableType = SI.Power)
//      annotation (Dialog, Placement(transformation(extent={{-94,52},{-76,60}})));
//       replaceable ECATVariable GrossCoolingCapacity_W
//      (redeclare final type VariableType = SI.Power)
//      annotation (Dialog, Placement(transformation(extent={{-22,52},{-2,60}})));
//       replaceable ECATVariable GrossHeatingCapacity_W
//      (redeclare final type VariableType = SI.Power)
//      annotation (Dialog, Placement(transformation(extent={{4,52},{22,60}})));
//       replaceable ECATVariable NetUnitPower_W
//      (redeclare final type VariableType = SI.Power)
//      annotation (Dialog, Placement(transformation(extent={{24,30},{42,38}})));
//       replaceable ECATVariable NetCoolingCapacity_W
//      (redeclare final type VariableType = SI.Power)
//      annotation (Dialog, Placement(transformation(extent={{48,30},{66,38}})));
//       replaceable ECATVariable NetHeatingCapacity_W
//      (redeclare final type VariableType = SI.Power)
//      annotation (Dialog, Placement(transformation(extent={{72,30},{92,38}})));
//       replaceable ECATVariable dT_fullLoad_degK
//      ( redeclare final type VariableType = SI.TemperatureDifference)
//      annotation (Dialog, Placement(transformation(extent={{-70,-102},{-52,-94}})));
//       replaceable ECATVariable CondenserHeatTransferArea_m2
//      (redeclare final type VariableType = SI.Area)
//      annotation (Dialog, Placement(transformation(extent={{-46,-102},{-28,-94}})));
//       replaceable ECATVariable CoolerHeatTransferArea_m2
//      (redeclare final type    VariableType = SI.Area)
//      annotation (Dialog, Placement(transformation(extent={{-22,-102},{-4,-94}})));
//       replaceable ECATVariable FullLoadCoolingCapacity_W
//      (redeclare final type VariableType = SI.Power)
//      annotation (Dialog, Placement(transformation(extent={{-22,-88},{-4,-80}})));
//       replaceable ECATVariable maxSVP[nbrCircuit]
//      annotation (Dialog, Placement(transformation(extent={{40,-46},{58,-38}})));
//       replaceable ECATBoolean GBFlowMode[nbrCircuit]
//      (redeclare final type VariableType = Boolean)
//      annotation (Dialog, Placement(transformation(extent={{66,-46},{84,-38}})));
end ChillerBase;
