within Workspace.System.Multimodule.HPH;
model Modular_61AQ
  extends.Workspace.Controller.CL_control_system_heating_zenith(
    controllerSettings_crkA(
      Capacity_setpoint=TargetCapacity,
      SST_min=243.15,
      coefficients=Module.fanCurveCoefficientsHeating[1,:],
      minfanfreq=10,
      maxfanfreq=if is_ULN_option then Module.max_fan_ULN_heating[1] else Module.BlockA.max_fan_frequency,
      extPressure_setpoint=Pdispo,
      LWT_setpoint=LWT,
      EWT_setpoint=EWT),
    controllerSettings_crkB(
      Capacity_setpoint=TargetCapacity,
      SST_min=243.15,
      coefficients=Module.fanCurveCoefficientsHeating[2,:],
      minfanfreq=10,
      maxfanfreq= if is_ULN_option then Module.max_fan_ULN_heating[2] else Module.BlockB.max_fan_frequency,
      extPressure_setpoint=Pdispo,
      LWT_setpoint=LWT,
      EWT_setpoint=EWT),
    controller_crkA(
      isOffDGTmax_fan=false,
      isOffSSTmin_EXV=false,
      isOffSSTmax_EXV=false,
      isOffDSHmin_EXV=false,
      isOffDGTmax_EXV=false,
      isOffSSTmin_comp=false,
      isOffSDTmax_comp=false,
      isOffDGTmax_comp=false,
      Fan_MaxFrequency=controllerSettings_crkA.maxfanfreq,
      completeCompressorControl_base(
        compressorControl(
          manualOff=controller_crkA.manualOff_compressor_block_A,
          AV_value_off=controller_crkA.frq_comp_sp_manual_block_A)),
      manualOff_fan_block_A=false,
      manualOff_fan_block_B=false,
      manualOff_compressor_block_A=false,
      manualOff_compressor_block_B=false,
      fanControl(
        manualOff=controller_crkA.manualOff_fan_block_A,
        AV_value_off=controller_crkA.frq_fan_sp_manual_block_A),
      Fan_MinFrequency=controllerSettings_crkA.minfanfreq,
      min_speed=Module.fmin[1],
      max_speed=if is_ULN_option then Module.max_cp_ULN_heating[1] else Module.fmax[1],
      isOffSSTmax_fan=false,
      isOffSSTmin_fan=false,
      Use_fake_pump=Module.Use_fake_pump,
      is_LWTcontrol=LWT_fixed,
      is_EWTcontrol=EWT_fixed and not LWT_fixed,
      pumpUserControl(
        isHeatingMode=true),
      Ka_start=-10,
      isOff=isOFF,
      Pump_speed_start=3000,Nominal_capacity = Module.BlockA.capacity_design),
    controller_crkB(
      isOffDGTmax_fan=false,
      isOffSSTmin_EXV=false,
      isOffSSTmax_EXV=false,
      isOffDSHmin_EXV=false,
      isOffDGTmax_EXV=false,
      isOffSSTmin_comp=false,
      isOffSDTmax_comp=false,
      isOffDGTmax_comp=false,
      Fan_MaxFrequency=controllerSettings_crkB.maxfanfreq,
      fanControl(
        manualOff=controller_crkB.manualOff_compressor_block_B,
        AV_value_off=controller_crkB.frq_fan_sp_manual_block_B),
      completeCompressorControl_base(
        compressorControl(
          manualOff=controller_crkB.manualOff_compressor_block_B,
          AV_value_off=controller_crkB.frq_comp_sp_manual_block_B)),
      manualOff_fan_block_A=false,
      manualOff_fan_block_B=false,
      manualOff_compressor_block_A=false,
      manualOff_compressor_block_B=false,
      Fan_MinFrequency=controllerSettings_crkB.minfanfreq,
      min_speed=Module.fmin[2],
      max_speed=if is_ULN_option then Module.max_cp_ULN_heating[2] else Module.fmax[2],
      isOffSSTmax_fan=false,
      isOffSSTmin_fan=false,
      is_EWTcontrol=EWT_fixed and not LWT_fixed,
      is_LWTcontrol=LWT_fixed,
      Use_fake_pump=Module.Use_fake_pump,
      pumpUserControl(
        isHeatingMode=true),
      Ka_start=-10,
      isOff=Module.is_monobloc or isOFF,
      Pump_speed_start=3000,Nominal_capacity = Module.BlockB.capacity_design));
  .Workspace.System.Multimodule.HPH.Equipement_Modular Module(
    CoolantMedium=CoolantMedium,
    OAT=OAT,
    LWT=LWT,
    EWT=EWT,
    Use_pump=choiceBlock.is_Pump,
    capacity_design={choiceBlock.Unit_Block_A.capacity_design,choiceBlock.Unit_Block_B.capacity_design},
    fmax={choiceBlock.Unit_Block_A.fmax,choiceBlock.Unit_Block_B.fmax},
    fmin={choiceBlock.Unit_Block_A.fmin,choiceBlock.Unit_Block_B.fmin},
    Dport_coolant_a={choiceBlock.Unit_Block_A.cond_diameter_brine_port_in,choiceBlock.Unit_Block_B.cond_diameter_brine_port_in},
    Dport_coolant_b={choiceBlock.Unit_Block_A.cond_diameter_brine_port_out,choiceBlock.Unit_Block_B.cond_diameter_brine_port_out},
    Dport_ref_a={choiceBlock.Unit_Block_A.cond_diameter_ref_port_in,choiceBlock.Unit_Block_B.cond_diameter_ref_port_in},
    Dport_ref_b={choiceBlock.Unit_Block_A.cond_diameter_ref_port_out,choiceBlock.Unit_Block_B.cond_diameter_ref_port_out},
    nPlate={choiceBlock.Unit_Block_A.nplate,choiceBlock.Unit_Block_B.nplate},
    nCoils={choiceBlock.Unit_Block_A.nCoils,choiceBlock.Unit_Block_B.nCoils},
    Itube={choiceBlock.Unit_Block_A.Itube,choiceBlock.Unit_Block_B.Itube},
    nCir={choiceBlock.Unit_Block_A.nCir,choiceBlock.Unit_Block_B.nCir},
    Ntube={choiceBlock.Unit_Block_A.Ntube,choiceBlock.Unit_Block_B.Ntube},
    Nrow={choiceBlock.Unit_Block_A.Nrow,choiceBlock.Unit_Block_B.Nrow},
    Ltube={choiceBlock.Unit_Block_A.Ltube,choiceBlock.Unit_Block_B.Ltube},
    Dotube={choiceBlock.Unit_Block_A.Dotube,choiceBlock.Unit_Block_B.Dotube},
    Ttube={choiceBlock.Unit_Block_A.Ttube,choiceBlock.Unit_Block_B.Ttube},
    Ptube={choiceBlock.Unit_Block_A.Ptube,choiceBlock.Unit_Block_B.Ptube},
    Prow={choiceBlock.Unit_Block_A.Prow,choiceBlock.Unit_Block_B.Prow},
    Dfin={choiceBlock.Unit_Block_A.Dfin,choiceBlock.Unit_Block_B.Dfin},
    Tfin={choiceBlock.Unit_Block_A.Tfin,choiceBlock.Unit_Block_B.Tfin},
    Fw_fan={choiceBlock.Unit_Block_A.Fan_FW,choiceBlock.Unit_Block_B.Fan_FW},
    max_fan_frequency={choiceBlock.Unit_Block_A.max_fan_frequency,choiceBlock.Unit_Block_B.max_fan_frequency},
    Suction_line_diameter={choiceBlock.Unit_Block_A.Suction_line_diameter,choiceBlock.Unit_Block_B.Suction_line_diameter},
    Suction_line_length={choiceBlock.Unit_Block_A.Suction_line_length,choiceBlock.Unit_Block_B.Suction_line_length},
    coil_line_diameter={choiceBlock.Unit_Block_A.Coil_line_diameter,choiceBlock.Unit_Block_B.Coil_line_diameter},
    coil_line_length={choiceBlock.Unit_Block_A.Coil_line_length,choiceBlock.Unit_Block_B.Coil_line_length},
    liquid_line_diameter={choiceBlock.Unit_Block_A.Liquid_line_diameter,choiceBlock.Unit_Block_B.Liquid_line_diameter},
    liquid_line_length={choiceBlock.Unit_Block_A.Liquid_line_length,choiceBlock.Unit_Block_B.Liquid_line_length},
    discharge_line_diameter={choiceBlock.Unit_Block_A.discharge_line_diameter,choiceBlock.Unit_Block_B.discharge_line_diameter},
    discharge_line_length={choiceBlock.Unit_Block_A.discharge_line_length,choiceBlock.Unit_Block_B.discharge_line_length},
    EXV_in_line_diameter={choiceBlock.Unit_Block_A.EXV_in_line_diameter,choiceBlock.Unit_Block_B.EXV_in_line_diameter},
    EXV_in_line_length={choiceBlock.Unit_Block_A.EXV_in_line_length,choiceBlock.Unit_Block_B.EXV_in_line_length},
    Ac_duct={choiceBlock.Unit_Block_A.duct_Ac,choiceBlock.Unit_Block_B.duct_Ac},
    Ka_duct={choiceBlock.Unit_Block_A.duct_Ka,choiceBlock.Unit_Block_B.duct_Ka},
    UA_duct={choiceBlock.Unit_Block_A.duct_UA,choiceBlock.Unit_Block_B.duct_UA},
    selector_Comp={choiceBlock.Unit_Block_A.selector_Comp,choiceBlock.Unit_Block_B.selector_Comp},
    EXV_main={choiceBlock.Unit_Block_A.EXV_main_A,choiceBlock.Unit_Block_B.EXV_main_A},
    selector_geo_BPHE={choiceBlock.Unit_Block_A.cond_select_geo,choiceBlock.Unit_Block_B.cond_select_geo},
    selector_pump={choiceBlock.Unit_Block_A.Pump_type,choiceBlock.Unit_Block_B.Pump_type},
    fanCurveCoefficientsHeating={choiceBlock.Unit_Block_A.fanCurveCoefficientsHeating,choiceBlock.Unit_Block_B.fanCurveCoefficientsHeating},
    CompVoltage={choiceBlock.Unit_Block_A.CompVoltage,choiceBlock.Unit_Block_B.CompVoltage},
    is_monobloc=is_monobloc,
    fanCurveCoefficientsCooling={choiceBlock.Unit_Block_A.fanCurveCoefficientsCooling,choiceBlock.Unit_Block_B.fanCurveCoefficientsCooling},
    use_bf=use_bf,
    BrineConcentration=BrineConcentration,
    mdot_start=Module.capacity_design[1]/((((4180*(Module.LWT-Module.EWT))))),
    isCoating=choiceBlock.isCoatingOption,
    BlockA(
      node_EXV_in(
        dTsh_fixed=not Mref_fixed[1])),
    BlockB(
      node_EXV_in(
        dTsh_fixed=not Mref_fixed[2])),
    PDC_4WV={choiceBlock.Unit_Block_A.PDC_4WV,choiceBlock.Unit_Block_B.PDC_4WV},
    Zevap_coated_HPH={choiceBlock.Unit_Block_A.Zevap_coated_chaud,choiceBlock.Unit_Block_B.Zevap_coated_chaud},
    Zflow_intercept={choiceBlock.Unit_Block_A.Zflow_intercept,choiceBlock.Unit_Block_B.Zflow_intercept},
    Zflow_SST={choiceBlock.Unit_Block_A.Zflow_SST,choiceBlock.Unit_Block_B.Zflow_SST},
    Zflow_SDT={choiceBlock.Unit_Block_A.Zflow_SDT,choiceBlock.Unit_Block_B.Zflow_SDT},
    Zflow_heatcap={choiceBlock.Unit_Block_A.Zflow_Heatcap,choiceBlock.Unit_Block_B.Zflow_Heatcap},
    Zpower_intercept={choiceBlock.Unit_Block_A.Zpower_intercept,choiceBlock.Unit_Block_B.Zpower_intercept},
    Zpower_DGT={choiceBlock.Unit_Block_A.Zpower_DGT,choiceBlock.Unit_Block_B.Zpower_DGT},
    Zpower_SST={choiceBlock.Unit_Block_A.Zpower_SST,choiceBlock.Unit_Block_B.Zpower_SST},
    Zpower_SST2={choiceBlock.Unit_Block_A.Zpower_SST2,choiceBlock.Unit_Block_B.Zpower_SST2},
    FW={choiceBlock.Unit_Block_A.FW,choiceBlock.Unit_Block_B.FW},
    use_Calib=use_Calib,
    Zevap_HPH_cst={choiceBlock.Unit_Block_A.Zevap_chaud_intercept,choiceBlock.Unit_Block_B.Zevap_chaud_intercept},
    Zevap_HPH_SST={choiceBlock.Unit_Block_A.Zevap_chaud_SST,choiceBlock.Unit_Block_B.Zevap_chaud_SST},
    Zevap_HPC_cst={choiceBlock.Unit_Block_A.Zevap_froid_intercept,choiceBlock.Unit_Block_B.Zevap_froid_intercept},
    Zevap_HPC_SST={choiceBlock.Unit_Block_A.Zevap_froid_SST,choiceBlock.Unit_Block_B.Zevap_froid_SST},
    Zcond_HPH_cst={choiceBlock.Unit_Block_A.Zcond_chaud_intercept,choiceBlock.Unit_Block_B.Zcond_chaud_intercept},
    Zcond_HPH_heatcap={choiceBlock.Unit_Block_A.Zcond_chaud_Heatcap,choiceBlock.Unit_Block_B.Zcond_chaud_Heatcap},
    Zcond_HPH_SST={choiceBlock.Unit_Block_A.Zcond_chaud_SST,choiceBlock.Unit_Block_B.Zcond_chaud_SST},
    Zcond_HPC_cst={choiceBlock.Unit_Block_A.Zcond_froid_intercept,choiceBlock.Unit_Block_B.Zcond_froid_intercept},
    Zcond_HPC_SDT={choiceBlock.Unit_Block_A.Zcond_froid_SDT,choiceBlock.Unit_Block_B.Zcond_froid_SDT},
    use_Calib=use_Calib,
    Zcond_HPH_SDT={choiceBlock.Unit_Block_A.Zcond_chaud_SDT,choiceBlock.Unit_Block_B.Zcond_chaud_SDT},
    use_Calib=use_Calib,
    relative_humidity=Relative_humidity,
    Use_fake_pump=not choiceBlock.is_Pump,
    Use_EN=use_en,
    Use_defrost=use_defrost,
    const_bf_cap=choiceBlock.Unit_Block_A.const_bf_cap_chaud,
    load_bf_cap=choiceBlock.Unit_Block_A.load_bf_cap_chaud,
    OAT_bf_cap=choiceBlock.Unit_Block_A.OAT_bf_cap_chaud,
    OAT2_bf_cap=0,
    OAT3_bf_cap=0,
    const_bf_pow=choiceBlock.Unit_Block_A.const_bf_pow_chaud,
    load_bf_pow=choiceBlock.Unit_Block_A.load_bf_pow_chaud,
    OAT_bf_pow=choiceBlock.Unit_Block_A.OAT_bf_pow_chaud,
    OAT2_bf_pow=0,
    OAT3_bf_pow=0,
    max_bf_cap=choiceBlock.Unit_Block_A.max_bf_cap_chaud,
    min_bf_cap=choiceBlock.Unit_Block_A.min_bf_cap_chaud,
    max_bf_pow=choiceBlock.Unit_Block_A.max_bf_pow_chaud,
    min_bf_pow=choiceBlock.Unit_Block_A.min_bf_pow_chaud,
    SC_fixed={SC_fixed[1],SC_fixed[2]},
    Mref_fixed={Mref_fixed[1],Mref_fixed[2]},
    SC_setpoint={SC_setpoint[1],SC_setpoint[2]},
    ssh_setPoint={ssh_setPoint[1],ssh_setPoint[2]},
    M_ref={choiceBlock.Unit_Block_A.m_ref_chaud,choiceBlock.Unit_Block_B.m_ref_chaud},
    isOFF=isOFF,
    Zpower_Ncomp={choiceBlock.Unit_Block_A.Zpower_Ncomp,choiceBlock.Unit_Block_B.Zpower_Ncomp},
    Zpower_SDT={choiceBlock.Unit_Block_A.Zpower_SDT,choiceBlock.Unit_Block_B.Zpower_SDT},
    Zpower_heatcap={choiceBlock.Unit_Block_A.Zpower_heatcap,choiceBlock.Unit_Block_B.Zpower_heatcap},
    isFilter=isFilter,
    Coef_filter=Coef_filter,
    isBufferTank=isBufferTank,
    Coef_bufferTank=Coef_bufferTank,
    is_relative_humidity=is_relative_humidity,
    OAT_WB=OAT_WB,
    is_OAT_WB=is_OAT_WB,
    Heatcap_Tbiv={choiceBlock.Unit_Block_A.Heatcap_Tbiv,choiceBlock.Unit_Block_B.Heatcap_Tbiv},
    Zcond_HPC_coated={choiceBlock.Unit_Block_A.Zcond_chaud_coated,choiceBlock.Unit_Block_B.Zcond_chaud_coated},
    Freq_degi={choiceBlock.Unit_Block_A.Frequence_degi,choiceBlock.Unit_Block_B.Frequence_degi},Zevap_chaud_Coolcap = {choiceBlock.Unit_Block_A.Zevap_chaud_Coolcap,choiceBlock.Unit_Block_B.Zevap_chaud_Coolcap},Zevap_froid_Coolcap = {choiceBlock.Unit_Block_A.Zevap_froid_Coolcap,choiceBlock.Unit_Block_B.Zevap_froid_Coolcap},Altitude = Altitude,Nominal_cap = {choiceBlock.Unit_Block_A.capacity_design,choiceBlock.Unit_Block_B.capacity_design},OAT_target_cap = {choiceBlock.Unit_Block_A.OAT_target_cap,choiceBlock.Unit_Block_B.OAT_target_cap},cst_target_cap = {choiceBlock.Unit_Block_A.intercept_target_cap,choiceBlock.Unit_Block_B.intercept_target_cap},Max_max_target_cap = {choiceBlock.Unit_Block_A.Max_max_target_cap,choiceBlock.Unit_Block_B.Max_max_target_cap},max_cp_ULN_heating = {choiceBlock.Unit_Block_A.max_cp_ULN_heating,choiceBlock.Unit_Block_B.max_cp_ULN_heating},max_cp_ULN_cooling = {choiceBlock.Unit_Block_A.max_cp_ULN_cooling,choiceBlock.Unit_Block_B.max_cp_ULN_cooling},max_fan_ULN_heating = {choiceBlock.Unit_Block_A.max_fan_ULN_heating,choiceBlock.Unit_Block_B.max_fan_ULN_heating},max_fan_ULN_cooling = {choiceBlock.Unit_Block_A.max_fan_ULN_cooling,choiceBlock.Unit_Block_B.max_fan_ULN_cooling},is_ULN_option = choiceBlock.is_SoundOption,Heatcap_Tbiv_LN_option = {choiceBlock.Unit_Block_A.Heatcap_Tbiv_LN_option,choiceBlock.Unit_Block_B.Heatcap_Tbiv_LN_option},Nominal_cap_LN = {choiceBlock.Unit_Block_A.capacity_design_LN,choiceBlock.Unit_Block_B.capacity_design_LN},OAT_target_cap_LN = {choiceBlock.Unit_Block_A.OAT_target_cap_LN,choiceBlock.Unit_Block_B.OAT_target_cap_LN},cst_target_cap_LN = {choiceBlock.Unit_Block_A.intercept_target_cap_LN,choiceBlock.Unit_Block_B.intercept_target_cap_LN},Max_max_target_cap_LN = {choiceBlock.Unit_Block_A.Max_max_target_cap_LN,choiceBlock.Unit_Block_B.Max_max_target_cap_LN},max_fan_frequency2 = {choiceBlock.Unit_Block_A.max_fan_frequency2,choiceBlock.Unit_Block_B.max_fan_frequency2},Zflow_Ncomp = {choiceBlock.Unit_Block_A.Zflow_Ncomp,choiceBlock.Unit_Block_B.Zflow_Ncomp},Zflow_SST2 = {choiceBlock.Unit_Block_A.Zflow_SST2,choiceBlock.Unit_Block_B.Zflow_SST2},Zflow_SST3 = {choiceBlock.Unit_Block_A.Zflow_SST3,choiceBlock.Unit_Block_B.Zflow_SST3},Zevap_HPH_min = {choiceBlock.Unit_Block_A.Z_evap_HPH_min,choiceBlock.Unit_Block_B.Z_evap_HPH_min},Zevap_HPH_max = {choiceBlock.Unit_Block_A.Z_evap_HPH_max,choiceBlock.Unit_Block_B.Z_evap_HPH_max},Zevap_HPC_min = {choiceBlock.Unit_Block_A.Z_evap_HPC_min,choiceBlock.Unit_Block_B.Z_evap_HPC_min},Zevap_HPC_max = {choiceBlock.Unit_Block_A.Z_evap_HPC_max,choiceBlock.Unit_Block_B.Z_evap_HPC_max},Zcond_HPH_min = {choiceBlock.Unit_Block_A.Z_cond_HPH_min,choiceBlock.Unit_Block_B.Z_cond_HPH_min},Zcond_HPH_max = {choiceBlock.Unit_Block_A.Z_cond_HPH_max,choiceBlock.Unit_Block_B.Z_cond_HPH_max},Zcond_HPC_min = {choiceBlock.Unit_Block_A.Z_cond_HPC_min,choiceBlock.Unit_Block_B.Z_cond_HPC_min},Zcond_HPC_max = {choiceBlock.Unit_Block_A.Z_cond_HPC_max,choiceBlock.Unit_Block_B.Z_cond_HPC_max},Zpower_min = {choiceBlock.Unit_Block_A.Z_power_min,choiceBlock.Unit_Block_B.Z_power_min},Zpower_max = {choiceBlock.Unit_Block_A.Z_power_max,choiceBlock.Unit_Block_B.Z_power_max},Zflow_min = {choiceBlock.Unit_Block_A.Z_flow_min,choiceBlock.Unit_Block_B.Z_flow_min},Zflow_max = {choiceBlock.Unit_Block_A.Z_flow_max,choiceBlock.Unit_Block_B.Z_flow_max})
    annotation (Placement(transformation(extent={{-24.60556317292926,4.208974521254968},{-4.6055631729292585,24.208974521254966}},origin={0.0,0.0},rotation=0.0)));
  parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector CoolantMedium
    "Coolant Medium Selection"
    annotation (Dialog(group="Medium"));
  final parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Empty coolantInf=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.getSelector(
    CoolantMedium)
    "Coolant Medium"
    annotation ();
  .BOLT.InternalLibrary.Media.Coolant.CoolantCommon.Temperature FreezTemp=.BOLT.InternalLibrary.Media.Coolant.CoolantCommon.freezeTemp(
    coolantInf,
    BrineConcentration,
    1);
  parameter Real BrineConcentration
    annotation (Dialog(group="Medium"));
  parameter Boolean LWT_fixed=true
    annotation (Dialog(group="Conditions"));
  parameter.Modelica.SIunits.Temperature LWT
    annotation (Dialog(group="Conditions"));
  parameter Boolean EWT_fixed=true
    annotation (Dialog(group="Conditions"));
  parameter.Modelica.SIunits.Temperature EWT
    annotation (Dialog(group="Conditions"));
  parameter.Modelica.SIunits.Temperature OAT
    annotation (Dialog(group="Conditions"));
  parameter Real Relative_humidity=0.87
    annotation (Dialog(group="Conditions",tab="General"));
  parameter Boolean is_relative_humidity=false
    annotation (Dialog(group="Conditions",tab="General"));
  parameter Real OAT_WB=0.87
    annotation (Dialog(group="Conditions",tab="General"));
  parameter Boolean is_OAT_WB=false
    annotation (Dialog(group="Conditions",tab="General"));
  parameter Boolean FlowRate_fixed=false
    annotation (Dialog(group="Conditions"));
  parameter.Modelica.SIunits.VolumeFlowRate FlowRate=0.015
    annotation (Dialog(group="Conditions"));
  parameter.Modelica.SIunits.PressureDifference Pdispo=0
    annotation (Dialog(group="Conditions",tab="General"));
  parameter Boolean use_bf=false
    annotation (Dialog(group="Use Parameters"));
  parameter Boolean use_defrost=true
    annotation (Dialog(group="Use Parameters"));
  parameter Boolean use_en=false
    annotation (Dialog(group="Use Parameters"));
  parameter Boolean use_Calib=false
    annotation (Dialog(group="Use Parameters"));
  .Workspace.Auxiliary.OptionBlock.ChoiceBlock choiceBlock(
    is_monobloc=is_monobloc,
    Selector_Block_A=Selector_block_A,
    Selector_Block_B=Selector_block_B,
    Pump_selector=Pump_selector,
    Coating_selector=Coating_selector,SoundOption_selector = Sound_selector)
    annotation (Placement(transformation(extent={{-87.74467033432845,68.09766263500208},{-66.97602804811041,88.86630492122012}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.InternalLibrary.BuildingBlocks.Coolant.Ports.FluidPort_a inlet
    annotation (Placement(transformation(extent={{-24.0,-81.46169093003043},{-4.0,-61.46169093003043}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={0,-100})));
  .BOLT.InternalLibrary.BuildingBlocks.Coolant.Ports.FluidPort_b outlet
    annotation (Placement(transformation(extent={{-23.863714585519403,87.36285414480588},{-3.8637145855194053,107.36285414480588}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={0,100})));
  .Modelica.Blocks.Interfaces.RealInput capacity_total
    annotation (Placement(transformation(extent={{-147.4276482707046,-43.64578922969507},{-124.02699933181917,-20.24514029080966}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={-110,0})));
  parameter.Modelica.SIunits.Power TargetCapacity
    annotation (Dialog(group="Cp_control"));
  parameter Boolean isOFF
    annotation (Dialog(group="Cp_control"));
  .Modelica.SIunits.Frequency CompressorFreq=Module.BlockA.compressor.summary.Ncomp;
  parameter.Workspace.Auxiliary.OptionBlock.Record_Base.Selector Selector_block_A=.Workspace.Auxiliary.OptionBlock.Record_Base.Selector.Unit_Z_040
    annotation (Dialog(group="Unit",tab="ChoiceBlock"));
  parameter.Workspace.Auxiliary.OptionBlock.Record_Base.Selector Selector_block_B=.Workspace.Auxiliary.OptionBlock.Record_Base.Selector.Unit_Z_040
    annotation (Dialog(group="Unit",tab="ChoiceBlock"));
  parameter Boolean is_monobloc=true
    annotation (Dialog(group="Units",tab="ChoiceBlock"));
  parameter.Workspace.Auxiliary.OptionBlock.PumpOption.Pump_Option Pump_selector=.Workspace.Auxiliary.OptionBlock.PumpOption.Pump_Option.STANDARD
    annotation (Dialog(group="Options",tab="ChoiceBlock"));
  parameter.Workspace.Auxiliary.OptionBlock.SoundOption.Selector Sound_selector=.Workspace.Auxiliary.OptionBlock.SoundOption.Selector.STANDARD
    annotation (Dialog(group="Options",tab="ChoiceBlock"));
  parameter.Workspace.Auxiliary.OptionBlock.CoatingOption.Coating_selector Coating_selector=.Workspace.Auxiliary.OptionBlock.CoatingOption.Coating_selector.STANDARD
    annotation (Dialog(group="Options",tab="ChoiceBlock"));
  parameter Boolean isFilter=false
    annotation (Dialog(group="Options",tab="ChoiceBlock"));
  parameter Real[2] Coef_filter={0.00001,0}
    annotation (Dialog(group="Options",tab="ChoiceBlock"));
  parameter Boolean isBufferTank=false
    annotation (Dialog(group="Options",tab="ChoiceBlock"));
  parameter Real[2] Coef_bufferTank={0.00001,0}
    annotation (Dialog(group="Options",tab="ChoiceBlock"));
  parameter.Modelica.SIunits.TemperatureDifference[2] ssh_setPoint={5,5}
    "circuit A ssh set point"
    annotation (Dialog(group="Refrigerant"));
  parameter.Modelica.SIunits.TemperatureDifference[2] SC_setpoint={-2,-2}
    annotation (Dialog(group="Refrigerant"));
  parameter Boolean[2] SC_fixed={true,true}
    annotation (Dialog(group="Refrigerant"));
  parameter Boolean[2] Mref_fixed={false,false}
    annotation (Dialog(group="Refrigerant"));
  parameter Boolean is_monobloc_db_initialization
    annotation (Dialog(tab="Initialization"));
  .Modelica.SIunits.PressureDifference InternalPressureDrop=Module.InternalPressureDrop;
  .Modelica.SIunits.PressureDifference AvailableStaticPressure=Module.AvailableStaticPressure;
  .Workspace.Interfaces.MeasurementBus measurementBus
    annotation (Placement(transformation(extent={{-118.84404214808976,79.5607949068835},{-78.84404214808976,119.5607949068835}},origin={0,0},rotation=0)));
    parameter Real Altitude;
    parameter Boolean is_ULN_option = if Integer(choiceBlock.SoundOption_selector) == 1 then false else true;
equation
  connect(Module.measurementBusA,controllerSettings_crkA.measurementBus)
    annotation (Line(points={{-22.60556317292926,24.208974521254966},{-22.60556317292926,40.08035972173846},{-70.29681143199207,40.08035972173846}},color={255,204,51}));
  connect(Module.measurementBusB,controllerSettings_crkB.measurementBus)
    annotation (Line(points={{-6.6055631729292585,24.208974521254966},{-6.6055631729292585,40.8407638560912},{40.53140745356192,40.8407638560912}},color={255,204,51}));
  connect(controller_crkA.compressor,Module.Compressor_controller_A)
    annotation (Line(points={{-49,5},{-36.90843151447778,5},{-36.90843151447778,6.708974521254968},{-25.60556317292926,6.708974521254968}},color={0,0,127}));
  connect(controller_crkA.exv,Module.EXV_controller_A)
    annotation (Line(points={{-49,2.5},{-36.90843151447778,2.5},{-36.90843151447778,16.708974521254966},{-25.60556317292926,16.708974521254966}},color={0,0,127}));
  connect(controller_crkA.fan,Module.Fan_controller_A)
    annotation (Line(points={{-49,0},{-36.90843151447778,0},{-36.90843151447778,11.708974521254968},{-25.60556317292926,11.708974521254968}},color={0,0,127}));
  connect(controller_crkA.pump,Module.ActuatorPumpUser_A)
    annotation (Line(points={{-49,-2.5},{-36.90843151447778,-2.5},{-36.90843151447778,21.208974521254966},{-25.60556317292926,21.208974521254966}},color={0,0,127}));
  connect(controller_crkB.compressor,Module.Compressor_controller_B)
    annotation (Line(points={{22.672455613343857,5.519176064177298},{-3.6055631729292585,5.519176064177298},{-3.6055631729292585,6.708974521254968}},color={0,0,127}));
  connect(controller_crkB.exv,Module.EXV_controller_B)
    annotation (Line(points={{22.672455613343857,3.019176064177298},{9.927796292194149,3.019176064177298},{9.927796292194149,16.708974521254966},{-3.6055631729292585,16.708974521254966}},color={0,0,127}));
  connect(controller_crkB.fan,Module.Fan_controller_B)
    annotation (Line(points={{22.672455613343857,0.519176064177298},{9.927796292194149,0.519176064177298},{9.927796292194149,11.708974521254968},{-3.6055631729292585,11.708974521254968}},color={0,0,127}));
  connect(controller_crkB.pump,Module.ActuatorPumpUser_B)
    annotation (Line(points={{22.672455613343857,-1.980823935822702},{9.927796292194149,-1.980823935822702},{9.927796292194149,21.208974521254966},{-3.6055631729292585,21.208974521254966}},color={0,0,127}));
  connect(Module.coolant_in,inlet)
    annotation (Line(points={{-14.953572160030713,-0.4616201212904034},{-14.953572160030713,-71.46169093003043},{-14,-71.46169093003043}},color={0,127,0}));
  connect(capacity_total,controller_crkA.measurementBus.capacity)
    annotation (Line(points={{-135.7273238012619,-31.945464760252364},{-84.93284365162644,-31.945464760252364},{-84.93284365162644,7},{-70,7}},color={0,0,127}));
  connect(capacity_total,controller_crkB.measurementBus.capacity)
    annotation (Line(points={{-135.7273238012619,-31.945464760252364},{49.67245561334386,-31.945464760252364},{49.67245561334386,7.519176064177298},{43.67245561334386,7.519176064177298}},color={0,0,127}));
  connect(outlet,Module.coolant_out)
    annotation (Line(points={{-13.863714585519405,97.36285414480588},{-13.863714585519405,72.20300453996053},{-14.795576595488551,72.20300453996053},{-14.795576595488551,25.360048738666237}},color={0,127,0}));
  connect(Module.measurementBusA,measurementBus)
    annotation (Line(points={{-22.60556317292926,24.208974521254966},{-22.60556317292926,60.189879277553125},{-98.84404214808976,60.189879277553125},{-98.84404214808976,99.5607949068835}},color={255,204,51}));
  connect(controller_crkA.measurementBus,measurementBus)
    annotation (Line(points={{-70,7},{-102.06695017965727,7},{-102.06695017965727,99.5607949068835},{-98.84404214808976,99.5607949068835}},color={255,204,51}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name"),
        Rectangle(
          extent={{-100,100},{100,-100}},
          fillPattern=FillPattern.Solid,
          fillColor={84,27,99}),
        Text(
          textString="MODULE",
          origin={0,-4},
          extent={{-101,49},{101,-49}},
          lineColor={245,245,245})}));
end Modular_61AQ;
