within Workspace.Controller.SubSystems.Tests.FanControl;
model FanControlCooling
  .Workspace.Controller.SubSystems.FanControl_cooling fanControl_cooling(
    isOffSDTmin=true,
    isOffSDTmax=true,
    isOffDGTmax=true)
    annotation (Placement(transformation(extent={{-12.0,2.0},{8.0,22.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_sdt(
    y=SDT)
    annotation (Placement(transformation(extent={{-84.0,38.0},{-64.0,58.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_DGT(
    y=DGT)
    annotation (Placement(transformation(extent={{-84.0,24.0},{-64.0,44.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression SDT_max(
    y=SDTmax)
    annotation (Placement(transformation(extent={{-90.0,-34.0},{-70.0,-14.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression DGT_max(
    y=DGTmax)
    annotation (Placement(transformation(extent={{-90.0,-48.0},{-70.0,-28.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression SDT_min(
    y=SDTmin)
    annotation (Placement(transformation(extent={{-89.68285774248906,-17.68285774248907},{-70.31714225751094,1.6828577424890696}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression Fan_speed_setpoint(
    y=Fan_speed_set)
    annotation (Placement(transformation(extent={{-88.0,-4.0},{-68.0,16.0}},origin={0.0,0.0},rotation=0.0)));
  parameter Real Fan_speed_set=720;
  parameter.Modelica.SIunits.Temperature DGT=423.15;
  parameter.Modelica.SIunits.Temperature SDT=354.15;
  parameter.Modelica.SIunits.Temperature SDTmax=353.15;
  parameter.Modelica.SIunits.Temperature DGTmax=418.15;
  parameter.Modelica.SIunits.Temperature SDTmin=308.15;
  .Modelica.Blocks.Interfaces.RealOutput actuatorSignal
    annotation (Placement(transformation(extent={{20.0,2.0},{40.0,22.0}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{90,-10},{110,10}})));
equation
  connect(T_sdt.y,fanControl_cooling.measurementBus.T_sdt)
    annotation (Line(points={{-63,48},{-63,18},{-12,18}},color={0,0,127}));
  connect(T_DGT.y,fanControl_cooling.measurementBus.T_dgt)
    annotation (Line(points={{-63,34},{-63,18},{-12,18}},color={0,0,127}));
  connect(SDT_min.y,fanControl_cooling.limitsBus.T_sdt_min_limit)
    annotation (Line(points={{-69.34885648326203,-8},{-55.5,-8},{-55.5,6},{-12,6}},color={0,0,127}));
  connect(SDT_max.y,fanControl_cooling.limitsBus.T_sdt_max_limit_fan)
    annotation (Line(points={{-69,-24},{-55.5,-24},{-55.5,6},{-12,6}},color={0,0,127}));
  connect(DGT_max.y,fanControl_cooling.limitsBus.T_dgt_max_limit_fan)
    annotation (Line(points={{-69,-38},{-56,-38},{-56,6},{-12,6}},color={0,0,127}));
  connect(Fan_speed_setpoint.y,fanControl_cooling.limitsBus.fanSpeed_setpoint)
    annotation (Line(points={{-67,6},{-12,6}},color={0,0,127}));
  connect(fanControl_cooling.actuatorSignal,actuatorSignal)
    annotation (Line(points={{8,12},{30,12}},color={0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end FanControlCooling;
