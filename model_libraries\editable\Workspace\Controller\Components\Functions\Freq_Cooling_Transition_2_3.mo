within Workspace.Controller.Components.Functions;
function Freq_Cooling_Transition_2_3
  extends.Modelica.Icons.Function;
  input.Modelica.SIunits.Temperature T_lwt;
  input.Modelica.SIunits.Temperature T_oat;
  input Real freq_compressor=70;
  input Real target_cap;
  input Real current_cap;
  input Integer N_module;
  output Real freq_Transition;
  output Boolean Freq_Cooling_Transition_2_3;
  parameter Real intercept=95.57626477;
  parameter Real LWT_1=-11.5495655;
  parameter Real OAT_2_LWT_1=-0.0005012;
  parameter Real LWT_2=0.728074015;
  parameter Real LWT_3=-0.012208239;
  parameter Real OAT_2=-0.012559196;
  parameter Real OAT_3=0.000292682;
  parameter Real OAT_1_LWT_1=0.02796287;
  parameter Real clamp=90;
protected
  .Modelica.SIunits.Temp_C OAT=T_lwt-273.15
    "Leaving water temperature in centigrade";
  .Modelica.SIunits.Temp_C LWT=T_oat-273.15
    "Outside air temperature in centigrade";
//   Real freq_Transition;
algorithm
  freq_Transition := min(
    intercept+LWT_1*LWT+OAT_2_LWT_1*OAT^2*LWT+LWT_2*LWT^2+LWT_3*LWT^3+OAT_2*OAT^2+OAT_3*OAT^3+OAT_1_LWT_1*OAT*LWT,
    clamp);
  Freq_Cooling_Transition_2_3 :=(freq_compressor >= freq_Transition or target_cap-10 > current_cap) and N_module > 2;
end Freq_Cooling_Transition_2_3;
