within Workspace.Controller;
model CL_control_system_heating_zenith
  .Workspace.Controller.Controller_1C_heating controller_crkA
    annotation (Placement(transformation(extent={{-70.0,-10.0},{-50.0,10.0}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Controller.ControllerSettings_heating controllerSettings_crkA
    annotation (Placement(transformation(extent={{-71.437859591774,29.75877192982456},{-91.437859591774,49.75877192982456}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Controller.ControllerSettings_heating controllerSettings_crkB
    annotation (Placement(transformation(extent={{41.67245561334386,30.5191760641773},{61.67245561334386,50.5191760641773}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Controller.Controller_1C_heating controller_crkB
    annotation (Placement(transformation(extent={{43.67245561334386,-9.480823935822702},{23.672455613343857,10.519176064177298}},origin={0.0,0.0},rotation=0.0)));
equation
  connect(controller_crkA.measurementBus,controllerSettings_crkA.measurementBus)
    annotation (Line(points={{-70,7},{-56.74928883430485,7},{-56.74928883430485,40.08035972173846},{-70.29681143199207,40.08035972173846}},color={255,204,51}));
  connect(controllerSettings_crkA.limitsBus,controller_crkA.limitsBus)
    annotation (Line(points={{-90.21980425208893,36.737955530902454},{-101.92377701106989,36.737955530902454},{-101.92377701106989,3},{-70,3}},color={255,204,51}));
  connect(controller_crkB.measurementBus,controllerSettings_crkB.measurementBus)
    annotation (Line(points={{43.67245561334386,7.519176064177298},{29.902568383471312,7.519176064177298},{29.902568383471312,41.119176064177296},{40.07245561334386,41.119176064177296}},color={255,204,51}));
  connect(controllerSettings_crkB.limitsBus,controller_crkB.limitsBus)
    annotation (Line(points={{60.38014792103617,37.45763760263884},{74.11492742081197,37.45763760263884},{74.11492742081197,3.519176064177298},{43.67245561334386,3.519176064177298}},color={255,204,51}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end CL_control_system_heating_zenith;
