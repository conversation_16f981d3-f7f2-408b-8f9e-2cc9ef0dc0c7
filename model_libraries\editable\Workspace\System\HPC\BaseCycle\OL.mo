within Workspace.System.HPC.BaseCycle;
model OL
  import CoolantCommonMedium=BOLT.InternalLibrary.Media.Coolant.CoolantCommon;
  parameter Boolean isOff=false
    "Set true to turn off";
  parameter Boolean use_Z_in=true
    "Set false to enable calibration capability";
  parameter.Modelica.SIunits.Conversions.NonSIunits.AngularVelocity_rpm max_fan_rpm=720
    "maximum fan speed";
  parameter.Modelica.SIunits.Frequency max_motor_frequency=max_fan_rpm/max_fan_rpm_STD*max_motor_frequency_STD
    "maximum motor frequency";
  .BOLT.Compressor.PD.CompVS_2P compressor(
    RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R290,
    isLoopBreaker=true,
    isOff=isOff_ref,
    selector_CompARIS=selector_Comp,
    use_z_flow_suc_expression=true,
    Z_flow_suc_expression=
      if use_Calib then
        calibrationBlock.Z_Flow
      else
        compressor.Z_flow_suc,
    use_z_power_expression=true,
    Z_power_expression=
      if use_Calib then
        calibrationBlock.Z_Power
      else
        compressor.Z_power,
    Fw=FW)
    annotation (Placement(transformation(extent={{54.0,4.378573167467998},{82.0,32.378573167468}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput EXV_controller
    annotation (Placement(transformation(extent={{-104.75133464479472,65.46037029926849},{-93.0496370818542,77.16206786220901}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={-110,60})));
  .Modelica.Blocks.Interfaces.RealInput compressor_controller
    annotation (Placement(transformation(extent={{61.58782573549413,35.58782573549413},{70.41217426450588,44.41217426450587}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={-110,100})));
  .BOLT.BoundaryNode.Coolant.Node node_sinkBrine(
    isOff=isOff,
    X=BrineConcentration,
    CoolantMedium=CoolantMedium,
    T_start=LWT)
    annotation (Placement(transformation(extent={{-113.30767109734703,5.331930450505816},{-121.30767109734703,13.331930450505816}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Node node_sourcebrine(
    isOff=isOff,
    X=BrineConcentration,
    CoolantMedium=CoolantMedium,
    T_start=EWT)
    annotation (Placement(transformation(extent={{-64.93730371491128,5.538631221224016},{-72.93730371491128,13.538631221224016}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.InternalLibrary.BuildingBlocks.Coolant.Ports.FluidPort_a port_a(
    Xi_set=BrineConcentration,
    CoolantMedium=CoolantMedium)
    annotation (Placement(transformation(extent={{-50.35124695223881,-4.505220368156785},{-41.64875304776119,4.197273536320836}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={-110,-20})));
  .BOLT.InternalLibrary.BuildingBlocks.Coolant.Ports.FluidPort_b port_b(
    Xi_set=BrineConcentration,
    CoolantMedium=CoolantMedium)
    annotation (Placement(transformation(extent={{-133.40252113301653,-7.840173702834168},{-123.82817990592656,1.7341675242558132}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={116,-20})));
  .Modelica.Blocks.Interfaces.RealInput actual_FSFanSpd_in
    annotation (Placement(transformation(extent={{-3.228709177677082,-3.228709177677075},{3.228709177677082,3.228709177677075}},origin={-19.685967643818884,164.4643568337106},rotation=90.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={-110,20})));
  .BOLT.Evaporator.RefCoolant.EvapBPHE_MVB evapBPHE(
    RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R290,
    isOff=isOff,
    T_a_start_coolant=EWT,
    T_b_start_coolant=LWT,
    p_start_coolant=Water_pressure,
    counterFlow=false,
    isOff_ref=isOff_ref,
    capacity_ref_fixed=false,
    capacity_ref=40000,
    Dport_coolant_a=Dport_coolant_a,
    Dport_coolant_b=Dport_coolant_b,
    Dport_ref_a=Dport_ref_a,
    Dport_ref_b=Dport_ref_b,
    nPlate=nPlate,
    selectGeo=BlackBoxLibrary.GeoSelector.GeoSelector_1C_Evap.ACK74,
    R_foul=EvapFoulingFactor,
    X=BrineConcentration,
    CoolantMedium=CoolantMedium,
    use_Z_UA_expression=true,
    Z_UA_expression=
      if use_Calib then
        calibrationBlock.Z_Evap_HPC
      else
        evapBPHE.Z_UA,
    refBriBPHXBase_MVB(
      manturn_ref_in(
        setOff=evapBPHE.setOff_ref or evapBPHE.isOff)))
    annotation (Placement(transformation(extent={{-100.9277283954552,28.615626253960606},{-80.9277283954552,2.615626253960606}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Condenser.RefAir.CondAir1_MVB condAir(
    RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R290,
    isOff=isOff_ref,
    Z_Ucd=0.9,
    nCoils=nCoils,
    userDefined_geo(
      Itube=Itube,
      nCiri=nCir,
      NRow=Nrow,
      LTube=Ltube,
      Dotube=Dotube,
      Ttube=Ttube,
      Ptube=Ptube,
      PRow=Prow,
      Dfin=Dfin,
      Tfin=Tfin,
      NTube=Ntube),
    use_Z_Ucd_expression=true,
    Z_Ucd_expression=
      if use_Calib then
        calibrationBlock.Z_Cond_HPC
      else
        condAir.Z_Ucd,
    isOffRef=isOff_ref,
    isOffAir=isOff_ref)
    annotation (Placement(transformation(extent={{-14.0,114.0},{-34.0,138.0}},origin={0.0,0.0},rotation=0.0)));
  parameter.Modelica.SIunits.Temperature EWT=285.15;
  parameter.Modelica.SIunits.Temperature LWT=280.15;
  parameter.Modelica.SIunits.Temperature OAT=308.15;
  parameter Real relative_humidity=0.87;
  parameter Boolean isOff_ref=false;
  .BOLT.BoundaryNode.Air.Node node_out_coil_air(
    isOff=isOff_ref)
    annotation (Placement(transformation(extent={{-8.912710487663084,126.99854317721625},{-0.9127104876630838,134.99854317721625}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Air.Node node_out_duct_air(
    isOff=isOff_ref)
    annotation (Placement(transformation(extent={{-4.0,-4.0},{4.0,4.0}},origin={19.75616606007201,138.5044444090297},rotation=90.0)));
  parameter Boolean Use_EN=false;
  parameter Boolean use_Calib=false;
  parameter BOLT.InternalLibrary.Compressors.PD_2Port.SubComponents.PD.DataBase.Polynomial.ARI_VS.Selector selector_Comp
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Real capacity_design
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Real CompVoltage
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Real fmax
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Real fmin
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter BOLT.InternalLibrary.Refrigerant.Valves.TwoPort.DataBase.Selector EXV_main
    annotation (Dialog(group="EXV",tab="Unit Characteristics"));
  parameter Real Dport_coolant_a
    annotation (Dialog(group="BPHE",tab="Unit Characteristics"));
  parameter Real Dport_coolant_b
    annotation (Dialog(group="BPHE",tab="Unit Characteristics"));
  parameter Real Dport_ref_a
    annotation (Dialog(group="BPHE",tab="Unit Characteristics"));
  parameter Real Dport_ref_b
    annotation (Dialog(group="BPHE",tab="Unit Characteristics"));
  parameter Integer nPlate
    annotation (Dialog(group="BPHE",tab="Unit Characteristics"));
  parameter Real EvapFoulingFactor
    annotation (Dialog(group="BPHE",tab="Unit Characteristics"));
  parameter.BlackBoxLibrary.GeoSelector.GeoSelector_1C_Cond selector_geo_BPHE
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Integer nCoils
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Integer Itube
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Integer nCir
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Integer Ntube
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Integer Nrow
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Ltube
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Dotube
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Ttube
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Ptube
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Prow
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Dfin
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Tfin
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Boolean isCoating
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter BOLT.InternalLibrary.Coolant.Pumps.DataBase.PumpPoly.Selector selector_pump
    annotation (Dialog(group="Pump",tab="Unit Characteristics"));
  parameter Real Fw_fan
    annotation (Dialog(group="Fan",tab="Unit Characteristics"));
  parameter Real max_fan_frequency
    annotation (Dialog(group="Fan",tab="Unit Characteristics"));
  parameter Real[9] fanCurveCoefficientsCooling
    annotation (Dialog(group="Fan",tab="Unit Characteristics"));
  parameter Real[9] fanCurveCoefficientsHeating
    annotation (Dialog(group="Fan",tab="Unit Characteristics"));
  parameter Real Suction_line_diameter
    annotation (Dialog(group="Suction Line",tab="Unit Characteristics"));
  parameter Real Suction_line_length
    annotation (Dialog(group="Suction Line",tab="Unit Characteristics"));
  parameter Real coil_line_diameter
    annotation (Dialog(group="Coil Line",tab="Unit Characteristics"));
  parameter Real coil_line_length
    annotation (Dialog(group="Coil Line",tab="Unit Characteristics"));
  parameter Real liquid_line_diameter
    annotation (Dialog(group="Liquid Line",tab="Unit Characteristics"));
  parameter Real liquid_line_length
    annotation (Dialog(group="Liquid Line",tab="Unit Characteristics"));
  parameter Real discharge_line_diameter
    annotation (Dialog(group="Discharge Line",tab="Unit Characteristics"));
  parameter Real discharge_line_length
    annotation (Dialog(group="Discharge Line",tab="Unit Characteristics"));
  parameter Real EXV_in_line_diameter
    annotation (Dialog(group="EXV in Line",tab="Unit Characteristics"));
  parameter Real EXV_in_line_length
    annotation (Dialog(group="EXV in Line",tab="Unit Characteristics"));
  parameter Real Ac_duct
    annotation (Dialog(group="Air Duct",tab="Unit Characteristics"));
  parameter Real Ka_duct
    annotation (Dialog(group="Air Duct",tab="Unit Characteristics"));
  parameter Real UA_duct
    annotation (Dialog(group="Air Duct",tab="Unit Characteristics"));
  parameter Real PDC_4WV
    annotation (Dialog(group="4WV",tab="Unit Characteristics"));
  parameter Real Zevap_HPH_cst
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_HPH_SST
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_HPH_Ncomp
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_HPH_heatcap
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_coated_HPH
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_HPC_cst
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_HPC_heatcap
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_HPC_SST
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zcond_HPH_cst
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPH_SDT
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPH_heatcap
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPH_SST
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPC_cst
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPC_DGT
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPC_Ncomp
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPC_SDT
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zflow_intercept
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zflow_Ncomp
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zflow_SST
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zflow_SDT
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zflow_heatcap
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zpower_intercept
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zpower_SH
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zpower_DGT
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zpower_SST
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zpower_Zflow
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real FW
    annotation (Dialog(group="compressor",tab="Calibration"));
  .Workspace.Auxiliary.Calibration.CalibrationBlock calibrationBlock(
    Zpower_intercept=Zpower_intercept,
    Zpower_SH=Zpower_SH,
    Zpower_DGT=Zpower_DGT,
    Zpower_SST=Zpower_SST,
    Zpower_Zflow=Zpower_Zflow,
    Zflow_intercept=Zflow_intercept,
    Zflow_Ncomp=Zflow_Ncomp,
    Zflow_SDT=Zflow_SDT,
    Zflow_SST=Zflow_SST,
    Zflow_heatcap=Zflow_heatcap,
    Zevap_HPC_cst=Zevap_HPC_cst,
    Zevap_HPC_heatcap=Zevap_HPC_heatcap,
    Zevap_HPC_SST=Zevap_HPC_SST,
    Zevap_HPH_cst=Zevap_HPH_cst,
    Zevap_HPH_heatcap=Zevap_HPH_heatcap,
    Zevap_HPH_Ncomp=Zevap_HPH_Ncomp,
    Zevap_HPH_SST=Zevap_HPH_SST,
    Zcond_HPC_cst=Zcond_HPC_cst,
    Zcond_HPC_DGT=Zcond_HPC_DGT,
    Zcond_HPC_Ncomp=Zcond_HPC_Ncomp,
    Zcond_HPC_SDT=Zcond_HPC_SDT,
    Zcond_HPH_cst=Zcond_HPH_cst,
    Zcond_HPH_heatcap=Zcond_HPH_heatcap,
    Zcond_HPH_SST=Zcond_HPH_SST,
    Zcond_HPH_SDT=Zcond_HPH_SDT,
    Zcoated=0.8)
    annotation (Placement(transformation(extent={{-139.83712423865995,174.16287576134005},{-116.16287576134005,197.83712423865995}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression SST(
    y=node_suction.summary.Tsat)
    annotation (Placement(transformation(extent={{-180.88015545719892,215.11984454280108},{-171.11984454280108,224.88015545719892}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression SDT(
    y=node_discharge.summary.Tsat)
    annotation (Placement(transformation(extent={{-180.88015545719892,207.11984454280108},{-171.11984454280108,216.88015545719892}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression Q_flow(
    y=evapBPHE.summary.Q_flow_ref)
    annotation (Placement(transformation(extent={{-180.88015545719892,183.11984454280108},{-171.11984454280108,192.88015545719892}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression Ncomp(
    y=compressor.summary.Ncomp)
    annotation (Placement(transformation(extent={{-180.88015545719892,201.11984454280108},{-171.11984454280108,210.88015545719892}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression DGT(
    y=node_discharge.summary.T)
    annotation (Placement(transformation(extent={{-180.88015545719892,191.11984454280108},{-171.11984454280108,200.88015545719892}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression SH(
    y=node_suction.summary.dTsh)
    annotation (Placement(transformation(extent={{-180.88015545719892,197.11984454280108},{-171.11984454280108,206.88015545719892}},origin={0.0,0.0},rotation=0.0)));
  parameter Real Water_pressure=200000
    annotation (Dialog(group="Medium"));
  parameter Real ssh_setPoint=5
    "circuit A ssh set point"
    annotation (Dialog(group="Refrigerant"));
  parameter.Modelica.SIunits.TemperatureDifference SC_setpoint=-2
    annotation (Dialog(group="Refrigerant"));
  parameter Boolean SC_fixed=true
    annotation (Dialog(group="Refrigerant"));
  parameter Boolean Mref_fixed=false
    annotation (Dialog(group="Refrigerant"));
  parameter.Modelica.SIunits.Mass Mref=5
    annotation (Dialog(group="Refrigerant"));
protected
  parameter.Modelica.SIunits.Frequency max_motor_frequency_STD=39.5
    "maximum motor frequency when not low noise fan";
  parameter.Modelica.SIunits.Conversions.NonSIunits.AngularVelocity_rpm max_fan_rpm_STD=720
    "maximum fan speed when not low noise fan";
public
  .BOLT.BoundaryNode.Refrigerant.Node node_discharge(
    RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R290,
    isOff=isOff_ref)
    annotation (Placement(transformation(extent={{88.0,14.0},{96.0,22.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Refrigerant.Node node_suction(
    dTsh_fixed=false,
    RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R290,
    isOff=isOff_ref)
    annotation (Placement(transformation(extent={{38.0,14.0},{46.0,22.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Refrigerant.Node node_valveout(
    RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R290,
    isOff=isOff_ref)
    annotation (Placement(transformation(extent={{-4.0,-4.0},{4.0,4.0}},origin={-114.0,32.0},rotation=-90.0)));
  .BOLT.Valve.Refrigerant.ValveEXV valveEXV(
    RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R290,
    Tsat_a_start=globalParameters.Tsat_cond_start,
    use_actuator_in=true,
    isOff=isOff_ref,
    selector_flowData=EXV_main)
    annotation (Placement(transformation(extent={{-6,-6},{6,6}},origin={-114,72},rotation=-90)));
  inner.BOLT.GlobalParameters globalParameters(
    T_ambient=OAT,
    RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R290,
    Tsat_cond_start=max(
      300,
      globalParameters.T_ambient+15),
    varLevel=.BOLT.InternalLibrary.BuildingBlocks.Types.Var_level.Advanced,
    capacity_design=capacity_design,
    capacity_start=globalParameters.capacity_design/(2),
    EvapCoolantMedium=CoolantMedium,
    Evap_X=BrineConcentration,
    Tsat_evap_start=LWT-5)
    annotation (Placement(transformation(extent={{2.0,-28.0},{22.0,-8.0}},origin={0.0,0.0},rotation=0.0)));
  inner.BOLT.InternalLibrary.Refrigerant.Aggregation.AggregateStreams_2 systemVariables(
    fan_ducted=false,
    integrated_pump_in=false,
    nStreams=1,
    isOff={isOff},
    mRef_set={Mref},
    mRef_fixed={Mref_fixed})
    annotation (Placement(transformation(extent={{34.49674749972682,-28.0},{54.49674749972682,-8.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Air.Source sourceAir(
    Vds_flow_fixed=false,
    pg_fixed=true,
    Tdb_set=OAT,
    isOff=isOff_ref,
    RH_fixed=true,
    RH_set=relative_humidity)
    annotation (Placement(transformation(extent={{5.43441899229564,-5.434418992295633},{-5.43441899229564,5.434418992295633}},origin={-47.37858637245692,173.20648888241539},rotation=90.0)));
  .BOLT.BoundaryNode.Air.Sink sinkAir(
    Vds_flow_fixed=false,
    pg_fixed=true,
    Vds_flow_set=4.2,
    isOff=isOff_ref)
    annotation (Placement(transformation(extent={{-4.776077345930645,-4.776077345930645},{4.776077345930645,4.776077345930645}},origin={21.271895878131872,172.94496758576295},rotation=-90.0)));
  .BOLT.BoundaryNode.Refrigerant.Node node(
    RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R290,
    dTsh_fixed=false,
    dTsh_set=-5,
    isOff=isOff_ref)
    annotation (Placement(transformation(extent={{-54,124},{-62,132}},origin={0,0},rotation=0)));
  .BOLT.Mechanical.Motor motor(
    selector_AC=.BOLT.InternalLibrary.Mechanicals.Motor.DataBase.MotorCurveMap.AC_motor.Selector.LEROYSOMER_4860698,
    shaftSpeed_set=720,
    MotorHz=37.5,
    MotorHz_fixed=false,
    use_scaling_motor_in=false,
    use_shaftSpeed_in=true,
    use_frequency_in=false,
    isOff=isOff_ref,
    Mtrfreq_start=37.5,
    FanMotType=BOLT.InternalLibrary.Mechanicals.Motor.Interfaces.FanMotType.EC_FanMot,
    selector_EC=BOLT.InternalLibrary.Mechanicals.Motor.DataBase.MotorCurveMap.EC_motor.Selector.EBM_M3G150_IF25_A5,
    Z_power=0.85)
    annotation (Placement(transformation(extent={{-4.561700292289942,-5.017870321518927},{4.561700292289942,5.017870321518927}},origin={-12.0,148.0},rotation=-90.0)));
  .BOLT.AirMisc.FanCurve fanCurve(
    selector_curve=BOLT.InternalLibrary.Air.Fans.DataBase.Selector.ZN080_ZIQ,
    nStage=0.5,
    dp_start=50,
    isOff=isOff_ref,
    userDefined_curve(
      dp_curve={{0},{43},{81},{115},{147},{173},{200},{224},{224},{224},{224}},
      V_curve={{6.66},{6.3},{5.95},{5.6},{5.23},{4.86},{4.5},{4.11},{4.11},{4.11},{4.11}},
      P_curve={{1360},{1487},{1580},{1656},{1711},{1749},{1785},{1823},{1823},{1823},{1823}}),
    Fw=Fw_fan)
    annotation (Placement(transformation(extent={{-6.84841519774433,-6.848415197744345},{6.84841519774433,6.848415197744345}},origin={20.550443815142522,155.34091453241732},rotation=90.0)));
  .BOLT.RefMisc.ReducedPipe liquid_line(
    RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R290,
    Tsat_a_start=globalParameters.Tsat_cond_start,
    x_a_start=-0.1,
    isOff=isOff_ref,
    Di=EXV_in_line_diameter,
    length=EXV_in_line_length,
    use_k_dp=true)
    annotation (Placement(transformation(extent={{-6.0,-6.599999999999994},{6.0,6.599999999999994}},origin={-82.0,128.0},rotation=-180.0)));
  .BOLT.RefMisc.ReducedPipe suction_line(
    RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R290,
    Tsat_a_start=globalParameters.Tsat_evap_start,
    x_a_start=1,
    isOff=isOff_ref,
    k_dp=0.1,
    Di=Suction_line_diameter,
    length=Suction_line_length,
    use_k_dp=true)
    annotation (Placement(transformation(extent={{16.036525022476276,11.399999999999999},{28.036525022476276,24.6}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Refrigerant.Node node_evapout(
    RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R290,
    isOff=isOff_ref)
    annotation (Placement(transformation(extent={{-65.75,14.75},{-57.75,22.75}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Refrigerant.Node node_liquid(
    RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R290,
    dTsh_fixed=SC_fixed,
    dTsh_set=SC_setpoint,
    isOff=isOff_ref)
    annotation (Placement(transformation(extent={{-100.0,124.0},{-108.0,132.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Valve.Refrigerant.ReversingValve reversingValve(
    use_k_dp=false,
    selector_valveData=.BOLT.InternalLibrary.Refrigerant.Valves.FourPort.DataBase.Selector.userDefined,
    use_leak=false,
    RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R290,
    Tsat_high_start=globalParameters.Tsat_cond_start,
    Tsat_low_start=globalParameters.Tsat_evap_start,
    isOff=isOff_ref,
    redeclare replaceable.BOLT.Valve.Refrigerant.UserDefinedBase.ReversingValve_valveData userDefined_valveData(
      m_flow_leak_ref=1.56e-4,
      dp_low_ref=PDC_4WV))
    annotation (Placement(transformation(extent={{-10.0,10.0},{10.0,-10.0}},origin={36.0,62.0},rotation=90.0)));
  .BOLT.BoundaryNode.Refrigerant.Node node_4VW_HP_out(
    RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R290,
    isOff=isOff_ref)
    annotation (Placement(transformation(extent={{-4,-4},{4,4}},origin={36,82},rotation=90)));
  .BOLT.BoundaryNode.Refrigerant.Node node_condin(
    RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R290,
    isOff=isOff_ref)
    annotation (Placement(transformation(extent={{-4.0,-3.999999999999993},{4.0,3.999999999999993}},origin={4.0,124.90598080396364},rotation=180.0)));
  .BOLT.RefMisc.ReducedPipe Coil_line(
    RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R290,
    Tsat_a_start=globalParameters.Tsat_cond_start,
    x_a_start=1.1,
    isOff=isOff_ref,
    Di=coil_line_diameter,
    length=coil_line_length,
    use_k_dp=true)
    annotation (Placement(transformation(extent={{6.0,-6.599999999999994},{-6.0,6.599999999999994}},origin={35.53683063404505,106.93284343140262},rotation=-90.0)));
  .BOLT.BoundaryNode.Refrigerant.Node node_4WV_LP_out(
    RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R290,
    isOff=isOff_ref)
    annotation (Placement(transformation(extent={{-4,-4},{4,4}},origin={12,38},rotation=-90)));
  .BOLT.AirMisc.Duct duct(
    Ac=Ac_duct,
    Ka=Ka_duct,
    isOff=isOff_ref,
    UA=UA_duct)
    annotation (Placement(transformation(extent={{6.049852116230479,125.7305311275924},{15.805604674679994,135.4862836860419}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.MeasurementBus measurementBus
    annotation (Placement(transformation(extent={{135.34942040500349,110.82049136455792},{162.2277663744487,137.6988373340031}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={110,60})));
  .Modelica.Blocks.Sources.RealExpression dT_ssh(
    y=node_suction.summary.dTsh)
    annotation (Placement(transformation(extent={{77.86673549370565,154.19770318718042},{90.01379235021548,166.3447600436902}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_sdt(
    y=node_discharge.summary.Tsat)
    annotation (Placement(transformation(extent={{77.9311308879046,142.7092615287291},{90.07818774441438,154.85631838523886}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_dgt(
    y=node_discharge.summary.T)
    annotation (Placement(transformation(extent={{77.9311308879046,125.70338192961536},{90.07818774441438,137.85043878612515}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_sst(
    y=node_suction.summary.Tsat)
    annotation (Placement(transformation(extent={{77.649102444865,134.2063217291722},{89.7961593013748,146.35337858568204}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_ewt(
    y=node_sourcebrine.summary.T)
    annotation (Placement(transformation(extent={{77.9311308879046,117.48247057309808},{90.07818774441438,129.6295274296079}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_lwt(
    y=node_sinkBrine.summary.T)
    annotation (Placement(transformation(extent={{77.9311308879046,109.91220801615262},{90.07818774441438,122.05926487266242}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_oat(
    y=sourceAir.Tdb_set)
    annotation (Placement(transformation(extent={{77.9311308879046,101.40926821659575},{90.07818774441438,113.55632507310555}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression compressor_frequency(
    y=compressor.summary.Ncomp)
    annotation (Placement(transformation(extent={{77.90168347734334,91.48733132061933},{90.04874033385315,103.63438817712914}},origin={0.0,0.0},rotation=0.0)));
  // Declaration of Coolant Medium Package
  //package CoolantCommonMedium =
  //.BOLT.InternalLibrary.Media.Coolant.CoolantCommon                             "Coolant Medium Package" annotation ();
  // Declaration of Coolant Medium at Evaporator Circuit
  parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector CoolantMedium=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector.FW
    "Coolant Medium Selection"
    annotation (Dialog(group="Medium"));
  final parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Empty coolantInf=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.getSelector(
    CoolantMedium)
    "Coolant Medium"
    annotation ();
  CoolantCommonMedium.Temperature FreezTemp=CoolantCommonMedium.freezeTemp(
    coolantInf,
    BrineConcentration,
    1);
  parameter Real BrineConcentration=0.2
    annotation (Dialog(group="Medium"));
equation
  connect(valveEXV.port_b,node_valveout.port_a)
    annotation (Line(points={{-114,66},{-114,36}},color={255,0,0}));
  connect(motor.ShaftOut,fanCurve.flange)
    annotation (Line(points={{-6.982129678481073,149.18604207599537},{13.702028617398177,149.18604207599537},{13.702028617398177,155.34091453241732}},color={127,0,0}));
  connect(node.port_b,liquid_line.port_a)
    annotation (Line(points={{-62,128},{-76,128}},color={255,0,0}));
  connect(liquid_line.port_b,node_liquid.port_a)
    annotation (Line(points={{-88,128},{-100,128}},color={255,0,0}));
  connect(suction_line.port_b,node_suction.port_a)
    annotation (Line(points={{28.036525022476276,18},{38,18}},color={255,0,0}));
  connect(reversingValve.port_C,node_4VW_HP_out.port_a)
    annotation (Line(points={{36,71},{36,78}},color={255,0,0}));
  connect(reversingValve.port_S,node_4WV_LP_out.port_a)
    annotation (Line(points={{27,62},{12,62},{12,42}},color={255,0,0}));
  connect(node_4WV_LP_out.port_b,suction_line.port_a)
    annotation (Line(points={{12,34},{12,18},{16.036525022476276,18}},color={255,0,0}));
  connect(node_4VW_HP_out.port_b,Coil_line.port_a)
    annotation (Line(points={{36,86},{36,93.4664217157013},{35.53683063404505,93.4664217157013},{35.53683063404505,100.93284343140262}},color={255,0,0}));
  connect(Coil_line.port_b,node_condin.port_a)
    annotation (Line(points={{35.53683063404505,112.93284343140262},{35.53683063404505,124.90598080396364},{8,124.90598080396364}},color={255,0,0}));
  connect(EXV_controller,valveEXV.openCommand)
    annotation (Line(points={{-98.90048586332446,71.31121908073875},{-110.1,71.31121908073875},{-110.1,72}},color={0,0,127}));
  connect(compressor_controller,compressor.N_speed)
    annotation (Line(points={{66,40},{66,47.63192154306077},{58.48,47.63192154306077},{58.48,28.178573167468}},color={0,0,127}));
  connect(node_suction.port_b,compressor.port_a)
    annotation (Line(points={{46,18},{54,18},{54,18.378573167467998}},color={255,0,0}));
  connect(compressor.port_b,node_discharge.port_a)
    annotation (Line(points={{82,18.378573167467998},{82,18},{88,18}},color={255,0,0}));
  connect(node_liquid.port_b,valveEXV.port_a)
    annotation (Line(points={{-108,128},{-114,128},{-114,78}},color={255,0,0}));
  connect(motor.speed_in,actual_FSFanSpd_in)
    annotation (Line(points={{-14.965105189988458,152.88101931275023},{-14.965105189988458,164.4643568337106},{-19.685967643818884,164.4643568337106}},color={0,0,127}));
  connect(evapBPHE.port_ref_b,node_evapout.port_a)
    annotation (Line(points={{-81.0277283954552,15.715626253960606},{-65.75,15.715626253960606},{-65.75,18.75}},color={255,0,0}));
  connect(node_valveout.port_b,evapBPHE.port_ref_a)
    annotation (Line(points={{-114,28},{-114,15.715626253960606},{-101.0277283954552,15.715626253960606}},color={255,0,0}));
  connect(node_condin.port_b,condAir.port_a_ref)
    annotation (Line(points={{0,124.90598080396366},{-7.1,124.90598080396366},{-7.1,125.2},{-14.2,125.2}},color={255,0,0}));
  connect(condAir.port_b_ref,node.port_a)
    annotation (Line(points={{-34.2,125.2},{-44.1,125.2},{-44.1,128},{-54,128}},color={255,0,0}));
  connect(sourceAir.port,condAir.port_a_air)
    annotation (Line(points={{-47.37858637245692,167.77206989011975},{-47.37858637245692,130.8},{-34,130.8}},color={0,0,255}));
  connect(sinkAir.port,fanCurve.port_b)
    annotation (Line(points={{21.271895878131872,168.1688902398323},{21.271895878131872,162.18932973016166},{20.550443815142522,162.18932973016166}},color={0,0,255}));
  connect(dT_ssh.y,measurementBus.dT_ssh)
    annotation (Line(points={{90.62114519304097,160.2712316154353},{122.21225140356657,160.2712316154353},{122.21225140356657,124.25966434928051},{148.7885933897261,124.25966434928051}},color={0,0,127}));
  connect(T_sdt.y,measurementBus.T_sdt)
    annotation (Line(points={{90.68554058723987,148.78278995698398},{121.86132066245338,148.78278995698398},{121.86132066245338,124.25966434928051},{148.7885933897261,124.25966434928051}},color={0,0,127}));
  connect(T_sst.y,measurementBus.T_sst)
    annotation (Line(points={{90.40351214420028,140.27985015742712},{121.86132066245338,140.27985015742712},{121.86132066245338,124.25966434928051},{148.7885933897261,124.25966434928051}},color={0,0,127}));
  connect(T_dgt.y,measurementBus.T_dgt)
    annotation (Line(points={{90.68554058723987,131.77691035787026},{121.86132066245338,131.77691035787026},{121.86132066245338,124.25966434928051},{148.7885933897261,124.25966434928051}},color={0,0,127}));
  connect(T_ewt.y,measurementBus.T_ewt)
    annotation (Line(points={{90.68554058723987,123.55599900135299},{121.86132066245338,123.55599900135299},{121.86132066245338,124.25966434928051},{148.7885933897261,124.25966434928051}},color={0,0,127}));
  connect(T_lwt.y,measurementBus.T_lwt)
    annotation (Line(points={{90.68554058723987,115.98573644440752},{121.86132066245338,115.98573644440752},{121.86132066245338,124.25966434928051},{148.7885933897261,124.25966434928051}},color={0,0,127}));
  connect(T_oat.y,measurementBus.T_oat)
    annotation (Line(points={{90.68554058723987,107.48279664485065},{121.86132066245338,107.48279664485065},{121.86132066245338,124.25966434928051},{148.7885933897261,124.25966434928051}},color={0,0,127}));
  connect(compressor_frequency.y,measurementBus.compressorFrequency)
    annotation (Line(points={{90.65609317667864,97.56085974887424},{122.28859338972609,97.56085974887424},{122.28859338972609,124.25966434928051},{148.7885933897261,124.25966434928051}},color={0,0,127}));
  connect(condAir.port_b_air,node_out_coil_air.port_a)
    annotation (Line(points={{-12.6,131.4},{-12.6,130.99854317721625},{-8.912710487663084,130.99854317721625}},color={0,0,255}));
  connect(node_out_coil_air.port_b,duct.port_a)
    annotation (Line(points={{-0.9127104876630838,130.99854317721625},{6.049852116230479,130.99854317721625},{6.049852116230479,130.60840740681715}},color={0,0,255}));
  connect(duct.port_b,node_out_duct_air.port_a)
    annotation (Line(points={{15.805604674679994,130.60840740681715},{19.75616606007201,130.60840740681715},{19.75616606007201,134.5044444090297}},color={0,0,255}));
  connect(node_out_duct_air.port_b,fanCurve.port_a)
    annotation (Line(points={{19.75616606007201,142.5044444090297},{19.75616606007201,148.49249933467297},{20.550443815142522,148.49249933467297}},color={0,0,255}));
  connect(node_discharge.port_b,reversingValve.port_D)
    annotation (Line(points={{96,18},{102,18},{102,62},{45,62}},color={255,0,0}));
  connect(node_evapout.port_b,reversingValve.port_E)
    annotation (Line(points={{-57.75,18.75},{-57.75,30},{36,30},{36,53}},color={255,0,0}));
  connect(node_sourcebrine.port_b,evapBPHE.port_coolant_a)
    annotation (Line(points={{-72.93730371491128,9.538631221224016},{-76.32167316231909,9.538631221224016},{-76.32167316231909,9.515626253960605},{-81.0277283954552,9.515626253960605}},color={0,127,0}));
  connect(evapBPHE.port_coolant_b,node_sinkBrine.port_a)
    annotation (Line(points={{-101.2277283954552,9.515626253960605},{-107.26769974640112,9.515626253960605},{-107.26769974640112,9.331930450505816},{-113.30767109734703,9.331930450505816}},color={0,127,0}));
  connect(port_a,node_sourcebrine.port_a)
    annotation (Line(points={{-46,-0.15397341591797442},{-54.807808964591494,-0.15397341591797442},{-54.807808964591494,9.538631221224016},{-64.93730371491128,9.538631221224016}},color={0,127,0}));
  connect(node_sinkBrine.port_b,port_b)
    annotation (Line(points={{-121.30767109734703,9.331930450505816},{-125.48325012193953,9.331930450505816},{-125.48325012193953,-3.0530030892891773},{-128.61535051947155,-3.0530030892891773}},color={0,127,0}));
  connect(SDT.y,calibrationBlock.SDT)
    annotation (Line(points={{-170.6318289970812,212},{-160,212},{-160,194.99621442138155},{-137.23295690615475,194.99621442138155}},color={0,0,127}));
  connect(Ncomp.y,calibrationBlock.Ncomp)
    annotation (Line(points={{-170.6318289970812,206},{-160,206},{-160,189.55113727159798},{-137.23295690615475,189.55113727159798}},color={0,0,127}));
  connect(Q_flow.y,calibrationBlock.Q_flow)
    annotation (Line(points={{-170.6318289970812,188},{-160,188},{-160,184.5795450913608},{-137.23295690615475,184.5795450913608}},color={0,0,127}));
  connect(SST.y,calibrationBlock.SST)
    annotation (Line(points={{-170.6318289970812,220},{-160,220},{-160,202},{-137.23295690615475,202},{-137.23295690615475,196.78362018141922}},color={0,0,127}));
  connect(calibrationBlock.DGT,DGT.y)
    annotation (Line(points={{-137.23295690615475,186.2367424847732},{-153.93239295161797,186.2367424847732},{-153.93239295161797,196},{-170.6318289970812,196}},color={0,0,127}));
  connect(SH.y,calibrationBlock.SH)
    annotation (Line(points={{-170.6318289970812,202},{-153.93239295161797,202},{-153.93239295161797,187.8939398781856},{-137.23295690615475,187.8939398781856}},color={0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          fillColor={117,117,117},
          fillPattern=FillPattern.Solid,
          extent={{-103,-104},{103,104}},
          origin={3,4}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end OL;
