within Workspace.Icons;
partial model InitializationValve
  //extends VFD30XW.Icons.InitializationCycle2;
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100,-100},{100,100}}),
      graphics={
        Text(
          extent={{-132,-134},{136,-152}},
          lineColor={0,0,0},
          fillColor={255,0,0},
          fillPattern=FillPattern.Solid,
          textString="%name"),
        Polygon(
          points={{-80,80},{80,80},{0,0},{-80,80}},
          lineColor={0,0,0},
          smooth=Smooth.None,
          fillColor={255,0,0},
          fillPattern=FillPattern.Solid),
        Polygon(
          points={{-80,-80},{80,-80},{0,0},{-80,-80}},
          lineColor={0,0,0},
          smooth=Smooth.None,
          fillColor={255,0,0},
          fillPattern=FillPattern.Solid)}));
end InitializationValve;
