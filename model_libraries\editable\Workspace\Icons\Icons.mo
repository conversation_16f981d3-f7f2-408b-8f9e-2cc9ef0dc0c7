within Workspace.Icons;
partial class Icons
  extends Workspace.Icons.PackageIcon;
equation
  annotation (
    Icon(
      graphics={
        Rectangle(
          extent={{-60,48},{-16,8}},
          lineColor={0,0,0},
          fillColor={0,0,255},
          fillPattern=FillPattern.Solid),
        Rectangle(
          extent={{18,48},{62,8}},
          lineColor={0,0,0},
          fillColor={255,0,0},
          fillPattern=FillPattern.Solid),
        Rectangle(
          extent={{-60,-22},{-16,-62}},
          lineColor={0,0,0},
          fillColor={0,255,0},
          fillPattern=FillPattern.Solid),
        Rectangle(
          extent={{18,-22},{62,-62}},
          lineColor={0,0,0},
          fillColor={255,255,0},
          fillPattern=FillPattern.Solid)}));
end Icons;
