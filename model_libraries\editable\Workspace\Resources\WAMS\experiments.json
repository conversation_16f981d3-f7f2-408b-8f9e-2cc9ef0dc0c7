{"Workspace.System_ASHP_R290.HPH.BiBloc_with_EVI.BaseCycles.OL_modular": [{"created": "2023-03-09T13:30:31.344Z", "lastModified": "2023-03-09T13:30:31.344Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.System_ASHP_R290.HPH.BiBloc_with_EVI.BaseCycles.OL_modular", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "<PERSON><PERSON>"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-03-15T14:36:23.955Z", "lastModified": "2023-03-15T14:36:23.955Z", "name": "Experiment 2", "expressions": [], "model": "Workspace.System_ASHP_R290.HPH.BiBloc_with_EVI.BaseCycles.OL_modular", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-04-11T10:55:46.224Z", "lastModified": "2023-04-11T10:55:46.224Z", "name": "Experiment 3", "expressions": [], "model": "Workspace.System_ASHP_R290.HPH.BiBloc_with_EVI.BaseCycles.OL_modular", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "Test_EN14511"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-06-14T08:55:57.773Z", "lastModified": "2023-06-14T08:55:57.773Z", "name": "Experiment 4", "expressions": [], "model": "Workspace.System_ASHP_R290.HPH.BiBloc_with_EVI.BaseCycles.OL_modular", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-06-14T09:12:03.768Z", "lastModified": "2023-06-14T09:12:03.768Z", "name": "Experiment 5", "expressions": [], "model": "Workspace.System_ASHP_R290.HPH.BiBloc_with_EVI.BaseCycles.OL_modular", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": true}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.System_ASHP_R290.HPC.BiBloc.BaseCycles.OL_with_actuator": [{"created": "2023-03-15T06:59:27.551Z", "lastModified": "2023-03-15T06:59:27.551Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.System_ASHP_R290.HPC.BiBloc.BaseCycles.OL_with_actuator", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "HPC"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-03-15T07:02:40.751Z", "lastModified": "2023-03-15T07:02:40.751Z", "name": "Experiment 2", "expressions": [], "model": "Workspace.System_ASHP_R290.HPC.BiBloc.BaseCycles.OL_with_actuator", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "HPC"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.System_ASHP_R290.HPC.BiBloc.BaseCycles.OL_modular": [{"created": "2023-03-15T08:38:39.334Z", "lastModified": "2023-03-15T08:38:39.334Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.System_ASHP_R290.HPC.BiBloc.BaseCycles.OL_modular", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "HPC"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-03-15T10:11:26.496Z", "lastModified": "2023-03-15T10:11:26.496Z", "name": "Experiment 2", "expressions": [], "model": "Workspace.System_ASHP_R290.HPC.BiBloc.BaseCycles.OL_modular", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-03-15T12:11:25.450Z", "lastModified": "2023-03-15T12:11:25.450Z", "name": "Experiment 3", "expressions": [], "model": "Workspace.System_ASHP_R290.HPC.BiBloc.BaseCycles.OL_modular", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-04-12T13:39:54.784Z", "lastModified": "2023-04-12T13:39:54.784Z", "name": "Experiment 4", "expressions": [], "model": "Workspace.System_ASHP_R290.HPC.BiBloc.BaseCycles.OL_modular", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iGLBL", "value": false}, {"name": "iPRVS", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.System_ASHP_R290.HPC.MonoBloc.BaseCycles.system_zenith": [{"created": "2023-04-21T11:24:26.262Z", "lastModified": "2023-04-21T11:24:26.262Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.System_ASHP_R290.HPC.MonoBloc.BaseCycles.system_zenith", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.System_ASHP_R290.HPH.BiBloc_with_EVI.BaseCycles.OL_modular_wo_EVI": [{"created": "2023-06-14T08:38:25.547Z", "lastModified": "2023-06-14T08:38:25.547Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.System_ASHP_R290.HPH.BiBloc_with_EVI.BaseCycles.OL_modular_wo_EVI", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-06-14T08:48:32.499Z", "lastModified": "2023-06-14T08:48:32.499Z", "name": "Experiment 2", "expressions": [], "model": "Workspace.System_ASHP_R290.HPH.BiBloc_with_EVI.BaseCycles.OL_modular_wo_EVI", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "dynamic"}, "filters": [], "isDefault": false}, {"created": "2023-06-14T09:19:39.026Z", "lastModified": "2023-06-14T09:19:39.026Z", "name": "Experiment 3", "expressions": [], "model": "Workspace.System_ASHP_R290.HPH.BiBloc_with_EVI.BaseCycles.OL_modular_wo_EVI", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-06-14T11:57:54.767Z", "lastModified": "2023-06-14T11:57:54.767Z", "name": "Experiment 4", "expressions": [], "model": "Workspace.System_ASHP_R290.HPH.BiBloc_with_EVI.BaseCycles.OL_modular_wo_EVI", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-06-14T12:41:59.372Z", "lastModified": "2023-06-14T12:41:59.372Z", "name": "Experiment 5", "expressions": [], "model": "Workspace.System_ASHP_R290.HPH.BiBloc_with_EVI.BaseCycles.OL_modular_wo_EVI", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.System_ASHP_R290.HPC.System_model.BaseCycles.OL_modular": [{"created": "2023-06-16T06:38:12.969Z", "lastModified": "2023-06-16T06:38:12.969Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.System_ASHP_R290.HPC.System_model.BaseCycles.OL_modular", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-21T09:00:38.067Z", "lastModified": "2023-07-21T09:00:38.067Z", "name": "Experiment 2", "expressions": [], "model": "Workspace.System_ASHP_R290.HPC.System_model.BaseCycles.OL_modular", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "1131243"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-21T09:09:29.325Z", "lastModified": "2023-07-21T09:09:29.325Z", "name": "Experiment 3", "expressions": [], "model": "Workspace.System_ASHP_R290.HPC.System_model.BaseCycles.OL_modular", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-21T09:16:54.463Z", "lastModified": "2023-07-21T09:16:54.463Z", "name": "Experiment 4", "expressions": [], "model": "Workspace.System_ASHP_R290.HPC.System_model.BaseCycles.OL_modular", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-21T09:24:55.100Z", "lastModified": "2023-07-21T09:24:55.100Z", "name": "Experiment 5", "expressions": [], "model": "Workspace.System_ASHP_R290.HPC.System_model.BaseCycles.OL_modular", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-21T09:41:17.858Z", "lastModified": "2023-07-21T09:41:17.858Z", "name": "Experiment 6", "expressions": [], "model": "Workspace.System_ASHP_R290.HPC.System_model.BaseCycles.OL_modular", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-21T10:37:42.319Z", "lastModified": "2023-07-21T10:37:42.319Z", "name": "Experiment 7", "expressions": [], "model": "Workspace.System_ASHP_R290.HPC.System_model.BaseCycles.OL_modular", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-21T10:45:29.483Z", "lastModified": "2023-07-21T10:45:29.483Z", "name": "Experiment 8", "expressions": [], "model": "Workspace.System_ASHP_R290.HPC.System_model.BaseCycles.OL_modular", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.OL_MODULAR_RIFAI": [{"created": "2023-06-21T08:58:38.936Z", "lastModified": "2023-06-21T08:58:38.936Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.OL_MODULAR_RIFAI", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "OL"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-06-21T09:12:48.182Z", "lastModified": "2023-06-21T09:12:48.182Z", "name": "Experiment 2", "expressions": [], "model": "Workspace.OL_MODULAR_RIFAI", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Sync_To_Existing_Workbook", "value": true}, {"name": "Workbook_Name", "value": "OL"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-06-21T09:18:30.439Z", "lastModified": "2023-06-21T09:18:30.439Z", "name": "Experiment 3", "expressions": [], "model": "Workspace.OL_MODULAR_RIFAI", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "OL"}, {"name": "Parameter_View", "value": "Advanced"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-06-21T09:22:46.418Z", "lastModified": "2023-06-21T09:22:46.418Z", "name": "Experiment 4", "expressions": [], "model": "Workspace.OL_MODULAR_RIFAI", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "OL"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-06-21T09:34:46.199Z", "lastModified": "2023-06-21T09:34:46.199Z", "name": "Experiment 5", "expressions": [], "model": "Workspace.OL_MODULAR_RIFAI", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "Refoulement-Aspiration"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-06-21T11:32:15.602Z", "lastModified": "2023-06-21T11:32:15.602Z", "name": "Experiment 6", "expressions": [], "model": "Workspace.OL_MODULAR_RIFAI", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "Refoulement"}, {"name": "Parameter_View", "value": "Advanced"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-06-21T11:42:59.272Z", "lastModified": "2023-06-21T11:42:59.272Z", "name": "Experiment 7", "expressions": [], "model": "Workspace.OL_MODULAR_RIFAI", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "SDT"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-06-23T08:29:43.867Z", "lastModified": "2023-06-23T08:29:43.867Z", "name": "Experiment 8", "expressions": [], "model": "Workspace.OL_MODULAR_RIFAI", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-06-23T08:48:48.111Z", "lastModified": "2023-06-23T08:48:48.111Z", "name": "Experiment 9", "expressions": [], "model": "Workspace.OL_MODULAR_RIFAI", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "90HZ"}, {"name": "Parameter_View", "value": "Full"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-06-23T09:07:05.768Z", "lastModified": "2023-06-23T09:07:05.768Z", "name": "Experiment 10", "expressions": [], "model": "Workspace.OL_MODULAR_RIFAI", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-06-26T07:52:22.486Z", "lastModified": "2023-06-26T07:52:22.486Z", "name": "Experiment 11", "expressions": [], "model": "Workspace.OL_MODULAR_RIFAI", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "110Hz"}, {"name": "Parameter_View", "value": "Full"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-06-29T11:05:41.519Z", "lastModified": "2023-06-29T11:05:41.519Z", "name": "Experiment 12", "expressions": [], "model": "Workspace.OL_MODULAR_RIFAI", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "20HZ"}, {"name": "Parameter_View", "value": "Full"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-06-30T10:34:36.315Z", "lastModified": "2023-06-30T10:34:36.315Z", "name": "Experiment 13", "expressions": [], "model": "Workspace.OL_MODULAR_RIFAI", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-06-30T11:51:39.824Z", "lastModified": "2023-06-30T11:51:39.824Z", "name": "Experiment 14", "expressions": [], "model": "Workspace.OL_MODULAR_RIFAI", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "100Hz"}, {"name": "Parameter_View", "value": "Full"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-03T07:29:04.720Z", "lastModified": "2023-07-03T07:29:04.720Z", "name": "Experiment 15", "expressions": [], "model": "Workspace.OL_MODULAR_RIFAI", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-04T07:07:24.357Z", "lastModified": "2023-07-04T07:07:24.357Z", "name": "Experiment 16", "expressions": [], "model": "Workspace.OL_MODULAR_RIFAI", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "120hz"}, {"name": "Parameter_View", "value": "Full"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-04T07:15:01.656Z", "lastModified": "2023-07-04T07:15:01.656Z", "name": "Experiment 17", "expressions": [], "model": "Workspace.OL_MODULAR_RIFAI", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "120"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-04T10:58:08.339Z", "lastModified": "2023-07-04T10:58:08.339Z", "name": "Experiment 18", "expressions": [], "model": "Workspace.OL_MODULAR_RIFAI", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "140"}, {"name": "Parameter_View", "value": "Advanced"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-04T14:14:13.257Z", "lastModified": "2023-07-04T14:14:13.257Z", "name": "Experiment 19", "expressions": [], "model": "Workspace.OL_MODULAR_RIFAI", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "140Hz"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-04T14:22:01.399Z", "lastModified": "2023-07-04T14:22:01.399Z", "name": "Experiment 20", "expressions": [], "model": "Workspace.OL_MODULAR_RIFAI", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "30"}, {"name": "Parameter_View", "value": "Full"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-05T14:46:18.151Z", "lastModified": "2023-07-05T14:46:18.151Z", "name": "Experiment 21", "expressions": [], "model": "Workspace.OL_MODULAR_RIFAI", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-07T08:05:55.273Z", "lastModified": "2023-07-07T08:05:55.273Z", "name": "Experiment 22", "expressions": [], "model": "Workspace.OL_MODULAR_RIFAI", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "30CONT"}, {"name": "Parameter_View", "value": "Full"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-07T08:29:08.364Z", "lastModified": "2023-07-07T08:29:08.364Z", "name": "Experiment 23", "expressions": [], "model": "Workspace.OL_MODULAR_RIFAI", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "40CO"}, {"name": "Parameter_View", "value": "Advanced"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-07T08:50:06.022Z", "lastModified": "2023-07-07T08:50:06.022Z", "name": "Experiment 24", "expressions": [], "model": "Workspace.OL_MODULAR_RIFAI", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "140CONT"}, {"name": "Parameter_View", "value": "Advanced"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-07T12:32:47.662Z", "lastModified": "2023-07-07T12:32:47.662Z", "name": "Experiment 25", "expressions": [], "model": "Workspace.OL_MODULAR_RIFAI", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "20BIBLOCHZ"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-11T08:03:36.847Z", "lastModified": "2023-07-11T08:03:36.847Z", "name": "Experiment 26", "expressions": [], "model": "Workspace.OL_MODULAR_RIFAI", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "2rows20"}, {"name": "Parameter_View", "value": "Advanced"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-11T08:22:01.054Z", "lastModified": "2023-07-11T08:22:01.054Z", "name": "Experiment 27", "expressions": [], "model": "Workspace.OL_MODULAR_RIFAI", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "2rows30"}, {"name": "Parameter_View", "value": "Advanced"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-11T08:50:36.130Z", "lastModified": "2023-07-11T08:50:36.130Z", "name": "Experiment 28", "expressions": [], "model": "Workspace.OL_MODULAR_RIFAI", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "140R"}, {"name": "Parameter_View", "value": "Advanced"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-13T08:46:01.681Z", "lastModified": "2023-07-13T08:46:01.681Z", "name": "Experiment 29", "expressions": [], "model": "Workspace.OL_MODULAR_RIFAI", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-13T12:26:11.206Z", "lastModified": "2023-07-13T12:26:11.206Z", "name": "Experiment 30", "expressions": [], "model": "Workspace.OL_MODULAR_RIFAI", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "20Rows"}, {"name": "Parameter_View", "value": "Full"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.HPC_ILHAM_OL": [{"created": "2023-06-21T11:31:39.854Z", "lastModified": "2023-06-21T11:31:39.854Z", "name": "Experiment 1", "expressions": [{"name": "node_suction.T_fixed", "expression": "false"}], "model": "Workspace.HPC_ILHAM_OL", "settings": {"dynamic": {}, "steadyState": {}, "custom": {}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.OL_Modular_ILHAM": [{"created": "2023-07-19T08:18:21.152Z", "lastModified": "2023-07-19T08:18:21.152Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.OL_Modular_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-19T08:56:45.890Z", "lastModified": "2023-07-19T08:56:45.890Z", "name": "Experiment 2", "expressions": [], "model": "Workspace.OL_Modular_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-19T09:12:08.749Z", "lastModified": "2023-07-19T09:12:08.749Z", "name": "Experiment 3", "expressions": [], "model": "Workspace.OL_Modular_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-19T12:06:56.195Z", "lastModified": "2023-07-19T12:06:56.195Z", "name": "Experiment 4", "expressions": [], "model": "Workspace.OL_Modular_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-19T12:14:22.264Z", "lastModified": "2023-07-19T12:14:22.264Z", "name": "Experiment 5", "expressions": [], "model": "Workspace.OL_Modular_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-19T12:22:54.329Z", "lastModified": "2023-07-19T12:22:54.329Z", "name": "Experiment 6", "expressions": [], "model": "Workspace.OL_Modular_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-19T12:33:37.847Z", "lastModified": "2023-07-19T12:33:37.847Z", "name": "Experiment 7", "expressions": [], "model": "Workspace.OL_Modular_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-19T12:41:37.769Z", "lastModified": "2023-07-19T12:41:37.769Z", "name": "Experiment 8", "expressions": [], "model": "Workspace.OL_Modular_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-19T12:58:55.861Z", "lastModified": "2023-07-19T12:58:55.861Z", "name": "Experiment 9", "expressions": [], "model": "Workspace.OL_Modular_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-19T13:08:41.370Z", "lastModified": "2023-07-19T13:08:41.370Z", "name": "Experiment 10", "expressions": [], "model": "Workspace.OL_Modular_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.OL_Modular_ILHAM_1": [{"created": "2023-07-19T08:18:21.152Z", "lastModified": "2023-07-19T08:18:21.152Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.OL_Modular_ILHAM_1", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-19T08:56:45.890Z", "lastModified": "2023-07-19T08:56:45.890Z", "name": "Experiment 2", "expressions": [], "model": "Workspace.OL_Modular_ILHAM_1", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-19T09:12:08.749Z", "lastModified": "2023-07-19T09:12:08.749Z", "name": "Experiment 3", "expressions": [], "model": "Workspace.OL_Modular_ILHAM_1", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-19T12:06:56.195Z", "lastModified": "2023-07-19T12:06:56.195Z", "name": "Experiment 4", "expressions": [], "model": "Workspace.OL_Modular_ILHAM_1", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-19T12:14:22.264Z", "lastModified": "2023-07-19T12:14:22.264Z", "name": "Experiment 5", "expressions": [], "model": "Workspace.OL_Modular_ILHAM_1", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-19T12:22:54.329Z", "lastModified": "2023-07-19T12:22:54.329Z", "name": "Experiment 6", "expressions": [], "model": "Workspace.OL_Modular_ILHAM_1", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-19T12:33:37.847Z", "lastModified": "2023-07-19T12:33:37.847Z", "name": "Experiment 7", "expressions": [], "model": "Workspace.OL_Modular_ILHAM_1", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-19T12:41:37.769Z", "lastModified": "2023-07-19T12:41:37.769Z", "name": "Experiment 8", "expressions": [], "model": "Workspace.OL_Modular_ILHAM_1", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-19T12:58:55.861Z", "lastModified": "2023-07-19T12:58:55.861Z", "name": "Experiment 9", "expressions": [], "model": "Workspace.OL_Modular_ILHAM_1", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-19T13:08:41.370Z", "lastModified": "2023-07-19T13:08:41.370Z", "name": "Experiment 10", "expressions": [], "model": "Workspace.OL_Modular_ILHAM_1", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-20T08:01:02.083Z", "lastModified": "2023-07-20T08:01:02.083Z", "name": "Experiment 11", "expressions": [], "model": "Workspace.OL_Modular_ILHAM_1", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "DoE Generator & Rating Calculations", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "SEERTEST"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.TESTOLMODULAR": [{"created": "2023-07-20T11:46:49.400Z", "lastModified": "2023-07-20T11:46:49.400Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.TESTOLMODULAR", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "DoE Generator & Rating Calculations", "parameters": [{"name": "Workbook_Name", "value": "TESTSEER"}, {"name": "Parameter_View", "value": "Advanced"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-20T12:02:56.224Z", "lastModified": "2023-07-20T12:02:56.224Z", "name": "Experiment 2", "expressions": [], "model": "Workspace.TESTOLMODULAR", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "DoE Generator & Rating Calculations", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "ILSEER1"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-20T12:09:06.642Z", "lastModified": "2023-07-20T12:09:06.642Z", "name": "Experiment 3", "expressions": [], "model": "Workspace.TESTOLMODULAR", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "DoE Generator & Rating Calculations", "parameters": [{"name": "Workbook_Name", "value": "ASEER"}, {"name": "Parameter_View", "value": "Advanced"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-20T12:25:40.739Z", "lastModified": "2023-07-20T12:25:40.739Z", "name": "Experiment 4", "expressions": [], "model": "Workspace.TESTOLMODULAR", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.HPC_MODEL_ILHAM": [{"created": "2023-07-24T09:03:14.878Z", "lastModified": "2023-07-24T09:03:14.878Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.HPC_MODEL_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "DoE Generator & Rating Calculations", "parameters": [{"name": "Parameter_View", "value": "Advanced"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-24T09:20:36.292Z", "lastModified": "2023-07-24T09:20:36.292Z", "name": "Experiment 2", "expressions": [], "model": "Workspace.HPC_MODEL_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "DoE Generator & Rating Calculations", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-21T14:18:57.103Z", "lastModified": "2023-08-21T14:18:57.103Z", "name": "Experiment 3", "expressions": [], "model": "Workspace.HPC_MODEL_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.HPC_MODULAR_ILHAM": [{"created": "2023-07-24T12:37:58.407Z", "lastModified": "2023-07-24T12:37:58.407Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.HPC_MODULAR_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "DoE Generator & Rating Calculations", "parameters": [{"name": "Workbook_Name", "value": "SEERTEST7"}, {"name": "Parameter_View", "value": "Advanced"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-21T12:23:28.619Z", "lastModified": "2023-08-21T12:23:28.619Z", "name": "Experiment 2", "expressions": [], "model": "Workspace.HPC_MODULAR_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "SEERB"}, {"name": "Parameter_View", "value": "Advanced"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-21T12:36:53.720Z", "lastModified": "2023-08-21T12:36:53.720Z", "name": "Experiment 3", "expressions": [], "model": "Workspace.HPC_MODULAR_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "SEERB"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-21T12:37:50.584Z", "lastModified": "2023-08-21T12:37:50.584Z", "name": "Experiment 4", "expressions": [], "model": "Workspace.HPC_MODULAR_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "SEERBTEST"}, {"name": "Parameter_View", "value": "Advanced"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-21T12:52:56.548Z", "lastModified": "2023-08-21T12:52:56.548Z", "name": "Experiment 5", "expressions": [], "model": "Workspace.HPC_MODULAR_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "SEERBTEST2"}, {"name": "Parameter_View", "value": "Advanced"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-21T14:14:15.660Z", "lastModified": "2023-08-21T14:14:15.660Z", "name": "Experiment 6", "expressions": [], "model": "Workspace.HPC_MODULAR_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "TESTSEERB"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-21T14:20:49.784Z", "lastModified": "2023-08-21T14:20:49.784Z", "name": "Experiment 7", "expressions": [], "model": "Workspace.HPC_MODULAR_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "testserB"}, {"name": "Parameter_View", "value": "Advanced"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.HPH_MODULAR_ILHAM": [{"created": "2023-08-01T13:40:23.565Z", "lastModified": "2023-08-01T13:40:23.565Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.HPH_MODULAR_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "HPH30HZ"}, {"name": "Parameter_View", "value": "Advanced"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-01T14:09:39.679Z", "lastModified": "2023-08-01T14:09:39.679Z", "name": "Experiment 2", "expressions": [], "model": "Workspace.HPH_MODULAR_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-02T09:24:04.558Z", "lastModified": "2023-08-02T09:24:04.558Z", "name": "Experiment 3", "expressions": [], "model": "Workspace.HPH_MODULAR_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "120HZHPH"}, {"name": "Parameter_View", "value": "Advanced"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-02T11:29:45.941Z", "lastModified": "2023-08-02T11:29:45.941Z", "name": "Experiment 4", "expressions": [], "model": "Workspace.HPH_MODULAR_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-02T11:35:32.642Z", "lastModified": "2023-08-02T11:35:32.642Z", "name": "Experiment 5", "expressions": [], "model": "Workspace.HPH_MODULAR_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "140HZHPH"}, {"name": "Parameter_View", "value": "Advanced"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-02T13:04:43.604Z", "lastModified": "2023-08-02T13:04:43.604Z", "name": "Experiment 6", "expressions": [], "model": "Workspace.HPH_MODULAR_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "HPH50"}, {"name": "Parameter_View", "value": "Advanced"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-03T12:25:48.702Z", "lastModified": "2023-08-03T12:25:48.702Z", "name": "Experiment 7", "expressions": [], "model": "Workspace.HPH_MODULAR_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "720TEST"}, {"name": "Parameter_View", "value": "Advanced"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-04T09:36:30.818Z", "lastModified": "2023-08-04T09:36:30.818Z", "name": "Experiment 8", "expressions": [], "model": "Workspace.HPH_MODULAR_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "TESTHPHCP1211"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-04T11:36:07.209Z", "lastModified": "2023-08-04T11:36:07.209Z", "name": "Experiment 9", "expressions": [], "model": "Workspace.HPH_MODULAR_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "AC1112SAJ"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-04T12:47:44.359Z", "lastModified": "2023-08-04T12:47:44.359Z", "name": "Experiment 10", "expressions": [], "model": "Workspace.HPH_MODULAR_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "zz"}, {"name": "Parameter_View", "value": "Advanced"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-04T13:12:50.263Z", "lastModified": "2023-08-04T13:12:50.263Z", "name": "Experiment 11", "expressions": [], "model": "Workspace.HPH_MODULAR_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "GGJSSW1J"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-07T11:05:01.230Z", "lastModified": "2023-08-07T11:05:01.230Z", "name": "Experiment 12", "expressions": [], "model": "Workspace.HPH_MODULAR_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "R&2SH"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-07T11:19:50.793Z", "lastModified": "2023-08-07T11:19:50.793Z", "name": "Experiment 13", "expressions": [], "model": "Workspace.HPH_MODULAR_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "ER1"}, {"name": "Parameter_View", "value": "Advanced"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-07T11:25:57.852Z", "lastModified": "2023-08-07T11:25:57.852Z", "name": "Experiment 14", "expressions": [], "model": "Workspace.HPH_MODULAR_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "SCSV"}, {"name": "Parameter_View", "value": "Advanced"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-07T11:29:32.462Z", "lastModified": "2023-08-07T11:29:32.462Z", "name": "Experiment 15", "expressions": [], "model": "Workspace.HPH_MODULAR_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "FQSFWJQXD"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-07T12:00:01.155Z", "lastModified": "2023-08-07T12:00:01.155Z", "name": "Experiment 16", "expressions": [], "model": "Workspace.HPH_MODULAR_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "QXSDSCX"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-07T12:28:45.310Z", "lastModified": "2023-08-07T12:28:45.310Z", "name": "Experiment 17", "expressions": [], "model": "Workspace.HPH_MODULAR_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "TEST22"}, {"name": "Parameter_View", "value": "Advanced"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-07T12:34:13.793Z", "lastModified": "2023-08-07T12:34:13.793Z", "name": "Experiment 18", "expressions": [], "model": "Workspace.HPH_MODULAR_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "DDFSFDDEZFDSS"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-07T13:16:21.677Z", "lastModified": "2023-08-07T13:16:21.677Z", "name": "Experiment 19", "expressions": [], "model": "Workspace.HPH_MODULAR_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "DSDC"}, {"name": "Parameter_View", "value": "Advanced"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-07T13:20:11.435Z", "lastModified": "2023-08-07T13:20:11.435Z", "name": "Experiment 20", "expressions": [], "model": "Workspace.HPH_MODULAR_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "SSSRDVSSSSQSDH"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-08T09:14:04.429Z", "lastModified": "2023-08-08T09:14:04.429Z", "name": "Experiment 21", "expressions": [], "model": "Workspace.HPH_MODULAR_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "WWW"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-08T09:37:48.659Z", "lastModified": "2023-08-08T09:37:48.659Z", "name": "Experiment 22", "expressions": [], "model": "Workspace.HPH_MODULAR_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "FFFD"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-08T11:48:38.235Z", "lastModified": "2023-08-08T11:48:38.235Z", "name": "Experiment 23", "expressions": [], "model": "Workspace.HPH_MODULAR_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "HH9X"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-08T12:11:27.284Z", "lastModified": "2023-08-08T12:11:27.284Z", "name": "Experiment 24", "expressions": [], "model": "Workspace.HPH_MODULAR_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "UUU"}, {"name": "Parameter_View", "value": "Advanced"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-10T11:53:02.100Z", "lastModified": "2023-08-10T11:53:02.100Z", "name": "Experiment 25", "expressions": [], "model": "Workspace.HPH_MODULAR_ILHAM", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "tt2"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.TestCOMPRESSEUR": [{"created": "2023-08-02T14:13:26.077Z", "lastModified": "2023-08-02T14:13:26.077Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.TestCOMPRESSEUR", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "TEST_compresseurDanfoss"}, {"name": "Parameter_View", "value": "Advanced"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-03T07:09:53.384Z", "lastModified": "2023-08-03T07:09:53.384Z", "name": "Experiment 2", "expressions": [], "model": "Workspace.TestCOMPRESSEUR", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-04T06:56:16.569Z", "lastModified": "2023-08-04T06:56:16.569Z", "name": "Experiment 3", "expressions": [], "model": "Workspace.TestCOMPRESSEUR", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "TESTM"}, {"name": "Parameter_View", "value": "Advanced"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-04T13:38:17.717Z", "lastModified": "2023-08-04T13:38:17.717Z", "name": "Experiment 4", "expressions": [], "model": "Workspace.TestCOMPRESSEUR", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "B"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.OPtifan": [{"created": "2023-08-11T06:58:09.252Z", "lastModified": "2023-08-11T06:58:09.252Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.OPtifan", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "OPTI1"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-11T07:03:53.841Z", "lastModified": "2023-08-11T07:03:53.841Z", "name": "Experiment 2", "expressions": [], "model": "Workspace.OPtifan", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "optifan1-30"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-11T07:31:20.154Z", "lastModified": "2023-08-11T07:31:20.154Z", "name": "Experiment 3", "expressions": [], "model": "Workspace.OPtifan", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "OPTIFAN10C"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-11T08:01:39.001Z", "lastModified": "2023-08-11T08:01:39.001Z", "name": "Experiment 4", "expressions": [], "model": "Workspace.OPtifan", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-17T07:07:01.017Z", "lastModified": "2023-08-17T07:07:01.017Z", "name": "Experiment 5", "expressions": [], "model": "Workspace.OPtifan", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "RAE"}, {"name": "Parameter_View", "value": "Advanced"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-18T08:17:47.306Z", "lastModified": "2023-08-18T08:17:47.306Z", "name": "Experiment 6", "expressions": [], "model": "Workspace.OPtifan", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "TESTSCOPD"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-22T07:28:50.436Z", "lastModified": "2023-08-22T07:28:50.436Z", "name": "Experiment 7", "expressions": [], "model": "Workspace.OPtifan", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-22T11:09:15.753Z", "lastModified": "2023-08-22T11:09:15.753Z", "name": "Experiment 8", "expressions": [], "model": "Workspace.OPtifan", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "TT"}, {"name": "Parameter_View", "value": "Advanced"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-23T13:24:23.638Z", "lastModified": "2023-08-23T13:24:23.638Z", "name": "Experiment 9", "expressions": [], "model": "Workspace.OPtifan", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "RT2q"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-23T13:59:10.613Z", "lastModified": "2023-08-23T13:59:10.613Z", "name": "Experiment 10", "expressions": [], "model": "Workspace.OPtifan", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-24T07:08:54.686Z", "lastModified": "2023-08-24T07:08:54.686Z", "name": "Experiment 11", "expressions": [], "model": "Workspace.OPtifan", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "rty6H"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-25T09:02:41.242Z", "lastModified": "2023-08-25T09:02:41.242Z", "name": "Experiment 12", "expressions": [], "model": "Workspace.OPtifan", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-28T07:13:34.252Z", "lastModified": "2023-08-28T07:13:34.252Z", "name": "Experiment 13", "expressions": [], "model": "Workspace.OPtifan", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-31T13:52:32.536Z", "lastModified": "2023-08-31T13:52:32.536Z", "name": "Experiment 14", "expressions": [], "model": "Workspace.OPtifan", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-09-01T07:44:04.377Z", "lastModified": "2023-09-01T07:44:04.377Z", "name": "Experiment 15", "expressions": [], "model": "Workspace.OPtifan", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-09-04T07:41:51.445Z", "lastModified": "2023-09-04T07:41:51.445Z", "name": "Experiment 16", "expressions": [], "model": "Workspace.OPtifan", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "scopCA"}, {"name": "Parameter_View", "value": "Advanced"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-09-04T07:51:51.037Z", "lastModified": "2023-09-04T07:51:51.037Z", "name": "Experiment 17", "expressions": [], "model": "Workspace.OPtifan", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-09-04T08:02:37.732Z", "lastModified": "2023-09-04T08:02:37.732Z", "name": "Experiment 18", "expressions": [], "model": "Workspace.OPtifan", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "eez"}, {"name": "Parameter_View", "value": "Advanced"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.OPtifanFroid": [{"created": "2023-07-20T08:45:42.810Z", "lastModified": "2023-07-20T08:45:42.810Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.OPtifanFroid", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "DoE Generator & Rating Calculations", "parameters": [{"name": "Workbook_Name", "value": "SEERTEST1"}, {"name": "Parameter_View", "value": "Advanced"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-20T14:49:59.057Z", "lastModified": "2023-07-20T14:49:59.057Z", "name": "Experiment 2", "expressions": [], "model": "Workspace.OPtifanFroid", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State Diagnostics", "parameters": [{"name": "template", "value": "Extended Diagnostics"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-20T14:57:02.944Z", "lastModified": "2023-07-20T14:57:02.944Z", "name": "Experiment 3", "expressions": [], "model": "Workspace.OPtifanFroid", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "DoE Generator & Rating Calculations", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "SEER4"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-24T07:32:00.988Z", "lastModified": "2023-07-24T07:32:00.988Z", "name": "Experiment 4", "expressions": [], "model": "Workspace.OPtifanFroid", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "DoE Generator & Rating Calculations", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "TESTEER4"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-07-24T14:01:29.735Z", "lastModified": "2023-07-24T14:01:29.735Z", "name": "Experiment 5", "expressions": [], "model": "Workspace.OPtifanFroid", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "DoE Generator & Rating Calculations", "parameters": [{"name": "Workbook_Name", "value": "SEERTEST0"}, {"name": "Parameter_View", "value": "Advanced"}, {"name": "Sync_To_Existing_Workbook", "value": false}, {"name": "Skip_MR_creation", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-22T07:42:01.496Z", "lastModified": "2023-08-22T07:42:01.496Z", "name": "Experiment 6", "expressions": [], "model": "Workspace.OPtifanFroid", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "COND7"}, {"name": "Parameter_View", "value": "Advanced"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-22T11:10:48.897Z", "lastModified": "2023-08-22T11:10:48.897Z", "name": "Experiment 7", "expressions": [], "model": "Workspace.OPtifanFroid", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Basic"}, {"name": "Workbook_Name", "value": "UTTR4"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-22T14:27:06.125Z", "lastModified": "2023-08-22T14:27:06.125Z", "name": "Experiment 8", "expressions": [], "model": "Workspace.OPtifanFroid", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "220COND1"}, {"name": "Parameter_View", "value": "Advanced"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-23T07:10:17.378Z", "lastModified": "2023-08-23T07:10:17.378Z", "name": "Experiment 9", "expressions": [], "model": "Workspace.OPtifanFroid", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "SDB5Z"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-25T11:38:10.366Z", "lastModified": "2023-08-25T11:38:10.366Z", "name": "Experiment 10", "expressions": [], "model": "Workspace.OPtifanFroid", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-28T09:07:22.451Z", "lastModified": "2023-08-28T09:07:22.451Z", "name": "Experiment 11", "expressions": [], "model": "Workspace.OPtifanFroid", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-29T09:23:00.363Z", "lastModified": "2023-08-29T09:23:00.363Z", "name": "Experiment 12", "expressions": [], "model": "Workspace.OPtifanFroid", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "JKLI"}, {"name": "Parameter_View", "value": "Advanced"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-08-30T07:34:02.435Z", "lastModified": "2023-08-30T07:34:02.435Z", "name": "Experiment 13", "expressions": [], "model": "Workspace.OPtifanFroid", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "DCD"}, {"name": "Parameter_View", "value": "Advanced"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-09-04T08:49:35.108Z", "lastModified": "2023-09-04T08:49:35.108Z", "name": "Experiment 14", "expressions": [], "model": "Workspace.OPtifanFroid", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "SEERAA"}, {"name": "Parameter_View", "value": "Advanced"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-09-04T09:19:04.824Z", "lastModified": "2023-09-04T09:19:04.824Z", "name": "Experiment 15", "expressions": [], "model": "Workspace.OPtifanFroid", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2023-09-05T07:52:33.271Z", "lastModified": "2023-09-05T07:52:33.271Z", "name": "Experiment 16", "expressions": [], "model": "Workspace.OPtifanFroid", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.System.HPH.BaseCycles.OL_modular": [{"created": "2024-01-09T13:22:06.108Z", "lastModified": "2024-01-09T13:22:06.108Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.OL_modular", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-01-10T07:34:14.494Z", "lastModified": "2024-01-10T07:34:14.494Z", "name": "Experiment 2", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.OL_modular", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-01-10T09:37:12.664Z", "lastModified": "2024-01-10T09:37:12.664Z", "name": "Experiment 3", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.OL_modular", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-01-15T13:12:33.259Z", "lastModified": "2024-01-15T13:12:33.259Z", "name": "Experiment 4", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.OL_modular", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": "workspace_system_hph_basecycles_ol_modular_20240110_111906_2ef489f"}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "test_convergence"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-01-15T14:25:23.775Z", "lastModified": "2024-01-15T14:25:23.775Z", "name": "Experiment 5", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.OL_modular", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-01-15T14:44:54.449Z", "lastModified": "2024-01-15T14:44:54.449Z", "name": "Experiment 6", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.OL_modular", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-01-16T08:53:47.341Z", "lastModified": "2024-01-16T08:53:47.341Z", "name": "Experiment 7", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.OL_modular", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-01-16T09:05:38.506Z", "lastModified": "2024-01-16T09:05:38.506Z", "name": "Experiment 8", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.OL_modular", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-01-16T12:21:37.412Z", "lastModified": "2024-01-16T12:21:37.412Z", "name": "Experiment 9", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.OL_modular", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-01-17T07:24:35.410Z", "lastModified": "2024-01-17T07:24:35.410Z", "name": "Experiment 10", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.OL_modular", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-01-23T14:52:37.391Z", "lastModified": "2024-01-23T14:52:37.391Z", "name": "Experiment 11", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.OL_modular", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {}, "simulationType": "dynamic"}, "filters": [], "isDefault": false}, {"created": "2024-01-24T09:31:46.943Z", "lastModified": "2024-01-24T09:31:46.943Z", "name": "Experiment 12", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.OL_modular", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "test_is_mono"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-01-24T11:08:59.575Z", "lastModified": "2024-01-24T11:08:59.575Z", "name": "Experiment 13", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.OL_modular", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-01-24T14:07:10.863Z", "lastModified": "2024-01-24T14:07:10.863Z", "name": "Experiment 14", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.OL_modular", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-01-24T14:13:55.744Z", "lastModified": "2024-01-24T14:13:55.744Z", "name": "Experiment 15", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.OL_modular", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "Test_FB6"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-01-24T14:22:59.289Z", "lastModified": "2024-01-24T14:22:59.289Z", "name": "Experiment 16", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.OL_modular", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.System.HPH.BaseCycles.OL_bloc_2_ckt": [{"created": "2024-01-09T13:22:06.108Z", "lastModified": "2024-01-09T13:22:06.108Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.OL_bloc_2_ckt", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-01-10T07:34:14.494Z", "lastModified": "2024-01-10T07:34:14.494Z", "name": "Experiment 2", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.OL_bloc_2_ckt", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-01-10T09:37:12.664Z", "lastModified": "2024-01-10T09:37:12.664Z", "name": "Experiment 3", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.OL_bloc_2_ckt", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-01-15T13:12:33.259Z", "lastModified": "2024-01-15T13:12:33.259Z", "name": "Experiment 4", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.OL_bloc_2_ckt", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": "workspace_system_hph_basecycles_ol_modular_20240110_111906_2ef489f"}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "test_convergence"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-01-15T14:25:23.775Z", "lastModified": "2024-01-15T14:25:23.775Z", "name": "Experiment 5", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.OL_bloc_2_ckt", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-01-15T14:44:54.449Z", "lastModified": "2024-01-15T14:44:54.449Z", "name": "Experiment 6", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.OL_bloc_2_ckt", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-01-16T08:53:47.341Z", "lastModified": "2024-01-16T08:53:47.341Z", "name": "Experiment 7", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.OL_bloc_2_ckt", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-01-16T09:05:38.506Z", "lastModified": "2024-01-16T09:05:38.506Z", "name": "Experiment 8", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.OL_bloc_2_ckt", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-01-16T12:21:37.412Z", "lastModified": "2024-01-16T12:21:37.412Z", "name": "Experiment 9", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.OL_bloc_2_ckt", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-01-17T07:24:35.410Z", "lastModified": "2024-01-17T07:24:35.410Z", "name": "Experiment 10", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.OL_bloc_2_ckt", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-01-23T14:52:37.391Z", "lastModified": "2024-01-23T14:52:37.391Z", "name": "Experiment 11", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.OL_bloc_2_ckt", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {}, "simulationType": "dynamic"}, "filters": [], "isDefault": false}, {"created": "2024-01-24T09:31:46.943Z", "lastModified": "2024-01-24T09:31:46.943Z", "name": "Experiment 12", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.OL_bloc_2_ckt", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "test_is_mono"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-01-24T11:08:59.575Z", "lastModified": "2024-01-24T11:08:59.575Z", "name": "Experiment 13", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.OL_bloc_2_ckt", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-01-24T14:07:10.863Z", "lastModified": "2024-01-24T14:07:10.863Z", "name": "Experiment 14", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.OL_bloc_2_ckt", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-01-24T14:13:55.744Z", "lastModified": "2024-01-24T14:13:55.744Z", "name": "Experiment 15", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.OL_bloc_2_ckt", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "Test_FB6"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-01-24T14:22:59.289Z", "lastModified": "2024-01-24T14:22:59.289Z", "name": "Experiment 16", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.OL_bloc_2_ckt", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.System.HPH.BaseCycles.Unit": [{"created": "2024-01-26T10:17:24.637Z", "lastModified": "2024-01-26T10:17:24.637Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.Unit", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.System.HPH.BaseCycles.Unit_optimization": [{"created": "2024-01-26T10:17:24.637Z", "lastModified": "2024-01-26T10:17:24.637Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.Unit_optimization", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-03-26T14:52:05.105Z", "lastModified": "2024-03-26T14:52:05.105Z", "name": "Experiment 2", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.Unit_optimization", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "unit_opti_test"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-03-26T14:57:06.522Z", "lastModified": "2024-03-26T14:57:06.522Z", "name": "Experiment 3", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.Unit_optimization", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.Controller.test_map_Cp_SDT_max": [{"created": "2024-02-07T14:23:56.684Z", "lastModified": "2024-02-07T14:23:56.684Z", "name": "Experiment 1", "expressions": [{"name": "SST_input", "expression": "range(-30,25,80)"}], "model": "Workspace.Controller.test_map_Cp_SDT_max", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.Controller.test_map_Cp_SDT_min": [{"created": "2024-02-07T14:23:56.684Z", "lastModified": "2024-02-07T14:23:56.684Z", "name": "Experiment 1", "expressions": [{"name": "SST_input", "expression": "range(-30,25,80)"}], "model": "Workspace.Controller.test_map_Cp_SDT_min", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.Controller.test_map_Cp_SDT_max_avec_dgt": [{"created": "2024-02-07T14:23:56.684Z", "lastModified": "2024-02-07T14:23:56.684Z", "name": "Experiment 1", "expressions": [{"name": "SST_input", "expression": "range(-30,25,80)"}], "model": "Workspace.Controller.test_map_Cp_SDT_max_avec_dgt", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.System.HPH.BaseCycles.ECS1": [{"created": "2024-02-19T08:06:14.518Z", "lastModified": "2024-02-19T08:06:14.518Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.ECS1", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "ECS1"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-03-05T09:25:27.660Z", "lastModified": "2024-03-05T09:25:27.660Z", "name": "Experiment 2", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.ECS1", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-03-05T09:57:00.654Z", "lastModified": "2024-03-05T09:57:00.654Z", "name": "Experiment 3", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.ECS1", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-03-21T14:51:49.800Z", "lastModified": "2024-03-21T14:51:49.800Z", "name": "Experiment 4", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.ECS1", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Sync_To_Existing_Workbook", "value": true}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-04-22T17:17:51.146Z", "lastModified": "2024-04-22T17:17:51.146Z", "name": "Experiment 5", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.ECS1", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "test_pompe"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-06-12T07:07:48.872Z", "lastModified": "2024-06-12T07:07:48.872Z", "name": "Experiment 6", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.ECS1", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Workbook_Name", "value": "Calibration"}, {"name": "Parameter_View", "value": "Advanced"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.Controller.test_controllerSettings": [{"created": "2024-02-19T14:27:27.706Z", "lastModified": "2024-02-19T14:27:27.706Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.Controller.test_controllerSettings", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.Controller.Tests.test_map_Cp_SDT_max": [{"created": "2024-02-07T14:23:56.684Z", "lastModified": "2024-02-07T14:23:56.684Z", "name": "Experiment 1", "expressions": [{"name": "SST_input", "expression": "range(-30,25,80)"}], "model": "Workspace.Controller.Tests.test_map_Cp_SDT_max", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.Controller.Tests.test_map_Cp_SDT_min": [{"created": "2024-02-07T14:23:56.684Z", "lastModified": "2024-02-07T14:23:56.684Z", "name": "Experiment 1", "expressions": [{"name": "SST_input", "expression": "range(-30,25,80)"}], "model": "Workspace.Controller.Tests.test_map_Cp_SDT_min", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.Controller.Tests.test_map_Cp_SDT_max_avec_dgt": [{"created": "2024-02-07T14:23:56.684Z", "lastModified": "2024-02-07T14:23:56.684Z", "name": "Experiment 1", "expressions": [{"name": "SST_input", "expression": "range(-30,25,80)"}], "model": "Workspace.Controller.Tests.test_map_Cp_SDT_max_avec_dgt", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.System.HPH.BaseCycles.ECS3": [{"created": "2024-02-20T13:48:27.129Z", "lastModified": "2024-02-20T13:48:27.129Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.ECS3", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "ECS3_HPH"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.System.HPC.BaseCycle.ECS2": [{"created": "2024-02-20T15:28:23.239Z", "lastModified": "2024-02-20T15:28:23.239Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.System.HPC.BaseCycle.ECS2", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "ECS2_HPC"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-02-29T14:56:19.182Z", "lastModified": "2024-02-29T14:56:19.182Z", "name": "Experiment 2", "expressions": [], "model": "Workspace.System.HPC.BaseCycle.ECS2", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "TEST_unit_size"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-02-20T15:28:23.239Z", "lastModified": "2024-02-20T15:28:23.239Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.System.HPC.BaseCycle.ECS2", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "ECS2_HPC"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-02-21T14:40:10.267Z", "lastModified": "2024-02-21T14:40:10.267Z", "name": "Experiment 2", "expressions": [], "model": "Workspace.System.HPC.BaseCycle.ECS2", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "ECS1_HPC"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-02-27T14:00:21.773Z", "lastModified": "2024-02-27T14:00:21.773Z", "name": "Experiment 3", "expressions": [], "model": "Workspace.System.HPC.BaseCycle.ECS2", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-02-27T14:34:42.101Z", "lastModified": "2024-02-27T14:34:42.101Z", "name": "Experiment 4", "expressions": [], "model": "Workspace.System.HPC.BaseCycle.ECS2", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "ECS1_HPC_physical"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.System.HPC.BaseCycle.test": [{"created": "2024-02-21T09:00:05.963Z", "lastModified": "2024-02-21T09:00:05.963Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.System.HPC.BaseCycle.test", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-02-21T09:22:04.963Z", "lastModified": "2024-02-21T09:22:04.963Z", "name": "Experiment 2", "expressions": [], "model": "Workspace.System.HPC.BaseCycle.test", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.System.HPC.BaseCycle.ECS3": [{"created": "2024-02-20T15:28:23.239Z", "lastModified": "2024-02-20T15:28:23.239Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.System.HPC.BaseCycle.ECS3", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "ECS2_HPC"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-02-21T14:51:33.315Z", "lastModified": "2024-02-21T14:51:33.315Z", "name": "Experiment 2", "expressions": [], "model": "Workspace.System.HPC.BaseCycle.ECS3", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "ECS3_HPC"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.Controller.SubSystems.Tests.FanControl.FanControlCooling": [{"created": "2024-02-27T08:15:30.928Z", "lastModified": "2024-02-27T08:15:30.928Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.Controller.SubSystems.Tests.FanControl.FanControlCooling", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-02-27T08:23:13.341Z", "lastModified": "2024-02-27T08:23:13.341Z", "name": "Experiment 2", "expressions": [], "model": "Workspace.Controller.SubSystems.Tests.FanControl.FanControlCooling", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-04-04T08:26:20.900Z", "lastModified": "2024-04-04T08:26:20.900Z", "name": "Experiment 3", "expressions": [], "model": "Workspace.Controller.SubSystems.Tests.FanControl.FanControlCooling", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.Controller.SubSystems.Tests.Compressor_control.Cp_control": [{"created": "2024-02-27T09:32:21.185Z", "lastModified": "2024-02-27T09:32:21.185Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.Controller.SubSystems.Tests.Compressor_control.Cp_control", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-04-04T08:26:58.789Z", "lastModified": "2024-04-04T08:26:58.789Z", "name": "Experiment 2", "expressions": [], "model": "Workspace.Controller.SubSystems.Tests.Compressor_control.Cp_control", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.Controller.SubSystems.Tests.EXVControl.Test_EXV_cooling": [{"created": "2024-02-27T09:51:19.588Z", "lastModified": "2024-02-27T09:51:19.588Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.Controller.SubSystems.Tests.EXVControl.Test_EXV_cooling", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-04-10T12:32:29.747Z", "lastModified": "2024-04-10T12:32:29.747Z", "name": "Experiment 2", "expressions": [], "model": "Workspace.Controller.SubSystems.Tests.EXVControl.Test_EXV_cooling", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.System.HPH.BaseCycles.ECS2": [{"created": "2024-02-19T08:06:14.518Z", "lastModified": "2024-02-19T08:06:14.518Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.ECS2", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "ECS1"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-03-01T17:32:51.247Z", "lastModified": "2024-03-01T17:32:51.247Z", "name": "Experiment 2", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.ECS2", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.System.HPH.BaseCycles.ECS1_1": [{"created": "2024-02-19T08:06:14.518Z", "lastModified": "2024-02-19T08:06:14.518Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.ECS1_1", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "ECS1"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.Controller.Tests.Test_control_2C_cooling": [{"created": "2024-03-29T13:57:29.458Z", "lastModified": "2024-03-29T13:57:29.458Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.Controller.Tests.Test_control_2C_cooling", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.System.HPC.BaseCycle.CL_system": [{"created": "2024-04-02T11:01:32.858Z", "lastModified": "2024-04-02T11:01:32.858Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.System.HPC.BaseCycle.CL_system", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "test_CL_system"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-04-03T12:28:29.033Z", "lastModified": "2024-04-03T12:28:29.033Z", "name": "Experiment 2", "expressions": [], "model": "Workspace.System.HPC.BaseCycle.CL_system", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-04-03T12:33:41.034Z", "lastModified": "2024-04-03T12:33:41.034Z", "name": "Experiment 3", "expressions": [], "model": "Workspace.System.HPC.BaseCycle.CL_system", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-04-26T10:53:16.268Z", "lastModified": "2024-04-26T10:53:16.268Z", "name": "Experiment 4", "expressions": [], "model": "Workspace.System.HPC.BaseCycle.CL_system", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-05-28T06:13:11.462Z", "lastModified": "2024-05-28T06:13:11.462Z", "name": "Experiment 5", "expressions": [], "model": "Workspace.System.HPC.BaseCycle.CL_system", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-05-28T06:14:31.477Z", "lastModified": "2024-05-28T06:14:31.477Z", "name": "Experiment 6", "expressions": [], "model": "Workspace.System.HPC.BaseCycle.CL_system", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-05-31T10:49:36.541Z", "lastModified": "2024-05-31T10:49:36.541Z", "name": "Experiment 7", "expressions": [], "model": "Workspace.System.HPC.BaseCycle.CL_system", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-05-31T11:24:15.537Z", "lastModified": "2024-05-31T11:24:15.537Z", "name": "Experiment 8", "expressions": [], "model": "Workspace.System.HPC.BaseCycle.CL_system", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "TEST_HPC"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-05-31T11:45:38.310Z", "lastModified": "2024-05-31T11:45:38.310Z", "name": "Experiment 9", "expressions": [{"name": "Use_pump", "expression": "false"}], "model": "Workspace.System.HPC.BaseCycle.CL_system", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": "workspace_system_hpc_basecycle_cl_system_20240410_092222_07af9f0"}, "custom": {}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-05-31T13:50:14.386Z", "lastModified": "2024-05-31T13:50:14.386Z", "name": "Experiment 10", "expressions": [], "model": "Workspace.System.HPC.BaseCycle.CL_system", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-06-05T15:01:19.557Z", "lastModified": "2024-06-05T15:01:19.557Z", "name": "Experiment 11", "expressions": [], "model": "Workspace.System.HPC.BaseCycle.CL_system", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "Test_EN14511_HPC"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.Controller.SubSystems.Tests.EXVControl.Test_EXV_heating": [{"created": "2024-02-27T09:51:19.588Z", "lastModified": "2024-02-27T09:51:19.588Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.Controller.SubSystems.Tests.EXVControl.Test_EXV_heating", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-04-10T12:32:29.747Z", "lastModified": "2024-04-10T12:32:29.747Z", "name": "Experiment 2", "expressions": [], "model": "Workspace.Controller.SubSystems.Tests.EXVControl.Test_EXV_heating", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.Controller.Tests.test_controllerSettings_cooling": [{"created": "2024-02-19T14:27:27.706Z", "lastModified": "2024-02-19T14:27:27.706Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.Controller.Tests.test_controllerSettings_cooling", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-02-20T12:07:36.811Z", "lastModified": "2024-02-20T12:07:36.811Z", "name": "Experiment 2", "expressions": [], "model": "Workspace.Controller.Tests.test_controllerSettings_cooling", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-02-20T13:10:03.764Z", "lastModified": "2024-02-20T13:10:03.764Z", "name": "Experiment 3", "expressions": [], "model": "Workspace.Controller.Tests.test_controllerSettings_cooling", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-03-29T13:55:31.370Z", "lastModified": "2024-03-29T13:55:31.370Z", "name": "Experiment 4", "expressions": [], "model": "Workspace.Controller.Tests.test_controllerSettings_cooling", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.Controller.Tests.test_controllerSettings_heating": [{"created": "2024-02-19T14:27:27.706Z", "lastModified": "2024-02-19T14:27:27.706Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.Controller.Tests.test_controllerSettings_heating", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-02-20T12:07:36.811Z", "lastModified": "2024-02-20T12:07:36.811Z", "name": "Experiment 2", "expressions": [], "model": "Workspace.Controller.Tests.test_controllerSettings_heating", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-02-20T13:10:03.764Z", "lastModified": "2024-02-20T13:10:03.764Z", "name": "Experiment 3", "expressions": [], "model": "Workspace.Controller.Tests.test_controllerSettings_heating", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-03-29T13:55:31.370Z", "lastModified": "2024-03-29T13:55:31.370Z", "name": "Experiment 4", "expressions": [], "model": "Workspace.Controller.Tests.test_controllerSettings_heating", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.Controller.SubSystems.Tests.FanControl.FanControlHeating": [{"created": "2024-02-27T08:15:30.928Z", "lastModified": "2024-02-27T08:15:30.928Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.Controller.SubSystems.Tests.FanControl.FanControlHeating", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-02-27T08:23:13.341Z", "lastModified": "2024-02-27T08:23:13.341Z", "name": "Experiment 2", "expressions": [], "model": "Workspace.Controller.SubSystems.Tests.FanControl.FanControlHeating", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-04-04T08:26:20.900Z", "lastModified": "2024-04-04T08:26:20.900Z", "name": "Experiment 3", "expressions": [], "model": "Workspace.Controller.SubSystems.Tests.FanControl.FanControlHeating", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.System.HPH.BaseCycles.CL_system": [{"created": "2024-04-11T12:51:15.501Z", "lastModified": "2024-04-11T12:51:15.501Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.CL_system", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "Test_CL_system_HPH"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-04-11T14:07:08.621Z", "lastModified": "2024-04-11T14:07:08.621Z", "name": "Experiment 2", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.CL_system", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-04-11T14:07:13.107Z", "lastModified": "2024-04-11T14:07:13.107Z", "name": "Experiment 3", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.CL_system", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-04-12T07:15:55.587Z", "lastModified": "2024-04-12T07:15:55.587Z", "name": "Experiment 4", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.CL_system", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "Test_CL_system_HPH"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-04-25T12:36:42.259Z", "lastModified": "2024-04-25T12:36:42.259Z", "name": "Experiment 5", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.CL_system", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": "workspace_system_hph_basecycles_cl_system_20240425_142527_9b22501"}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-04-25T14:30:18.726Z", "lastModified": "2024-04-25T14:30:18.726Z", "name": "Experiment 6", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.CL_system", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": "workspace_system_hph_basecycles_cl_system_20240425_143448_91f7f58"}, "custom": {}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-04-26T13:17:06.850Z", "lastModified": "2024-04-26T13:17:06.850Z", "name": "Experiment 7", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.CL_system", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "HPH_CL_system"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-05-28T05:51:08.438Z", "lastModified": "2024-05-28T05:51:08.438Z", "name": "Experiment 8", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.CL_system", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "test_pompe_debug"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-05-29T09:22:05.085Z", "lastModified": "2024-05-29T09:22:05.085Z", "name": "Experiment 9", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.CL_system", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-05-30T10:45:25.308Z", "lastModified": "2024-05-30T10:45:25.308Z", "name": "Experiment 10", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.CL_system", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "TEST_pompe"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-05-31T07:31:16.974Z", "lastModified": "2024-05-31T07:31:16.974Z", "name": "Experiment 11", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.CL_system", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "SCOP_pump"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-06-04T07:20:53.886Z", "lastModified": "2024-06-04T07:20:53.886Z", "name": "Experiment 12", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.CL_system", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "Test_EN14511_HPH"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-06-04T13:40:04.913Z", "lastModified": "2024-06-04T13:40:04.913Z", "name": "Experiment 13", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.CL_system", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "HPH_61AQ050"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-06-05T06:05:11.584Z", "lastModified": "2024-06-05T06:05:11.584Z", "name": "Experiment 14", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.CL_system", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "Test_EN14511"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-06-05T07:01:29.362Z", "lastModified": "2024-06-05T07:01:29.362Z", "name": "Experiment 15", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.CL_system", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "Test_EN14511"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-06-05T08:33:05.676Z", "lastModified": "2024-06-05T08:33:05.676Z", "name": "Experiment 16", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.CL_system", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "Test_EN14511_2"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-06-05T08:37:31.978Z", "lastModified": "2024-06-05T08:37:31.978Z", "name": "Experiment 17", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.CL_system", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "Test_EN14511_2"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-06-05T08:43:25.569Z", "lastModified": "2024-06-05T08:43:25.569Z", "name": "Experiment 18", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.CL_system", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "test_EN14511.2"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.Controller.Tests.test_freq_min_SDT": [{"created": "2024-02-07T14:23:56.684Z", "lastModified": "2024-02-07T14:23:56.684Z", "name": "Experiment 1", "expressions": [{"name": "SST_input", "expression": "range(-30,25,80)"}], "model": "Workspace.Controller.Tests.test_freq_min_SDT", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-04-26T08:12:04.628Z", "lastModified": "2024-04-26T08:12:04.628Z", "name": "Experiment 2", "expressions": [], "model": "Workspace.Controller.Tests.test_freq_min_SDT", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.Auxiliary.EN14511.Test_EN14511_Zenith": [{"created": "2024-06-05T07:47:47.703Z", "lastModified": "2024-06-05T07:47:47.703Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.Auxiliary.EN14511.Test_EN14511_Zenith", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "EN14511_Test"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.System.HPC.BaseCycle.System_61AQ": [{"created": "2024-07-23T12:40:35.695Z", "lastModified": "2024-07-23T12:40:35.695Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.System.HPC.BaseCycle.System_61AQ", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-07-24T13:29:10.416Z", "lastModified": "2024-07-24T13:29:10.416Z", "name": "Experiment 2", "expressions": [], "model": "Workspace.System.HPC.BaseCycle.System_61AQ", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iGLBL", "value": false}, {"name": "iPRVS", "value": true}]}, "simulationType": "custom"}, "filters": [], "isDefault": true}], "Workspace.System.HPC.BaseCycle.optimization.Unit_optimization": [{"created": "2024-02-07T07:32:42.925Z", "lastModified": "2024-02-07T07:32:42.925Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.System.HPC.BaseCycle.optimization.Unit_optimization", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-03-07T13:11:29.758Z", "lastModified": "2024-03-07T13:11:29.758Z", "name": "Experiment 2", "expressions": [], "model": "Workspace.System.HPC.BaseCycle.optimization.Unit_optimization", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "Unit_optimization_HPC"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-03-08T07:49:08.895Z", "lastModified": "2024-03-08T07:49:08.895Z", "name": "Experiment 3", "expressions": [], "model": "Workspace.System.HPC.BaseCycle.optimization.Unit_optimization", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iGLBL", "value": false}, {"name": "iPRVS", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.System.HPC.BaseCycle.optimization.Unit_optimization_1": [{"created": "2024-02-07T07:32:42.925Z", "lastModified": "2024-02-07T07:32:42.925Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.System.HPC.BaseCycle.optimization.Unit_optimization_1", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.System.HPC.BaseCycle.optimization.ECS1": [{"created": "2024-02-20T15:28:23.239Z", "lastModified": "2024-02-20T15:28:23.239Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.System.HPC.BaseCycle.optimization.ECS1", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "ECS2_HPC"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-02-21T14:40:10.267Z", "lastModified": "2024-02-21T14:40:10.267Z", "name": "Experiment 2", "expressions": [], "model": "Workspace.System.HPC.BaseCycle.optimization.ECS1", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "ECS1_HPC"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-02-27T14:00:21.773Z", "lastModified": "2024-02-27T14:00:21.773Z", "name": "Experiment 3", "expressions": [], "model": "Workspace.System.HPC.BaseCycle.optimization.ECS1", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-02-27T14:34:42.101Z", "lastModified": "2024-02-27T14:34:42.101Z", "name": "Experiment 4", "expressions": [], "model": "Workspace.System.HPC.BaseCycle.optimization.ECS1", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "ECS1_HPC_physical"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-03-19T10:36:25.970Z", "lastModified": "2024-03-19T10:36:25.970Z", "name": "Experiment 5", "expressions": [], "model": "Workspace.System.HPC.BaseCycle.optimization.ECS1", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "Test_library_1.51"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-06-12T12:05:20.592Z", "lastModified": "2024-06-12T12:05:20.592Z", "name": "Experiment 6", "expressions": [], "model": "Workspace.System.HPC.BaseCycle.optimization.ECS1", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "Calibration_HPC"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.System.HPH.BaseCycles.System_61AQ": [{"created": "2024-07-23T12:40:35.695Z", "lastModified": "2024-07-23T12:40:35.695Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.System_61AQ", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-07-24T13:29:10.416Z", "lastModified": "2024-07-24T13:29:10.416Z", "name": "Experiment 2", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.System_61AQ", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-07-30T13:30:31.893Z", "lastModified": "2024-07-30T13:30:31.893Z", "name": "Experiment 3", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.System_61AQ", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-07-31T07:38:42.738Z", "lastModified": "2024-07-31T07:38:42.738Z", "name": "Experiment 4", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.System_61AQ", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-07-31T10:57:08.267Z", "lastModified": "2024-07-31T10:57:08.267Z", "name": "Experiment 5", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.System_61AQ", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-07-31T11:58:15.520Z", "lastModified": "2024-07-31T11:58:15.520Z", "name": "Experiment 6", "expressions": [{"name": "controllerSettings_crkA.SST_min", "expression": "243.15"}], "model": "Workspace.System.HPH.BaseCycles.System_61AQ", "settings": {"dynamic": {}, "steadyState": {}, "custom": {}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-07-31T14:14:17.705Z", "lastModified": "2024-07-31T14:14:17.705Z", "name": "Experiment 7", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.System_61AQ", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-07-31T14:17:56.585Z", "lastModified": "2024-07-31T14:17:56.585Z", "name": "Experiment 8", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.System_61AQ", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-07-31T14:25:14.783Z", "lastModified": "2024-07-31T14:25:14.783Z", "name": "Experiment 9", "expressions": [{"name": "ECAT.CondFoulingFactor_m2KW.setPoint", "expression": "0"}], "model": "Workspace.System.HPH.BaseCycles.System_61AQ", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-07-31T14:51:20.830Z", "lastModified": "2024-07-31T14:51:20.830Z", "name": "Experiment 10", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.System_61AQ", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.System.HPH.BaseCycles.optimization.CL_system_ECAT": [{"created": "2024-04-23T10:37:05.961Z", "lastModified": "2024-04-23T10:37:05.961Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.optimization.CL_system_ECAT", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "Test_ECAT"}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-04-23T10:38:44.373Z", "lastModified": "2024-04-23T10:38:44.373Z", "name": "Experiment 2", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.optimization.CL_system_ECAT", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-07-23T13:18:04.651Z", "lastModified": "2024-07-23T13:18:04.651Z", "name": "Experiment 3", "expressions": [], "model": "Workspace.System.HPH.BaseCycles.optimization.CL_system_ECAT", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.System.HPH.R290.CL_61AQ": [{"created": "2024-09-12T08:27:09.995Z", "lastModified": "2024-09-12T08:27:09.995Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.System.HPH.R290.CL_61AQ", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": true}], "Workspace.System.HPC.R290.CL_61AQ": [{"created": "2024-09-12T08:34:59.880Z", "lastModified": "2024-09-12T08:34:59.880Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.System.HPC.R290.CL_61AQ", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "Mono_unit_140"}]}, "simulationType": "custom"}, "filters": [], "isDefault": true}], "Workspace.Controller.Tests.Test_Controller_1C_Zenith": [{"created": "2024-10-04T10:52:44.454Z", "lastModified": "2024-10-04T10:52:44.454Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.Controller.Tests.Test_Controller_1C_Zenith", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": true}], "Workspace.System.HPC.BaseCycle.System_61AQ_multimodule": [{"created": "2024-10-09T08:05:33.407Z", "lastModified": "2024-10-09T08:05:33.407Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.System.HPC.BaseCycle.System_61AQ_multimodule", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iGLBL", "value": false}, {"name": "iPRVS", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": true}], "Workspace.Controller.Tests.test_optimization_function": [{"created": "2024-10-09T12:50:06.070Z", "lastModified": "2024-10-09T12:50:06.070Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.Controller.Tests.test_optimization_function", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": true}], "Workspace.System.Multimodule.System_61AQ_multimodule_bis_2": [{"created": "2024-10-09T08:05:33.407Z", "lastModified": "2024-10-09T08:05:33.407Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.System.Multimodule.System_61AQ_multimodule_bis_2", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iGLBL", "value": false}, {"name": "iPRVS", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": true}], "Workspace.System.Multimodule.HPH.Equipement_Modular": [{"created": "2024-10-16T12:30:10.966Z", "lastModified": "2024-10-16T12:30:10.966Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.System.Multimodule.HPH.Equipement_Modular", "settings": {"dynamic": {}, "steadyState": {}, "custom": {}, "simulationType": "steady state"}, "filters": [], "isDefault": true}], "Workspace.System.Multimodule.HPH.Modular_61AQ": [{"created": "2024-07-23T12:40:35.695Z", "lastModified": "2024-07-23T12:40:35.695Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.System.Multimodule.HPH.Modular_61AQ", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-07-24T13:29:10.416Z", "lastModified": "2024-07-24T13:29:10.416Z", "name": "Experiment 2", "expressions": [], "model": "Workspace.System.Multimodule.HPH.Modular_61AQ", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-07-30T13:30:31.893Z", "lastModified": "2024-07-30T13:30:31.893Z", "name": "Experiment 3", "expressions": [], "model": "Workspace.System.Multimodule.HPH.Modular_61AQ", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-07-31T07:38:42.738Z", "lastModified": "2024-07-31T07:38:42.738Z", "name": "Experiment 4", "expressions": [], "model": "Workspace.System.Multimodule.HPH.Modular_61AQ", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-07-31T10:57:08.267Z", "lastModified": "2024-07-31T10:57:08.267Z", "name": "Experiment 5", "expressions": [], "model": "Workspace.System.Multimodule.HPH.Modular_61AQ", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-07-31T11:58:15.520Z", "lastModified": "2024-07-31T11:58:15.520Z", "name": "Experiment 6", "expressions": [{"name": "controllerSettings_crkA.SST_min", "expression": "243.15"}], "model": "Workspace.System.Multimodule.HPH.Modular_61AQ", "settings": {"dynamic": {}, "steadyState": {}, "custom": {}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-07-31T14:14:17.705Z", "lastModified": "2024-07-31T14:14:17.705Z", "name": "Experiment 7", "expressions": [], "model": "Workspace.System.Multimodule.HPH.Modular_61AQ", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-07-31T14:17:56.585Z", "lastModified": "2024-07-31T14:17:56.585Z", "name": "Experiment 8", "expressions": [], "model": "Workspace.System.Multimodule.HPH.Modular_61AQ", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-07-31T14:25:14.783Z", "lastModified": "2024-07-31T14:25:14.783Z", "name": "Experiment 9", "expressions": [{"name": "ECAT.CondFoulingFactor_m2KW.setPoint", "expression": "0"}], "model": "Workspace.System.Multimodule.HPH.Modular_61AQ", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-07-31T14:51:20.830Z", "lastModified": "2024-07-31T14:51:20.830Z", "name": "Experiment 10", "expressions": [], "model": "Workspace.System.Multimodule.HPH.Modular_61AQ", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}], "Workspace.System.Multimodule.HPC.Modular_61AQ": [{"created": "2024-07-23T12:40:35.695Z", "lastModified": "2024-07-23T12:40:35.695Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.System.Multimodule.HPC.Modular_61AQ", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-07-24T13:29:10.416Z", "lastModified": "2024-07-24T13:29:10.416Z", "name": "Experiment 2", "expressions": [], "model": "Workspace.System.Multimodule.HPC.Modular_61AQ", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": true}], "Workspace.System.Multimodule.HPH.System_61AQ_Modular": [{"created": "2024-10-09T08:05:33.407Z", "lastModified": "2024-10-09T08:05:33.407Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.System.Multimodule.HPH.System_61AQ_Modular", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": true}], "Workspace.System.Multimodule.HPH.R290.CL_61AQ_Modular": [{"created": "2024-10-16T14:31:50.451Z", "lastModified": "2024-10-16T14:31:50.451Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.System.Multimodule.HPH.R290.CL_61AQ_Modular", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": true}], "Workspace.System.Multimodule.HPC.System_61AQ_Modular": [{"created": "2024-10-09T08:05:33.407Z", "lastModified": "2024-10-09T08:05:33.407Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.System.Multimodule.HPC.System_61AQ_Modular", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": true}], "Workspace.System.Multimodule.HPC.R290.CL_61AQ_Modular": [{"created": "2024-07-23T12:40:35.695Z", "lastModified": "2024-07-23T12:40:35.695Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.System.Multimodule.HPC.R290.CL_61AQ_Modular", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": [{"name": "iPRVS", "value": false}, {"name": "iGLBL", "value": false}]}, "simulationType": "custom"}, "filters": [], "isDefault": false}, {"created": "2024-07-24T13:29:10.416Z", "lastModified": "2024-07-24T13:29:10.416Z", "name": "Experiment 2", "expressions": [], "model": "Workspace.System.Multimodule.HPC.R290.CL_61AQ_Modular", "settings": {"dynamic": {}, "steadyState": {"initializeFrom": ""}, "custom": {"selectedCustomFunction": "BOLT Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": true}], "Workspace.System.HPH.R290.CL61AQ_calibration_HPH": [{"created": "2024-10-29T14:14:13.234Z", "lastModified": "2024-10-29T14:14:13.234Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.System.HPH.R290.CL61AQ_calibration_HPH", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": []}, "simulationType": "custom"}, "filters": [], "isDefault": true}], "Workspace.System.Multimodule.HPH.CL_61AQ_Modular": [{"created": "2025-03-31T09:25:28.363Z", "lastModified": "2025-03-31T09:25:28.363Z", "name": "Experiment 1", "expressions": [], "model": "Workspace.System.Multimodule.HPH.CL_61AQ_Modular", "settings": {"dynamic": {}, "steadyState": {}, "custom": {"selectedCustomFunction": "FMU Multi Run - Steady State", "parameters": [{"name": "Parameter_View", "value": "Advanced"}, {"name": "Workbook_Name", "value": "Test_ULN_option_heating"}]}, "simulationType": "custom"}, "filters": [], "isDefault": true}]}