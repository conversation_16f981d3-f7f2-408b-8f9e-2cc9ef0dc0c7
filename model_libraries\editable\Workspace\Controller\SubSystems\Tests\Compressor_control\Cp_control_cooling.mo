within Workspace.Controller.SubSystems.Tests.Compressor_control;
model Cp_control_cooling
  .Workspace.Controller.SubSystems.CompressorControl_cooling compressorControl_cooling(
    isOffSSTmin=true,
    isOffSDTmax=true)
    annotation (Placement(transformation(extent={{-14.0,-10.0},{6.0,10.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_sst(
    y=SST)
    annotation (Placement(transformation(extent={{-102.0,22.0},{-82.0,42.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_sdt(
    y=SDT)
    annotation (Placement(transformation(extent={{-101.71016138186982,8.289838618130169},{-82.28983861813018,27.71016138186983}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression Cp_speed_setpoint(
    y=Cp_speed_set)
    annotation (Placement(transformation(extent={{-100.0,-12.0},{-80.0,8.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression SST_min(
    y=SSTmin)
    annotation (Placement(transformation(extent={{-98.0161910758224,-32.68285774248907},{-78.65047559084428,-13.31714225751093}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression SDT_max(
    y=SDTmax)
    annotation (Placement(transformation(extent={{-98.33333333333334,-49.0},{-78.33333333333334,-29.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput actuatorSignal
    annotation (Placement(transformation(extent={{28.664633127364837,-9.67908766194944},{48.66463312736484,10.32091233805056}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{90,-10},{110,10}})));
  parameter.Modelica.SIunits.Temperature SDT=323.15;
  parameter.Modelica.SIunits.Temperature SST=290.15;
  parameter Real Cp_speed_set=120;
  parameter.Modelica.SIunits.Temperature SSTmin=275.15;
  parameter.Modelica.SIunits.Temperature SDTmax=354.15;
  .Modelica.Blocks.Sources.RealExpression Freq_maxi(
    y=Freq_maxi_limit)
    annotation (Placement(transformation(extent={{-97.49073919340587,-66.12849679090334},{-77.49073919340587,-46.12849679090334}},origin={0.0,0.0},rotation=0.0)));
  parameter.Modelica.SIunits.Frequency Freq_maxi_limit=100;
  .Modelica.Blocks.Sources.RealExpression Freq_mini(
    y=Freq_mini_limit)
    annotation (Placement(transformation(extent={{-97.28996972957951,-81.01060721545284},{-77.28996972957951,-61.010607215452836}},origin={0.0,0.0},rotation=0.0)));
  parameter.Modelica.SIunits.Frequency Freq_mini_limit=50;
equation
  connect(compressorControl_cooling.actuatorSignal,actuatorSignal)
    annotation (Line(points={{15.017636699220454,-0.1925474028303295},{26.841134913292645,-0.1925474028303295},{26.841134913292645,0.32091233805055985},{38.66463312736484,0.32091233805055985}},color={0,0,127}));
  connect(Freq_mini.y,compressorControl_cooling.limitsBus.Freq_min_SDT_limit_comp)
    annotation (Line(points={{-76.28996972957951,-71.01060721545284},{-44.74498486478976,-71.01060721545284},{-44.74498486478976,-12},{-13.200000000000001,-12}},color={0,0,127}));
  connect(Freq_mini.y,compressorControl_cooling.limitsBus.Min_frequency)
    annotation (Line(points={{-76.28996972957951,-71.01060721545284},{-45.144984864789755,-71.01060721545284},{-45.144984864789755,-6},{-14,-6}},color={0,0,127}));
  connect(Freq_maxi.y,compressorControl_cooling.limitsBus.Freq_max_SDT_limit_comp)
    annotation (Line(points={{-76.49073919340587,-56.12849679090334},{-44.84536959670294,-56.12849679090334},{-44.84536959670294,-12},{-13.200000000000001,-12}},color={0,0,127}));
  connect(Freq_maxi.y,compressorControl_cooling.limitsBus.Max_frequency)
    annotation (Line(points={{-76.49073919340587,-56.12849679090334},{-45.245369596702936,-56.12849679090334},{-45.245369596702936,-6},{-14,-6}},color={0,0,127}));
  connect(SDT_max.y,compressorControl_cooling.limitsBus.T_sdt_max_limit_comp)
    annotation (Line(points={{-77.33333333333334,-39},{-70.66666666666667,-39},{-70.66666666666667,-6},{-14,-6}},color={0,0,127}));
  connect(SST_min.y,compressorControl_cooling.limitsBus.T_sst_min_limit_comp)
    annotation (Line(points={{-77.68218981659538,-23},{-70.84109490829769,-23},{-70.84109490829769,-6},{-14,-6}},color={0,0,127}));
  connect(Cp_speed_setpoint.y,compressorControl_cooling.compressorFrequency)
    annotation (Line(points={{-79,-2},{-79,0.20000000000000007},{-16,0.20000000000000007}},color={0,0,127}));
  connect(Cp_speed_setpoint.y,compressorControl_cooling.measurementBus.compressorFrequency)
    annotation (Line(points={{-79,-2},{-46.5,-2},{-46.5,6},{-14,6}},color={0,0,127}));
  connect(T_sdt.y,compressorControl_cooling.measurementBus.T_sdt)
    annotation (Line(points={{-81.3188224799432,18},{-72.6594112399716,18},{-72.6594112399716,6},{-14,6}},color={0,0,127}));
  connect(T_sst.y,compressorControl_cooling.measurementBus.T_sst)
    annotation (Line(points={{-81,32},{-72.5,32},{-72.5,6},{-14,6}},color={0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end Cp_control_cooling;
