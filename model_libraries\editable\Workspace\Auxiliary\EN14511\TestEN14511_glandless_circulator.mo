within Workspace.Auxiliary.EN14511;
model TestEN14511_glandless_circulator
  EN14511 eN14511(
    dry_motor_pump=false,
    heating_mode=false,
    integrated_pump=true,
    glandless_circulator=true)
    annotation (Placement(transformation(extent={{-14,-24},{44,36}})));
  Modelica.Blocks.Sources.Constant const(
    k=-30000)
    annotation (Placement(transformation(extent={{-92,28},{-72,48}})));
  Modelica.Blocks.Sources.Constant const1(
    k=0.01)
    annotation (Placement(transformation(extent={{-92,-6},{-72,14}})));
  Modelica.Blocks.Sources.Constant const2(
    k=300000)
    annotation (Placement(transformation(extent={{-92,-46},{-72,-26}})));
  Modelica.Blocks.Sources.Constant const3(
    k=100000)
    annotation (Placement(transformation(extent={{-96,-80},{-76,-60}})));
  Modelica.Blocks.Sources.Constant const4(
    k=20000)
    annotation (Placement(transformation(extent={{-96,66},{-76,86}})));
equation
  connect(const4.y,eN14511.DPe)
    annotation (Line(points={{-75,76},{-32.5,76},{-32.5,33},{-14,33}},color={0,0,127}));
  connect(const.y,eN14511.DPi)
    annotation (Line(points={{-71,38},{-44,38},{-44,21},{-14,21}},color={0,0,127}));
  connect(const1.y,eN14511.q)
    annotation (Line(points={{-71,4},{-45.5,4},{-45.5,9},{-14,9}},color={0,0,127}));
  connect(const2.y,eN14511.inst_gross_cap)
    annotation (Line(points={{-71,-36},{-44,-36},{-44,-3},{-14,-3}},color={0,0,127}));
  connect(const3.y,eN14511.inst_gross_pow)
    annotation (Line(points={{-75,-70},{-42,-70},{-42,-15},{-14,-15}},color={0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false)),
    Diagram(
      coordinateSystem(
        preserveAspectRatio=false)));
end TestEN14511_glandless_circulator;
