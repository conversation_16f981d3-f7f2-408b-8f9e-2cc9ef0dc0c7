within Workspace.Auxiliary.Defrost;
model Test_defrost
  .Workspace.Auxiliary.Defrost.Defrost_factor defrost_factor(
    is_monobloc=true)
    annotation (Placement(transformation(extent={{62.0,22.0},{82.0,42.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression RH(
    y=0.8392)
    annotation (Placement(transformation(extent={{-15.039655150757227,40.0},{15.039655150757227,60.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression OAT(
    y=275.15)
    annotation (Placement(transformation(extent={{-15.039655150757227,52.0},{15.039655150757227,72.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression Gross_Power(
    y=3550)
    annotation (Placement(transformation(extent={{-15.039655150757227,4.0},{15.039655150757227,24.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression Net_Power(
    y=3550)
    annotation (Placement(transformation(extent={{-15.039655150757227,20.0},{15.039655150757227,40.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression Net_Heatcap(
    y=12200)
    annotation (Placement(transformation(extent={{-15.039655150757227,-8.0},{15.039655150757227,12.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression Gross_Heatcap(
    y=12200)
    annotation (Placement(transformation(extent={{-17.039655150757227,-22.0},{13.039655150757227,-2.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression Ncomp(
    y=30)
    annotation (Placement(transformation(extent={{-13.039655150757227,66.0},{17.039655150757227,86.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression Pcomp(
    y=2750)
    annotation (Placement(transformation(extent={{-11.039655150757227,78.0},{19.039655150757227,98.0}},origin={0.0,0.0},rotation=0.0)));
equation
  connect(defrost_factor.RH,RH.y)
    annotation (Line(points={{62.45468154537929,35.82437529087824},{30.371810332916475,35.82437529087824},{30.371810332916475,50},{16.54362066583295,50}},color={0,0,127}));
  connect(defrost_factor.OAT,OAT.y)
    annotation (Line(points={{62.45468154537929,37.33437529087824},{36,37.33437529087824},{36,58},{16.54362066583295,58},{16.54362066583295,62}},color={0,0,127}));
  connect(defrost_factor.Net_Power,Net_Power.y)
    annotation (Line(points={{62.45468154537929,32.82437529087824},{31.371810332916475,32.82437529087824},{31.371810332916475,30},{16.54362066583295,30}},color={0,0,127}));
  connect(defrost_factor.Gross_Power,Gross_Power.y)
    annotation (Line(points={{62.45468154537929,31.22437529087824},{30.371810332916475,31.22437529087824},{30.371810332916475,14},{16.54362066583295,14}},color={0,0,127}));
  connect(defrost_factor.Net_Heatcap,Net_Heatcap.y)
    annotation (Line(points={{62.45468154537929,29.624375290878238},{30.371810332916475,29.624375290878238},{30.371810332916475,2},{16.54362066583295,2}},color={0,0,127}));
  connect(defrost_factor.Gross_Heatcap,Gross_Heatcap.y)
    annotation (Line(points={{62.45468154537929,28.22437529087824},{29.371810332916475,28.22437529087824},{29.371810332916475,-12},{14.54362066583295,-12}},color={0,0,127}));
  connect(defrost_factor.Ncomp,Ncomp.y)
    annotation (Line(points={{62.5,40.1},{38,40.1},{38,76},{18.54362066583295,76}},color={0,0,127}));
  connect(defrost_factor.Pcompresseur,Pcomp.y)
    annotation (Line(points={{62.4,41.8},{46,41.8},{46,88},{20.54362066583295,88}},color={0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end Test_defrost;
