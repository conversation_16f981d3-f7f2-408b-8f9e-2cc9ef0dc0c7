within Workspace.Icons;
partial class Interfaces
  extends Workspace.Icons.PackageIcon;
equation
  annotation (
    Icon(
      graphics={
        Polygon(
          points={{-44,38},{-44,-60},{16,-60},{16,-20},{10,-20},{0,-28},{-10,-28},{-22,-24},{-26,-10},{-22,4},{-10,10},{0,10},{6,4},{10,-4},{16,-4},{16,38},{-44,38}},
          lineColor={0,0,255},
          smooth=Smooth.None,
          fillColor={255,0,0},
          fillPattern=FillPattern.Solid),
        Polygon(
          points={{42,48},{42,-60},{16,-60},{16,-20},{10,-20},{0,-28},{-10,-28},{-22,-24},{-26,-10},{-22,4},{-10,10},{0,10},{6,4},{10,-4},{16,-4},{16,48},{42,48}},
          lineColor={0,0,255},
          smooth=Smooth.None,
          fillColor={128,0,255},
          fillPattern=FillPattern.Solid)}));
end Interfaces;
