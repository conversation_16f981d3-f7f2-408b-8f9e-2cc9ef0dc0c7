within Workspace.Icons;
partial class ControllerPackage
  extends Workspace.Icons.PackageIcon;
equation
  annotation (
    Icon(
      graphics={
        Line(
          points={{0,-62},{0,34},{66,-44}},
          color={0,0,0}),
        Line(
          points={{0,34},{-64,-40}},
          color={0,0,0}),
        Ellipse(
          extent={{-68,-20},{-36,-50}},
          fillColor={95,95,95},
          fillPattern=FillPattern.Sphere,
          lineColor={0,0,0}),
        Line(
          points={{0,34},{-54,46},{0,58},{56,46},{0,34}},
          color={0,0,0}),
        Line(
          points={{-40,-14},{-26,-32},{-20,-36},{-12,-38},{-2,-38},{10,-38},{18,-36},{26,-32},{42,-16}},
          color={0,0,0}),
        Ellipse(
          extent={{38,-20},{70,-50}},
          fillColor={95,95,95},
          fillPattern=FillPattern.Sphere,
          lineColor={0,0,0})}));
end ControllerPackage;
