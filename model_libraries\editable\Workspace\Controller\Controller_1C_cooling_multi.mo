within Workspace.Controller;
model Controller_1C_cooling_multi
  extends.Workspace.Controller.SubSystems.BaseClasses.ControllerBase_1C(
    redeclare.Workspace.Controller.SubSystems.FanControl_cooling fanControl(
      MaxFrequency=Fan_MaxFrequency,
      isOffSDTmin=isOffSDTmin_fan,
      isOffSDTmax=isOffSDTmax_fan,
      isOffDGTmax=isOffDGTmax_fan,
      MinFrequency=Fan_MinFrequency),
    redeclare.Workspace.Controller.SubSystems.EXVControl_cooling eXVControl(
      isOffSSTmax=isOffSSTmax_EXV),
    redeclare.Workspace.Controller.SubSystems.CompleteCompressorControl_1C_cooling completeCompressorControl_base(
      Nominal_capacity=Nominal_capacity,
      max_speed=max_speed,
      min_speed=min_speed,
      load_ratio=load_ratio,
      is_load_ratio=is_load_ratio));
  parameter Boolean isOffSDTmin_fan
    annotation (Dialog(group="fanControl",tab="isOff controller"));
  parameter Boolean isOffSDTmax_fan
    annotation (Dialog(group="fanControl",tab="isOff controller"));
  parameter Boolean isOffDGTmax_fan
    annotation (Dialog(group="fanControl",tab="isOff controller"));
  parameter Boolean isOffSSTmin_EXV
    annotation (Dialog(group="eXVControl ",tab="isOff controller"));
  parameter Boolean isOffSSTmax_EXV
    annotation (Dialog(group="eXVControl ",tab="isOff controller"));
  parameter Boolean isOffDSHmin_EXV
    annotation (Dialog(group="eXVControl ",tab="isOff controller"));
  parameter Boolean isOffDGTmax_EXV
    annotation (Dialog(group="eXVControl ",tab="isOff controller"));
  parameter Boolean isOffSSTmin_comp
    annotation (Dialog(group="Compressor ",tab="isOff controller"));
  parameter Boolean isOffSDTmax_comp
    annotation (Dialog(group="Compressor ",tab="isOff controller"));
  parameter Boolean isOffDGTmax_comp
    annotation (Dialog(group="Compressor ",tab="isOff controller"));
  parameter Boolean manualOff_fan_block_A
    annotation (Dialog(group="Fan ",tab="manualOff"));
  parameter Real frq_fan_sp_manual_block_A=720
    annotation (Dialog(group="Fan ",tab="manualOff"));
  parameter Boolean manualOff_fan_block_B
    annotation (Dialog(group="Fan ",tab="manualOff"));
  parameter Real frq_fan_sp_manual_block_B=720
    annotation (Dialog(group="Fan ",tab="manualOff"));
  parameter Boolean manualOff_compressor_block_A
    annotation (Dialog(group="Compressor ",tab="manualOff"));
  parameter Real frq_comp_sp_manual_block_A=100
    annotation (Dialog(group="Compressor ",tab="manualOff"));
  parameter Boolean manualOff_compressor_block_B
    annotation (Dialog(group="Compressor ",tab="manualOff"));
  parameter Real frq_comp_sp_manual_block_B=100
    annotation (Dialog(group="Compressor ",tab="manualOff"));
  parameter.Modelica.SIunits.Power Nominal_capacity=60000
    annotation (Dialog(group="compressor"));
end Controller_1C_cooling_multi;
