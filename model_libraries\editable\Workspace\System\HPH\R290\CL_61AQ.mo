within Workspace.System.HPH.R290;
model CL_61AQ
  extends.Workspace.System.HPH.BaseCycles.System_61AQ(
    choiceBlock(
      is_monobloc=false,
      Selector_Block_A=Workspace.Auxiliary.OptionBlock.Record_Base.Selector.Unit_Z_040,
      Selector_Block_B=Workspace.Auxiliary.OptionBlock.Record_Base.Selector.Unit_Z_060,
      Pump_selector=Workspace.Auxiliary.OptionBlock.PumpOption.Pump_Option.Pump),
    ECAT(
      LoadRatio_nd(
        setPoint=100),
      CondBrineLWT_K(
        setPoint=308.15),
      CondBrineEWT_K(
        setPoint=303.15),
      TargetHeatingCapacity_W(
        setPoint=300000),
      AmbientAirRH_nd(
        setPoint=0.2),
      HeatingAmbientAirDBTemp_K(
        setPoint=273.15)));
end CL_61AQ;
