within Workspace.Icons;
partial class FunctionsPackage
  extends Workspace.Icons.PackageIcon;
  annotation (
    Icon(
      graphics={
        Ellipse(
          extent={{-60,52},{58,-62}},
          lineColor={255,170,85},
          fillColor={255,255,255},
          fillPattern=FillPattern.Solid),
        Polygon(
          points={{-6,-6},{-6,-44},{0,-44},{0,-6},{14,-6},{14,-2},{0,-2},{0,14},{0,16},{2,18},{6,18},{8,18},{14,18},{14,22},{2,22},{-4,22},{-6,18},{-6,-2},{-18,-2},{-18,-6},{-6,-6}},
          lineColor={255,170,85},
          smooth=Smooth.None,
          fillColor={255,170,85},
          fillPattern=FillPattern.Solid)}));
end FunctionsPackage;
