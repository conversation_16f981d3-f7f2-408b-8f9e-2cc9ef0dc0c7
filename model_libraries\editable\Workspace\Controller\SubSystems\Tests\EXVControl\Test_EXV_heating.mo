within Workspace.Controller.SubSystems.Tests.EXVControl;
model Test_EXV_heating
  .Workspace.Controller.SubSystems.EXVControl_heating eXVControl(
    isOffSSTmax=false)
    annotation (Placement(transformation(extent={{22,6},{42,26}},origin={0,0},rotation=0)));
  .Modelica.Blocks.Sources.RealExpression T_ssh(
    y=dT_ssh)
    annotation (Placement(transformation(extent={{-23.5,26.0},{-3.5,46.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_sst(
    y=SST)
    annotation (Placement(transformation(extent={{-23.5,12.0},{-3.5,32.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_sst_max(
    y=SSTmax)
    annotation (Placement(transformation(extent={{-27.5,-16.0},{-7.5,4.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression SSH_setpoint(
    y=SSH_set)
    annotation (Placement(transformation(extent={{-27.682857742489063,-31.68285774248907},{-8.317142257510937,-12.31714225751093}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput actuatorSignal
    annotation (Placement(transformation(extent={{58.0,4.0},{78.0,24.0}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{90,-10},{110,10}})));
  parameter.Modelica.SIunits.TemperatureDifference dT_ssh=5;
  parameter.Modelica.SIunits.Temperature SST=258.15;
  parameter.Modelica.SIunits.Temperature SSTmax=298.15;
  parameter.Modelica.SIunits.TemperatureDifference SSH_set=10;
equation
  connect(SSH_setpoint.y,eXVControl.limitsBus.dT_ssh_setpoint)
    annotation (Line(points={{-7.348856483262033,-22},{6.575571758368984,-22},{6.575571758368984,10},{22,10}},color={0,0,127}));
  connect(T_sst_max.y,eXVControl.limitsBus.T_sst_max_limit_EXV)
    annotation (Line(points={{-6.5,-6},{7.75,-6},{7.75,10},{22,10}},color={0,0,127}));
  connect(T_sst.y,eXVControl.measurementBus.T_sst)
    annotation (Line(points={{-2.5,22},{22,22}},color={0,0,127}));
  connect(T_ssh.y,eXVControl.measurementBus.dT_ssh)
    annotation (Line(points={{-2.5,36},{9.75,36},{9.75,22},{22,22}},color={0,0,127}));
  connect(eXVControl.actuatorSignal,actuatorSignal)
    annotation (Line(points={{42,16},{55,16},{55,14},{68,14}},color={0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end Test_EXV_heating;
