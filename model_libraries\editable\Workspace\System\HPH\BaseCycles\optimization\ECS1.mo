within Workspace.System.HPH.BaseCycles.optimization;
model ECS1
  import CoolantCommonMedium=BOLT.InternalLibrary.Media.Coolant.CoolantCommon;
  .Workspace.System.HPH.BaseCycles.Equipement oL_modular(
    EWT=EWT,
    LWT=LWT,
    Water_pressure=sourceBrine.p_set,
    is_monobloc=is_monobloc,
    OAT=OAT,
    OAT_WB=OAT_WB,
    isOFFA=isOFFA,
    isOFFB=isOFFB,
    Unit_size=Unit_size,
    CoolantMedium=CoolantMedium,
    BrineConcentration=BrineConcentration)
    annotation (Placement(transformation(extent={{-31.358175323898898,1.6790876619494526},{-11.358175323898898,21.679087661949453}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Source sourceBrine(
    Vd_fixed=false,
    T_set=EWT,
    p_fixed=true,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration)
    annotation (Placement(transformation(extent={{-4.0,-4.0},{4.0,4.0}},origin={-22.0,-22.0},rotation=90.0)));
  .BOLT.BoundaryNode.Coolant.Sink sinkBrine(
    p_fixed=true,
    T_fixed=true,
    T_set=LWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    p_set=sourceBrine.p_set)
    annotation (Placement(transformation(extent={{-4.0,-4.0},{4.0,4.0}},origin={-20.0,44.0},rotation=-90.0)));
  .BOLT.Control.SteadyState.SetpointControl.Actuator Fan_actuatorA(
    fixed=true,
    maxBound=950,
    minBound=0,
    setPoint=720,
    isOff=isOFFA)
    annotation (Placement(transformation(extent={{-70.0,0.0},{-90.0,20.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Actuator Fan_actuatorB(
    setPoint=720,
    minBound=0,
    maxBound=950,
    fixed=true,
    isOff=is_monobloc or isOFFB)
    annotation (Placement(transformation(extent={{20.0,0.0},{40.0,20.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointControllerCpA(
    AV_max=140,
    AV_min=30,
    AV_value_off=FrequencyA,
    manualOff=Use_Cp_actuator,
    isOff=isOFFA)
    annotation (Placement(transformation(extent={{-64.41267748322487,-20.14135730512981},{-44.41267748322487,-0.1413573051298158}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation errorCalculationCpA(
    gain=1/70000,
    setPoint=TargetCapacityA,
    measurement=oL_modular.BlocA.condBPHE.summary.Q_flow_coolant,
    isOff=isOFFA)
    annotation (Placement(transformation(extent={{-89.12292751311718,-19.178620290978145},{-69.12292751311718,0.8213797090218549}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointControllerCpB(
    manualOff=Use_Cp_actuator,
    AV_value_off=FrequencyB,
    AV_min=30,
    AV_max=140,
    isOff=isOFFB or is_monobloc)
    annotation (Placement(transformation(extent={{18.686910238509263,-18.977850827151816},{-1.3130897614907369,1.0221491728481773}},origin={0.0,0.0},rotation=0.0)));
  // Declaration of Coolant Medium Package
  //package CoolantCommonMedium =
  //.BOLT.InternalLibrary.Media.Coolant.CoolantCommon                             "Coolant Medium Package" annotation ();
  // Declaration of Coolant Medium at Evaporator Circuit
  parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector CoolantMedium=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector.FW
    "Coolant Medium Selection"
    annotation (Dialog(group="Medium"));
  final parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Empty coolantInf=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.getSelector(
    CoolantMedium)
    "Coolant Medium"
    annotation ();
  .BOLT.InternalLibrary.Media.Coolant.CoolantCommon.Temperature FreezTemp=.BOLT.InternalLibrary.Media.Coolant.CoolantCommon.freezeTemp(
    coolantInf,
    BrineConcentration,
    1);
  parameter Real BrineConcentration=0.2
    annotation (Dialog(group="Medium"));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation errorCalculationCpB(
    isOff=isOFFB or is_monobloc,
    gain=1/70000,
    setPoint=TargetCapacityB,
    measurement=oL_modular.BlocB.condBPHE.summary.Q_flow_coolant)
    annotation (Placement(transformation(extent={{53.54621221179499,-17.814344349173748},{33.54621221179499,2.185655650826252}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation errorCalculationEXVA(
    isOff=isOFFA,
    measurement=oL_modular.BlocA.node_suction.summary.dTsh,
    setPoint=SH_A,
    gain=-1/5)
    annotation (Placement(transformation(extent={{-90.84763207759413,33.65177261313838},{-70.84763207759413,53.65177261313838}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointControllerEXVA(
    isOff=isOFFA,
    AV_min=0.1,
    AV_max=1)
    annotation (Placement(transformation(extent={{-65.81646970965124,32.689035598986706},{-45.81646970965125,52.689035598986706}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointControllerEXVB(
    AV_max=1,
    AV_min=0.1,
    isOff=isOFFB or is_monobloc)
    annotation (Placement(transformation(extent={{28.835962181902655,33.2107174008636},{8.835962181902655,53.2107174008636}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation errorCalculationEXVB(
    gain=-1/5,
    setPoint=SH_B,
    measurement=oL_modular.BlocB.node_suction.summary.dTsh,
    isOff=isOFFB or is_monobloc)
    annotation (Placement(transformation(extent={{67.34544274796866,34.49436675306583},{47.34544274796867,54.49436675306583}},origin={0.0,0.0},rotation=0.0)));
  parameter.Modelica.SIunits.Temperature OAT=280.15
    annotation (Dialog(group="Conditions"));
  parameter.Modelica.SIunits.Temperature OAT_WB=279.15
    annotation (Dialog(group="Conditions"));
  parameter.Modelica.SIunits.Temperature EWT=303.15
    annotation (Dialog(group="Conditions"));
  parameter.Modelica.SIunits.Temperature LWT=308.15
    annotation (Dialog(group="Conditions"));
  parameter.Modelica.SIunits.Power TargetCapacityA=40000
    annotation (Dialog(group="Cp_control"));
  parameter.Modelica.SIunits.Power TargetCapacityB=40000
    annotation (Dialog(group="Cp_control"));
  parameter Boolean isOFFA=false
    annotation (Dialog(group="Cp_control"));
  parameter Boolean isOFFB=false
    annotation (Dialog(group="Cp_control"));
  parameter Real FrequencyA=30
    annotation (Dialog(group="Cp_control"));
  parameter Real FrequencyB=30
    annotation (Dialog(group="Cp_control"));
  parameter.Modelica.SIunits.TemperatureDifference SH_A=5
    annotation (Dialog(group="Cp_control"));
  parameter.Modelica.SIunits.TemperatureDifference SH_B=5
    annotation (Dialog(group="Cp_control"));
  parameter Boolean Use_Cp_actuator=true
    annotation (Dialog(group="Cp_control"));
  parameter Boolean is_monobloc=
    if Unit_size == 40 then
      true
    elseif Unit_size == 50 then
      true
    elseif Unit_size == 60 then
      true
    elseif Unit_size == 70 then
      true
    else
      false
    annotation (Dialog(group="Unit_config"));
  parameter Boolean Use_pump=false
    annotation (Dialog(group="Unit_config"));
  parameter Integer Unit_size=50
    "Stringify values -- IPM Cloud limitation"
    annotation (Dialog(group="Unit_config"));
  parameter Boolean Use_EN=true;
equation
  connect(oL_modular.coolant_out,sinkBrine.port)
    annotation (Line(points={{-20.916456719952322,21.362524869842595},{-20.916456719952322,40},{-20,40}},color={0,127,0}));
  connect(sourceBrine.port,oL_modular.coolant_in)
    annotation (Line(points={{-22,-18},{-22,-10.5},{-21.1581753238989,-10.5},{-21.1581753238989,-3.3209123380505474}},color={0,127,0}));
  connect(Fan_actuatorB.value,oL_modular.Fan_controller_B)
    annotation (Line(points={{25,10},{7,10},{7,9.179087661949453},{-10.358175323898898,9.179087661949453}},color={0,0,127}));
  connect(errorCalculationCpA.sensor,setpointControllerCpA.errorSignal)
    annotation (Line(points={{-69.3229275131172,-9.978620290978146},{-69.3229275131172,-10.141357305129812},{-64.41267748322487,-10.141357305129812}},color={28,108,200}));
  connect(setpointControllerCpA.actuatorSignal,oL_modular.Compressor_controller_A)
    annotation (Line(points={{-43.81267748322487,-10.141357305129812},{-38.727251079662985,-10.141357305129812},{-38.727251079662985,4.179087661949453},{-32.3581753238989,4.179087661949453}},color={0,0,127}));
  connect(oL_modular.Compressor_controller_B,setpointControllerCpB.actuatorSignal)
    annotation (Line(points={{-10.358175323898898,4.179087661949453},{-6.4565448807453585,4.179087661949453},{-6.4565448807453585,-8.97785082715182},{-1.9130897614907383,-8.97785082715182}},color={0,0,127}));
  connect(errorCalculationCpB.sensor,setpointControllerCpB.errorSignal)
    annotation (Line(points={{33.74621221179498,-8.614344349173749},{26.53747356320267,-8.614344349173749},{26.53747356320267,-8.97785082715182},{18.686910238509263,-8.97785082715182}},color={28,108,200}));
  connect(errorCalculationEXVA.sensor,setpointControllerEXVA.errorSignal)
    annotation (Line(points={{-71.04763207759413,42.85177261313838},{-71.04763207759413,42.689035598986706},{-65.81646970965124,42.689035598986706}},color={28,108,200}));
  connect(setpointControllerEXVA.actuatorSignal,oL_modular.EXV_controller_A)
    annotation (Line(points={{-45.21646970965125,42.689035598986706},{-39.10823485482561,42.689035598986706},{-39.10823485482561,14.179087661949453},{-32.3581753238989,14.179087661949453}},color={0,0,127}));
  connect(setpointControllerEXVB.actuatorSignal,oL_modular.EXV_controller_B)
    annotation (Line(points={{8.235962181902654,43.2107174008636},{-1.5424750780739327,43.2107174008636},{-1.5424750780739327,14.179087661949453},{-10.358175323898898,14.179087661949453}},color={0,0,127}));
  connect(errorCalculationEXVB.sensor,setpointControllerEXVB.errorSignal)
    annotation (Line(points={{47.54544274796867,43.694366753065836},{37.3884216198093,43.694366753065836},{37.3884216198093,43.2107174008636},{28.835962181902655,43.2107174008636}},color={28,108,200}));
  connect(Fan_actuatorA.value,oL_modular.Fan_controller_A)
    annotation (Line(points={{-75,10},{-53.999999999999986,10},{-53.999999999999986,9.179087661949453},{-32.3581753238989,9.179087661949453}},color={0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end ECS1;
