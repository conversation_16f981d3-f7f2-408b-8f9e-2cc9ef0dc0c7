within Workspace.Icons;
partial model CycleIconBi
  //extends VFD30XW.Icons.InitializationCycle2;
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=true,
        extent={{-125,-125},{125,125}}),
      graphics={
        Text(
          extent={{-132,-106},{136,-124}},
          lineColor={0,0,0},
          fillColor={255,0,0},
          fillPattern=FillPattern.Solid,
          textString="%name"),
        Rectangle(
          extent={{-40,66},{40,40}},
          lineColor={255,0,0},
          lineThickness=0.5,
          fillColor={0,127,0},
          fillPattern=FillPattern.HorizontalCylinder),
        Rectangle(
          extent={{-40,-54},{40,-80}},
          lineColor={255,0,0},
          lineThickness=0.5,
          fillColor={0,127,0},
          fillPattern=FillPattern.HorizontalCylinder),
        Line(
          points={{54,12},{48,-16},{68,-16},{62,12},{54,12}},
          color={255,0,0},
          thickness=0.5,
          smooth=Smooth.None),
        Line(
          points={{-86,16},{-74,16},{-86,-16},{-74,-16},{-86,16}},
          color={255,0,0},
          thickness=0.5,
          smooth=Smooth.None),
        Line(
          points={{-80,16},{-80,60},{-40,60}},
          color={255,0,0},
          thickness=0.5,
          smooth=Smooth.None),
        Line(
          points={{-40,-74},{-80,-74},{-80,-16}},
          color={255,0,0},
          thickness=0.5,
          smooth=Smooth.None),
        Line(
          points={{58,-16},{58,-60},{40,-60}},
          color={255,0,0},
          thickness=0.5,
          smooth=Smooth.None),
        Line(
          points={{58,12},{58,46},{40,46}},
          color={255,0,0},
          thickness=0.5,
          smooth=Smooth.None),
        Line(
          points={{78,12},{72,-16},{92,-16},{86,12},{78,12}},
          color={255,0,0},
          thickness=0.5,
          smooth=Smooth.None),
        Line(
          points={{82,12},{82,60},{40,60}},
          color={255,0,0},
          thickness=0.5,
          smooth=Smooth.None),
        Line(
          points={{82,-16},{82,-74},{40,-74}},
          color={255,0,0},
          thickness=0.5,
          smooth=Smooth.None),
        Line(
          points={{-64,16},{-52,16},{-64,-16},{-52,-16},{-64,16}},
          color={255,0,0},
          thickness=0.5,
          smooth=Smooth.None),
        Line(
          points={{-58,16},{-58,46},{-40,46}},
          color={255,0,0},
          thickness=0.5,
          smooth=Smooth.None),
        Line(
          points={{-40,-60},{-58,-60},{-58,-16}},
          color={255,0,0},
          thickness=0.5,
          smooth=Smooth.None),
        Text(
          extent={{50,0},{66,-10}},
          lineColor={255,0,0},
          textString="A"),
        Text(
          extent={{74,0},{90,-10}},
          lineColor={255,0,0},
          textString="B")}),
    Diagram(
      graphics,
      coordinateSystem(
        preserveAspectRatio=true,
        extent={{-125,-125},{125,125}})));
end CycleIconBi;
