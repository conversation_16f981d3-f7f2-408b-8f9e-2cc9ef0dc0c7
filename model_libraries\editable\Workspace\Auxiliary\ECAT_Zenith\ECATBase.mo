within Workspace.Auxiliary.ECAT_Zenith;
model <PERSON>ATBase
  extends Workspace.Auxiliary.ECAT_Zenith.ChillerBase(
    nbrCircuit=2,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable CondCoilAirPressDrop_Pa,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable RefrigerantSST_K,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable RefrigerantSDT_K,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable RefrigerantSET_K,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable RefrigerantSCT_K,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable RefrigerantDGT_K,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable SuctionSuperheat_K,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable CondSubcooling_K,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable DischargeSuperheat_K,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATFixedVariable CompressorSpeed_rpm,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable CompressorFrequency_Hz,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable FanFrequency_Hz,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable FanSpeed_rpm,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable ElecFanBoxFrequency_Hz,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATVariable CondPumpSpeed_Hz,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATVariable CondPumpSpeed_rpm,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATVariable EvapPumpSpeed_Hz,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATVariable EvapPumpSpeed_rpm,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATVariable CondFanAirflowRate_m3s,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable CompressorPower_W,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable FanPower_W,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATFixedVariable Altitude_m,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATFixedVariable AmbientAirDBTemp_K,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATVariable HeatingAmbientAirWBTemp_K,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATFixedVariable HeatingAmbientAirDBTemp_K,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATFixedVariable AmbientAirRH_nd,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATFixedVariable CondBrineConcentration_nd,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATFixedVariable CondFoulingFactor_m2KW,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATFixedVariable EvapBrineConcentration_nd,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATFixedVariable EvapFoulingFactor_m2KW,
    redeclare.BOLT.InternalLibrary.ECATBlock.ECATVariable TargetCoolingCapacity_W,
    redeclare.BOLT.InternalLibrary.ECATBlock.ECATVariable TargetHeatingCapacity_W,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable TotalRefrigerantCharge_kg,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable TotalOilCharge_kg,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable PubUnitPower_W,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable PubCoolingCapacity_W,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable CondCoilHeatingCapacity_W,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable PubHeatingCapacity_W,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable PubHeatingCapacityInstantaneous_W,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable TotalCompressorPower_W,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable TotalFanPower_W,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable EvapPumpPower_W,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable CondPumpPower_W,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable EvapBrineIntPressDrop_Pa,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable EvapPumpTotalHead_m,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable EvapBrineDensity_kgm3,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable CondBrineIntPressDrop_Pa,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable CondPumpTotalHead_m,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable CondBrineDensity_kgm3,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable EvapBrineVelocity_mps,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable CondBrineVelocity_mps,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable EN14511PumpPowerCorrectionWithPump_W,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable EN14511PumpPowerCorrectionWithoutPump_W,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable CondEN14511PumpPowerCorrectionWithPump_W,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable CondEN14511PumpPowerCorrectionWithoutPump_W,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable CoolantFreezingTemp_K);
  replaceable.BOLT.InternalLibrary.ECATBlock.ECATVariable LoadRatio_nd
    annotation (Placement(transformation(extent={{42.809770255186784,29.522755519546052},{68.62186849081334,41.75677683170174}},origin={0.0,0.0},rotation=0.0)));
  replaceable BOLT.InternalLibrary.ECATBlock.ECATVariable HeatingAmbientAirRH_nd
    annotation (Placement(transformation(extent={{76.21253900052113,29.677062584251587},{102.0246372361477,41.91108389640728}},origin={0.0,0.0},rotation=0.0)));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          fillColor={122,114,0},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name"),
        Text(
          textString="ECAT",
          origin={0,-6},
          extent={{-98,48},{98,-48}})}));
end ECATBase;
