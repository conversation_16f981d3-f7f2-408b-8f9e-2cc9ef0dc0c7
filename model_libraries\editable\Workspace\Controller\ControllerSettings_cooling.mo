within Workspace.Controller;
model ControllerSettings_cooling
  extends.BOLT.InternalLibrary.BuildingBlocks.Icons.CtrlSettings;
  import SI=Modelica.SIunits;
  import fanspeed=Workspace.Controller.Components.Functions.fanSpeed;
  import Elec_Fan_Speed_Box=Workspace.Controller.Components.Functions.Elec_Fan_Speed_Box;
  // Import records
  //import .Workspace.Controller.Components.Records.fanCurveCoefficients;
  Real fanspeed_sp;
  Real FanSpeedBox;
  //speed of electrical fan box
  parameter Real[9] fancoefficients={0.198957,5.398716,-0.010786,0.00956,0.046429,0.033962,0.01804,0.010765,152.821278};
  parameter Real minfanfreq=100;
  parameter Real maxfanfreq=720;
  // Import functions
  //import BOLT.InternalLibrary.BuildingBlocks.Utilities.Math.Smoothing.softMin;
  //import BOLT.InternalLibrary.BuildingBlocks.Utilities.Math.Smoothing.softMax;
  //import .Workspace.Controller.Components.Functions.subcooling;
  //import .Workspace.Controller.Components.Functions.fanSpeed;
  .Modelica.Blocks.Sources.RealExpression T_sst_max_limit_block(
    y=SST_max-1)
    annotation (Placement(transformation(extent={{8.960344849242773,58.0},{39.03965515075723,78.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_dgt_max_limit_comp_block(
    y=DGT_max-5)
    annotation (Placement(transformation(extent={{8.960344849242773,25.71405301681466},{39.03965515075723,46.28594698318534}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression Freq_max_SDT_limit_comp_block(
    y=Table_Freq_max_SDT.y)
    annotation (Placement(transformation(extent={{8.960344849242773,-4.0},{39.03965515075723,16.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_sdt_min_limit_block(
    y=Table_SDT_min.y[1] + 12)
    annotation (Placement(transformation(extent={{8.960344849242773,-48.0},{39.03965515075723,-28.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression capacity_setpoint_block(
    y=Capacity_setpoint)
    annotation (Placement(transformation(extent={{6.960344849242773,-82.0},{37.03965515075723,-62.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression fanSpeed_setpoint_block(
    y=fanspeed_sp)
    annotation (Placement(transformation(extent={{8.960344849242773,-98.0},{39.03965515075723,-78.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_sst_min_limit_comp_block(
    y=SST_min)
    annotation (Placement(transformation(extent={{8.960344849242773,42.0},{39.03965515075723,62.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_sdt_max_limit_fan_block(
    y=Table_SDT_max.y[1]-7)
    annotation (Placement(transformation(extent={{9.177802896835313,-33.78254195240744},{39.25711319834977,-13.782541952407446}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_dgt_max_limit_fan_block(
    y=DGT_max-10)
    annotation (Placement(transformation(extent={{8.960344849242773,10.0},{39.03965515075723,30.0}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.MeasurementBus measurementBus
    annotation (Placement(transformation(extent={{-135.5275989484145,-25.43006851711062},{-95.52759894841446,14.569931482889379}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.LimitsBus limitsBus
    annotation (Placement(transformation(extent={{68.0,-50.0},{108.0,-10.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Tables.CombiTable2D Table_Freq_max_SDT(
    table={{0,333.15,338.15,343.15,349.15,355.15},{243.15,140,140,140,140,140},{258.15,140,140,140,130,120},{268.15,140,140,140,130,120},{273.15,140,130,120,110,100},{283.15,140,130,120,110,100},{288.15,120,120,120,100,100},{293.15,100,100,100,100,100},{298.15,100,100,100,100,100}},
    extrapolation=Modelica.Blocks.Types.Extrapolation.HoldLastPoint)
    annotation (Placement(transformation(extent={{-44.0,-82.0},{-24.0,-62.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Tables.CombiTable2D Table_Freq_min_SDT(
    table={{0,333.15,338.15,343.15,349.15,355.15},{243.15,30,30,30,30,30},{258.15,30,30,30,30,30},{268.15,30,30,30,30,30},{273.15,30,40,50,50,50},{283.15,30,40,50,50,50},{288.15,50,50,50,50,50},{293.15,50,50,50,50,50},{298.15,50,50,50,50,50}},
    extrapolation=Modelica.Blocks.Types.Extrapolation.HoldLastPoint)
    annotation (Placement(transformation(extent={{-44.0,-110.0},{-24.0,-90.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Tables.CombiTable1D Table_freq_min_SST(
    table={{243.15,30},{283.15,30},{288.15,50},{298.15,50}})
    annotation (Placement(transformation(extent={{-44.0,-162.0},{-24.0,-142.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Tables.CombiTable1D Table_freq_max_SST(
    table={{243.15,140},{283.15,140},{288.15,120},{293.15,100},{298.15,100}})
    annotation (Placement(transformation(extent={{-44.0,-136.0},{-24.0,-116.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Tables.CombiTable1D Table_SDT_min(
    table={{243.15,283.15},{273.15,283.15},{298.15,308.15}})
    annotation (Placement(transformation(extent={{-44.0,-56.0},{-24.0,-36.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression dT_ssh_setpoint_block(
    y=
      if is_manual_ssh then
        Manual_ssh
      else
        Table_ssh_setpoint.y[1])
    annotation (Placement(transformation(extent={{8.960344849242773,-64.0},{39.03965515075723,-44.0}},origin={0.0,0.0},rotation=0.0)));
  parameter.Modelica.SIunits.Temperature SST_min=274.65;
  parameter.Modelica.SIunits.Temperature DGT_max=423.15;
  .Modelica.Blocks.Tables.CombiTable1D Table_ssh_setpoint(
    table={{243.15,5},{283.15,5},{288.15,10},{298.15,10}})
    annotation (Placement(transformation(extent={{-44.0,0.0},{-24.0,20.0}},origin={0.0,0.0},rotation=0.0)));
  parameter.Modelica.SIunits.Power Capacity_setpoint=50000;
  .Modelica.Blocks.Sources.RealExpression Freq_mini(
    y=Table_freq_min_SST.y[1])
    annotation (Placement(transformation(extent={{7.430550864047689,-112.25351954035742},{37.50986116556214,-92.25351954035742}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression Freq_maxi(
    y=Table_freq_max_SST.y[1])
    annotation (Placement(transformation(extent={{8.36138296636306,-128.23572824681042},{38.44069326787751,-108.23572824681041}},origin={0.0,0.0},rotation=0.0)));
  parameter.Modelica.SIunits.Temperature SST_max=298.15;
  parameter Modelica.SIunits.Pressure extPressure_setpoint=80000;
  parameter Modelica.SIunits.Temperature LWT_setpoint=280.15;
  parameter Modelica.SIunits.Temperature EWT_setpoint=285.15;
  .Modelica.Blocks.Sources.RealExpression Freq_min_SDT_limit_comp_block(
    y=Table_Freq_min_SDT.y)
    annotation (Placement(transformation(extent={{8.960344849242773,-20.0},{39.03965515075723,0.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression extPressure_SetPoint(
    y=extPressure_setpoint)
    annotation (Placement(transformation(extent={{8.960344849242773,-146.0},{39.03965515075723,-126.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Tables.CombiTable1D Table_SDT_max(
    table={{243.15,333.15},{258.15,355.15},{288.15,355.15},{298.15,343.15}})
    annotation (Placement(transformation(extent={{-44.0,-30.0},{-24.0,-10.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_sdt_max_limit_comp_block(
    y=max(
      Table_SDT_max.y[1]-4,
      343.15))
    annotation (Placement(transformation(extent={{8.960344849242773,-164.0},{39.03965515075723,-144.0}},origin={0.0,0.0},rotation=0.0)));
  parameter Boolean is_manual_ssh=false;
  parameter Real Manual_ssh;
  .Modelica.Blocks.Sources.RealExpression LWT_SetPoint(
    y=LWT_setpoint)
    annotation (Placement(transformation(extent={{9.356567976834967,-180.77451707912064},{39.43587827834942,-160.77451707912064}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression EWT_SetPoint(
    y=EWT_setpoint)
    annotation (Placement(transformation(extent={{8.297420295827216,-196.8928124411362},{38.37673059734167,-176.8928124411362}},origin={0.0,0.0},rotation=0.0)));
protected
  .Modelica.Blocks.Interfaces.RealOutput T_lwt
    annotation (Placement(transformation(extent={{-91.04054495960074,22.944307605824214},{-51.04054495960074,62.944307605824214}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput T_oat
    annotation (Placement(transformation(extent={{-92.0,52.0},{-52.0,92.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Cp_frequency
    annotation (Placement(transformation(extent={{-93.05236235997744,78.26928611618315},{-53.05236235997744,118.26928611618315}},origin={0.0,0.0},rotation=0.0)));
equation
  connect(measurementBus.T_sst,Table_SDT_min.u[1])
    annotation (Line(points={{-115.52759894841448,-5.430068517110621},{-80,-5.430068517110621},{-80,-46},{-46,-46}},color={255,204,51}));
  connect(measurementBus.T_sst,Table_ssh_setpoint.u[1])
    annotation (Line(points={{-115.52759894841448,-5.430068517110621},{-46,-5.430068517110621},{-46,10}},color={255,204,51}));
  connect(Cp_frequency,measurementBus.compressorFrequency)
    annotation (Line(points={{-73.05236235997744,98.26928611618315},{-115.52759894841448,98.26928611618315},{-115.52759894841448,-5.430068517110621}},color={0,0,127}));
  connect(T_lwt,measurementBus.T_lwt)
    annotation (Line(points={{-71.04054495960074,42.944307605824214},{-115.52759894841448,42.944307605824214},{-115.52759894841448,-5.430068517110621}},color={0,0,127}));
  FanSpeedBox=Elec_Fan_Speed_Box(
    T_oat);
  fanspeed_sp=fanspeed(
    Cp_frequency,
    T_lwt,
    T_oat,
    fancoefficients,
    minfanfreq,
    maxfanfreq);
  connect(Freq_mini.y,limitsBus.Min_frequency)
    annotation (Line(points={{39.01382668063786,-102.25351954035742},{88,-102.25351954035742},{88,-30}},color={0,0,127}));
  connect(Freq_maxi.y,limitsBus.Max_frequency)
    annotation (Line(points={{39.94465878295324,-118.23572824681041},{88,-118.23572824681041},{88,-30}},color={0,0,127}));
  connect(measurementBus.T_sst,Table_Freq_min_SDT.u1)
    annotation (Line(points={{-115.52759894841448,-5.4300685171106196},{-80.27932949282425,-5.4300685171106196},{-80.27932949282425,-94},{-46,-94}},color={255,204,51}));
  connect(measurementBus.T_sdt,Table_Freq_min_SDT.u2)
    annotation (Line(points={{-115.52759894841448,-5.4300685171106196},{-80.27932949282425,-5.4300685171106196},{-80.27932949282425,-106},{-46,-106}},color={255,204,51}));
  connect(T_sdt_max_limit_fan_block.y,limitsBus.T_sdt_max_limit_fan)
    annotation (Line(points={{40.76107871342549,-23.782541952407446},{88,-23.782541952407446},{88,-30}},color={0,0,127}));
  connect(Freq_min_SDT_limit_comp_block.y,limitsBus.Freq_min_SDT_limit_comp)
    annotation (Line(points={{40.54362066583295,-10},{88,-10},{88,-30}},color={0,0,127}));
  connect(measurementBus.T_sst,Table_Freq_max_SDT.u1)
    annotation (Line(points={{-115.52759894841448,-5.4300685171106196},{-80,-5.4300685171106196},{-80,-66},{-46,-66}},color={255,204,51}));
  connect(measurementBus.T_sdt,Table_Freq_max_SDT.u2)
    annotation (Line(points={{-115.52759894841448,-5.4300685171106196},{-79.60663869811194,-5.4300685171106196},{-79.60663869811194,-78},{-46,-78}},color={255,204,51}));
  connect(Freq_min_SDT_limit_comp_block.y,limitsBus.Freq_min_SDT_limit_comp)
    annotation (Line(points={{38.42182066705807,-9.567403902178576},{63.12063363510373,-9.567403902178576},{63.12063363510373,-30.208163989221056},{87.81944660314939,-30.208163989221056}},color={0,0,127}));
  connect(extPressure_SetPoint.y,limitsBus.extPressure_SetPoint)
    annotation (Line(points={{40.54362066583295,-136},{88,-136},{88,-30}},color={0,0,127}));
  connect(measurementBus.T_sst,Table_SDT_max.u[1])
    annotation (Line(points={{-115.52759894841448,-5.4300685171106196},{-80,-5.4300685171106196},{-80,-20},{-46,-20}},color={255,204,51}));
  connect(T_sdt_max_limit_comp_block.y,limitsBus.T_sdt_max_limit_comp)
    annotation (Line(points={{40.54362066583295,-154},{88,-154},{88,-30}},color={0,0,127}));
  connect(measurementBus.T_sst,Table_freq_max_SST.u[1])
    annotation (Line(points={{-115.52759894841448,-5.4300685171106196},{-80.20011246049992,-5.4300685171106196},{-80.20011246049992,-126},{-46,-126}},color={255,204,51}));
  connect(measurementBus.T_sst,Table_freq_min_SST.u[1])
    annotation (Line(points={{-115.52759894841448,-5.4300685171106196},{-80,-5.4300685171106196},{-80,-152},{-46,-152}},color={255,204,51}));
  connect(T_sst_max_limit_block.y,limitsBus.T_sst_max_limit_EXV)
    annotation (Line(points={{40.54362066583295,68},{88,68},{88,-30}},color={0,0,127}));
  connect(T_sst_min_limit_comp_block.y,limitsBus.T_sst_min_limit_comp)
    annotation (Line(points={{40.54362066583295,52},{88,52},{88,-30}},color={0,0,127}));
  connect(T_dgt_max_limit_comp_block.y,limitsBus.T_dgt_max_limit_comp)
    annotation (Line(points={{40.54362066583295,36},{88,36},{88,-30}},color={0,0,127}));
  connect(T_dgt_max_limit_fan_block.y,limitsBus.T_dgt_max_limit_fan)
    annotation (Line(points={{40.54362066583295,20},{88,20},{88,-30}},color={0,0,127}));
  connect(Freq_max_SDT_limit_comp_block.y,limitsBus.Freq_max_SDT_limit_comp)
    annotation (Line(points={{40.54362066583295,6},{88,6},{88,-30}},color={0,0,127}));
  connect(Freq_max_SDT_limit_comp_block.y,limitsBus.Freq_max_SDT_limit_comp)
    annotation (Line(points={{40.54362066583295,6},{88,6},{88,-30}},color={0,0,127}));
  connect(T_dgt_max_limit_comp_block.y,limitsBus.T_dgt_max_limit_comp)
    annotation (Line(points={{40.54362066583295,36},{88,36},{88,-30}},color={0,0,127}));
  connect(Freq_min_SDT_limit_comp_block.y,limitsBus.Freq_min_SDT_limit_comp)
    annotation (Line(points={{40.54362066583295,-10},{88,-10},{88,-30}},color={0,0,127}));
  connect(T_sdt_max_limit_comp_block.y,limitsBus.T_sdt_max_limit_comp)
    annotation (Line(points={{40.54362066583295,-154},{88,-154},{88,-30}},color={0,0,127}));
  connect(extPressure_SetPoint.y,limitsBus.extPressure_SetPoint)
    annotation (Line(points={{40.54362066583295,-136},{88,-136},{88,-30}},color={0,0,127}));
  connect(Freq_maxi.y,limitsBus.Max_frequency)
    annotation (Line(points={{39.94465878295324,-118.23572824681041},{88,-118.23572824681041},{88,-30}},color={0,0,127}));
  connect(Freq_mini.y,limitsBus.Min_frequency)
    annotation (Line(points={{39.01382668063786,-102.25351954035742},{88,-102.25351954035742},{88,-30}},color={0,0,127}));
  connect(fanSpeed_setpoint_block.y,limitsBus.fanSpeed_setpoint)
    annotation (Line(points={{40.54362066583295,-88},{88,-88},{88,-30}},color={0,0,127}));
  connect(capacity_setpoint_block.y,limitsBus.capacity_setpoint)
    annotation (Line(points={{38.54362066583295,-72},{88,-72},{88,-30}},color={0,0,127}));
  connect(dT_ssh_setpoint_block.y,limitsBus.dT_ssh_setpoint)
    annotation (Line(points={{40.54362066583295,-54},{88,-54},{88,-30}},color={0,0,127}));
  connect(T_sdt_min_limit_block.y,limitsBus.T_sdt_min_limit)
    annotation (Line(points={{40.54362066583295,-38},{88,-38},{88,-30}},color={0,0,127}));
  connect(T_sdt_max_limit_fan_block.y,limitsBus.T_sdt_max_limit_fan)
    annotation (Line(points={{40.54362066583295,-24},{88,-24},{88,-30}},color={0,0,127}));
  connect(Freq_min_SDT_limit_comp_block.y,limitsBus.Freq_min_SDT_limit_comp)
    annotation (Line(points={{38.42182066705807,-9.567403902178576},{63.12063363510373,-9.567403902178576},{63.12063363510373,-30.208163989221056},{87.81944660314939,-30.208163989221056}},color={0,0,127}));
  connect(Freq_max_SDT_limit_comp_block.y,limitsBus.Freq_max_SDT_limit_comp)
    annotation (Line(points={{40.54362066583295,6},{88,6},{88,-30}},color={0,0,127}));
  connect(T_dgt_max_limit_fan_block.y,limitsBus.T_dgt_max_limit_fan)
    annotation (Line(points={{40.54362066583295,20},{88,20},{88,-30}},color={0,0,127}));
  connect(T_dgt_max_limit_comp_block.y,limitsBus.T_dgt_max_limit_comp)
    annotation (Line(points={{40.54362066583295,36},{88,36},{88,-30}},color={0,0,127}));
  connect(T_sst_min_limit_comp_block.y,limitsBus.T_sst_min_limit_comp)
    annotation (Line(points={{40.54362066583295,52},{88,52},{88,-30}},color={0,0,127}));
  connect(T_sst_max_limit_block.y,limitsBus.T_sst_max_limit_EXV)
    annotation (Line(points={{40.54362066583295,68},{88,68},{88,-30}},color={0,0,127}));
  connect(T_sdt_max_limit_comp_block.y,limitsBus.T_sdt_max_limit_comp)
    annotation (Line(points={{40.54362066583295,-154},{64.27181033291647,-154},{64.27181033291647,-30},{88,-30}},color={0,0,127}));
  connect(measurementBus.T_sst,Table_SDT_max.u[1])
    annotation (Line(points={{-115.52759894841448,-5.4300685171106196},{-80,-5.4300685171106196},{-80,-20},{-46,-20}},color={255,204,51}));
  connect(T_oat,measurementBus.T_oat)
    annotation (Line(points={{-71.37584785966354,71.78035701122342},{-115.52759894841448,71.78035701122342},{-115.52759894841448,-5.4300685171106196}},color={0,0,127}));
  connect(Freq_max_SDT_limit_comp_block.y,limitsBus.Freq_max_SDT_limit_comp)
    annotation (Line(points={{40.54362066583295,6},{88,6},{88,-30}},color={0,0,127}));
  connect(LWT_SetPoint.y,limitsBus.LWT_setpoint)
    annotation (Line(points={{40.93984379342514,-170.77451707912064},{88,-170.77451707912064},{88,-30}},color={0,0,127}));
  connect(EWT_SetPoint.y,limitsBus.EWT_setpoint)
    annotation (Line(points={{39.8806961124174,-186.8928124411362},{88,-186.8928124411362},{88,-30}},color={0,0,127}));
  annotation (
    Placement(
      transformation(
        extent={{-80,-80},{-60,-60}})));
end ControllerSettings_cooling;
