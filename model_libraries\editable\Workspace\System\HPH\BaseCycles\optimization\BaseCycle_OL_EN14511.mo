within Workspace.System.HPH.BaseCycles.optimization;
model BaseCycle_OL_EN14511
  extends.Workspace.System.HPH.BaseCycles.OpenLoop();
  .Workspace.Auxiliary.Calibration.CalibrationBlock calibrationBlock
    annotation (Placement(transformation(extent={{-146.0,42.0},{-126.0,62.0}},origin={0.0,0.0},rotation=0.0)));
  parameter Boolean is_monobloc=true
    "true if unit is monocircuit and false if unit is bi circuit";
  .Workspace.Auxiliary.EN14511Horizon.EN14511_CO eN14511_CO
    annotation (Placement(transformation(extent={{-144.0,96.0},{-124.0,116.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression brineDP(
    y=-externalSystem.summary.dp)
    annotation (Placement(transformation(extent={{-173.22936262267984,110.77063737732016},{-162.77063737732016,121.22936262267984}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression brineFlow(
    y=sinkBrine.Vd)
    annotation (Placement(transformation(extent={{-173.22936262267984,98.77063737732016},{-162.77063737732016,109.22936262267984}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression grossCoolCap(
    y=BlocA.systemVariables.summary.CoolCap_total+BlocB.systemVariables.summary.CoolCap_total)
    annotation (Placement(transformation(extent={{-173.22936262267984,82.77063737732016},{-162.77063737732016,93.22936262267984}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression grossPower(
    y=BlocA.systemVariables.summary.pow_total+BlocB.systemVariables.summary.pow_total)
    annotation (Placement(transformation(extent={{-173.22936262267984,74.77063737732016},{-162.77063737732016,85.22936262267984}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression airDP_A(
    y=BlocA.externalDuct.summary.dp)
    annotation (Placement(transformation(extent={{-173.22936262267984,118.77063737732016},{-162.77063737732016,129.22936262267984}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression airFlowA(
    y=BlocA.sinkAir.Vd_flow)
    annotation (Placement(transformation(extent={{-173.22936262267984,90.77063737732016},{-162.77063737732016,101.22936262267984}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression brineDP2(
    y=externalSystem.dp_loss)
    annotation (Placement(transformation(extent={{-173.8577539265228,106.57920452612709},{-162.1422460734772,113.42079547387291}},origin={0.0,0.0},rotation=0.0)));
equation
  connect(grossCoolCap.y,eN14511_CO.inst_gross_cap)
    annotation (Line(points={{-162.24770111505217,88},{-153.8238505575261,88},{-153.8238505575261,98.8},{-145.4,98.8}},color={0,0,127}));
  connect(grossPower.y,eN14511_CO.inst_gross_pow)
    annotation (Line(points={{-162.24770111505217,80},{-154,80},{-154,96.6},{-145.4,96.6}},color={0,0,127}));
  connect(airFlowA.y,eN14511_CO.q_air)
    annotation (Line(points={{-162.24770111505217,96},{-153.8238505575261,96},{-153.8238505575261,102.4},{-145.4,102.4}},color={0,0,127}));
  connect(airDP_A.y,eN14511_CO.DPe_air)
    annotation (Line(points={{-162.24770111505217,124},{-153.8238505575261,124},{-153.8238505575261,110.2},{-145.4,110.2}},color={0,0,127}));
  connect(brineDP.y,eN14511_CO.DPe)
    annotation (Line(points={{-162.24770111505217,116},{-153.8238505575261,116},{-153.8238505575261,112.2},{-145.4,112.2}},color={0,0,127}));
  connect(brineFlow.y,eN14511_CO.q)
    annotation (Line(points={{-162.24770111505217,104},{-153.8238505575261,104},{-153.8238505575261,104.6},{-145.4,104.6}},color={0,0,127}));
  connect(brineDP2.y,eN14511_CO.DPi)
    annotation (Line(points={{-161.55647068082493,110},{-161.55647068082493,106.4},{-145.4,106.4}},color={0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end BaseCycle_OL_EN14511;
