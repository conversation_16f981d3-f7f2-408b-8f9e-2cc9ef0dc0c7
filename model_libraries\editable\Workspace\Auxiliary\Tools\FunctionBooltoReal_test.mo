within Workspace.Auxiliary.Tools;
model FunctionBooltoReal_test
    
    extends Modelica.Icons.Function;
    import BoolToReal = Workspace.Auxiliary.Tools.booleanToReal;
    parameter Boolean bool_out = true;
    Real real_out;
    
    equation
    real_out = BoolToReal(bool_out);
    annotation(Icon(coordinateSystem(preserveAspectRatio = false,extent = {{-100.0,-100.0},{100.0,100.0}}),graphics = {Rectangle(lineColor={0,0,0},fillColor={230,230,230},fillPattern=FillPattern.Solid,extent={{-100.0,-100.0},{100.0,100.0}}),Text(lineColor={0,0,255},extent={{-150,150},{150,110}},textString="%name")}));
end FunctionBooltoReal_test;
