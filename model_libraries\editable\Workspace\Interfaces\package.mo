within Workspace;
package Interfaces
  expandable connector LimitsBus
    extends.Modelica.Icons.SignalBus;
    .Modelica.SIunits.Temperature T_sst_min_limit_comp
      "Saturated suction temprature min limit";
    .Modelica.SIunits.Temperature T_sst_min_limit_fan
      "Saturated suction temprature min limit";
    .Modelica.SIunits.Temperature T_dgt_max_limit_comp
      "Discharge gas temperature max limit";
    .Modelica.SIunits.Temperature T_dgt_max_limit_fan
      "Discharge gas temperature max limit";
    .Modelica.SIunits.Temperature T_sdt_max_limit_comp
      "Suction discharge temperature max limit";
    .Modelica.SIunits.Temperature Freq_max_SDT_limit_comp
      "Suction discharge temperature max limit";
    .Modelica.SIunits.Temperature Freq_min_SDT_limit_comp
      "Suction discharge temperature max limit";
    .Modelica.SIunits.Temperature T_sdt_max_limit_fan
      "Suction discharge temperature max limit";
    .Modelica.SIunits.Temperature T_sdt_min_limit
      "Suction discharge temperature min limit";
    .Modelica.SIunits.Power capacity_setpoint
      "Cooling capacity setpoint";
    Real fanSpeed_setpoint
      "Fan speed setpoint (Hz)";
    .Modelica.SIunits.TemperatureDifference dT_ssh_setpoint
      "Suction superheat setpoint";
    .Modelica.SIunits.Temperature T_sst_max_limit_EXV;
    .Modelica.SIunits.Temperature T_sst_max_limit_fan;
    .Modelica.SIunits.Frequency Max_frequency;
    .Modelica.SIunits.Frequency Min_frequency;
    Real extPressure_SetPoint;
    .Modelica.SIunits.Temperature LWT_setpoint;
    .Modelica.SIunits.Temperature EWT_setpoint;
  end LimitsBus;
  expandable connector MeasurementBus
    "MeasurmentBus with different nomenclature corresponding to the BOLTSteadyStateControls Workflow document"
    extends.Modelica.Icons.SignalBus;
    .Modelica.SIunits.Temperature T_oat
      "Outside air temperature";
    .Modelica.SIunits.Temperature T_ewt
      "Entering water temperature (evaporator)";
    .Modelica.SIunits.Temperature T_lwt
      "Leaving water temperature (evaporator)";
    .Modelica.SIunits.Temperature T_sst
      "Saturated suction temperature (compressor)";
    .Modelica.SIunits.Temperature T_sdt
      "Saturated discharge temperature (compressor)";
    .Modelica.SIunits.Temperature T_dgt
      "Discharge gas temperature (compressor)";
    .Modelica.SIunits.TemperatureDifference dT_ssh
      "Suction superheat (compressor)";
    .Modelica.SIunits.Power capacity
      "Cooling capacity";
    // Note that compressorFrequency is an output of compressor controller which is needed in controller settings.
    // It was added to this bus as this is more convenient than making a separate connection on a system level.
    .Modelica.SIunits.Frequency compressorFrequency
      "Compressor speed [Hz], output of a controller";
    Real Fan_signal
      "Fan signal in rpm";
    Real External_Pressure;
    .Modelica.SIunits.VolumeFlowRate CoolantFlowrate;
    Integer PumpControllerID;
  end MeasurementBus;
end Interfaces;
