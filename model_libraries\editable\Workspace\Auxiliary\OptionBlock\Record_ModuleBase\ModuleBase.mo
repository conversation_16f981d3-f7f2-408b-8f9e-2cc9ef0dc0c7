within Workspace.Auxiliary.OptionBlock.Record_ModuleBase;
record ModuleBase
  extends Modelica.Icons.Record;
  parameter Workspace.Auxiliary.OptionBlock.Record_Base.Selector BlocA
    "None";
  parameter Workspace.Auxiliary.OptionBlock.Record_Base.Selector BlocB
    "None";
  parameter Real[2] Coef_filter={0.0001,0}
    "hydraulic filter Pressure drop coefficients {Ka, Kb}";
  parameter Real[2] Coef_BufferTank={0.0001,0}
    "hydraulic filter Pressure drop coefficients {Ka, Kb}";
  parameter Boolean is_monobloc
    "None";
end ModuleBase;
