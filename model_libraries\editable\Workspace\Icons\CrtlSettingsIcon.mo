within Workspace.Icons;
partial model CrtlSettingsIcon
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false),
      graphics={
        Rectangle(
          extent={{-100,100},{100,-100}},
          lineColor={0,0,0},
          fillColor={255,255,255},
          fillPattern=FillPattern.Solid),
        Line(
          points={{-66,78},{-66,-86},{82,-86}},
          color={0,0,0}),
        Line(
          points={{-60,-2},{-38,-2},{30,46},{64,46}},
          color={217,67,180}),
        Line(
          points={{-60,-26},{-38,-26},{30,22},{64,22}},
          color={238,46,47}),
        Line(
          points={{-60,-46},{-38,-46},{30,2},{64,2}},
          color={0,140,72}),
        Line(
          points={{-60,-68},{-38,-68},{30,-20},{64,-20}},
          color={0,0,0})}),
    Diagram(
      coordinateSystem(
        preserveAspectRatio=false)));
end CrtlSettingsIcon;
