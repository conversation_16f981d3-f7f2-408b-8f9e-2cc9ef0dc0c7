within Workspace.Controller.Tests;
model test_ssh_setpoint
  .Modelica.Blocks.Tables.CombiTable1D ssh_setpoint(
    table={{-30,5},{5,5},{10,10},{25,10}})
    annotation (Placement(transformation(extent={{-18.0,-40.0},{2.0,-20.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression SST(
    y=20)
    annotation (Placement(transformation(extent={{-66.0,-42.0},{-46.0,-22.0}},origin={0.0,0.0},rotation=0.0)));
equation
  connect(SST.y,ssh_setpoint.u[1])
    annotation (Line(points={{-45,-32},{-32.5,-32},{-32.5,-30},{-20,-30}},color={0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end test_ssh_setpoint;
