within Workspace.Icons;
partial model UTCTSDPackage
  //extends VFD30XW.Icons.InitializationCycle2;
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=true,
        extent={{-125,-125},{125,125}}),
      graphics={
        Text(
          extent={{-132,-134},{136,-152}},
          lineColor={0,0,0},
          fillColor={255,0,0},
          fillPattern=FillPattern.Solid,
          textString="%name"),
        Bitmap(
          extent={{-124,-80},{124,90}},
          fileName="modelica://UTCTSD2/Images/UTC_icon.png")}),
    Diagram(
      graphics,
      coordinateSystem(
        preserveAspectRatio=true,
        extent={{-125,-125},{125,125}})));
end UTCTSDPackage;
