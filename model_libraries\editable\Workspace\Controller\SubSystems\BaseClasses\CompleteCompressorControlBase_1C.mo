within Workspace.Controller.SubSystems.BaseClasses;
partial model CompleteCompressorControlBase_1C
  extends BOLT.InternalLibrary.BuildingBlocks.Icons.Compressor;
  parameter Boolean isOff=false;
  parameter Real max_speed=140
    "Maximum speed of compressor A (Hz)";
  parameter Real min_speed=30
    "Minimum speed of compressor A (Hz)";
  parameter Real load_ratio=100
    "input percentage load";
  parameter Boolean is_load_ratio=false
    "When true set load_ration instead of target capacity";
  replaceable.Workspace.Controller.SubSystems.BaseClasses.CompressorControlBase compressorControl(
    crkIsOff=isOff,
    compressorFrequency_max=max_speed,
    compressorFrequency_min=min_speed)
    annotation (Placement(transformation(extent={{-16.0,46.000000000000014},{20.0,81.99999999999999}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.MeasurementBus measurementBus
    annotation (Placement(transformation(extent={{-124.7484012223562,49.06306556605638},{-75.03232352791517,98.7791432604974}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-128.7484012223562,3.0630655660563804},{-79.03232352791517,52.779143260497406}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.LimitsBus limitsBus
    annotation (Placement(transformation(extent={{-108.36101481229286,13.827518399294561},{-83.85826043743577,38.33027277415165}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-112.36101481229286,-32.17248160070544},{-87.85826043743577,-7.669727225848344}},origin={0.0,-20},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput actuatorSignal
    annotation (Placement(transformation(extent={{72.69063045405085,48.69063045405085},{103.30936954594915,79.30936954594915}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{90.69063045405085,-11.30936954594916},{121.30936954594915,19.30936954594916}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation capacity_error(
    ID=1,
    measurement=capacity,
    setPoint=capacity_setpoint,
    gain=1/(50000))
    annotation (Placement(transformation(extent={{-82.1111539986356,-9.620605},{-37.8888460013644,13.620605}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController capacity_controller(
    AV_min=0,
    AV_max=1,
    manualOff=is_load_ratio,
    AV_start=1,
    AV_value_off=load_ratio/(100))
    annotation (Placement(transformation(extent={{-17.874214936954402,-10.301069063045595},{2.3009850630455997,9.874298936954403}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Math.Gain gain(
    k=max_speed)
    annotation (Placement(transformation(extent={{18.110827416227572,-5.889172583772428},{29.889172583772428,5.889172583772428}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression min_speed_comp(
    y=min_speed)
    annotation (Placement(transformation(extent={{29.702259465301143,24.29774053469886},{14.297740534698857,39.70225946530114}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Math.Max max
    annotation (Placement(transformation(extent={{-7.789490597078327,7.789490597078327},{7.789490597078327,-7.789490597078327}},origin={-24.0,28.0},rotation=180.0)));
protected
  .Modelica.Blocks.Interfaces.RealOutput capacity
    annotation (Placement(transformation(extent={{-55.923164602347825,78.07683539765218},{-36.076835397652175,97.92316460234782}},origin={-24,-68},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput capacity_setpoint
    annotation (Placement(transformation(extent={{-79.92316460234782,-27.923164602347825},{-60.076835397652175,-8.076835397652175}},origin={0.0,0.0},rotation=0.0)));
equation
  connect(capacity_error.sensor,capacity_controller.errorSignal)
    annotation (Line(points={{-38.33106908133711,1.0703516},{-17.874214936954402,1.0703516},{-17.874214936954402,-0.2133850630455969}},color={28,108,200}));
  connect(capacity_setpoint,limitsBus.capacity_setpoint)
    annotation (Line(points={{-70,-18},{-96.0484,-18},{-96.0484,26.1402}},color={0,0,127}));
  connect(compressorControl.measurementBus,measurementBus)
    annotation (Line(points={{-16,74.79999999999998},{-99.89036237513568,74.79999999999998},{-99.89036237513568,73.9211044132769}},color={255,204,51}));
  connect(compressorControl.limitsBus,limitsBus)
    annotation (Line(points={{-14.56,42.4},{-96.1096,42.4},{-96.1096,26.0789}},color={255,204,51}));
  connect(capacity,measurementBus.capacity)
    annotation (Line(points={{-70,20},{-70,46.9606},{-99.7661,46.9606},{-99.7661,74.0454}},color={0,0,127}));
  connect(capacity_controller.actuatorSignal,gain.u)
    annotation (Line(points={{2.9062410630456004,-0.2133850630455969},{2.9062410630456004,0},{16.932992899473085,0}},color={0,0,127}));
  connect(gain.y,max.u2)
    annotation (Line(points={{30.47808984214967,0},{36.47808984214967,0},{36.47808984214967,23.326305641753002},{-14.652611283506008,23.326305641753002}},color={0,0,127}));
  connect(min_speed_comp.y,max.u1)
    annotation (Line(points={{13.527514588168742,32},{-14.652611283506008,32},{-14.652611283506008,32.673694358247}},color={0,0,127}));
  connect(max.y,compressorControl.compressorFrequency)
    annotation (Line(points={{-32.56843965678616,28},{-38.56843965678616,28},{-38.56843965678616,64},{-23.919999999999998,64}},color={0,0,127}));
  connect(compressorControl.actuatorSignal,actuatorSignal)
    annotation (Line(points={{40.879999999999995,64},{88,64}},color={0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end CompleteCompressorControlBase_1C;
