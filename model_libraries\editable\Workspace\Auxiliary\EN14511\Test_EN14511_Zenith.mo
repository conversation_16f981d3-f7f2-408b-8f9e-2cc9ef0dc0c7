within Workspace.Auxiliary.EN14511;
model Test_EN14511_Zenith
  .Modelica.Blocks.Sources.RealExpression DPe(
    y=DPeA)
    annotation (Placement(transformation(extent={{-84.39999999999999,38.0},{-64.39999999999999,58.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression DPi(
    y=DPiA)
    annotation (Placement(transformation(extent={{-84.39999999999999,24.0},{-64.39999999999999,44.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression q(
    y=qA)
    annotation (Placement(transformation(extent={{-84.0,10.0},{-64.0,30.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression gross_cap(
    y=CapA)
    annotation (Placement(transformation(extent={{-82.39999999999999,-4.0},{-62.39999999999999,16.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression gross_pow(
    y=PowA)
    annotation (Placement(transformation(extent={{-84.0,-18.0},{-64.0,2.0}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Auxiliary.EN14511.PostCalc_Bloc_HPH postCalc_Bloc_HPH(
    is_monobloc=true,
    Use_Pump=true)
    annotation (Placement(transformation(extent={{-32.0,16.0},{-12.0,36.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression DPe2(
    y=DPeB)
    annotation (Placement(transformation(extent={{-82.39999999999999,-41.43714186530147},{-62.39999999999999,-22.0295248013652}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression DPi2(
    y=DPiB)
    annotation (Placement(transformation(extent={{-84.39999999999999,-55.06666666666665},{-64.39999999999999,-35.06666666666665}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression q2(
    y=qB)
    annotation (Placement(transformation(extent={{-84.39999999999999,-67.06666666666665},{-64.39999999999999,-47.06666666666665}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression gross_cap2(
    y=CapB)
    annotation (Placement(transformation(extent={{-84.39999999999999,-81.06666666666665},{-64.39999999999999,-61.06666666666665}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression gross_pow2(
    y=PowB)
    annotation (Placement(transformation(extent={{-84.39999999999999,-95.06666666666665},{-64.39999999999999,-75.06666666666665}},origin={0.0,0.0},rotation=0.0)));
  parameter Real DPeA=15000;
  parameter Real DPiA=-15000;
  parameter Real qA=0.0024;
  parameter.Modelica.SIunits.Power CapA=50000;
  parameter.Modelica.SIunits.Power PowA=10000;
  parameter Real DPeB=15000;
  parameter Real DPiB=-15000;
  parameter Real qB=0.0024;
  parameter.Modelica.SIunits.Power CapB=50000;
  parameter.Modelica.SIunits.Power PowB=10000;
equation
  connect(DPe.y,postCalc_Bloc_HPH.measurementBusA.Dpe)
    annotation (Line(points={{-63.39999999999999,48},{-45.39999999999999,48},{-45.39999999999999,31},{-27.4,31}},color={0,0,127}));
  connect(DPi.y,postCalc_Bloc_HPH.measurementBusA.Dpi)
    annotation (Line(points={{-63,34},{-45.39999999999999,34},{-45.39999999999999,31},{-27.4,31}},color={0,0,127}));
  connect(q.y,postCalc_Bloc_HPH.measurementBusA.q)
    annotation (Line(points={{-63,20},{-45.39999999999999,20},{-45.39999999999999,31},{-27.4,31}},color={0,0,127}));
  connect(gross_cap.y,postCalc_Bloc_HPH.measurementBusA.capacity)
    annotation (Line(points={{-61.39999999999999,6},{-44.39999999999999,6},{-44.39999999999999,31},{-27.4,31}},color={0,0,127}));
  connect(gross_pow.y,postCalc_Bloc_HPH.measurementBusA.pow_total)
    annotation (Line(points={{-63,-8},{-45.39999999999999,-8},{-45.39999999999999,31},{-27.4,31}},color={0,0,127}));
  connect(DPe2.y,postCalc_Bloc_HPH.measurementBusB.Dpe)
    annotation (Line(points={{-61.39999999999999,-31.733333333333334},{-44.699999999999996,-31.733333333333334},{-44.699999999999996,20.6},{-28,20.6}},color={0,0,127}));
  connect(DPi2.y,postCalc_Bloc_HPH.measurementBusB.Dpi)
    annotation (Line(points={{-63.39999999999999,-45.06666666666665},{-45.699999999999996,-45.06666666666665},{-45.699999999999996,20.6},{-28,20.6}},color={0,0,127}));
  connect(q2.y,postCalc_Bloc_HPH.measurementBusB.q)
    annotation (Line(points={{-63.39999999999999,-57.06666666666665},{-45.699999999999996,-57.06666666666665},{-45.699999999999996,20.6},{-28,20.6}},color={0,0,127}));
  connect(gross_cap2.y,postCalc_Bloc_HPH.measurementBusB.capacity)
    annotation (Line(points={{-63.39999999999999,-71.06666666666665},{-45.699999999999996,-71.06666666666665},{-45.699999999999996,20.6},{-28,20.6}},color={0,0,127}));
  connect(gross_pow2.y,postCalc_Bloc_HPH.measurementBusB.pow_total)
    annotation (Line(points={{-63.39999999999999,-85.06666666666665},{-45.699999999999996,-85.06666666666665},{-45.699999999999996,20.6},{-28,20.6}},color={0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end Test_EN14511_Zenith;
