within Workspace.Auxiliary;

model Switch_param_test
    .Modelica.Blocks.Logical.Switch Gain annotation(Placement(transformation(extent = {{-7.247576259480241,-7.1372174756078675},{7.77381945482884,7.884178238701219}},origin = {0.0,0.0},rotation = 0.0)));
    .Modelica.Blocks.Sources.RealExpression LWT_gain(y = Gain_1) annotation(Placement(transformation(extent = {{-38.16569372830318,0.10339967847459341},{-25.807590583537152,12.461502823240627}},origin = {0.0,0.0},rotation = 0.0)));
    .Modelica.Blocks.Sources.RealExpression extPressure_gain(y = Gain_2) annotation(Placement(transformation(extent = {{-37.72838912568652,-19.081072638004173},{-25.37028598092047,-6.722969493238134}},origin = {0.0,0.0},rotation = 0.0)));
    .Modelica.Blocks.Sources.BooleanExpression LWT_control_switch(y = Bool) annotation(Placement(transformation(extent = {{-38.804787834929826,14.364510609981654},{-26.446684690163778,26.722613754747687}},origin = {0.0,0.0},rotation = 0.0)));
    parameter Boolean Bool = false;
    parameter Real Gain_1 = 0.1;
    parameter Real Gain_2 = -0.001;
    .Workspace.Auxiliary.Switch_param switch_param(Bool = Bool,Gain_1 = Gain_1,Gain_2 = Gain_2) annotation(Placement(transformation(extent = {{-10.64014608750196,-49.51891721664809},{9.35985391249804,-29.51891721664809}},origin = {0.0,0.0},rotation = 0.0)));
equation
    connect(LWT_gain.y,Gain.u1) annotation(Line(points = {{-25.18968542629885,6.28245125085761},{-25.18968542629885,6.38203866727031},{-8.74971583091115,6.38203866727031}},color = {0,0,127}));
    connect(extPressure_gain.y,Gain.u3) annotation(Line(points = {{-24.752380823682167,-12.902021065621154},{-24.752380823682167,-5.635077904176958},{-8.74971583091115,-5.635077904176958}},color = {0,0,127}));
    connect(LWT_control_switch.y,Gain.u2) annotation(Line(points = {{-25.828779532925477,20.54356218236467},{-14.437040256025695,20.54356218236467},{-14.437040256025695,0.37348038154667584},{-8.74971583091115,0.37348038154667584}},color = {255,0,255}));
    annotation(Icon(coordinateSystem(preserveAspectRatio = false,extent = {{-100.0,-100.0},{100.0,100.0}}),graphics = {Rectangle(lineColor={0,0,0},fillColor={230,230,230},fillPattern=FillPattern.Solid,extent={{-100.0,-100.0},{100.0,100.0}}),Text(lineColor={0,0,255},extent={{-150,150},{150,110}},textString="%name")}));
end Switch_param_test;
