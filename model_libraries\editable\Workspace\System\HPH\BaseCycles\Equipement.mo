within Workspace.System.HPH.BaseCycles;
model Equipement
  import CoolantCommonMedium=BOLT.InternalLibrary.Media.Coolant.CoolantCommon;
  .Workspace.System.HPH.BaseCycles.OL BlockA(
    EWT=EWT,
    LWT=LWT,
    OAT=OAT,
    Water_pressure=Water_pressure,
    isOff_ref=isOFFA,
    CoolantMedium=CoolantMedium,
    BrineConcentration=BrineConcentration,
    Use_EN=Use_EN,
    use_Z_in=false,
    selector_Comp=selector_Comp[1],
    capacity_design=capacity_design[1],
    CompVoltage=CompVoltage[1],
    fmax=fmax[1],
    fmin=fmin[1],
    EXV_main=EXV_main[1],
    Dport_coolant_a=Dport_coolant_a[1],
    Dport_coolant_b=Dport_coolant_b[1],
    Dport_ref_a=Dport_ref_a[1],
    Dport_ref_b=Dport_ref_b[1],
    nPlate=nPlate[1],
    selector_geo_BPHE=selector_geo_BPHE[1],
    nCoils=nCoils[1],
    Itube=Itube[1],
    nCir=nCir[1],
    Ntube=Ntube[1],
    Nrow=Nrow[1],
    Ltube=Ltube[1],
    Dotube=Dotube[1],
    Ttube=Ttube[1],
    Ptube=Ptube[1],
    Prow=Prow[1],
    Dfin=Dfin[1],
    Tfin=Tfin[1],
    Fw_fan=Fw_fan[1],
    max_fan_frequency=max_fan_frequency[1],
    fanCurveCoefficientsCooling=fanCurveCoefficientsCooling[1,:],
    fanCurveCoefficientsHeating=fanCurveCoefficientsHeating[1,:],
    Suction_line_diameter=Suction_line_diameter[1],
    Suction_line_length=Suction_line_length[1],
    coil_line_length=coil_line_length[1],
    liquid_line_diameter=liquid_line_diameter[1],
    liquid_line_length=liquid_line_length[1],
    discharge_line_diameter=discharge_line_diameter[1],
    discharge_line_length=discharge_line_length[1],
    EXV_in_line_diameter=EXV_in_line_diameter[1],
    EXV_in_line_length=EXV_in_line_length[1],
    Ac_duct=Ac_duct[1],
    Ka_duct=Ka_duct[1],
    UA_duct=UA_duct[1],
    coil_line_diameter=coil_line_diameter[1],
    selector_pump=selector_pump[1],
    isCoating=isCoating,
    CondFoulingFactor=CondFoulingFactor,
    relative_humidity=relative_humidity,
    PDC_4WV=PDC_4WV[1],
    FW=FW[1],
    use_Calib=use_Calib,
    Zflow_heatcap=Zflow_heatcap[1],
    Zpower_SH=Zpower_SH[1],
    Zpower_DGT=Zpower_DGT[1],
    Zcond_HPH_cst=Zcond_HPH_cst[1],
    Zcond_HPH_heatcap=Zcond_HPH_heatcap[1],
    Zcond_HPH_SST=Zcond_HPH_SST[1],
    Zcond_HPC_DGT=Zcond_HPC_DGT[1],
    Zcond_HPC_Ncomp=Zcond_HPC_Ncomp[1],
    Zcond_HPC_SDT=Zcond_HPC_SDT[1],
    Zcond_HPC_cst=Zcond_HPC_cst[1],
    Zevap_HPH_cst=Zevap_HPH_cst[1],
    Zevap_HPH_SST=Zevap_HPH_SST[1],
    Zevap_HPH_Ncomp=Zevap_HPH_Ncomp[1],
    Zevap_HPH_heatcap=Zevap_HPH_heatcap[1],
    Zevap_HPC_cst=Zevap_HPC_cst[1],
    Zevap_HPC_heatcap=Zevap_HPC_heatcap[1],
    Zevap_HPC_SST=Zevap_HPC_SST[1],
    Zevap_coated_HPH=Zevap_coated_HPH[1],
    Zflow_intercept=Zflow_intercept[1],
    Zflow_Ncomp=Zflow_Ncomp[1],
    Zflow_SST=Zflow_SST[1],
    Zflow_SDT=Zflow_SDT[1],
    Zpower_intercept=Zpower_intercept[1],
    Zpower_SST=Zpower_SST[1],
    Zpower_Zflow=Zpower_Zflow[1],
    Zcond_HPH_SDT=Zcond_HPH_SDT[1],
    SC_fixed=SC_fixed[1],
    ssh_setPoint=ssh_setPoint[1],
    SC_setpoint=SC_setpoint[1],
    Mref_fixed=Mref_fixed[1],
    Mref=Mref[1])
    annotation (Placement(transformation(extent={{-54.0,42.0},{-34.0,62.0}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.System.HPH.BaseCycles.OL BlockB(
    isOff=is_monobloc,
    EWT=EWT,
    LWT=LWT,
    OAT=OAT,
    Water_pressure=Water_pressure,
    isOff_ref=isOFFB or is_monobloc,
    CoolantMedium=CoolantMedium,
    BrineConcentration=BrineConcentration,
    Use_EN=Use_EN,
    use_Z_in=false,
    selector_Comp=selector_Comp[2],
    capacity_design=capacity_design[2],
    CompVoltage=CompVoltage[2],
    fmax=fmax[2],
    fmin=fmin[2],
    EXV_main=EXV_main[2],
    Dport_coolant_a=Dport_coolant_a[2],
    Dport_coolant_b=Dport_coolant_b[2],
    Dport_ref_a=Dport_ref_a[2],
    Dport_ref_b=Dport_ref_b[2],
    nPlate=nPlate[2],
    selector_geo_BPHE=selector_geo_BPHE[2],
    nCoils=nCoils[2],
    Itube=Itube[2],
    nCir=nCir[2],
    Ntube=Ntube[2],
    Nrow=Nrow[2],
    Ltube=Ltube[2],
    Dotube=Dotube[2],
    Ttube=Ttube[2],
    Ptube=Ptube[2],
    Prow=Prow[2],
    Dfin=Dfin[2],
    Tfin=Tfin[2],
    Fw_fan=Fw_fan[2],
    max_fan_frequency=max_fan_frequency[2],
    fanCurveCoefficientsCooling=fanCurveCoefficientsCooling[2,:],
    fanCurveCoefficientsHeating=fanCurveCoefficientsHeating[2,:],
    Suction_line_diameter=Suction_line_diameter[2],
    Suction_line_length=Suction_line_length[2],
    coil_line_diameter=coil_line_diameter[2],
    coil_line_length=coil_line_length[2],
    liquid_line_diameter=liquid_line_diameter[2],
    liquid_line_length=liquid_line_length[2],
    discharge_line_diameter=discharge_line_diameter[2],
    discharge_line_length=discharge_line_length[2],
    EXV_in_line_diameter=EXV_in_line_diameter[2],
    EXV_in_line_length=EXV_in_line_length[2],
    Ac_duct=Ac_duct[2],
    Ka_duct=Ka_duct[2],
    UA_duct=UA_duct[2],
    selector_pump=selector_pump[2],
    isCoating=isCoating,
    CondFoulingFactor=CondFoulingFactor,
    relative_humidity=relative_humidity,
    PDC_4WV=PDC_4WV[2],
    FW=FW[2],
    use_Calib=use_Calib,
    Zflow_heatcap=Zflow_heatcap[2],
    Zpower_SH=Zpower_SH[2],
    Zpower_DGT=Zpower_DGT[2],
    Zcond_HPH_cst=Zcond_HPH_cst[2],
    Zcond_HPH_heatcap=Zcond_HPH_heatcap[2],
    Zcond_HPH_SST=Zcond_HPH_SST[2],
    Zcond_HPC_DGT=Zcond_HPC_DGT[2],
    Zcond_HPC_Ncomp=Zcond_HPC_Ncomp[2],
    Zcond_HPC_SDT=Zcond_HPC_SDT[2],
    Zcond_HPC_cst=Zcond_HPC_cst[2],
    Zevap_HPH_cst=Zevap_HPH_cst[2],
    Zevap_HPH_SST=Zevap_HPH_SST[2],
    Zevap_HPH_Ncomp=Zevap_HPH_Ncomp[2],
    Zevap_HPH_heatcap=Zevap_HPH_heatcap[2],
    Zevap_HPC_cst=Zevap_HPC_cst[2],
    Zevap_HPC_heatcap=Zevap_HPC_heatcap[2],
    Zevap_HPC_SST=Zevap_HPC_SST[2],
    Zevap_coated_HPH=Zevap_coated_HPH[2],
    Zflow_intercept=Zflow_intercept[2],
    Zflow_Ncomp=Zflow_Ncomp[2],
    Zflow_SST=Zflow_SST[2],
    Zflow_SDT=Zflow_SDT[2],
    Zpower_intercept=Zpower_intercept[2],
    Zpower_SST=Zpower_SST[2],
    Zpower_Zflow=Zpower_Zflow[2],
    Zcond_HPH_SDT=Zcond_HPH_SDT[2],
    ssh_setPoint=ssh_setPoint[2],
    SC_setpoint=SC_setpoint[2],
    SC_fixed=SC_fixed[2],
    Mref_fixed=Mref_fixed[2],
    Mref=Mref[2])
    annotation (Placement(transformation(extent={{54.0,44.0},{34.0,64.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.Mixer mixer(
    fa_fixed=false,
    isOff_b=is_monobloc,
    T_start=LWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration)
    annotation (Placement(transformation(extent={{-10.0,-10.0},{10.0,10.0}},origin={-2.6003915644383007,66.06009079921068},rotation=90.0)));
  parameter Boolean Use_pump;
  parameter Boolean use_Calib;
  parameter Boolean is_monobloc;
  parameter Real Water_pressure=200000;
  .BOLT.CoolantMisc.Split split_pumpA(
    mDot_a_start=
      if not Use_pump then
        mdot_start
      else
        0,
    mDot_b_start=
      if Use_pump then
        mdot_start
      else
        0,
    p_start=Water_pressure,
    T_start=EWT,
    isOff_b=not Use_pump,
    isOff_a=Use_pump,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    fa_set=
      if Use_pump then
        0
      else
        1)
    annotation (Placement(transformation(extent={{-5.275067375849304,-5.275067375849304},{5.275067375849304,5.275067375849304}},origin={-99.73066594633582,-56.58705980812161},rotation=90.0)));
  .BOLT.CoolantMisc.PumpPoly pumpPolyA(
    T_start=EWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff=not Use_pump,
    selector=selector_pump[1],
    k_pow=1)
    annotation (Placement(transformation(extent={{8.39339786044772,-8.39339786044772},{-8.39339786044772,8.39339786044772}},origin={-65.08160928199607,-29.698159792787976},rotation=-90.0)));
  .BOLT.CoolantMisc.ReducedPipe check_valve(
    T_start=EWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    Ka_set=0.2322,
    Kb=1.5823)
    annotation (Placement(transformation(extent={{-6.386404559420782,-6.386404559420782},{6.386404559420782,6.386404559420782}},origin={-2.4147855791865496,-104.77814292215895},rotation=90.0)));
  .BOLT.CoolantMisc.Split split_bloc(
    mDot_a_start=mdot_start,
    mDot_b_start=
      if not is_monobloc then
        mdot_start
      else
        0,
    isOff_b=is_monobloc,
    T_start=EWT,
    p_start=Water_pressure,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration)
    annotation (Placement(transformation(extent={{-5.275067375849304,-5.275067375849304},{5.275067375849304,5.275067375849304}},origin={-2.168738601331569,-75.78324388608779},rotation=90.0)));
  .BOLT.CoolantMisc.ReducedPipe external_system(
    T_start=LWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    Ka_fixed=false)
    annotation (Placement(transformation(extent={{-6.386404559420779,-6.386404559420782},{6.386404559420779,6.386404559420782}},origin={-2.6003915644383007,102.06009079921068},rotation=90.0)));
  .BOLT.CoolantMisc.Split split_pumpB(
    mDot_a_start=
      if Use_pump and not is_monobloc then
        mdot_start
      else
        0,
    mDot_b_start=
      if not Use_pump and not is_monobloc then
        mdot_start
      else
        0,
    isOff_b=is_monobloc or Use_pump,
    T_start=EWT,
    p_start=Water_pressure,
    isOff_a=is_monobloc or not Use_pump,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    fa_set=
      if Use_pump then
        1
      else
        0)
    annotation (Placement(transformation(extent={{-5.275067375849304,-5.275067375849304},{5.275067375849304,5.275067375849304}},origin={97.91362258255033,-57.40960626581417},rotation=90.0)));
  .BOLT.CoolantMisc.PumpPoly pumpPolyB(
    isOff=not Use_pump or is_monobloc,
    T_start=EWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    selector=selector_pump[2],
    k_pow=1)
    annotation (Placement(transformation(extent={{-8.39339786044772,-8.39339786044772},{8.39339786044772,8.39339786044772}},origin={57.59422308194374,-29.0322955537304},rotation=90.0)));
  .BOLT.InternalLibrary.BuildingBlocks.Coolant.Ports.FluidPort_a coolant_in(
    CoolantMedium=CoolantMedium,
    Xi_set=BrineConcentration)
    annotation (Placement(transformation(extent={{-13.480089871014542,-156.7059464254537},{6.519910128985458,-136.7059464254537}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.InternalLibrary.BuildingBlocks.Coolant.Ports.FluidPort_b coolant_out(
    CoolantMedium=CoolantMedium,
    Xi_set=BrineConcentration)
    annotation (Placement(transformation(extent={{-12.6003915644383,114.06009079921068},{7.399608435561699,134.06009079921068}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput EXV_controller_A
    annotation (Placement(transformation(extent={{-95.37224004817512,65.18237406821589},{-103.73623419313878,73.54636821317959}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={-110,25})));
  .Modelica.Blocks.Interfaces.RealInput Fan_controller_A
    annotation (Placement(transformation(extent={{-93.68762778007806,55.21653743265198},{-103.01998396510851,64.54889361768241}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={-110,-25})));
  .Modelica.Blocks.Interfaces.RealInput Compressor_controller_B
    annotation (Placement(transformation(extent={{82.74141595718798,71.24016805630758},{90.48280695230586,78.98155905142546}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={110,-75})));
  .Modelica.Blocks.Interfaces.RealInput EXV_controller_B
    annotation (Placement(transformation(extent={{83.27294414718028,62.48467763450602},{90.60422313218908,69.81595661951482}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={110,25})));
  .Modelica.Blocks.Interfaces.RealInput Fan_controller_B
    annotation (Placement(transformation(extent={{82.93873487109778,54.18966501312022},{91.11881075280178,62.36974089482419}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={110,-25})));
  .Modelica.Blocks.Interfaces.RealInput Compressor_controller_A
    annotation (Placement(transformation(extent={{-94.52303104173927,74.80176781892371},{-103.35490732000406,83.63364409718852}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={-110,-75})));
  .Workspace.Interfaces.MeasurementBus measurementBusA
    annotation (Placement(transformation(extent={{-40.15608500378272,148.13769465476108},{-15.20881689978215,173.08496275876163}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={-80,100})));
  .Workspace.Interfaces.MeasurementBus measurementBusB
    annotation (Placement(transformation(extent={{9.939416093258004,147.5405819008838},{36.08090970501311,173.6820755126389}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={80,100})));
  parameter.Modelica.SIunits.Temperature EWT=285.15;
  parameter.Modelica.SIunits.Temperature LWT=280.15;
  parameter.Modelica.SIunits.Temperature OAT=308.15;
  parameter Real relative_humidity=0.87;
  .BOLT.BoundaryNode.Coolant.Node node_blocA_out(
    m_flow_start=mdot_start,
    T_start=LWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration)
    annotation (Placement(transformation(extent={{-22.619641313530828,45.72354917436189},{-14.619641313530828,53.72354917436189}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Node node_blocB_out(
    m_flow_start=
      if not is_monobloc then
        mdot_start
      else
        0,
    T_start=LWT,
    isOff=is_monobloc,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration)
    annotation (Placement(transformation(extent={{17.716900311317985,45.72354917436189},{9.716900311317985,53.72354917436189}},origin={0.0,0.0},rotation=0.0)));
  parameter Boolean isOFFA=false;
  parameter Boolean isOFFB=false;
  .BOLT.BoundaryNode.Coolant.Node node_coolant_in_pumpB(
    m_flow_start=
      if not is_monobloc then
        mdot_start
      else
        0,
    T_start=EWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff=is_monobloc)
    annotation (Placement(transformation(extent={{39.44772772364737,-69.23333333333332},{47.44772772364737,-61.23333333333332}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Node node_coolant_in_pumpA(
    m_flow_start=mdot_start,
    T_start=EWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration)
    annotation (Placement(transformation(extent={{-39.40982391781609,-68.02469182603615},{-47.40982391781609,-60.02469182603615}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Node node_coolant_in_splitbloc(
    m_flow_start=mdot_start,
    T_start=EWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration)
    annotation (Placement(transformation(extent={{4.0,-4.0},{-4.0,4.0}},origin={-2.502071934664909,-89.1165772194211},rotation=-90.0)));
  .BOLT.BoundaryNode.Coolant.Node node_coolant_source(
    m_flow_start=mdot_start,
    T_start=EWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration)
    annotation (Placement(transformation(extent={{4.0,-4.0},{-4.0,4.0}},origin={-3.0687386013315745,-121.88324388608778},rotation=-90.0)));
  // Declaration of Coolant Medium Package
  //package CoolantCommonMedium =
  //.BOLT.InternalLibrary.Media.Coolant.CoolantCommon                             "Coolant Medium Package" annotation ();
  // Declaration of Coolant Medium at Evaporator Circuit
  parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector CoolantMedium=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector.FW
    "Coolant Medium Selection"
    annotation (Dialog(group="Medium"));
  final parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Empty coolantInf=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.getSelector(
    CoolantMedium)
    "Coolant Medium"
    annotation ();
  CoolantCommonMedium.Temperature FreezTemp=CoolantCommonMedium.freezeTemp(
    coolantInf,
    BrineConcentration,
    1);
  parameter Real BrineConcentration=0.2
    annotation (Dialog(group="Medium"));
  .BOLT.CoolantMisc.ReducedPipe gaz_separatorA(
    T_start=EWT,
    Ka_set=4,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration)
    annotation (Placement(transformation(extent={{-6.076246165826419,-6.076246165826419},{6.076246165826419,6.076246165826419}},origin={-101.26278268171433,33.30635426390258},rotation=90.0)));
  .BOLT.CoolantMisc.ReducedPipe gaz_separatorB(
    T_start=EWT,
    Ka_set=4,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff=is_monobloc)
    annotation (Placement(transformation(extent={{-6.076246165826419,-6.076246165826419},{6.076246165826419,6.076246165826419}},origin={97.20855048825722,31.723549174361892},rotation=90.0)));
  .BOLT.CoolantMisc.ReducedPipe PipingA(
    T_start=EWT,
    Ka_set=0.738,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    Kb=2.3384)
    annotation (Placement(transformation(extent={{-6.076246165826419,-6.076246165826419},{6.076246165826419,6.076246165826419}},origin={-101.26278268171433,15.306354263902577},rotation=90.0)));
  .BOLT.CoolantMisc.ReducedPipe PipingB(
    T_start=EWT,
    Ka_set=0.738,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff=is_monobloc,
    Kb=2.3384)
    annotation (Placement(transformation(extent={{-6.076246165826419,-6.076246165826419},{6.076246165826419,6.076246165826419}},origin={97.57846085003577,13.7235491743619},rotation=90.0)));
  .BOLT.BoundaryNode.Shaft.Source shafA(
    isOff=not Use_pump,
    use_speed_in=true)
    annotation (Placement(transformation(extent={{-48.2918498052081,-7.32838118682638},{-40.2918498052081,0.6716188131736196}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Shaft.Source shafB(
    isOff=not Use_pump or is_monobloc,
    use_speed_in=true)
    annotation (Placement(transformation(extent={{41.23789886228211,-6.614264544162268},{33.23789886228211,1.3857354558377324}},origin={0.0,0.0},rotation=0.0)));
  parameter.Modelica.SIunits.Pressure Pdispo=50000;
  .BOLT.BoundaryNode.Coolant.Node node_out(
    T_start=LWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration)
    annotation (Placement(transformation(extent={{-4.0,-4.0},{4.0,4.0}},origin={-2.6003915644383007,86.06009079921068},rotation=90.0)));
  .Modelica.Blocks.Sources.RealExpression controlledCapacityA(
    y=
      if is_monobloc then
        controlledCapacity
      else
        controlledCapacity/2)
    annotation (Placement(transformation(extent={{-51.36273640817607,184.61132870676136},{-31.36273640817607,204.61132870676136}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression controlledCapacityB(
    y=controlledCapacity/2)
    annotation (Placement(transformation(extent={{45.010162899135565,184.61132870676136},{25.010162899135565,204.61132870676136}},origin={0.0,0.0},rotation=0.0)));
  Real controlledCapacity;
  Real controlledPower;
  parameter.Modelica.SIunits.Power Elec_box_power=160;
  .Modelica.Blocks.Sources.RealExpression External_Pressure(
    y=external_system.dp_value.y)
    annotation (Placement(transformation(extent={{-10.0,-10.0},{10.0,10.0}},origin={-4.75201389559944,219.19566392473865},rotation=-90.0)));
  .Modelica.Blocks.Interfaces.RealInput ActuatorPumpUser_A
    annotation (Placement(transformation(extent={{-46.864319381851224,10.298758953189513},{-56.19667556688167,19.631115138219947}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={-110,70})));
  .Modelica.Blocks.Interfaces.RealInput ActuatorPumpUser_B
    annotation (Placement(transformation(extent={{4.666178092515224,-4.666178092515217},{-4.666178092515224,4.666178092515217}},origin={43.02729244108584,15.820655037491726},rotation=-180.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={110,70})));
  .Workspace.Auxiliary.EN14511.EN14511_HPC_HPH eN14511(
    heating_mode=true,
    ie=0.88,
    isOffB=isOFFB or is_monobloc,
    isOffA=isOFFA,
    integrated_pump=Use_pump)
    annotation (Placement(transformation(extent={{-216.44542258813084,116.0},{-196.44542258813084,136.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression DPe(
    y=external_system.summary.dp)
    annotation (Placement(transformation(extent={{-267.5940885603243,141.78691261857034},{-247.59408856032428,161.78691261857034}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression DPi(
    y=external_system.summary.dp)
    annotation (Placement(transformation(extent={{-266.92907571496346,127.09355202715827},{-246.92907571496346,147.9802732099824}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression q(
    y=node_out.summary.Vd)
    annotation (Placement(transformation(extent={{-266.29360380627423,111.83616679299033},{-246.29360380627423,132.72288797581453}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression q_air_A_B(
    y=0)
    annotation (Placement(transformation(extent={{-266.2591014056851,97.09355202715824},{-246.2591014056851,117.98027320998244}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression gross_cap(
    y=BlockA.systemVariables.summary.HeatCap_total+BlockB.systemVariables.summary.HeatCap_total)
    annotation (Placement(transformation(extent={{-266.0,84.0},{-246.0,104.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression gross_pow(
    y=BlockA.systemVariables.summary.pow_total+BlockB.systemVariables.summary.pow_total+systemVariablesPump.summary.pow_total+Elec_box_power)
    annotation (Placement(transformation(extent={{-266.51527475472784,70.50119833285603},{-246.51527475472784,90.50119833285603}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Auxiliary.Business_Factor.Business_Factors_heating businessFactors(
    use_business_factor=use_bf,
    Nrow=Nrow[1],
    is_monobloc=is_monobloc,
    const_bf_cap=const_bf_cap,
    load_bf_cap=load_bf_cap,
    OAT_bf_cap=OAT_bf_cap,
    OAT2_bf_cap=OAT2_bf_cap,
    OAT3_bf_cap=OAT3_bf_cap,
    const_bf_pow=const_bf_pow,
    load_bf_pow=load_bf_pow,
    OAT_bf_pow=OAT_bf_pow,
    OAT2_bf_pow=OAT2_bf_pow,
    OAT3_bf_pow=OAT3_bf_pow,
    max_bf_cap=max_bf_cap,
    min_bf_cap=min_bf_cap,
    max_bf_pow=max_bf_pow,
    min_bf_pow=min_bf_pow)
    annotation (Placement(transformation(extent={{-139.47778692230398,118.52221307769602},{-112.52221307769602,145.47778692230398}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression load(
    y=defrost_factor.Integrated_net_heatcap/(Heatcap_Tbiv_tot))
    annotation (Placement(transformation(extent={{-267.***********,53.62668898431265},{-247.***********,73.62668898431265}},origin={0.0,0.0},rotation=0.0)));
  parameter BOLT.InternalLibrary.Compressors.PD_2Port.SubComponents.PD.DataBase.Polynomial.ARI_VS.Selector selector_Comp[2]
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Real capacity_design[2]
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Real CompVoltage[2]
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Real fmax[2]
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Real fmin[2]
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Real M_ref[2]
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter BOLT.InternalLibrary.Refrigerant.Valves.TwoPort.DataBase.Selector EXV_main[2]
    annotation (Dialog(group="EXV",tab="Unit Characteristics"));
  parameter Real Dport_coolant_a[2]
    annotation (Dialog(group="BPHE",tab="Unit Characteristics"));
  parameter Real Dport_coolant_b[2]
    annotation (Dialog(group="BPHE",tab="Unit Characteristics"));
  parameter Real Dport_ref_a[2]
    annotation (Dialog(group="BPHE",tab="Unit Characteristics"));
  parameter Real Dport_ref_b[2]
    annotation (Dialog(group="BPHE",tab="Unit Characteristics"));
  parameter Integer nPlate[2]
    annotation (Dialog(group="BPHE",tab="Unit Characteristics"));
  parameter Real CondFoulingFactor
    annotation (Dialog(group="BPHE",tab="Unit Characteristics"));
  parameter BlackBoxLibrary.GeoSelector.GeoSelector_1C_Cond selector_geo_BPHE[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Integer nCoils[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Integer Itube[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Integer nCir[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Integer Ntube[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Integer Nrow[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Ltube[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Dotube[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Ttube[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Ptube[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Prow[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Dfin[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Tfin[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Boolean isCoating
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter BOLT.InternalLibrary.Coolant.Pumps.DataBase.PumpPoly.Selector selector_pump[2]
    annotation (Dialog(group="Pump",tab="Unit Characteristics"));
  parameter Real Fw_fan[2]
    annotation (Dialog(group="Fan",tab="Unit Characteristics"));
  parameter Real max_fan_frequency[2]
    annotation (Dialog(group="Fan",tab="Unit Characteristics"));
  parameter Real fanCurveCoefficientsCooling[2,9]
    annotation (Dialog(group="Fan",tab="Unit Characteristics"));
  parameter Real fanCurveCoefficientsHeating[2,9]
    annotation (Dialog(group="Fan",tab="Unit Characteristics"));
  parameter Real Suction_line_diameter[2]
    annotation (Dialog(group="Suction Line",tab="Unit Characteristics"));
  parameter Real Suction_line_length[2]
    annotation (Dialog(group="Suction Line",tab="Unit Characteristics"));
  parameter Real coil_line_diameter[2]
    annotation (Dialog(group="Coil Line",tab="Unit Characteristics"));
  parameter Real coil_line_length[2]
    annotation (Dialog(group="Coil Line",tab="Unit Characteristics"));
  parameter Real liquid_line_diameter[2]
    annotation (Dialog(group="Liquid Line",tab="Unit Characteristics"));
  parameter Real liquid_line_length[2]
    annotation (Dialog(group="Liquid Line",tab="Unit Characteristics"));
  parameter Real discharge_line_diameter[2]
    annotation (Dialog(group="Discharge Line",tab="Unit Characteristics"));
  parameter Real discharge_line_length[2]
    annotation (Dialog(group="Discharge Line",tab="Unit Characteristics"));
  parameter Real EXV_in_line_diameter[2]
    annotation (Dialog(group="EXV in Line",tab="Unit Characteristics"));
  parameter Real EXV_in_line_length[2]
    annotation (Dialog(group="EXV in Line",tab="Unit Characteristics"));
  parameter Real Ac_duct[2]
    annotation (Dialog(group="Air Duct",tab="Unit Characteristics"));
  parameter Real Ka_duct[2]
    annotation (Dialog(group="Air Duct",tab="Unit Characteristics"));
  parameter Real UA_duct[2]
    annotation (Dialog(group="Air Duct",tab="Unit Characteristics"));
  parameter Real PDC_4WV[2]
    annotation (Dialog(group="4WV",tab="Unit Characteristics"));
  parameter Real Zevap_HPH_cst[2]
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_HPH_SST[2]
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_HPH_Ncomp[2]
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_HPH_heatcap[2]
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_coated_HPH[2]
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_HPC_cst[2]
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_HPC_heatcap[2]
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_HPC_SST[2]
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zcond_HPH_cst[2]
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPH_SDT[2]
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPH_heatcap[2]
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPH_SST[2]
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPC_cst[2]
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPC_DGT[2]
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPC_Ncomp[2]
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPC_SDT[2]
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zflow_intercept[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zflow_Ncomp[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zflow_SST[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zflow_SDT[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zflow_heatcap[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zpower_intercept[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zpower_SH[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zpower_DGT[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zpower_SST[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zpower_Zflow[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real FW[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Boolean use_bf
    annotation (Dialog(tab="BusinessFactor&EN14511"));
  //"if false BF's are set to 1 and controlledCapacity = engineering capacity"    
  parameter Boolean Use_EN
    annotation (Dialog(tab="BusinessFactor&EN14511"));
  //"if false controlledCapacity = gross capacity" ;
  parameter Boolean Use_defrost
    annotation (Dialog(tab="BusinessFactor&EN14511"));
  parameter Real const_bf_cap=-5150.1699488455915
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real load_bf_cap=-0.06492214515605722
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real OAT_bf_cap=56.03254044620711
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real OAT2_bf_cap=-0.20319757916979853
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real OAT3_bf_cap=0.00024561973442076556
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real const_bf_pow=3133.0906905004736
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real load_bf_pow=-0.143729093666267
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real OAT_bf_pow=-33.80116864068734
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real OAT2_bf_pow=0.12149690069425928
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real OAT3_bf_pow=0.00014549594213420189
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real max_bf_cap=0.05
    "capacity business factor max value"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real min_bf_cap=0
    "capacity business factor min value"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real max_bf_pow=-0.025
    "power business factor max value"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real min_bf_pow=0.025
    "power business factor min value"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real mdot_start
    annotation (Dialog(group="Pump ",tab="Initialization"));
  inner.BOLT.InternalLibrary.Refrigerant.Aggregation.AggregateStreams_2 systemVariablesPump(
    isOff={Use_pump,Use_pump})
    annotation (Placement(transformation(extent={{53.64941913376437,125.42875105810268},{73.64941913376437,145.42875105810268}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.Mixer mixerPumpUserA(
    T_start=EWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff_a=Use_pump,
    isOff_b=not Use_pump,
    fa_set=
      if Use_pump then
        0
      else
        1)
    annotation (Placement(transformation(extent={{-5.319920743950348,-5.319920743950334},{5.319920743950348,5.319920743950334}},origin={-100.77856842376012,-7.854595463337816},rotation=90.0)));
  .BOLT.CoolantMisc.Mixer mixerPumpUserB(
    T_start=EWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    fa_set=
      if Use_pump then
        1
      else
        0,
    isOff_b=Use_pump,
    isOff_a=not Use_pump)
    annotation (Placement(transformation(extent={{-5.319920743950348,-5.319920743950334},{5.319920743950348,5.319920743950334}},origin={97.5852880366944,-8.222272438398097},rotation=90.0)));
  .Workspace.Auxiliary.Defrost.Defrost_factor defrost_factor(
    use_DefrostFactor=Use_defrost,
    Nrow=Nrow[1],
    is_monobloc=is_monobloc)
    annotation (Placement(transformation(extent={{-178.2009048039103,177.0600768552218},{-158.2009048039103,197.0600768552218}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression DPe_air_A_B(
    y=0)
    annotation (Placement(transformation(extent={{-268.2591014056851,155.78195115465286},{-248.2591014056851,175.78195115465286}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression RH(
    y=BlockA.sourceAir.summary.RH)
    annotation (Placement(transformation(extent={{-270.0,172.0},{-250.0,192.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression OAT_1(
    y=BlockA.sourceAir.summary.Tdb)
    annotation (Placement(transformation(extent={{-270.0,184.0},{-250.0,204.0}},origin={0.0,0.0},rotation=0.0)));
  parameter Real[2] ssh_setPoint={5,5}
    "circuit A ssh set point"
    annotation (Dialog(group="Refrigerant"));
  parameter.Modelica.SIunits.TemperatureDifference[2] SC_setpoint={-2,-2}
    annotation (Dialog(group="Refrigerant"));
  parameter Boolean[2] SC_fixed={true,true}
    annotation (Dialog(group="Refrigerant"));
  parameter Boolean[2] Mref_fixed={false,false}
    annotation (Dialog(group="Refrigerant"));
  parameter.Modelica.SIunits.Mass[2] Mref={5,5}
    annotation (Dialog(group="Refrigerant"));
  .Modelica.Blocks.Sources.RealExpression P_comp(
    y=BlockA.compressor.summary.P_compression+BlockB.compressor.summary.P_compression)
    annotation (Placement(transformation(extent={{-269.6830866063234,198.0},{-249.6830866063234,218.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression Ncomp(
    y=BlockA.compressor.summary.Ncomp)
    annotation (Placement(transformation(extent={{-270.0,212.0},{-250.0,232.0}},origin={0.0,0.0},rotation=0.0)));
  parameter Real Heatcap_Tbiv=30000
    annotation (Dialog(tab="Unit Characteristics"));
  parameter Real Heatcap_Tbiv_tot=
    if is_monobloc then
      Heatcap_Tbiv
    else
      Heatcap_Tbiv*2;
equation
  controlledCapacity=
    if Use_EN then
      if use_bf then
        businessFactors.pub_net_heat_cap
      else
        defrost_factor.Integrated_net_heatcap
    else
      if use_bf then
        businessFactors.pub_gross_heat_cap
      else
        defrost_factor.Integrated_gross_heatcap;
  controlledPower=
    if Use_EN then
      if use_bf then
        businessFactors.pub_net_pow
      else
        defrost_factor.Integrated_net_power
    else
      if use_bf then
        businessFactors.pub_gross_pow
      else
        defrost_factor.Integrated_gross_power;
  connect(split_pumpB.port_a,pumpPolyB.port_a)
    annotation (Line(points={{95.1705875471087,-52.134538889964865},{95.1705875471087,-44.926847888774006},{57.59422308194374,-44.926847888774006},{57.59422308194374,-37.42569341417812}},color={0,127,0}));
  connect(node_blocB_out.port_b,mixer.port_b)
    annotation (Line(points={{9.716900311317987,49.723549174361885},{2.599608435561697,49.723549174361885},{2.599608435561697,56.06009079921068}},color={0,127,0}));
  connect(node_blocA_out.port_b,mixer.port_a)
    annotation (Line(points={{-14.619641313530828,49.723549174361885},{-7.400391564438303,49.723549174361885},{-7.400391564438303,56.06009079921068}},color={0,127,0}));
  connect(split_bloc.port_b,node_coolant_in_pumpB.port_a)
    annotation (Line(points={{0.5742964341100705,-70.50817651023848},{6.5666666666666655,-70.50817651023848},{6.5666666666666655,-65.23333333333332},{39.44772772364737,-65.23333333333332}},color={0,127,0}));
  connect(node_coolant_in_pumpB.port_b,split_pumpB.port_c)
    annotation (Line(points={{47.44772772364737,-65.23333333333332},{98.01912393006732,-65.23333333333332},{98.01912393006732,-62.79017498918046}},color={0,127,0}));
  connect(split_bloc.port_a,node_coolant_in_pumpA.port_a)
    annotation (Line(points={{-4.911773636773205,-70.50817651023848},{-10.88581550923115,-70.50817651023848},{-10.88581550923115,-64.02469182603615},{-39.40982391781609,-64.02469182603615}},color={0,127,0}));
  connect(node_coolant_in_splitbloc.port_b,split_bloc.port_c)
    annotation (Line(points={{-2.50207193466491,-85.1165772194211},{-2.0632372538145813,-81.16381260945407}},color={0,127,0}));
  connect(node_coolant_source.port_b,check_valve.port_a)
    annotation (Line(points={{-3.0687386013315754,-117.88324388608778},{-3.0687386013315754,-111.16454748157973},{-2.414785579186551,-111.16454748157973}},color={0,127,0}));
  connect(node_coolant_in_splitbloc.port_a,check_valve.port_b)
    annotation (Line(points={{-2.502071934664908,-93.1165772194211},{-2.502071934664908,-98.39173836273817},{-2.4147855791865482,-98.39173836273817}},color={0,127,0}));
  connect(shafA.flange,pumpPolyA.flange)
    annotation (Line(points={{-40.2918498052081,-3.3283811868263804},{-31.988777703356632,-3.3283811868263804},{-31.988777703356632,-29.698159792787973},{-56.688211421548345,-29.698159792787973}},color={127,0,0}));
  connect(shafB.flange,pumpPolyB.flange)
    annotation (Line(points={{33.23789886228211,-2.6142645441622676},{27.23789886228211,-2.6142645441622676},{27.23789886228211,-29.032295553730396},{49.20082522149602,-29.032295553730396}},color={127,0,0}));
  connect(controlledCapacityA.y,measurementBusA.capacity)
    annotation (Line(points={{-30.36273640817607,194.61132870676136},{-20.183305020500796,194.61132870676136},{-20.183305020500796,160.61132870676136},{-27.682450951782435,160.61132870676136}},color={0,0,127}));
  connect(controlledCapacityB.y,measurementBusB.capacity)
    annotation (Line(points={{24.01016289913556,194.61132870676136},{15.296853263385106,194.61132870676136},{15.296853263385106,160.61132870676136},{23.01016289913556,160.61132870676136}},color={0,0,127}));
  connect(load.y,businessFactors.load)
    annotation (Line(points={{-246.***********,63.62668898431265},{-154.27922672252794,63.62668898431265},{-154.27922672252794,123.8401018981519},{-141.1184645066755,123.8401018981519}},color={0,0,127}));
  connect(eN14511.DPe,DPe.y)
    annotation (Line(points={{-216.44542258813084,132.2},{-230.7372879222456,132.2},{-230.7372879222456,151.78691261857034},{-246.59408856032428,151.78691261857034}},color={0,0,127}));
  connect(eN14511.DPi,DPi.y)
    annotation (Line(points={{-216.44542258813084,126.4},{-230.37741864245248,126.4},{-230.37741864245248,137.53691261857034},{-245.92907571496343,137.53691261857034}},color={0,0,127}));
  connect(eN14511.q,q.y)
    annotation (Line(points={{-216.44542258813084,124.6},{-229.34778117740825,124.6},{-229.34778117740825,122.27952738440243},{-245.29360380627423,122.27952738440243}},color={0,0,127}));
  connect(eN14511.q_air_A,q_air_A_B.y)
    annotation (Line(points={{-216.44542258813084,122.6},{-229.33052997711368,122.6},{-229.33052997711368,107.53691261857034},{-245.25910140568513,107.53691261857034}},color={0,0,127}));
  connect(eN14511.q_air_B,q_air_A_B.y)
    annotation (Line(points={{-216.44542258813084,120.6},{-229.33052997711368,120.6},{-229.33052997711368,107.53691261857034},{-245.25910140568513,107.53691261857034}},color={0,0,127}));
  connect(eN14511.inst_gross_cap,gross_cap.y)
    annotation (Line(points={{-216.44542258813084,118.6},{-229.33052997711368,118.6},{-229.33052997711368,94},{-245,94}},color={0,0,127}));
  connect(eN14511.inst_gross_pow,gross_pow.y)
    annotation (Line(points={{-216.44542258813084,116.6},{-229.45861665163505,116.6},{-229.45861665163505,80.50119833285603},{-245.51527475472784,80.50119833285603}},color={0,0,127}));
  connect(External_Pressure.y,measurementBusA.External_Pressure)
    annotation (Line(points={{-4.752013895599437,208.19566392473865},{-4.752013895599437,160.61132870676136},{-27.682450951782435,160.61132870676136}},color={0,0,127}));
  connect(External_Pressure.y,measurementBusB.External_Pressure)
    annotation (Line(points={{-4.752013895599437,208.19566392473865},{-4.752013895599437,160.61132870676136},{23.01016289913556,160.61132870676136}},color={0,0,127}));
  connect(PipingB.port_b,gaz_separatorB.port_a)
    annotation (Line(points={{97.57846085003577,19.799795340188318},{97.20855048825722,19.799795340188318},{97.20855048825722,25.647303008535474}},color={0,127,0}));
  connect(PipingA.port_b,gaz_separatorA.port_a)
    annotation (Line(points={{-101.26278268171433,21.38260042972899},{-101.26278268171433,27.23010809807615}},color={0,127,0}));
  connect(ActuatorPumpUser_B,shafB.speed_in)
    annotation (Line(points={{43.02729244108585,15.820655037491711},{32.97848306969195,15.820655037491711},{32.97848306969195,-1.0142645441622733},{41.23789886228212,-1.0142645441622733}},color={0,0,127}));
  connect(ActuatorPumpUser_A,shafA.speed_in)
    annotation (Line(points={{-51.53049747436644,14.96493704570473},{-41.58287337345662,14.96493704570473},{-41.58287337345662,-1.728381186826379},{-48.291849805208116,-1.728381186826379}},color={0,0,127}));
  connect(mixerPumpUserA.port_c,PipingA.port_a)
    annotation (Line(points={{-100.77856842376012,-2.3218778896294543},{-100.77856842376012,3.4541151042233507},{-101.26278268171433,3.4541151042233507},{-101.26278268171433,9.230108098076158}},color={0,127,0}));
  connect(mixerPumpUserB.port_c,PipingB.port_a)
    annotation (Line(points={{97.5852880366944,-2.689554864689735},{97.5852880366944,2.4788740719228692},{97.57846085003577,2.4788740719228692},{97.57846085003577,7.647303008535481}},color={0,127,0}));
  connect(pumpPolyA.port_b,mixerPumpUserA.port_b)
    annotation (Line(points={{-65.08160928199607,-21.304761932340256},{-65.08160928199607,-13.174516207288164},{-98.01220963690595,-13.174516207288164}},color={0,127,0}));
  connect(pumpPolyB.port_b,mixerPumpUserB.port_a)
    annotation (Line(points={{57.59422308194374,-20.63889769328268},{57.59422308194374,-13.542193182348445},{95.03172607959823,-13.542193182348445}},color={0,127,0}));
  connect(split_pumpB.port_b,mixerPumpUserB.port_b)
    annotation (Line(points={{100.65665761799197,-52.134538889964865},{100.65665761799197,-32.83836603615666},{100.35164682354856,-32.83836603615666},{100.35164682354856,-13.542193182348445}},color={0,127,0}));
  connect(BlockA.port_b,node_blocA_out.port_a)
    annotation (Line(points={{-32.4,50},{-32.4,49.723549174361885},{-22.619641313530828,49.723549174361885}},color={0,127,0}));
  connect(BlockA.actual_FSFanSpd_in,Fan_controller_A)
    annotation (Line(points={{-55,52},{-76.90288730001818,52},{-76.90288730001818,59.88271552516719},{-98.35380587259328,59.88271552516719}},color={0,0,127}));
  connect(BlockA.EXV_controller,EXV_controller_A)
    annotation (Line(points={{-55,58},{-76.50394290903301,58},{-76.50394290903301,69.36437114069773},{-99.55423712065695,69.36437114069773}},color={0,0,127}));
  connect(BlockA.compressor_controller,Compressor_controller_A)
    annotation (Line(points={{-55,62},{-76.18453903904617,62},{-76.18453903904617,79.21770595805611},{-98.93896918087167,79.21770595805611}},color={0,0,127}));
  connect(BlockA.measurementBus,measurementBusA)
    annotation (Line(points={{-33,58},{-27.682450951782435,58},{-27.682450951782435,160.61132870676136}},color={255,204,51}));
  connect(gaz_separatorA.port_b,BlockA.port_a)
    annotation (Line(points={{-101.26278268171433,39.38260042972899},{-101.26278268171433,50},{-55,50}},color={0,127,0}));
  connect(BlockB.port_b,node_blocB_out.port_a)
    annotation (Line(points={{32.4,52},{32.4,49.723549174361885},{17.716900311317985,49.723549174361885}},color={0,127,0}));
  connect(BlockB.measurementBus,measurementBusB)
    annotation (Line(points={{33,60},{23.01016289913556,60},{23.01016289913556,160.61132870676136}},color={255,204,51}));
  connect(gaz_separatorB.port_b,BlockB.port_a)
    annotation (Line(points={{97.20855048825722,37.79979534018831},{97.20855048825722,52},{55,52}},color={0,127,0}));
  connect(Fan_controller_B,BlockB.actual_FSFanSpd_in)
    annotation (Line(points={{87.02877281194978,58.2797029539722},{70.19975331193633,58.2797029539722},{70.19975331193633,56},{55,56}},color={0,0,127}));
  connect(EXV_controller_B,BlockB.EXV_controller)
    annotation (Line(points={{86.93858363968468,66.15031712701042},{70.15465872580378,66.15031712701042},{70.15465872580378,61},{55,61}},color={0,0,127}));
  connect(Compressor_controller_B,BlockB.compressor_controller)
    annotation (Line(points={{86.61211145474692,75.11086355386652},{86.61211145474692,68.78444357635209},{70.2636455748654,68.78444357635209},{70.2636455748654,64},{55,64}},color={0,0,127}));
  connect(split_pumpA.port_a,mixerPumpUserA.port_a)
    annotation (Line(points={{-102.47370098177745,-51.311992432272305},{-102.47370098177745,-13.174516207288164},{-103.33213038085628,-13.174516207288164}},color={0,127,0}));
  connect(split_pumpA.port_b,pumpPolyA.port_a)
    annotation (Line(points={{-96.98763091089418,-51.311992432272305},{-96.98763091089418,-44.55022524213727},{-65.08160928199607,-44.55022524213727},{-65.08160928199607,-38.0915576532357}},color={0,127,0}));
  connect(node_coolant_in_pumpA.port_b,split_pumpA.port_c)
    annotation (Line(points={{-47.40982391781609,-64.02469182603615},{-99.62516459881883,-64.02469182603615},{-99.62516459881883,-61.9676285314879}},color={0,127,0}));
  connect(mixer.port_c,node_out.port_a)
    annotation (Line(points={{-2.6003915644382984,76.46009079921069},{-2.6003915644383016,82.06009079921068}},color={0,127,0}));
  connect(node_out.port_b,external_system.port_a)
    annotation (Line(points={{-2.6003915644382998,90.06009079921068},{-2.600391564438302,95.6736862397899}},color={0,127,0}));
  connect(coolant_in,node_coolant_source.port_a)
    annotation (Line(points={{-3.480089871014542,-146.7059464254537},{-3.480089871014542,-136.29459515577074},{-3.0687386013315736,-136.29459515577074},{-3.0687386013315736,-125.88324388608778}},color={0,127,0}));
  connect(external_system.port_b,coolant_out)
    annotation (Line(points={{-2.6003915644382993,108.44649535863147},{-2.6003915644383007,124.06009079921068}},color={0,127,0}));
  connect(defrost_factor.Net_Power,eN14511.inst_net_pow)
    annotation (Line(points={{-177.79522180708824,185.1405334268961},{-189.99194693180272,185.1405334268961},{-189.99194693180272,123},{-196.44542258813084,123}},color={0,0,127}));
  connect(defrost_factor.Gross_Power,gross_pow.y)
    annotation (Line(points={{-177.79522180708824,183.54053342689608},{-229.9613992070757,183.54053342689608},{-229.9613992070757,80},{-245,80}},color={0,0,127}));
  connect(defrost_factor.Net_Heatcap,eN14511.inst_net_cap)
    annotation (Line(points={{-177.79522180708824,181.94053342689608},{-189.82571723936604,181.94053342689608},{-189.82571723936604,129},{-196.44542258813084,129}},color={0,0,127}));
  connect(defrost_factor.Gross_Heatcap,gross_cap.y)
    annotation (Line(points={{-177.79522180708824,180.54053342689608},{-190.30005693335582,180.54053342689608},{-190.30005693335582,94},{-245,94}},color={0,0,127}));
  connect(defrost_factor.OAT,OAT_1.y)
    annotation (Line(points={{-177.79522180708824,189.6505334268961},{-203.12390937592673,189.6505334268961},{-203.12390937592673,194},{-249,194}},color={0,0,127}));
  connect(defrost_factor.RH,RH.y)
    annotation (Line(points={{-177.79522180708824,188.1405334268961},{-203.270891713022,188.1405334268961},{-203.270891713022,182},{-249,182}},color={0,0,127}));
  connect(defrost_factor.Integrated_net_heatcap,businessFactors.net_heat_cap)
    annotation (Line(points={{-157.01396875223486,184.1573198231367},{-153.20997805958692,184.1573198231367},{-153.20997805958692,141.9039695133949},{-140.98487598529059,141.9039695133949}},color={0,0,127}));
  connect(defrost_factor.Integrated_gross_heatcap,businessFactors.gross_heat_cap)
    annotation (Line(points={{-157.01396875223486,186.86284957495886},{-153.49527736153556,186.86284957495886},{-153.49527736153556,127.8430981907299},{-141.12892406112982,127.8430981907299}},color={0,0,127}));
  connect(defrost_factor.Integrated_net_power,businessFactors.net_pow)
    annotation (Line(points={{-157.05818994230773,189.03571575656233},{-153.52591287056325,189.03571575656233},{-153.52591287056325,138.44637091306436},{-140.95606637012273,138.44637091306436}},color={0,0,127}));
  connect(defrost_factor.Integrated_gross_power,businessFactors.gross_pow)
    annotation (Line(points={{-157.05818994230773,191.93923621904042},{-153.25419924965982,191.93923621904042},{-153.25419924965982,131.4447448668997},{-140.98487598529059,131.4447448668997}},color={0,0,127}));
  connect(eN14511.DPe_air_A,DPe_air_A_B.y)
    annotation (Line(points={{-216.44542258813084,130.2},{-230.25991851893642,130.2},{-230.25991851893642,165.78195115465286},{-247.2591014056851,165.78195115465286}},color={0,0,127}));
  connect(eN14511.DPe_air_B,DPe_air_A_B.y)
    annotation (Line(points={{-216.44542258813084,128.2},{-230.62955070284255,128.2},{-230.62955070284255,165.78195115465286},{-247.2591014056851,165.78195115465286}},color={0,0,127}));
  connect(defrost_factor.Ncomp,Ncomp.y)
    annotation (Line(points={{-177.74990335246753,192.41615813601786},{-217.7520300230986,192.41615813601786},{-217.7520300230986,222},{-249,222}},color={0,0,127}));
  connect(defrost_factor.Pcompresseur,P_comp.y)
    annotation (Line(points={{-177.84990335246752,194.11615813601784},{-177.84990335246752,208},{-248.6830866063234,208}},color={0,0,127}));
  connect(businessFactors.OAT,OAT_1.y)
    annotation (Line(points={{-140.98487598529059,135.334487694748},{-190.02256993278468,135.334487694748},{-190.02256993278468,194},{-249,194}},color={0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={49,49,49},
          fillColor={181,124,237},
          fillPattern=FillPattern.Solid,
          extent={{-100,-119},{100,119}},
          origin={0,-19}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name"),
        Text(
          textString="MODULE",
          origin={0,-20},
          extent={{-100,24},{100,-24}})}));
end Equipement;
