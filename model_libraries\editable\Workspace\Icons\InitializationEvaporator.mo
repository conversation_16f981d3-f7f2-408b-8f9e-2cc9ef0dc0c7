within Workspace.Icons;
partial model InitializationEvaporator
  //extends VFD30XW.Icons.InitializationCycle2;
  annotation (
    Icon(
      graphics={
        Text(
          extent={{-132,-134},{136,-152}},
          lineColor={0,0,0},
          fillColor={255,0,0},
          fillPattern=FillPattern.Solid,
          textString="%name"),
        Ellipse(
          extent={{-94,32},{-20,-40}},
          lineColor={0,0,0},
          fillColor={0,127,0},
          fillPattern=FillPattern.Solid),
        Ellipse(
          extent={{24,32},{98,-40}},
          lineColor={0,0,0},
          fillColor={0,127,0},
          fillPattern=FillPattern.Solid),
        Rectangle(
          extent={{-60,32},{60,-40}},
          fillColor={255,255,255},
          fillPattern=FillPattern.Solid,
          pattern=LinePattern.None),
        Rectangle(
          extent={{-60,2},{60,-2}},
          lineColor={0,0,0},
          fillColor={0,127,0},
          fillPattern=FillPattern.Solid),
        Rectangle(
          extent={{-60,-6},{60,-40}},
          fillColor={255,0,0},
          fillPattern=FillPattern.Solid,
          pattern=LinePattern.None),
        Rectangle(
          extent={{-60,-22},{60,-26}},
          lineColor={0,0,0},
          fillColor={0,127,0},
          fillPattern=FillPattern.Solid),
        Line(
          points={{-60,-40},{60,-40}},
          pattern=LinePattern.None,
          smooth=Smooth.None),
        Line(
          points={{-62,32},{60,32}},
          pattern=LinePattern.None,
          smooth=Smooth.None),
        Line(
          points={{60,32},{60,-40}},
          color={0,0,0},
          smooth=Smooth.None,
          thickness=0.5),
        Line(
          points={{-60,32},{-60,-40}},
          color={0,0,0},
          smooth=Smooth.None,
          thickness=0.5),
        Rectangle(
          extent={{-60,-10},{60,-14}},
          lineColor={0,0,0},
          fillColor={0,127,0},
          fillPattern=FillPattern.Solid)}));
end InitializationEvaporator;
