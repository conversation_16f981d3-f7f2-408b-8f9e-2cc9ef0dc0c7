within Workspace.Controller.Tests;
model test_optimization_function
  parameter.Modelica.SIunits.Temp_K OAT=276.15;
  parameter.Modelica.SIunits.Temperature LWT=303.15;
  parameter Real frq_compressor=60.0;
  parameter Boolean freq_heating_1_2=Workspace.Controller.Components.Functions.Freq_Heating_Transition_1_2(
    OAT,
    LWT,
    frq_compressor);
  parameter Boolean freq_heating_2_3=Workspace.Controller.Components.Functions.Freq_Heating_Transition_2_3(
    OAT,
    LWT,
    frq_compressor);
  parameter Boolean freq_heating_3_4=Workspace.Controller.Components.Functions.Freq_Heating_Transition_3_4(
    OAT,
    LWT,
    frq_compressor);
  parameter Boolean freq_cooling_1_2=Workspace.Controller.Components.Functions.Freq_Cooling_Transition_1_2(
    OAT,
    LWT,
    frq_compressor);
  parameter Boolean freq_cooling_2_3=Workspace.Controller.Components.Functions.Freq_Cooling_Transition_2_3(
    OAT,
    LWT,
    frq_compressor);
  parameter Boolean freq_cooling_3_4=Workspace.Controller.Components.Functions.Freq_Cooling_Transition_3_4(
    OAT,
    LWT,
    frq_compressor);
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end test_optimization_function;
