within Workspace.Controller.SubSystems.BaseClasses;
partial model CompressorControlBase
  extends BOLT.InternalLibrary.BuildingBlocks.Icons.Compressor;
  parameter <PERSON>olean crkIsOff=false
    "Specify whether the controller is on or off";
  parameter Real compressorFrequency_min=23
    "Minimum speed of compressor (Hz)";
  parameter Real compressorFrequency_max=95
    "Maximum speed of compressor (Hz)";
  .Workspace.Interfaces.MeasurementBus measurementBus
    annotation (Placement(transformation(extent={{-120.66302965034427,39.33697034965573},{-79.33697034965573,80.66302965034427}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.LimitsBus limitsBus
    annotation (Placement(transformation(extent={{-102.0,-130.0},{-82.0,-110.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput actuatorSignal
    annotation (Placement(transformation(extent={{206.0,-10.0},{226.0,10.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput compressorFrequency
    annotation (Placement(transformation(extent={{-159.62340743557286,-15.623407435572865},{-128.37659256442714,15.623407435572865}},origin={0.0,0.0},rotation=0.0)));
end CompressorControlBase;
