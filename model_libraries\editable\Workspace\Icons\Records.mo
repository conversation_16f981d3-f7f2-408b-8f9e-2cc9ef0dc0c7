within Workspace.Icons;
partial class Records
  extends Workspace.Icons.PackageIcon;
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100,-100},{100,100}}),
      graphics={
        Rectangle(
          extent={{-60,38},{70,-36}},
          lineColor={0,0,0},
          fillColor={255,170,85},
          fillPattern=FillPattern.Solid),
        Line(
          points={{-2,38},{-2,-36}},
          color={0,0,0},
          smooth=Smooth.None),
        Line(
          points={{-60,8},{70,8}},
          color={0,0,0},
          smooth=Smooth.None),
        Line(
          points={{-60,-16},{70,-16}},
          color={0,0,0},
          smooth=Smooth.None)}));
end Records;
