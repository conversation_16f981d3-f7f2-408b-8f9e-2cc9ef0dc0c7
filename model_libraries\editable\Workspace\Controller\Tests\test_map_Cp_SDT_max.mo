within Workspace.Controller.Tests;
model test_map_Cp_SDT_max
  .Modelica.Blocks.Tables.CombiTable2D Table_SDT_max(
    table={{0,30,50,100,120,140},{-30,40,60,60,60,60},{-23,50,70,70,70,70},{-19,56,76,76,76,70},{-15,62,82,82,82,70},{-10,70,82,82,82,70},{-0.01,70,82,82,82,70},{+0.01,60,82,82,70,60},{15,60,82,82,70,60},{20,60,76,76,65,60},{25,60,70,70,65,60}})
    annotation (Placement(transformation(extent={{-58.0,-52.0},{-38.0,-32.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression SST(
    y=SST_input)
    annotation (Placement(transformation(extent={{-106.0,-28.0},{-86.0,-8.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression freq(
    y=Frequence)
    annotation (Placement(transformation(extent={{-106.0,-76.0},{-86.0,-56.0}},origin={0.0,0.0},rotation=0.0)));
  parameter Real SST_input=20;
  parameter Real Frequence=100;
equation
  connect(freq.y,Table_SDT_max.u2)
    annotation (Line(points={{-85,-66},{-68.5,-66},{-68.5,-48},{-60,-48}},color={0,0,127}));
  connect(SST.y,Table_SDT_max.u1)
    annotation (Line(points={{-85,-18},{-68.5,-18},{-68.5,-36},{-60,-36}},color={0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end test_map_Cp_SDT_max;
