within Workspace.System.HPC.BaseCycle.optimization;
model ECS1
  import CoolantCommonMedium=BOLT.InternalLibrary.Media.Coolant.CoolantCommon;
  .Workspace.System.HPC.BaseCycle.Equipement oL_modular(
    EWT=EWT,
    LWT=LWT,
    OAT=OAT,
    BlocB(),
    isOFFA=isOFFA,
    isOFFB=isOFFB,
    CoolantMedium=CoolantMedium,
    Unit_size=Unit_size)
    annotation (Placement(transformation(extent={{1.679087661949458,24.0},{21.67908766194946,44.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Source sourceBrine(
    Vd_fixed=false,
    T_set=EWT,
    p_fixed=true,
    CoolantMedium=CoolantMedium)
    annotation (Placement(transformation(extent={{-4.0,-4.0},{4.0,4.0}},origin={8.0,-6.0},rotation=90.0)));
  .BOLT.BoundaryNode.Coolant.Sink sinkBrine(
    p_fixed=true,
    T_fixed=true,
    T_set=LWT,
    CoolantMedium=CoolantMedium,
    p_set=sourceBrine.p_set)
    annotation (Placement(transformation(extent={{-4.0,-4.0},{4.0,4.0}},origin={8.0,64.0},rotation=-90.0)));
  .BOLT.Control.SteadyState.SetpointControl.Actuator Fan_actuatorA(
    fixed=true,
    maxBound=950,
    minBound=0,
    setPoint=720,
    isOff=isOFFA)
    annotation (Placement(transformation(extent={{-50.0,24.0},{-70.0,44.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Actuator Fan_actuatorB(
    setPoint=720,
    minBound=0,
    maxBound=950,
    fixed=true,
    isOff=is_monobloc or isOFFB)
    annotation (Placement(transformation(extent={{50.0,22.0},{70.0,42.0}},origin={0.0,0.0},rotation=0.0)));
  // Declaration of Coolant Medium Package
  //package CoolantCommonMedium =
  //.BOLT.InternalLibrary.Media.Coolant.CoolantCommon                             "Coolant Medium Package" annotation ();
  // Declaration of Coolant Medium at Evaporator Circuit
  parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector CoolantMedium=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector.FW
    "Coolant Medium Selection"
    annotation (Dialog(group="Medium"));
  final parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Empty coolantInf=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.getSelector(
    CoolantMedium)
    "Coolant Medium"
    annotation ();
  .BOLT.InternalLibrary.Media.Coolant.CoolantCommon.Temperature FreezTemp=.BOLT.InternalLibrary.Media.Coolant.CoolantCommon.freezeTemp(
    coolantInf,
    BrineConcentration,
    1);
  parameter Real BrineConcentration=0.2
    annotation (Dialog(group="Medium"));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation errorCalculationCpA(
    measurement=oL_modular.BlocA.evapBPHE.summary.Q_flow_ref,
    setPoint=TargetCapacityA,
    gain=1/70000,
    isOff=isOFFA)
    annotation (Placement(transformation(extent={{-64.21190801939854,-3.253146262674848},{-44.211908019398535,16.746853737325154}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointControllerCpA(
    manualOff=Use_Cp_actuator,
    AV_value_off=FrequencyA,
    AV_min=30,
    AV_max=140,
    isOff=isOFFA)
    annotation (Placement(transformation(extent={{-39.180745651455666,-4.536795614877073},{-19.180745651455663,15.46320438512292}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointControllerCpB(
    AV_max=140,
    AV_min=30,
    AV_value_off=FrequencyB,
    manualOff=Use_Cp_actuator,
    isOff=isOFFB or is_monobloc)
    annotation (Placement(transformation(extent={{63.17358235331146,-5.298763165202361},{43.17358235331146,14.701236834797632}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation errorCalculationCpB(
    gain=1/70000,
    setPoint=TargetCapacityB,
    measurement=oL_modular.BlocB.evapBPHE.summary.Q_flow_ref,
    isOff=isOFFB or is_monobloc)
    annotation (Placement(transformation(extent={{92.05569277786094,-4.656938489101204},{72.05569277786094,15.343061510898796}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation errorCalculationEXVA(
    gain=-1/5,
    setPoint=SH_A,
    measurement=oL_modular.BlocA.node_suction.summary.dTsh,
    isOff=isOFFA)
    annotation (Placement(transformation(extent={{-74.92215804929083,56.95823041660432},{-54.92215804929083,76.95823041660432}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointControllerEXVA(
    AV_max=1,
    AV_min=0.1,
    isOff=isOFFA)
    annotation (Placement(transformation(extent={{-50.21190801939848,56.316405740503185},{-30.21190801939848,76.31640574050319}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointControllerEXVB(
    isOff=isOFFB or is_monobloc,
    AV_min=0.1,
    AV_max=1)
    annotation (Placement(transformation(extent={{55.03063102782356,59.726298584835064},{35.03063102782356,79.72629858483506}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation errorCalculationEXVB(
    isOff=isOFFB or is_monobloc,
    measurement=oL_modular.BlocB.node_suction.summary.dTsh,
    setPoint=SH_B,
    gain=-1/5)
    annotation (Placement(transformation(extent={{87.12186483287854,60.36812326093617},{67.12186483287854,80.36812326093617}},origin={0.0,0.0},rotation=0.0)));
  parameter Integer Unit_size=60
    "Stringify values -- IPM Cloud limitation"
    annotation (Dialog(group="Unit_config"));
  parameter.Modelica.SIunits.Temperature LWT=280.15
    annotation (Dialog(group="Conditions"));
  parameter.Modelica.SIunits.Temperature EWT=285.15
    annotation (Dialog(group="Conditions"));
  parameter.Modelica.SIunits.Temperature OAT=308.15
    annotation (Dialog(group="Conditions"));
  parameter Boolean isOFFA=false
    annotation (Dialog(group="Cp_control"));
  parameter Boolean isOFFB=false
    annotation (Dialog(group="Cp_control"));
  parameter Boolean Use_Cp_actuator=true
    annotation (Dialog(group="Cp_control"));
  parameter.Modelica.SIunits.Power TargetCapacityA=25000
    annotation (Dialog(group="Cp_control"));
  parameter.Modelica.SIunits.Power TargetCapacityB=25000
    annotation (Dialog(group="Cp_control"));
  parameter Real FrequencyA=100
    annotation (Dialog(group="Cp_control"));
  parameter Real FrequencyB=100
    annotation (Dialog(group="Cp_control"));
  parameter.Modelica.SIunits.TemperatureDifference SH_A=5
    annotation (Dialog(group="Cp_control"));
  parameter.Modelica.SIunits.TemperatureDifference SH_B=5
    annotation (Dialog(group="Cp_control"));
  parameter Boolean is_monobloc=
    if Unit_size == 40 then
      true
    elseif Unit_size == 50 then
      true
    elseif Unit_size == 60 then
      true
    elseif Unit_size == 70 then
      true
    else
      false
    annotation (Dialog(group="Unit_config"));
equation
  connect(Fan_actuatorB.value,oL_modular.Fan_controller_B)
    annotation (Line(points={{55,32},{33,32},{33,31.5},{22.67908766194946,31.5}},color={0,0,127}));
  connect(Fan_actuatorA.value,oL_modular.Fan_controller_A)
    annotation (Line(points={{-55,34},{-29,34},{-29,31.5},{0.6790876619494579,31.5}},color={0,0,127}));
  connect(sourceBrine.port,oL_modular.coolant_in)
    annotation (Line(points={{8,-2},{8,19.917729746063408},{11.237952534981162,19.917729746063408}},color={0,127,0}));
  connect(oL_modular.coolant_out,sinkBrine.port)
    annotation (Line(points={{11.279087661949458,44.2},{11.279087661949458,52.1},{8,52.1},{8,60}},color={0,127,0}));
  connect(errorCalculationCpA.sensor,setpointControllerCpA.errorSignal)
    annotation (Line(points={{-44.41190801939854,5.946853737325152},{-44.41190801939854,5.463204385122923},{-39.180745651455666,5.463204385122923}},color={28,108,200}));
  connect(errorCalculationCpB.sensor,setpointControllerCpB.errorSignal)
    annotation (Line(points={{72.25569277786093,4.543061510898793},{63.17358235331146,4.701236834797635}},color={28,108,200}));
  connect(setpointControllerCpA.actuatorSignal,oL_modular.Compressor_controller_A)
    annotation (Line(points={{-18.58074565145566,5.463204385122923},{-9.432197501828924,5.463204385122923},{-9.432197501828924,26.5},{0.6790876619494597,26.5}},color={0,0,127}));
  connect(setpointControllerCpB.actuatorSignal,oL_modular.Compressor_controller_B)
    annotation (Line(points={{42.57358235331146,4.701236834797635},{32.30542266957992,4.701236834797635},{32.30542266957992,26.5},{22.67908766194946,26.5}},color={0,0,127}));
  connect(errorCalculationEXVA.sensor,setpointControllerEXVA.errorSignal)
    annotation (Line(points={{-55.12215804929083,66.15823041660433},{-55.12215804929083,66.31640574050319},{-50.21190801939848,66.31640574050319}},color={28,108,200}));
  connect(setpointControllerEXVA.actuatorSignal,oL_modular.EXV_controller_A)
    annotation (Line(points={{-29.611908019398477,66.31640574050319},{-14.787322516775067,66.31640574050319},{-14.787322516775067,36.5},{0.6790876619494597,36.5}},color={0,0,127}));
  connect(errorCalculationEXVB.sensor,setpointControllerEXVB.errorSignal)
    annotation (Line(points={{67.32186483287855,69.56812326093618},{58.76940539497195,69.56812326093618},{58.76940539497195,69.72629858483506},{55.03063102782356,69.72629858483506}},color={28,108,200}));
  connect(setpointControllerEXVB.actuatorSignal,oL_modular.EXV_controller_B)
    annotation (Line(points={{34.43063102782356,69.72629858483506},{28.067894013671914,69.72629858483506},{28.067894013671914,36.5},{22.67908766194946,36.5}},color={0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end ECS1;
