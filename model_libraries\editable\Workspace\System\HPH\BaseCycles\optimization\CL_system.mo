within Workspace.System.HPH.BaseCycles.optimization;
model CL_system
  import CoolantCommonMedium=BOLT.InternalLibrary.Media.Coolant.CoolantCommon;
  extends.Workspace.Controller.CL_control_system_heating_zenith(
    controllerSettings_crkA(
      Capacity_setpoint=TargetCapacityA,
      coefficients=oL_modular.BlocA.choiceBlock_HPH.Unit.fanCurveCoefficientsHeating),
    controllerSettings_crkB(
      Capacity_setpoint=TargetCapacityB,
      coefficients=oL_modular.BlocB.choiceBlock_HPH.Unit.fanCurveCoefficientsHeating),
    controller_crkA(
      isOff=isOFFA,
      isOffSDTmin_fan=false,
      isOffSDTmax_fan=false,
      isOffDGTmax_fan=false,
      isOffSSTmin_EXV=false,
      isOffSSTmax_EXV=false,
      isOffDSHmin_EXV=false,
      isOffDGTmax_EXV=false,
      isOffSSTmin_comp=false,
      isOffSDTmax_comp=false,
      isOffDGTmax_comp=false,
      manualOff_fan=false,
      frq_fan_sp_manual=720,
      manualOff_compressor_crkA=false,
      frq_comp_sp_manual_crkA=140,
      manualOff_compressor_crkB=false,
      frq_comp_sp_manual_crkB=140,
      Fan_MaxFrequency=720,
      completeCompressorControl_base(
        compressorControl(
          gain_Cp_speed=0.01),
        Nominal_capacity=oL_modular.BlocA.choiceBlock_HPH.Unit.capacity_design,
        Capacity_setpoint=TargetCapacityA),
      Capacity_setpoint=TargetCapacityA,
      Nominal_capacity=oL_modular.BlocA.choiceBlock_HPH.Unit.capacity_design),
    controller_crkB(
      isOff=is_monobloc or isOFFB,
      isOffSDTmin_fan=false,
      isOffSDTmax_fan=false,
      isOffDGTmax_fan=false,
      isOffSSTmin_EXV=false,
      isOffSSTmax_EXV=false,
      isOffDSHmin_EXV=false,
      isOffDGTmax_EXV=false,
      isOffSSTmin_comp=false,
      isOffSDTmax_comp=false,
      isOffDGTmax_comp=false,
      manualOff_fan=false,
      frq_fan_sp_manual=720,
      manualOff_compressor_crkA=false,
      manualOff_compressor_crkB=false,
      frq_comp_sp_manual_crkA=140,
      frq_comp_sp_manual_crkB=140,
      Fan_MaxFrequency=controllerSettings_crkB.maxfanfreq,
      completeCompressorControl_base(
        Nominal_capacity=oL_modular.BlocB.choiceBlock_HPH.Unit.capacity_design,
        Capacity_setpoint=TargetCapacityB),
      Capacity_setpoint=TargetCapacityB,
      Nominal_capacity=oL_modular.BlocB.choiceBlock_HPH.Unit.capacity_design));
  .Workspace.System.HPH.BaseCycles.Equipement oL_modular(
    Unit_size=Unit_size,
    CoolantMedium=CoolantMedium,
    isOFFB=isOFFB,
    isOFFA=isOFFA,
    OAT=OAT,
    LWT=LWT,
    EWT=EWT,
    BrineConcentration=BrineConcentration,
    OAT_WB=OAT_WB,
    Use_EN=Use_EN,
    Capacity_start=150000,
    Pdispo=Pdispo,
    external_system(
      Ka_fixed=false),
    use_bf=Use_bf,
    pumpPolyA(
      Z_power=1),
    BlocA(
      choiceBlock_HPH(
        Pump_Option=.Workspace.Auxiliary.OptionBlock.PumpOption.Pump_Option.Pump)))
    annotation (Placement(transformation(extent={{-10.0,-8.0},{10.0,12.0}},origin={0.0,0.0},rotation=0.0)));
  // Declaration of Coolant Medium Package
  //package CoolantCommonMedium =
  // .BOLT.InternalLibrary.Media.Coolant.CoolantCommon "Coolant Medium Package" annotation ();
  // Declaration of Coolant Medium at Evaporator Circuit
  parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector CoolantMedium=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector.FW
    "Coolant Medium Selection"
    annotation (Dialog(group="Medium"));
  final parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Empty coolantInf=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.getSelector(
    CoolantMedium)
    "Coolant Medium"
    annotation ();
  .BOLT.InternalLibrary.Media.Coolant.CoolantCommon.Temperature FreezTemp=.BOLT.InternalLibrary.Media.Coolant.CoolantCommon.freezeTemp(
    coolantInf,
    BrineConcentration,
    1);
  parameter Real BrineConcentration=0.2
    annotation (Dialog(group="Medium"));
  .BOLT.BoundaryNode.Coolant.Source sourceBrine(
    Vd_fixed=false,
    T_set=EWT,
    p_fixed=true,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    p_set=2e5)
    annotation (Placement(transformation(extent={{-4.0,-4.0},{4.0,4.0}},origin={0.0,-42.0},rotation=90.0)));
  .BOLT.BoundaryNode.Coolant.Sink sinkBrine(
    p_fixed=true,
    T_fixed=true,
    T_set=LWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    p_set=sourceBrine.p_set)
    annotation (Placement(transformation(extent={{-4.0,-4.0},{4.0,4.0}},origin={0.0,42.0},rotation=-90.0)));
  parameter Integer Unit_size=70
    "Stringify values -- IPM Cloud limitation"
    annotation (Dialog(group="Unit_config"));
  parameter.Modelica.SIunits.Temperature LWT=328.15
    annotation (Dialog(group="Conditions"));
  parameter.Modelica.SIunits.Temperature EWT=323.15
    annotation (Dialog(group="Conditions"));
  parameter.Modelica.SIunits.Temperature OAT=288.15
    annotation (Dialog(group="Conditions"));
  parameter.Modelica.SIunits.Temperature OAT_WB=287.15
    annotation (Dialog(group="Conditions"));
  parameter.Modelica.SIunits.Power TargetCapacityA=30000
    annotation (Dialog(group="Cp_control"));
  parameter.Modelica.SIunits.Power TargetCapacityB=30000
    annotation (Dialog(group="Cp_control"));
  parameter Boolean is_monobloc=
    if Unit_size == 40 then
      true
    elseif Unit_size == 50 then
      true
    elseif Unit_size == 60 then
      true
    elseif Unit_size == 70 then
      true
    else
      false
    annotation (Dialog(group="Unit_config"));
  parameter Boolean isOFFB=false
    annotation (Dialog(group="Cp_control"));
  parameter Boolean isOFFA=false
    annotation (Dialog(group="Cp_control"));
  parameter Boolean Use_EN=true;
  parameter.Modelica.SIunits.Pressure Pdispo=50000;
  parameter Boolean Use_bf=false;
equation
  connect(oL_modular.measurementBusB,controllerSettings_crkB.measurementBus)
    annotation (Line(points={{11,8},{28,8},{28,42.6},{68.4,42.6}},color={255,204,51}));
  connect(oL_modular.coolant_out,sinkBrine.port)
    annotation (Line(points={{0.20000000000000007,13},{0.20000000000000007,25.1},{8.881784197001252e-16,25.1},{8.881784197001252e-16,38}},color={0,127,0}));
  connect(controller_crkA.fan,oL_modular.Fan_controller_A)
    annotation (Line(points={{-48.4,6.000596956339052},{-29.7,6.000596956339052},{-29.7,-0.5},{-11,-0.5}},color={0,0,127}));
  connect(controller_crkA.exv,oL_modular.EXV_controller_A)
    annotation (Line(points={{-48.4,3.699850760915237},{-29.7,3.699850760915237},{-29.7,4.5},{-11,4.5}},color={0,0,127}));
  connect(controller_crkB.fan,oL_modular.Fan_controller_B)
    annotation (Line(points={{50.4,8.000596956339052},{30.7,8.000596956339052},{30.7,-0.5},{11,-0.5}},color={0,0,127}));
  connect(controller_crkB.exv,oL_modular.EXV_controller_B)
    annotation (Line(points={{50.4,5.699850760915237},{30.7,5.699850760915237},{30.7,4.5},{11,4.5}},color={0,0,127}));
  connect(controllerSettings_crkA.measurementBus,oL_modular.measurementBusA)
    annotation (Line(points={{-70.44724010515856,39.45699314828894},{-70.44724010515856,44.27067821904718},{-40.862081322736174,44.27067821904718},{-40.862081322736174,8},{-11,8}},color={255,204,51}));
  connect(oL_modular.coolant_in,sourceBrine.port)
    annotation (Line(points={{0.20000000000000007,-11.8},{0.20000000000000007,-25.5},{8.881784197001252e-16,-25.5},{8.881784197001252e-16,-38}},color={0,127,0}));
  connect(controller_crkA.compressor,oL_modular.Compressor_controller_A)
    annotation (Line(points={{-48.4,-3.9001492390847634},{-29.7,-3.9001492390847634},{-29.7,-5.5},{-11,-5.5}},color={0,0,127}));
  connect(controller_crkB.compressor,oL_modular.Compressor_controller_B)
    annotation (Line(points={{50.4,-1.9001492390847634},{30.7,-1.9001492390847634},{30.7,-5.5},{11,-5.5}},color={0,0,127}));
  connect(controller_crkB.compressor,oL_modular.Compressor_controller_B)
    annotation (Line(points={{50.4,-1.9001492390847634},{30.7,-1.9001492390847634},{30.7,-5.5},{11,-5.5}},color={0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end CL_system;
