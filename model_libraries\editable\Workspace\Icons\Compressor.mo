within Workspace.Icons;
partial model Compressor
  annotation (
    Icon(
      graphics={
        Ellipse(
          extent={{-88,90},{90,-90}},
          lineColor={0,0,0},
          fillColor={255,255,255},
          fillPattern=FillPattern.Solid),
        Polygon(
          points={{-52,70},{-52,-70},{82,-26},{82,26},{-52,70}},
          lineColor={0,0,255},
          smooth=Smooth.None,
          fillColor={255,0,0},
          fillPattern=FillPattern.Solid)}));
end Compressor;
