within Workspace.Auxiliary.OptionBlock;
model ChoiceBlock_multi
  parameter Workspace.Auxiliary.OptionBlock.Record_ModuleBase.Module_selector Module_1_selector=Workspace.Auxiliary.OptionBlock.Record_ModuleBase.Module_selector.Module_040
    annotation (choicesAllMatching=true,Evaluate=false);
  final parameter Workspace.Auxiliary.OptionBlock.Record_ModuleBase.ModuleBase Module_1=Workspace.Auxiliary.OptionBlock.Record_ModuleBase.getModule_selector(
    Module_1_selector);
  parameter Workspace.Auxiliary.OptionBlock.Record_ModuleBase.Module_selector Module_2_selector=Workspace.Auxiliary.OptionBlock.Record_ModuleBase.Module_selector.Module_040
    annotation (choicesAllMatching=true,Evaluate=false);
  final parameter Workspace.Auxiliary.OptionBlock.Record_ModuleBase.ModuleBase Module_2=Workspace.Auxiliary.OptionBlock.Record_ModuleBase.getModule_selector(
    Module_2_selector);
  parameter Workspace.Auxiliary.OptionBlock.Record_ModuleBase.Module_selector Module_3_selector=Workspace.Auxiliary.OptionBlock.Record_ModuleBase.Module_selector.Module_040
    annotation (choicesAllMatching=true,Evaluate=false);
  final parameter Workspace.Auxiliary.OptionBlock.Record_ModuleBase.ModuleBase Module_3=Workspace.Auxiliary.OptionBlock.Record_ModuleBase.getModule_selector(
    Module_3_selector);
  parameter Workspace.Auxiliary.OptionBlock.Record_ModuleBase.Module_selector Module_4_selector=Workspace.Auxiliary.OptionBlock.Record_ModuleBase.Module_selector.Module_040
    annotation (choicesAllMatching=true,Evaluate=false);
  final parameter Workspace.Auxiliary.OptionBlock.Record_ModuleBase.ModuleBase Module_4=Workspace.Auxiliary.OptionBlock.Record_ModuleBase.getModule_selector(
    Module_4_selector);
  parameter Integer Number_of_modules=4;
  parameter.Workspace.Auxiliary.OptionBlock.PumpOption.Pump_Option Pump_selector=.Workspace.Auxiliary.OptionBlock.PumpOption.Pump_Option.STANDARD
    annotation (Dialog(group="Options"),choicesAllMatching=true,Evaluate=false);
  final parameter Boolean is_Pump=.Workspace.Auxiliary.OptionBlock.PumpOption.getSelector(
    Pump_selector)
    annotation (Dialog(tab="Options",group="Options"));
  parameter Workspace.Auxiliary.OptionBlock.SoundOption.Selector 
  SoundOption_selector=Workspace.Auxiliary.OptionBlock.SoundOption.Selector.STANDARD
    annotation (Dialog(group="Options"),choicesAllMatching=true,Evaluate=false);
  final parameter Boolean is_SoundOption=Workspace.Auxiliary.OptionBlock.SoundOption.getSelector(
   SoundOption_selector)
    annotation (Dialog(tab="Options",group="Options"));
  parameter.Workspace.Auxiliary.OptionBlock.CoatingOption.Coating_selector Coating_selector=.Workspace.Auxiliary.OptionBlock.CoatingOption.Coating_selector.STANDARD
    annotation (Dialog(group="Options"));
  //     final parameter Boolean isCoatingOption = .Workspace.Auxiliary.OptionBlock.CoatingOption.getSelector_bool(Coating_selector) ;
  parameter.Workspace.Auxiliary.OptionBlock.FilterOption.Filter_selector Filter_selector=Workspace.Auxiliary.OptionBlock.FilterOption.Filter_selector.NONE
    annotation (Dialog(group="Options"));
  final parameter Boolean isFilter=Workspace.Auxiliary.OptionBlock.FilterOption.getSelector_bool(
    Filter_selector)
    annotation (Dialog(tab="Options",group="Options"));
  parameter.Workspace.Auxiliary.OptionBlock.BufferTankOption.BufferTank_selector BufferTank_selector=Workspace.Auxiliary.OptionBlock.BufferTankOption.BufferTank_selector.NONE
    annotation (Dialog(group="Options"));
  final parameter Boolean isBufferTank=Workspace.Auxiliary.OptionBlock.BufferTankOption.getSelector_bool(
    BufferTank_selector)
    annotation (Dialog(tab="Options",group="Options"));
  annotation (
    Icon(
      graphics={
        Rectangle(
          extent={{-100,-100},{100,100}},
          fillColor={115,55,0},
          fillPattern=FillPattern.Solid),
        Text(
          textString="ChoiceBlock",
          origin={0,-24},
          extent={{-99,49},{99,-49}},
          lineColor={255,255,255}),
        Text(
          textString="Multi Units",
          origin={0,28},
          extent={{-95.00000000000001,22.93213837520423},{96,-23}},
          lineColor={255,255,255})}));
end ChoiceBlock_multi;
