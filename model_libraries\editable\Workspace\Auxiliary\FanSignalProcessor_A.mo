within Workspace.Auxiliary;
model FanSignalProcessor_A
  "Processor fan signal based on specific fan option"
  .Modelica.Blocks.Interfaces.RealInput ControllerSignal
    annotation (Placement(transformation(extent={{-120.0,22.0},{-80.0,62.0}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Interfaces.RealOutput SetPoint_fan
    annotation (Placement(transformation(extent={{78.0,30.0},{98.0,50.0}},rotation=0.0,origin={0.0,0.0})));
  // .Modelica.Blocks.Interfaces.RealOutput Speed
  //annotation (Placement(transformation(extent={{80.0,4.0},{100.0,24.0}},rotation = 0.0,origin = {0.0,0.0})));
  parameter Boolean is_fixedSpeed=false
    "if Variable speed then false, if fixed speedtrue then true";
  parameter.Modelica.SIunits.Conversions.NonSIunits.AngularVelocity_rpm max_fan_rpm
    "maximum fan speed";
  parameter.Modelica.SIunits.Frequency max_motor_frequency
    "maximum motor frequency";
  parameter.Modelica.SIunits.Conversions.NonSIunits.AngularVelocity_rpm limit_max_rpm
    "maximum limit for fan speed";
  parameter.Modelica.SIunits.Frequency limit_max_frequency
    "maximum limit motor frequency";
  output Real fan_Nstage=ControllerSignal;
  .Modelica.Blocks.Interfaces.RealOutput SetPoint_ACmotor
    annotation (Placement(transformation(extent={{78.0,56.0},{98.0,76.0}},rotation=0.0,origin={0.0,0.0})));
equation
  SetPoint_ACmotor=
    if is_fixedSpeed then
      limit_max_frequency
    elseif max_motor_frequency-37.5 > 1e-6 then
      min(
        limit_max_frequency,
        max_motor_frequency*ControllerSignal)
    else
      min(
        max_motor_frequency*ControllerSignal,
        37.5);
  SetPoint_fan=
    if is_fixedSpeed then
      ControllerSignal
    else
      1;
  // Speed = SetPoint_ACmotor * 19;
  annotation (
    Icon(
      graphics={
        Rectangle(
          origin={-1,41},
          extent={{-79,57},{79,-57}},
          fillPattern=FillPattern.Solid,
          fillColor={98,27,27})}));
end FanSignalProcessor_A;
