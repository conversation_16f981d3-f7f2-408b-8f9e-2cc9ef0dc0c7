within Workspace.Icons;
partial class CompressorPackage
  extends Workspace.Icons.PackageIcon;
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100,-100},{100,100}}),
      graphics={
        Line(
          points={{-24,48},{-62,-58}},
          color={0,0,0},
          smooth=Smooth.None),
        Line(
          points={{34,48},{68,-58}},
          color={0,0,0},
          smooth=Smooth.None)}));
end CompressorPackage;
