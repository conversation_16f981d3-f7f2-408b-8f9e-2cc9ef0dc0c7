within Workspace.Controller.SubSystems;
model PumpUserControl
  extends.Workspace.Controller.SubSystems.BaseClasses.PumpUserControlBase;
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation error_extPressure(
    isOff=crkIsOff,
    ID=-100000,
    measurement=Measurement.y,
    gain=
      if Use_fake_pump then -Gain.Output_parameter else Gain.Output_parameter,
    setPoint=Setpoint.y)
    annotation (Placement(transformation(extent={{-52.136903192002904,13.344651954945803},{-19.86880100269468,45.612754144254026}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointController_PumpA(
    AV_min=
      if Use_fake_pump then
        Ka_min
      else
        Pump_speed_min,
    AV_max=
      if Use_fake_pump then
        Ka_max
      else
        Pump_speed_max,
    AV_start=
      if Use_fake_pump then
        Ka_start
      else
        Pump_speed_start,
    isOff=crkIsOff)
    annotation (Placement(transformation(extent={{15.665592581950758,19.015731079380714},{33.36747267592672,36.71761117335669}},origin={0.0,0.0},rotation=0.0)));
  parameter Boolean Use_fake_pump=false
    "When true User Pump is not present but pressure drop is compasate thanks to a reduced pipe";
  parameter Boolean is_LWT_control=false
    "true if the pump control the LWT instead of the pressure, made for multi units";
  parameter Boolean is_EWT_control=false;
  .Modelica.Blocks.Logical.Switch Setpoint0
    annotation (Placement(transformation(extent={{-3.2212442789227946,-11.983530596288434},{11.800151435386288,3.0378651180206457}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Logical.Switch Measurement0
    annotation (Placement(transformation(extent={{-3.1669085804285046,-56.235313516865034},{11.854487133880575,-41.213917802555954}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression LWT_sp(
    y=LWT_SetPoint)
    annotation (Placement(transformation(extent={{-34.090617640768954,-0.4399617339624937},{-21.732514496002906,11.918141410803539}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.BooleanExpression LWT_control_switch(
    y=is_LWT_control)
    annotation (Placement(transformation(extent={{-33.79217826734979,-10.413382201878537},{-21.43407512258375,1.9447209428874963}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression extPressure_sp(
    y=extPressure_SetPoint)
    annotation (Placement(transformation(extent={{-33.86706870084777,-19.588021764341242},{-21.508965556081726,-7.2299186195752085}},origin={0.0,0.0},rotation=0.0)));
  parameter Real Gain_lwt=1/(55-15);
  .Modelica.Blocks.Sources.RealExpression extPressure_measrure(
    y=extPressure)
    annotation (Placement(transformation(extent={{-33.62495964882036,-60.926205401151236},{-21.266856504054317,-48.56810225638519}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression LWT_measure(
    y=LWT)
    annotation (Placement(transformation(extent={{-34.179051572383024,-48.17905157238302},{-21.820948427616976,-35.82094842761698}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Logical.Switch Setpoint
    annotation (Placement(transformation(extent={{48.84699524356474,-19.056960603799},{63.86839095787382,-4.035564889489919}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Logical.Switch Measurement
    annotation (Placement(transformation(extent={{48.901330942059026,-63.55023638935912},{63.922726656368106,-48.52884067505004}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.BooleanExpression LWT_control_switch2(
    y=not is_EWT_control)
    annotation (Placement(transformation(extent={{19.705067567776837,-15.898198878653353},{32.06317071254287,-3.54009573388732}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression EWT_sp(
    y=EWT_SetPoint)
    annotation (Placement(transformation(extent={{19.127240527049743,-24.792451032827934},{31.48534367181579,-12.4343478880619}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression EWT_measrure(
    y=EWT)
    annotation (Placement(transformation(extent={{20.133729928551865,-68.2411282736453},{32.4918330733179,-55.88302512887927}},origin={0.0,0.0},rotation=0.0)));
  parameter Real Pump_speed_max=3000
    annotation (Dialog(group="Pump"));
  parameter Real Pump_speed_min=1200
    annotation (Dialog(group="Pump"));
  parameter Real Pump_speed_start=2000
    annotation (Dialog(group="Pump"));
  parameter Real Ka_max=-0.0001
    annotation (Dialog(group="Fake Pump"));
  parameter Real Ka_min=-100
    annotation (Dialog(group="Fake Pump"));
  parameter Real Ka_start=-10
    annotation (Dialog(group="Fake Pump"));
  parameter Boolean isHeatingMode;
    .Workspace.Auxiliary.Switch_param Gain0(Gain_1 = if not isHeatingMode then Gain_lwt else -Gain_lwt,Gain_2 = 1 / (((3000))),Bool = is_LWT_control) annotation(Placement(transformation(extent = {{-4.028415215621887,-35.09092624120461},{9.418910952324968,-21.64360007325776}},origin = {0.0,0.0},rotation = 0.0)));
    .Workspace.Auxiliary.Switch_param Gain(Bool = not is_EWT_control,Gain_1 = Gain0.Output_parameter,Gain_2 = if not isHeatingMode then -Gain_lwt else Gain_lwt) annotation(Placement(transformation(extent = {{49.359442153716294,-38.8789908596154},{62.80676832166315,-25.431664691668544}},origin = {0.0,0.0},rotation = 0.0)));
    
protected
    
  .Modelica.Blocks.Interfaces.RealOutput LWT
    annotation (Placement(transformation(extent={{-77.20658721993576,50.72116580904827},{-48.874347872202414,79.0534051567816}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Interfaces.RealOutput LWT_SetPoint
    annotation (Placement(transformation(extent={{-71.03560212911587,-78.47978635030205},{-46.52118401078039,-53.96536823196658}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput extPressure_SetPoint
    annotation (Placement(transformation(extent={{-72.47797314997209,-60.36123530749194},{-47.963555031636616,-35.84681718915647}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput extPressure
    annotation (Placement(transformation(extent={{-77.73382738401226,69.56419544680897},{-49.40158803627891,97.89643479454229}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Interfaces.RealOutput EWT
    annotation (Placement(transformation(extent={{-77.20658721993576,89.84300993638087},{-48.87434787220241,118.17524928411419}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Interfaces.RealOutput EWT_SetPoint
    annotation (Placement(transformation(extent={{-72.24306645403354,-42.25585660277186},{-47.728648335698075,-17.741438484436387}},origin={0.0,0.0},rotation=0.0)));
    
equation
    
  connect(error_extPressure.sensor,setpointController_PumpA.errorSignal)
    annotation (Line(points={{-20.191482024587764,28.187978962027586},{-7.868089612088529,28.187978962027586},{-7.868089612088529,27.8666711263687},{15.665592581950758,27.8666711263687}},color={28,108,200}));
  connect(setpointController_PumpA.actuatorSignal,actuatorSignal)
    annotation (Line(points={{33.898529078745995,27.8666711263687},{68.27858476235397,27.8666711263687},{68.27858476235397,0},{100,0}},color={0,0,127}));
  connect(extPressure_SetPoint,limitsBus.extPressure_SetPoint)
    annotation (Line(points={{-60.22076409080435,-48.1040262483242},{-80.59336777536925,-48.1040262483242},{-80.59336777536925,-60},{-100,-60}},color={0,0,127}));
  connect(extPressure,measurementBus.External_Pressure)
    annotation (Line(points={{-63.567707710145584,83.73031512067563},{-81.78385385507279,83.73031512067563},{-81.78385385507279,60},{-100,60}},color={0,0,127}));
  connect(LWT,measurementBus.T_lwt)
    annotation (Line(points={{-63.04046754606909,64.88728548291493},{-81.52023377303453,64.88728548291493},{-81.52023377303453,60},{-100,60}},color={0,0,127}));
  connect(LWT_SetPoint,limitsBus.LWT_setpoint)
    annotation (Line(points={{-58.77839306994813,-66.22257729113431},{-80.47591442739997,-66.22257729113431},{-80.47591442739997,-60},{-100,-60}},color={0,0,127}));
  connect(LWT_control_switch.y,Setpoint0.u2)
    annotation (Line(points={{-20.81616996534545,-4.2343306294955205},{-10.346493323406406,-4.2343306294955205},{-10.346493323406406,-4.472832739133894},{-4.723383850353702,-4.472832739133894}},color={255,0,255}));
  connect(LWT_sp.y,Setpoint0.u1)
    annotation (Line(points={{-21.114609338764602,5.739089838420523},{-4.723383850353702,5.739089838420523},{-4.723383850353702,1.5357255465897381}},color={0,0,127}));
  connect(extPressure_sp.y,Setpoint0.u3)
    annotation (Line(points={{-20.891060398843425,-13.408970191958225},{-4.723383850353702,-13.408970191958225},{-4.723383850353702,-10.481391024857526}},color={0,0,127}));
  connect(LWT_control_switch.y,Measurement0.u2)
    annotation (Line(points={{-20.81616996534545,-4.2343306294955205},{-10.81918821327243,-4.2343306294955205},{-10.81918821327243,-48.724615659710494},{-4.669048151859414,-48.724615659710494}},color={255,0,255}));
  connect(LWT_measure.y,Measurement0.u1)
    annotation (Line(points={{-21.20304327037867,-42},{-4.669048151859414,-42},{-4.669048151859414,-42.716057373986864}},color={0,0,127}));
  connect(extPressure_measrure.y,Measurement0.u3)
    annotation (Line(points={{-20.648951346816013,-54.74715382876821},{-4.669048151859414,-54.74715382876821},{-4.669048151859414,-54.733173945434125}},color={0,0,127}));
  connect(EWT_SetPoint,limitsBus.EWT_setpoint)
    annotation (Line(points={{-59.9858573948658,-29.998647543604122},{-79.9929286974329,-29.998647543604122},{-79.9929286974329,-60},{-100,-60}},color={0,0,127}));
  connect(EWT,measurementBus.T_ewt)
    annotation (Line(points={{-63.04046754606908,104.00912961024753},{-100,104.00912961024753},{-100,60}},color={0,0,127}));
  connect(LWT_control_switch2.y,Measurement.u2)
    annotation (Line(points={{32.68107586978117,-9.719147306270337},{44.38845855400102,-9.719147306270337},{44.38845855400102,-56.03953853220458},{47.39919137062812,-56.03953853220458}},color={255,0,255}));
  connect(Measurement0.y,Measurement.u1)
    annotation (Line(points={{12.60555691959603,-48.724615659710494},{47.39919137062812,-48.724615659710494},{47.39919137062812,-50.03098024648095}},color={0,0,127}));
  connect(EWT_measrure.y,Measurement.u3)
    annotation (Line(points={{33.109738230556204,-62.062076701262285},{47.39919137062812,-62.062076701262285},{47.39919137062812,-62.04809681792821}},color={0,0,127}));
  connect(LWT_control_switch2.y,Setpoint.u2)
    annotation (Line(points={{32.68107586978117,-9.719147306270337},{47.34485567213383,-9.719147306270337},{47.34485567213383,-11.546262746644459}},color={255,0,255}));
  connect(Setpoint0.y,Setpoint.u1)
    annotation (Line(points={{12.551221221101741,-4.472832739133894},{47.34485567213383,-4.472832739133894},{47.34485567213383,-5.537704460920827}},color={0,0,127}));
  connect(EWT_sp.y,Setpoint.u3)
    annotation (Line(points={{32.103248829054095,-18.613399460444917},{47.34485567213383,-18.613399460444917},{47.34485567213383,-17.55482103236809}},color={0,0,127}));
end PumpUserControl;