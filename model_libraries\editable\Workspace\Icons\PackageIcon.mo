within Workspace.Icons;
partial class PackageIcon
  "Icon for library where additional icon elements shall be added"
  extends Modelica.Icons.Package;
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=true,
        extent={{-100,-100},{100,100}},
        grid={1,1}),
      graphics={
        Text(
          extent={{-120,125},{120,70}},
          lineColor={255,0,0},
          textString="%name")}),
    Documentation(
      info="<html>
<p>
This icon is designed for a <b>package</b> where a package
specific graphic is additionally included in the icon.
</p>
</html>"));
end PackageIcon;
