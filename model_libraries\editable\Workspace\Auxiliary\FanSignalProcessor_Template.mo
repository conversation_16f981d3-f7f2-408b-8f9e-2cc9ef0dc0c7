within Workspace.Auxiliary;
model FanSignalProcessor_Template
  "Processor fan signal general template based on specific fan option"
  import BOLT.InternalLibrary.BuildingBlocks.Utilities.Math.Smoothing .*;
  parameter Boolean use_actual_max_FSFanSpd=false;
  parameter Boolean is_AC=false
    "if EC then false, AC then true";
  parameter Boolean is_fixedSpeed=false
    "if Variable speed then false, if fixed speedtrue then true";
  parameter Modelica.SIunits.Conversions.NonSIunits.AngularVelocity_rpm max_fan_rpm=950
    "maximum fan speed";
  parameter Modelica.SIunits.Frequency max_motor_frequency=50
    "actual_max_FSFanSpd motor frequency";
  Modelica.SIunits.Frequency actual_max_motor_frequency=actual_max_FSFanSpd_internal/max_fan_rpm*max_motor_frequency;
  Modelica.Blocks.Interfaces.RealInput ControllerSignal
    annotation (Placement(transformation(extent={{-120,-10},{-80,30}})));
  Modelica.Blocks.Interfaces.RealInput actual_max_FSFanSpd if use_actual_max_FSFanSpd
    annotation (Placement(transformation(extent={{-14,-14},{14,14}},rotation=-90,origin={-10,104})));
  Modelica.Blocks.Interfaces.RealOutput SetPoint_ECmotor
    annotation (Placement(transformation(extent={{80,32},{100,52}})));
  Modelica.Blocks.Interfaces.RealOutput SetPoint_fan
    annotation (Placement(transformation(extent={{80,-6},{100,14}})));
  Modelica.Blocks.Interfaces.RealOutput SetPoint_ACmotor
    annotation (Placement(transformation(extent={{80,64},{100,84}})));
protected
  Modelica.Blocks.Interfaces.RealInput actual_max_FSFanSpd_internal;
equation
  SetPoint_ECmotor=
    if is_fixedSpeed and(not use_actual_max_FSFanSpd) then
      max_fan_rpm
    elseif(not is_fixedSpeed) and(not use_actual_max_FSFanSpd) then
      max_fan_rpm*ControllerSignal
    elseif is_fixedSpeed and use_actual_max_FSFanSpd then
      actual_max_FSFanSpd_internal
    elseif(not is_fixedSpeed) and use_actual_max_FSFanSpd then
      max_fan_rpm*ControllerSignal
    else
      -999;
  SetPoint_ACmotor=
    if is_fixedSpeed and(not use_actual_max_FSFanSpd) then
      max_motor_frequency
    elseif(not is_fixedSpeed) and(not use_actual_max_FSFanSpd) then
      max_motor_frequency*ControllerSignal
    elseif is_fixedSpeed and use_actual_max_FSFanSpd then
      actual_max_motor_frequency
    elseif(not is_fixedSpeed) and use_actual_max_FSFanSpd then
      max_motor_frequency*ControllerSignal
    else
      -999;
  SetPoint_fan=
    if is_fixedSpeed and(not use_actual_max_FSFanSpd) then
      ControllerSignal
    elseif(not is_fixedSpeed) and(not use_actual_max_FSFanSpd) then
      1
    elseif is_fixedSpeed and is_AC and use_actual_max_FSFanSpd then
      softMin(
        1,
        ControllerSignal*max_motor_frequency/actual_max_motor_frequency,
        1e-4)
    elseif is_fixedSpeed and(not is_AC) and use_actual_max_FSFanSpd then
      softMin(
        1,
        ControllerSignal*max_fan_rpm/actual_max_FSFanSpd_internal,
        1e-4)
    elseif(not is_fixedSpeed) and use_actual_max_FSFanSpd then
      1
    else
      -1;
  if use_actual_max_FSFanSpd then
    connect(actual_max_FSFanSpd,actual_max_FSFanSpd_internal);
  else
    actual_max_FSFanSpd_internal=-1;
  end if;
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false)),
    Diagram(
      coordinateSystem(
        preserveAspectRatio=false)));
end FanSignalProcessor_Template;
