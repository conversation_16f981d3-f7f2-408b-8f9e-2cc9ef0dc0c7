within Workspace.Controller.Tests;
model Test_control_1C_Zenithcooling
  .Workspace.Controller.Controller_1C_cooling controller_1C(
    Fan_MaxFrequency=950,
    isOffSDTmin_fan=true,
    isOffSDTmax_fan=true,
    isOffDGTmax_fan=true,
    isOffSSTmin_EXV=true,
    isOffSSTmax_EXV=true,
    isOffDSHmin_EXV=true,
    isOffDGTmax_EXV=true,
    isOffSSTmin_comp=true,
    isOffSDTmax_comp=true,
    isOffDGTmax_comp=true,
    min_speed=30,
    max_speed=140)
    annotation (Placement(transformation(extent={{-20.720375897215625,-20.720375897215625},{20.720375897215625,20.720375897215625}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Controller.Tests.y_equalto_kx_plus_b y_equalto_kx_plus_b()
    annotation (Placement(transformation(extent={{53.07354107111482,-42.92645892888519},{142.92645892888518,46.92645892888519}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.MeasurementBus measurementBus
    annotation (Placement(transformation(extent={{28.09005754006398,36.303938623931735},{68.09005754006398,76.30393862393174}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Controller.ControllerSettings_cooling controllerSettings(
    SST_min=274.15)
    annotation (Placement(transformation(extent={{10.0,76.0},{-10.0,96.0}},origin={0.0,0.0},rotation=0.0)));
equation
  connect(controller_1C.fan,y_equalto_kx_plus_b.x[1])
    annotation (Line(points={{24.03563604077013,12.433462454303314},{50.0108355828082,12.433462454303314},{50.0108355828082,1.5507354107111482},{75.98603512484627,1.5507354107111482}},color={0,0,127}));
  connect(controller_1C.exv,y_equalto_kx_plus_b.x[2])
    annotation (Line(points={{24.03563604077013,7.666229852976297},{50.0108355828082,7.666229852976297},{50.0108355828082,1.5507354107111482},{75.98603512484627,1.5507354107111482}},color={0,0,127}));
  connect(controller_1C.compressor,y_equalto_kx_plus_b.x[3])
    annotation (Line(points={{24.03563604077013,-8.08125582890758},{50.0108355828082,-8.08125582890758},{50.0108355828082,1.5507354107111482},{75.98603512484627,1.5507354107111482}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[1],measurementBus.capacity)
    annotation (Line(points={{120.91249405373145,1.5507354107111482},{130,1.5507354107111482},{130,56.303938623931735},{48.09005754006398,56.303938623931735}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[2],measurementBus.dT_ssh)
    annotation (Line(points={{120.91249405373145,1.5507354107111482},{130,1.5507354107111482},{130,56.303938623931735},{48.09005754006398,56.303938623931735}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[3],measurementBus.T_sdt)
    annotation (Line(points={{120.91249405373145,1.5507354107111482},{130,1.5507354107111482},{130,56.303938623931735},{48.09005754006398,56.303938623931735}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[4],measurementBus.T_sst)
    annotation (Line(points={{120.91249405373145,1.5507354107111482},{130,1.5507354107111482},{130,56.303938623931735},{48.09005754006398,56.303938623931735}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[5],measurementBus.Fan_signal)
    annotation (Line(points={{120.91249405373145,1.5507354107111482},{126.91249405373145,1.5507354107111482},{126.91249405373145,56.303938623931735},{48.09005754006398,56.303938623931735}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[6],measurementBus.compressorFrequency)
    annotation (Line(points={{120.91249405373145,1.5507354107111482},{126.91249405373145,1.5507354107111482},{126.91249405373145,56.303938623931735},{48.09005754006398,56.303938623931735}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[7],measurementBus.T_dgt)
    annotation (Line(points={{120.91249405373145,1.5507354107111482},{126.91249405373145,1.5507354107111482},{126.91249405373145,56.303938623931735},{48.09005754006398,56.303938623931735}},color={0,0,127}));
  connect(controllerSettings.limitsBus,controller_1C.limitsBus)
    annotation (Line(points={{-8.78194466031494,82.97918360107789},{-40.11624547307057,82.97918360107789},{-40.11624547307057,6.216112769164688},{-20.720375897215625,6.216112769164688}},color={255,204,51}));
  connect(measurementBus,controller_1C.measurementBus)
    annotation (Line(points={{48.09005754006398,56.303938623931735},{-26.720375897215625,56.303938623931735},{-26.720375897215625,14.504263128050939},{-20.720375897215625,14.504263128050939}},color={255,204,51}));
  connect(measurementBus,controllerSettings.measurementBus)
    annotation (Line(points={{48.09005754006398,56.30393862393174},{29.821408717452716,56.30393862393174},{29.821408717452716,85.45699314828894},{11.552759894841449,85.45699314828894}},color={255,204,51}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end Test_control_1C_Zenithcooling;
