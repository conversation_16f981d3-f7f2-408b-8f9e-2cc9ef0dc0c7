within Workspace.Controller.SubSystems;
model PressureControl
  extends.BOLT.InternalLibrary.BuildingBlocks.Icons.Pump;
  .Modelica.Blocks.Interfaces.RealOutput actuatorSignal
    annotation (Placement(transformation(extent={{90.0,-10.0},{110.0,10.0}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{90,-10},{110,10}},origin={10,0})));
  parameter <PERSON><PERSON><PERSON> IsOff=false
    "Specify whether the controller is on or off";
  .Workspace.Interfaces.MeasurementBus measurementBus
    annotation (Placement(transformation(extent={{-106,54},{-66,94}},origin={0,0},rotation=0),iconTransformation(extent={{-120,40},{-80,80}})));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointController(
    AV_min=Av_min,
    AV_max=Av_max,
    isOff=IsOff,
    AV_start=AV_start,
    manualOff=IsOff,
    AV_value_off=0.00001)
    annotation (Placement(transformation(extent={{-10.620431370607152,-9.584307746859098},{9.379568629392848,10.415692253140902}},origin={0.0,0.0},rotation=0.0)));
  parameter Real Pressure_Gain
    annotation (Dialog(group="Pressure Error Calculation"));
  parameter Real Pressure_Setpoint
    annotation (Dialog(group="Pressure Error Calculation"));
  parameter Real LWT_Gain
    annotation (Dialog(group="Temperature Error Calculation"));
  parameter Real LWT_Setpoint
    annotation (Dialog(group="Temperature Error Calculation"));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation errorPressure(
    gain=Pressure_Gain,
    setPoint=Pressure_Setpoint,
    measurement=External_pressure,
    isOff=IsOff,
    ID=1)
    annotation (Placement(transformation(extent={{-89.4875525398281,10.571001100891934},{-69.4875525398281,30.571001100891934}},origin={0.0,0.0},rotation=0.0)));
  parameter Real Av_min
    annotation (Dialog(group="Controller"));
  parameter Real Av_max
    annotation (Dialog(group="Controller"));
  parameter Real AV_start
    annotation (Dialog(group="Controller"));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation errorLWT(
    ID=2,
    isOff=IsOff,
    measurement=LWT,
    setPoint=LWT_Setpoint,
    gain=-LWT_Gain / ((-2)) * 100000)
    annotation (Placement(transformation(extent={{-89.72106268152602,-20.76156745079942},{-69.72106268152602,-0.7615674507994203}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Min min
    annotation (Placement(transformation(extent={{-48.28168959558584,-9.850633057129217},{-28.28168959558584,10.149366942870783}},origin={0,0},rotation=0)));
protected
  .Modelica.Blocks.Interfaces.RealOutput External_pressure
    annotation (Placement(transformation(extent={{-67.15210280823945,46.374969312558925},{-44.94230644009946,68.5847656806989}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput LWT
    annotation (Placement(transformation(extent={{-65.88939312885753,66.77984394502688},{-46.37823181143837,86.29100526244603}},origin={0.0,0.0},rotation=0.0)));
equation
  connect(External_pressure,measurementBus.External_Pressure)
    annotation (Line(points={{-56.04720462416945,57.479867496628906},{-73.76244323465306,57.479867496628906},{-73.76244323465306,74},{-86,74}},color={0,0,127}));
  connect(setpointController.actuatorSignal,actuatorSignal)
    annotation (Line(points={{9.97956862939285,0.4156922531409013},{55.52106865325517,0.4156922531409013},{55.52106865325517,0},{100,0}},color={0,0,127}));
  connect(errorLWT.sensor,min.u2)
    annotation (Line(points={{-69.92106268152602,-11.561567450799421},{-59.09731221085657,-11.561567450799421},{-59.09731221085657,-5.850633057129217},{-48.28168959558584,-5.850633057129217}},color={28,108,200}));
  connect(errorPressure.sensor,min.u1)
    annotation (Line(points={{-69.6875525398281,19.771001100891933},{-58.586157813787906,19.771001100891933},{-58.586157813787906,5.749366942870783},{-48.28168959558584,5.749366942870783}},color={28,108,200}));
  connect(min.y,setpointController.errorSignal)
    annotation (Line(points={{-28.28168959558584,0.1493669428707829},{-10.620431370607152,0.1493669428707829},{-10.620431370607152,0.4156922531409013}},color={28,108,200}));
  connect(LWT,measurementBus.T_lwt)
    annotation (Line(points={{-56.13381247014795,76.53542460373646},{-71.06690623507397,76.53542460373646},{-71.06690623507397,74},{-86,74}},color={0,0,127}));
end PressureControl;
