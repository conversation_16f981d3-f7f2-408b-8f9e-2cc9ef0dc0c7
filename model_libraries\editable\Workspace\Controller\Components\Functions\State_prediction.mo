within Workspace.Controller.Components.Functions;
function State_prediction
  extends Modelica.Icons.Function;
  input Real freq_compressor;
  input Integer N_module;
  input Real FreqTransition_1_2;
  input Real FreqTransition_2_3;
  input Real FreqTransition_3_4;
  input Real CapDesign_1;
  input Real CapDesign_2;
  input Real CapDesign_3;
  input Real CapDesign_4;
  input Real Target_cap;
  input Real Cap_1unit;
  input Boolean notMode1;
  input Real offset=15;
  output Integer State_id=1;
  Real ratio;
  Real next_Freq;
  // Real Freq_3units;
  // Real Freq_4units;
  Real[4] Capdesign={CapDesign_1,CapDesign_2,CapDesign_3,CapDesign_4};
  Real[3] FreqTransition={FreqTransition_1_2,FreqTransition_2_3,FreqTransition_3_4};
  Real currentCap=0;
  Real transiFreq;
algorithm
  ratio := Cap_1unit/CapDesign_1;
  //   Freq_4units := Target_cap * freq_compressor / ratio / (CapDesign_1+CapDesign_2+CapDesign_3+CapDesign_4);
  //   Freq_3units := Target_cap * freq_compressor / ratio / (CapDesign_1+CapDesign_2+CapDesign_3);
  //   Freq_2units := Target_cap * freq_compressor / ratio / (CapDesign_1+CapDesign_2);
  if notMode1 then
    State_id := 0;
  else
    for n in 1:N_module-1 loop
      currentCap := currentCap+Capdesign[n];
      transiFreq := FreqTransition[n];
      next_Freq := Target_cap*freq_compressor/ratio/(currentCap);
      if next_Freq > transiFreq+offset then
        State_id := State_id+1;
      end if;
    end for;
  end if;
end State_prediction;
