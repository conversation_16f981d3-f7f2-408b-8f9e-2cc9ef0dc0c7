within Workspace.Controller.SubSystems;
model CompressorControl_cooling
  extends.Workspace.Controller.SubSystems.BaseClasses.CompressorControlBase(
    compressorFrequency_max=140,
    compressorFrequency_min=30);
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation T_sst_min_error(
    measurement=T_sst,
    setPoint=T_sst_min_limit_comp,
    ID=2,
    gain=-1/(((274-220))),
    isOff=isOffSSTmin or crkIsOff)
    annotation (Placement(transformation(extent={{-86.22000422560843,-2.3876164854338384},{-31.454296861788563,17.61238351456616}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation compressorFrequency_error(
    ID=1,
    measurement=actuatorSignal,
    gain=1/((compressorFrequency_max-compressorFrequency_min)),
    setPoint=compressorFrequency,
    isOff=crkIsOff)
    annotation (Placement(transformation(extent={{-87.38285368190995,14.0},{-32.61714631809005,34.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Min min
    annotation (Placement(transformation(extent={{-21.064836470141536,8.935163529858464},{-2.935163529858464,27.064836470141536}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointController(
    AV_max=compressorFrequency_max,
    AV_start=110,
    isOff=crkIsOff,
    AV_min=compressorFrequency_min,
    manualOff=manualOff,
    AV_value_off=AV_value_off)
    annotation (Placement(transformation(extent={{166.0,-10.594921761410077},{186.0,9.405078238589923}},origin={0.0,0.0},rotation=0.0)));
  parameter Boolean isOffSSTmin=false;
  parameter Boolean isOffSDTmax=false;
  parameter Boolean isOffFreq_max_SDT=false;
  parameter Boolean isOffFreq_min_SDT=false;
  parameter Boolean manualOff=false;
  parameter Real AV_value_off=720;
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation Freq_min_error(
    isOff=isOffMinFrequency or crkIsOff,
    gain=1/((compressorFrequency_max-compressorFrequency_min)),
    ID=3,
    setPoint=Min_frequency,
    measurement=actuatorSignal)
    annotation (Placement(transformation(extent={{-87.23717725482784,-18.2691920853195},{-32.26281975541751,1.7308079146805007}},origin={0.0,0.0},rotation=0.0)));
  parameter Boolean isOffMinFrequency=false;
  parameter Boolean isOffMaxFrequency=false;
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation Freq_max_error(
    measurement=actuatorSignal,
    setPoint=Max_frequency,
    ID=4,
    gain=1/((compressorFrequency_max-compressorFrequency_min)),
    isOff=isOffMaxFrequency or crkIsOff)
    annotation (Placement(transformation(extent={{-88.38286283142111,-32.55265345584578},{-33.408505332010776,-12.552653455845778}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Min min3
    annotation (Placement(transformation(extent={{30.05735234513225,1.6008294847059439},{45.361626017896306,16.90510315747}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Max max
    annotation (Placement(transformation(extent={{6.347863163617973,6.347863163617973},{21.652136836382027,21.652136836382027}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation Freq_max_sdt_error(
    isOff=isOffFreq_max_SDT or crkIsOff,
    gain=1/((compressorFrequency_max-compressorFrequency_min)),
    ID=6,
    setPoint=Freq_max_SDT_limit_comp,
    measurement=actuatorSignal)
    annotation (Placement(transformation(extent={{-88.6531160818531,-47.52237067436258},{-33.67875858244274,-27.522370674362577}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation Freq_min_sdt_error(
    measurement=actuatorSignal,
    setPoint=Freq_min_SDT_limit_comp,
    ID=7,
    gain=1/((compressorFrequency_max-compressorFrequency_min)),
    isOff=isOffFreq_min_SDT or crkIsOff)
    annotation (Placement(transformation(extent={{-87.20549885148102,-63.58899864858414},{-32.231141352070665,-43.58899864858414}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Min min2
    annotation (Placement(transformation(extent={{62.08727253988633,-3.983464104928636},{77.39154621265038,11.320809567835425}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Max max2
    annotation (Placement(transformation(extent={{90.68441979593945,-8.760912688297477},{105.9886934687035,6.5433609844665845}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation SDT_max_error(
    isOff=isOffSDTmax or crkIsOff,
    gain=1/(((348-300))),
    ID=8,
    setPoint=T_sdt_max_limit_comp,
    measurement=T_sdt)
    annotation (Placement(transformation(extent={{-87.48717874970518,-81.61238351456615},{-32.51282125029482,-61.61238351456615}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Min min4
    annotation (Placement(transformation(extent={{126.34786316361797,-7.652136836382031},{141.65213683638203,7.652136836382031}},origin={0.0,0.0},rotation=0.0)));
protected
  .Modelica.Blocks.Interfaces.RealOutput T_sst_min_limit_comp
    annotation (Placement(transformation(extent={{-70.60457743573775,-121.23016868301218},{-49.24024095637442,-99.86583220364886}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput T_sdt_max_limit_comp
    annotation (Placement(transformation(extent={{-70.68216823968166,-136.68216823968166},{-49.317831760318334,-115.31783176031834}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput T_sst
    annotation (Placement(transformation(extent={{-60.68216823968166,63.31783176031834},{-39.31783176031834,84.68216823968166}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput T_sdt
    annotation (Placement(transformation(extent={{-60.68216823968166,47.31783176031834},{-39.31783176031834,68.68216823968166}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Max_frequency
    annotation (Placement(transformation(extent={{-68.89627276199465,-171.08611197221072},{-47.53193628263132,-149.7217754928474}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Min_frequency
    annotation (Placement(transformation(extent={{-69.9459412362217,-152.47340188223063},{-48.58160475685837,-131.10906540286732}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Freq_max_SDT_limit_comp
    annotation (Placement(transformation(extent={{-71.23831489411842,-105.13966691604645},{-49.87397841475509,-83.77533043668313}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Freq_min_SDT_limit_comp
    annotation (Placement(transformation(extent={{-68.68216823968166,-186.68216823968166},{-47.31783176031835,-165.31783176031834}},origin={0.0,0.0},rotation=0.0)));
equation
  connect(compressorFrequency_error.sensor,min.u1)
    annotation (Line(points={{-33.16480339172825,23.2},{-21.064836470141536,23.2},{-21.064836470141536,23.07630842327926}},color={28,108,200}));
  connect(T_sst_min_error.sensor,min.u2)
    annotation (Line(points={{-32.00195393542676,6.812383514566162},{-26,6.812383514566162},{-26,12.561098117915078},{-21.064836470141536,12.561098117915078}},color={28,108,200}));
  connect(T_sst,measurementBus.T_sst)
    annotation (Line(points={{-50,74},{-100,74},{-100,60}},color={0,0,127}));
  connect(T_sdt,measurementBus.T_sdt)
    annotation (Line(points={{-50,58},{-50,60},{-100,60}},color={0,0,127}));
  connect(Freq_min_error.sensor,max.u2)
    annotation (Line(points={{-32.81256333041161,-9.0691920853195},{6.347863163617973,-9.0691920853195},{6.347863163617973,9.408717898170783}},color={28,108,200}));
  connect(Freq_max_error.sensor,min3.u2)
    annotation (Line(points={{-33.95824890700488,-23.35265345584578},{24.891378719525573,-23.35265345584578},{24.891378719525573,4.661684219258754},{30.05735234513225,4.661684219258754}},color={28,108,200}));
  connect(max.y,min3.u1)
    annotation (Line(points={{21.652136836382027,14},{21.652136836382027,13.538162949461906},{30.05735234513225,13.538162949461906}},color={28,108,200}));
  connect(min.y,max.u1)
    annotation (Line(points={{-2.935163529858464,18},{1.926909894218369,18},{1.926909894218369,18.285196628373935},{6.347863163617973,18.285196628373935}},color={28,108,200}));
  connect(min2.y,max2.u1)
    annotation (Line(points={{77.39154621265038,3.6686727314533947},{70.46300008023273,3.6686727314533947},{70.46300008023273,3.1764207764584924},{90.68441979593945,3.1764207764584924}},color={28,108,200}));
  connect(min3.y,min2.u1)
    annotation (Line(points={{45.361626017896306,9.252966321087971},{53.72444927889131,9.252966321087971},{53.72444927889131,7.9538693598273325},{62.08727253988633,7.9538693598273325}},color={28,108,200}));
  connect(Freq_max_sdt_error.sensor,min2.u2)
    annotation (Line(points={{-34.22850215743684,-38.322370674362574},{44.494729142731046,-38.322370674362574},{44.494729142731046,-0.9226093703758238},{62.08727253988633,-0.9226093703758238}},color={28,108,200}));
  connect(Freq_min_sdt_error.sensor,max2.u2)
    annotation (Line(points={{-32.78088492706477,-54.388998648584135},{72.04442896452659,-54.388998648584135},{72.04442896452659,-5.700057953744665},{90.68441979593945,-5.700057953744665}},color={28,108,200}));
  connect(SDT_max_error.sensor,min4.u2)
    annotation (Line(points={{-33.06256482528892,-72.41238351456614},{116,-72.41238351456614},{116,-4.5912821018292185},{126.34786316361797,-4.5912821018292185}},color={28,108,200}));
  connect(max2.y,min4.u1)
    annotation (Line(points={{105.9886934687035,-1.1087758519154463},{114.16827831616074,-1.1087758519154463},{114.16827831616074,4.285196628373938},{126.34786316361797,4.285196628373938}},color={28,108,200}));
  connect(min4.y,setpointController.errorSignal)
    annotation (Line(points={{141.65213683638203,0},{166,0},{166,-0.594921761410077}},color={28,108,200}));
  connect(setpointController.actuatorSignal,actuatorSignal)
    annotation (Line(points={{186.6,-0.594921761410077},{186.6,0},{216,0}},color={0,0,127}));
  connect(T_sst_min_limit_comp,limitsBus.T_sst_min_limit_comp)
    annotation (Line(points={{-59.92240919605609,-110.54800044333052},{-92,-110.54800044333052},{-92,-120}},color={0,0,127}));
  connect(Min_frequency,limitsBus.Min_frequency)
    annotation (Line(points={{-59.263772996540034,-141.79123364254897},{-92,-141.79123364254897},{-92,-120}},color={0,0,127}));
  connect(Max_frequency,limitsBus.Max_frequency)
    annotation (Line(points={{-58.21410452231299,-160.40394373252906},{-92,-160.40394373252906},{-92,-120}},color={0,0,127}));
  connect(Freq_max_SDT_limit_comp,limitsBus.Freq_max_SDT_limit_comp)
    annotation (Line(points={{-60.55614665443676,-94.45749867636479},{-92,-94.45749867636479},{-92,-120}},color={0,0,127}));
  connect(Freq_min_SDT_limit_comp,limitsBus.Freq_min_SDT_limit_comp)
    annotation (Line(points={{-58,-176},{-92,-176},{-92,-120}},color={0,0,127}));
  connect(T_sdt_max_limit_comp,limitsBus.T_sdt_max_limit_comp)
    annotation (Line(points={{-60,-126},{-92,-126},{-92,-120}},color={0,0,127}));
end CompressorControl_cooling;
