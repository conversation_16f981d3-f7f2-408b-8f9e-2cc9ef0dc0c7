within Workspace.System.HPH.BaseCycles.optimization;
model Unit
  import CoolantCommonMedium=BOLT.InternalLibrary.Media.Coolant.CoolantCommon;
  .Workspace.System.HPH.BaseCycles.Equipement oL_modular(
    EWT=EWT,
    LWT=LWT,
    Water_pressure=sourceBrine.p_set,
    is_monobloc=is_monobloc,
    Use_pump=Use_pump,
    Use_filter=Use_filter,
    Use_expansion_tank=Use_tank,
    OAT=OAT,
    OAT_WB=OAT_WB,
    isOFFA=isOFFA,
    isOFFB=isOFFB,
    BrineConcentration=BrineConcentration,
    CoolantMedium=CoolantMedium)
    annotation (Placement(transformation(extent={{-32.0,2.0},{-12.0,22.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Source sourceBrine(
    Vd_fixed=false,
    T_set=EWT,
    p_fixed=true,
    X=BrineConcentration,
    CoolantMedium=CoolantMedium)
    annotation (Placement(transformation(extent={{-4.0,-4.0},{4.0,4.0}},origin={-22.0,-22.0},rotation=90.0)));
  .BOLT.BoundaryNode.Coolant.Sink sinkBrine(
    p_fixed=false,
    T_fixed=true,
    T_set=LWT,
    X=BrineConcentration,
    CoolantMedium=CoolantMedium)
    annotation (Placement(transformation(extent={{-4.0,-4.0},{4.0,4.0}},origin={-20.0,44.0},rotation=-90.0)));
  .BOLT.Control.SteadyState.SetpointControl.Actuator EXV_actuatorA(
    setPoint=0.5,
    minBound=0,
    maxBound=1,
    fixed=false,
    isOff=isOFFA)
    annotation (Placement(transformation(extent={{-48.0,6.0},{-68.0,26.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Actuator EXV_actuatorB(
    fixed=false,
    maxBound=1,
    minBound=0,
    setPoint=0.5,
    isOff=is_monobloc or isOFFB)
    annotation (Placement(transformation(extent={{10.0,6.0},{30.0,26.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Actuator Fan_actuatorA(
    fixed=true,
    maxBound=950,
    minBound=0,
    setPoint=720,
    isOff=isOFFA)
    annotation (Placement(transformation(extent={{-70.0,0.0},{-90.0,20.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Actuator Fan_actuatorB(
    setPoint=720,
    minBound=0,
    maxBound=950,
    fixed=true,
    isOff=is_monobloc or isOFFB)
    annotation (Placement(transformation(extent={{20.0,0.0},{40.0,20.0}},origin={0.0,0.0},rotation=0.0)));
  parameter Boolean is_monobloc=true;
  parameter Boolean Use_pump=false;
  parameter Boolean Use_tank=false;
  parameter Boolean Use_filter=false;
  parameter Boolean is0ff=false;
  parameter.Modelica.SIunits.Temperature OAT=280.15;
  parameter.Modelica.SIunits.Temperature OAT_WB=279.15;
  parameter.Modelica.SIunits.Temperature EWT=303.15;
  parameter.Modelica.SIunits.Temperature LWT=308.15;
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointControllerA(
    AV_start=100,
    AV_max=140,
    AV_min=30,
    isOff=isOFFA)
    annotation (Placement(transformation(extent={{10.0,-10.0},{-10.0,10.0}},origin={-74.0,-24.0},rotation=-90.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation errorCalculationA(
    setPoint=TargetCapacity,
    measurement=oL_modular.BlocA.condBPHE.summary.Q_flow_coolant,
    isOff=isOFFA)
    annotation (Placement(transformation(extent={{10.0,-10.0},{-10.0,10.0}},origin={-74.0,-52.0},rotation=-90.0)));
  parameter Real TargetCapacity=.Workspace.Controller.Capacity_function_heating(
    sinkBrine.T_set-273.15);
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation errorCalculationB(
    measurement=oL_modular.BlocB.condBPHE.summary.Q_flow_coolant,
    setPoint=TargetCapacity,
    isOff=is_monobloc or isOFFB)
    annotation (Placement(transformation(extent={{10.0,-10.0},{-10.0,10.0}},origin={18.0,-56.0},rotation=-90.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointControllerB(
    AV_min=30,
    AV_max=140,
    AV_start=100,
    isOff=is_monobloc or isOFFB)
    annotation (Placement(transformation(extent={{10.0,-10.0},{-10.0,10.0}},origin={18.0,-28.0},rotation=-90.0)));
  parameter Boolean isOFFA=false;
  parameter Boolean isOFFB=false;
  // Declaration of Coolant Medium Package
  //package CoolantCommonMedium =
  //.BOLT.InternalLibrary.Media.Coolant.CoolantCommon                             "Coolant Medium Package" annotation ();
  // Declaration of Coolant Medium at Evaporator Circuit
  parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector CoolantMedium=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector.FW
    "Coolant Medium Selection"
    annotation (Dialog(group="Medium"));
  final parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Empty coolantInf=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.getSelector(
    CoolantMedium)
    "Coolant Medium"
    annotation ();
  .BOLT.InternalLibrary.Media.Coolant.CoolantCommon.Temperature FreezTemp=.BOLT.InternalLibrary.Media.Coolant.CoolantCommon.freezeTemp(
    coolantInf,
    BrineConcentration,
    1);
  parameter Real BrineConcentration=0.2
    annotation (Dialog(group="Medium"));
equation
  connect(setpointControllerA.actuatorSignal,oL_modular.Compressor_controller_A)
    annotation (Line(points={{-74,-13.399999999999999},{-74,4.5},{-33,4.5}},color={0,0,127}));
  connect(errorCalculationA.sensor,setpointControllerA.errorSignal)
    annotation (Line(points={{-74.8,-42.2},{-74.8,-38.099999999999994},{-74,-38.099999999999994},{-74,-34}},color={28,108,200}));
  connect(oL_modular.coolant_out,sinkBrine.port)
    annotation (Line(points={{-21.6,21.6},{-21.6,40},{-20,40}},color={0,127,0}));
  connect(sourceBrine.port,oL_modular.coolant_in)
    annotation (Line(points={{-22,-18},{-22,-10.5},{-21.8,-10.5},{-21.8,-3}},color={0,127,0}));
  connect(errorCalculationB.sensor,setpointControllerB.errorSignal)
    annotation (Line(points={{17.200000000000003,-46.2},{17.200000000000003,-42.099999999999994},{18.000000000000004,-42.099999999999994},{18.000000000000004,-38}},color={28,108,200}));
  connect(setpointControllerB.actuatorSignal,oL_modular.Compressor_controller_B)
    annotation (Line(points={{17.999999999999996,-17.4},{17.999999999999996,4.5},{-11,4.5}},color={0,0,127}));
  connect(EXV_actuatorA.value,oL_modular.EXV_controller_A)
    annotation (Line(points={{-53,16},{-33,16},{-33,14.5}},color={0,0,127}));
  connect(EXV_actuatorB.value,oL_modular.EXV_controller_B)
    annotation (Line(points={{15,16},{15,14.5},{-11,14.5}},color={0,0,127}));
  connect(Fan_actuatorA.value,oL_modular.Fan_controller_A)
    annotation (Line(points={{-75,10},{-54,10},{-54,9.5},{-33,9.5}},color={0,0,127}));
  connect(Fan_actuatorB.value,oL_modular.Fan_controller_B)
    annotation (Line(points={{25,10},{7,10},{7,9.5},{-11,9.5}},color={0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end Unit;
