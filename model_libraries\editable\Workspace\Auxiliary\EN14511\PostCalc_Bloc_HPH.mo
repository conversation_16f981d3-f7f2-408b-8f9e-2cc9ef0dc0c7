within Workspace.Auxiliary.EN14511;
model PostCalc_Bloc_HPH
  .Workspace.Auxiliary.EN14511.EN14511_HPC_HPH eN14511_HPC_HPH_A(
    integrated_pump=Use_Pump,
    heating_mode=Heating_mode)
    annotation (Placement(transformation(extent={{-18.0,22.0},{2.0,42.0}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.MeasurementBus measurementBusA
    annotation (Placement(transformation(extent={{-74.0,30.0},{-34.0,70.0}},origin={0.0,0.0},rotation=0.0)));
  parameter Boolean Use_Pump=false;
  parameter Boolean Is_HSF=false;
  .Workspace.Interfaces.MeasurementBus measurementBusB
    annotation (Placement(transformation(extent={{-80.0,-74.0},{-40.0,-34.0}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Auxiliary.EN14511.EN14511_HPC_HPH eN14511_HPC_HPH_B(
    integrated_pump=Use_Pump,
    heating_mode=Heating_mode)
    annotation (Placement(transformation(extent={{-18.0,-24.0},{2.0,-4.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Math.Add Net_pow(
    k1=1.0,
    k2=
      if is_monobloc then
        0.0
      else
        1.0)
    annotation (Placement(transformation(extent={{32.0,8.0},{52.0,28.0}},origin={0.0,0.0},rotation=0.0)));
  parameter Boolean is_monobloc=false;
  .Modelica.Blocks.Math.Add Net_cap(
    k2=
      if is_monobloc then
        0.0
      else
        1.0)
    annotation (Placement(transformation(extent={{34.0,-26.0},{54.0,-6.0}},origin={0.0,0.0},rotation=0.0)));
  parameter Boolean Heating_mode=true;
  .Modelica.Blocks.Sources.RealExpression q_air_A
    annotation (Placement(transformation(extent={{-76.0,-17.5},{-56.0,2.5}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression q_air_B
    annotation (Placement(transformation(extent={{-76.0,-31.5},{-56.0,-11.5}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression DPe_air_A
    annotation (Placement(transformation(extent={{-76.0,7.5},{-56.0,27.5}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression DPe_air_B
    annotation (Placement(transformation(extent={{-76.0,-6.5},{-56.0,13.5}},origin={0.0,0.0},rotation=0.0)));
  parameter Boolean Use_EN=true;
equation
  connect(eN14511_HPC_HPH_A.DPe,measurementBusA.Dpe)
    annotation (Line(points={{-18,38.2},{-36,38.2},{-36,50},{-54,50}},color={0,0,127}));
  connect(eN14511_HPC_HPH_A.DPe_air_A,DPe_air_A.y)
    annotation (Line(points={{-18,36.2},{-36.5,36.2},{-36.5,17.5},{-55,17.5}},color={0,0,127}));
  connect(eN14511_HPC_HPH_A.DPe_air_B,DPe_air_B.y)
    annotation (Line(points={{-18,34.2},{-36.5,34.2},{-36.5,3.5},{-55,3.5}},color={0,0,127}));
  connect(eN14511_HPC_HPH_A.DPi,measurementBusA.Dpi)
    annotation (Line(points={{-18,32.4},{-36,32.4},{-36,50},{-54,50}},color={0,0,127}));
  connect(eN14511_HPC_HPH_A.q,measurementBusA.q)
    annotation (Line(points={{-18,30.6},{-36,30.6},{-36,50},{-54,50}},color={0,0,127}));
  connect(eN14511_HPC_HPH_A.q_air_A,q_air_A.y)
    annotation (Line(points={{-18,28.6},{-36.5,28.6},{-36.5,-7.5},{-55,-7.5}},color={0,0,127}));
  connect(eN14511_HPC_HPH_A.q_air_B,q_air_B.y)
    annotation (Line(points={{-18,26.6},{-36.5,26.6},{-36.5,-21.5},{-55,-21.5}},color={0,0,127}));
  connect(eN14511_HPC_HPH_A.inst_gross_cap,measurementBusA.capacity)
    annotation (Line(points={{-18,24.6},{-36,24.6},{-36,50},{-54,50}},color={0,0,127}));
  connect(eN14511_HPC_HPH_A.inst_gross_pow,measurementBusA.pow_total)
    annotation (Line(points={{-18,22.6},{-36,22.6},{-36,50},{-54,50}},color={0,0,127}));
  connect(eN14511_HPC_HPH_B.DPe,measurementBusB.Dpe)
    annotation (Line(points={{-18,-7.799999999999999},{-38,-7.799999999999999},{-38,-54},{-60,-54}},color={0,0,127}));
  connect(eN14511_HPC_HPH_B.DPe_air_A,DPe_air_A.y)
    annotation (Line(points={{-18,-9.8},{-36.5,-9.8},{-36.5,17.5},{-55,17.5}},color={0,0,127}));
  connect(eN14511_HPC_HPH_B.DPe_air_B,DPe_air_B.y)
    annotation (Line(points={{-18,-11.8},{-36.5,-11.8},{-36.5,3.5},{-55,3.5}},color={0,0,127}));
  connect(eN14511_HPC_HPH_B.DPi,measurementBusB.Dpi)
    annotation (Line(points={{-18,-13.6},{-39,-13.6},{-39,-54},{-60,-54}},color={0,0,127}));
  connect(eN14511_HPC_HPH_B.q,measurementBusB.q)
    annotation (Line(points={{-18,-15.4},{-39,-15.4},{-39,-54},{-60,-54}},color={0,0,127}));
  connect(eN14511_HPC_HPH_B.q_air_A,q_air_A.y)
    annotation (Line(points={{-18,-17.4},{-36.5,-17.4},{-36.5,-7.5},{-55,-7.5}},color={0,0,127}));
  connect(eN14511_HPC_HPH_B.q_air_B,q_air_B.y)
    annotation (Line(points={{-18,-19.4},{-36.5,-19.4},{-36.5,-21.5},{-55,-21.5}},color={0,0,127}));
  connect(eN14511_HPC_HPH_B.inst_gross_cap,measurementBusB.capacity)
    annotation (Line(points={{-18,-21.4},{-39,-21.4},{-39,-54},{-60,-54}},color={0,0,127}));
  connect(eN14511_HPC_HPH_B.inst_gross_pow,measurementBusB.pow_total)
    annotation (Line(points={{-18,-23.4},{-39,-23.4},{-39,-54},{-60,-54}},color={0,0,127}));
  connect(eN14511_HPC_HPH_A.inst_net_cap,Net_cap.u1)
    annotation (Line(points={{2,35},{17,35},{17,-10},{32,-10}},color={0,0,127}));
  connect(eN14511_HPC_HPH_A.inst_net_pow,Net_pow.u1)
    annotation (Line(points={{2,29},{16,29},{16,24},{30,24}},color={0,0,127}));
  connect(eN14511_HPC_HPH_B.inst_net_cap,Net_cap.u2)
    annotation (Line(points={{2,-11},{17,-11},{17,-22},{32,-22}},color={0,0,127}));
  connect(eN14511_HPC_HPH_B.inst_net_pow,Net_pow.u2)
    annotation (Line(points={{2,-17},{16,-17},{16,12},{30,12}},color={0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end PostCalc_Bloc_HPH;
