within Workspace.Controller;
function Capacity_function_cooling
  extends Modelica.Icons.Function;
  input Real OAT;
  input Real Target_cap;
  input Real Unit;
  output Real Target_Capacity;
  Real Max_Target_Cap40;
  Real Max_Target_Cap50;
  Real Max_Target_Cap60;
  Real Max_Target_Cap70;
  parameter Boolean is_ULN_option = false; 
algorithm
  Max_Target_Cap40 := if not is_ULN_option then 32000 else 26188;
  Max_Target_Cap50 := if not is_ULN_option then 41000 else 33598;
  Max_Target_Cap60 := if not is_ULN_option then 52500 else 40689;
  Max_Target_Cap70 := if not is_ULN_option then 60000 else 46000;

  Target_Capacity := if Unit == 40 then min(Max_Target_Cap40,Target_cap) 
  else if Unit == 50 then min(Max_Target_Cap50,Target_cap)
  else if Unit == 60 then min(Max_Target_Cap60,Target_cap)
  else min(Max_Target_Cap70,Target_cap);
end Capacity_function_cooling;
