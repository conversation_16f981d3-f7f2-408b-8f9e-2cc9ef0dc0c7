within Workspace.Controller.Components.Functions;
function fanSpeed
  "Fan speed setpoint calculation"
  extends Modelica.Icons.Function;
  import SI=Modelica.SIunits;
  input SI.Frequency compressorFrequency=85
    "Compressor speed [Hz]";
  input SI.Temperature T_lwt=273.15-2
    "Leaving water temperature";
  input SI.Temperature T_oat=273+35
    "Outside air temperature";
  input Real[9] coefficients={2.26358,8.491337,-0.023577,-0.016601,-0.020694,0.046051,0.026276,-0.050755,127.148697}
    "Coefficients array";
  input Real minfreq=100;
  input Real maxfreq=720;
  output Real fanSpeed;
protected
  SI.Temp_C T_lwt_degC=T_lwt-273.15
    "Leaving water temperature in centigrade";
  SI.Temp_C T_oat_degC=T_oat-273.15
    "Outside air temperature in centigrade";
  Real[9] variables
    "Variables array";
algorithm
  variables := {T_lwt_degC,compressorFrequency,compressorFrequency^2,T_lwt_degC*compressorFrequency,T_lwt_degC^2,T_oat_degC*compressorFrequency,T_oat_degC*T_lwt_degC,T_oat_degC^2,1};
  fanSpeed := min(
    max(
      minfreq,
      coefficients*variables),
    maxfreq)
    "min output for fan is 20Hz when no override on fans";
end fanSpeed;
