within Workspace.Icons;
partial class ValvePackage
  extends Workspace.Icons.PackageIcon;
  annotation (
    Icon(
      graphics={
        Polygon(
          points={{-60,46},{-60,-50},{2,2},{-60,46}},
          lineColor={0,0,0},
          smooth=Smooth.None,
          fillColor={255,0,0},
          fillPattern=FillPattern.Solid),
        Polygon(
          points={{68,50},{68,-44},{4,2},{68,50}},
          lineColor={0,0,0},
          smooth=Smooth.None,
          fillPattern=FillPattern.Solid,
          fillColor={255,0,0})}));
end ValvePackage;
