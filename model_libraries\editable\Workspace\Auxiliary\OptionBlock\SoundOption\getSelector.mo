within Workspace.Auxiliary.OptionBlock.SoundOption;
function getSelector
    input .Workspace.Auxiliary.OptionBlock.SoundOption.Selector Selector_sound;
    output Boolean is_SoundOption;
    protected 
    
    Boolean[:] is_SoundOption_Base = {false,true};
    

    algorithm 

    is_SoundOption := is_SoundOption_Base[Integer(Selector_sound)];
    annotation(Icon(coordinateSystem(preserveAspectRatio = false,extent = {{-100.0,-100.0},{100.0,100.0}}),graphics = {Rectangle(lineColor={0,0,0},fillColor={230,230,230},fillPattern=FillPattern.Solid,extent={{-100.0,-100.0},{100.0,100.0}}),Text(lineColor={0,0,255},extent={{-150,150},{150,110}},textString="%name")}));
end getSelector;
