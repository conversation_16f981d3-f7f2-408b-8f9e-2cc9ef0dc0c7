within Workspace.Icons;
partial model CycleIcon
  //extends VFD30XW.Icons.InitializationCycle2;
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=true,
        extent={{-125,-125},{125,125}}),
      graphics={
        Text(
          extent={{-132,-134},{136,-152}},
          lineColor={0,0,0},
          fillColor={255,0,0},
          fillPattern=FillPattern.Solid,
          textString="%name"),
        Rectangle(
          extent={{-40,66},{40,40}},
          lineColor={255,0,0},
          lineThickness=0.5,
          fillColor={0,127,0},
          fillPattern=FillPattern.HorizontalCylinder),
        Rectangle(
          extent={{-40,-54},{40,-80}},
          lineColor={255,0,0},
          lineThickness=0.5,
          fillColor={0,127,0},
          fillPattern=FillPattern.HorizontalCylinder),
        Line(
          points={{62,16},{48,-20},{92,-20},{76,16},{62,16}},
          color={255,0,0},
          thickness=0.5,
          smooth=Smooth.None),
        Line(
          points={{-80,20},{-60,20},{-80,-20},{-60,-20},{-80,20}},
          color={255,0,0},
          thickness=0.5,
          smooth=Smooth.None),
        Line(
          points={{-70,20},{-70,52},{-40,52}},
          color={255,0,0},
          thickness=0.5,
          smooth=Smooth.None),
        Line(
          points={{-40,-66},{-70,-66},{-70,-20}},
          color={255,0,0},
          thickness=0.5,
          smooth=Smooth.None),
        Line(
          points={{72,-20},{72,-66},{40,-66}},
          color={255,0,0},
          thickness=0.5,
          smooth=Smooth.None),
        Line(
          points={{70,16},{70,52},{40,52}},
          color={255,0,0},
          thickness=0.5,
          smooth=Smooth.None)}),
    Diagram(
      graphics,
      coordinateSystem(
        preserveAspectRatio=true,
        extent={{-125,-125},{125,125}})));
end CycleIcon;
