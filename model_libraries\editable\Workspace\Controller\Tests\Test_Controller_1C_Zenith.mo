within Workspace.Controller.Tests;
model Test_Controller_1C_Zenith
  .Workspace.Controller.Controller_1C_cooling controller(
    is_HR_control=false,
    crkA_isOff=false,
    crkB_isOff=false,
    ecoA_isOff=false,
    ecoB_isOff=false)
    annotation (Placement(transformation(extent={{0.0,4.0},{20.0,24.0}},origin={0.0,0.0},rotation=0.0)));
  parameter Modelica.SIunits.Power capacity_setpoint=150000;
  parameter Modelica.SIunits.Temperature T_sst_min_limit_comp=0;
  parameter Modelica.SIunits.Temperature T_sdt_max_limit_comp=67;
  parameter Modelica.SIunits.Temperature T_dgt_max_limit_comp=94;
  parameter Modelica.SIunits.Temperature T_sst_min_limit_exv=0.5;
  parameter Modelica.SIunits.Temperature T_sst_max_limit=17;
  parameter Modelica.SIunits.Temperature dT_dsh_min_limit=7;
  parameter Modelica.SIunits.Temperature dT_sbc_setpoint=-5;
  parameter Modelica.SIunits.Temperature T_dgt_max_limit_exv=93;
  parameter Modelica.SIunits.Temperature dT_esh_setpoint=10;
  parameter Real fanSpeed_setpoint=0.8;
  parameter Real rel_cooler_level_setPoint=0.7;
  parameter Real ecoEXV_max_opening=1;
  parameter Modelica.SIunits.Temperature T_sdt_min_limit=20;
  parameter Modelica.SIunits.Temperature T_sdt_max_limit_fan=65;
  parameter Modelica.SIunits.Temperature T_dgt_max_limit_fan=92;
  .Modelica.Blocks.Sources.RealExpression T_sst_min_limit_ex(
    y=T_sst_min_limit_exv)
    annotation (Placement(transformation(extent={{-149.0,105.0},{-119.0,119.0}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Sources.RealExpression T_sdt_max_limit_fa(
    y=T_sdt_max_limit_fan)
    annotation (Placement(transformation(extent={{-151.0,25.0},{-121.0,39.0}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Sources.RealExpression T_sdt_max_limit_com(
    y=T_sdt_max_limit_comp)
    annotation (Placement(transformation(extent={{-151.0,41.0},{-121.0,55.0}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Sources.RealExpression T_dgt_max_limit_fa(
    y=T_dgt_max_limit_fan)
    annotation (Placement(transformation(extent={{-151.0,58.0},{-121.0,70.0}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Sources.RealExpression T_sdt_min_limi(
    y=T_sdt_min_limit)
    annotation (Placement(transformation(extent={{-151.0,8.450328813674913},{-121.0,23.549671186325085}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Sources.RealExpression T_sst_max_limi(
    y=T_sst_max_limit)
    annotation (Placement(transformation(extent={{-149.0,132.0},{-119.0,144.0}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Sources.RealExpression T_sst_min_limit_com(
    y=T_sst_min_limit_comp)
    annotation (Placement(transformation(extent={{-149.0,117.0},{-119.0,131.0}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Sources.RealExpression T_dgt_max_limit_com(
    y=T_dgt_max_limit_comp)
    annotation (Placement(transformation(extent={{-149.0,87.0},{-119.0,101.0}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Sources.RealExpression T_dgt_max_limit_ex(
    y=T_dgt_max_limit_exv)
    annotation (Placement(transformation(extent={{-149.0,71.0},{-119.0,85.0}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Sources.RealExpression dT_sbc_setpoin(
    y=dT_sbc_setpoint)
    annotation (Placement(transformation(extent={{-153.0,-6.0},{-123.0,6.0}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Sources.RealExpression dT_esh_setpoin(
    y=dT_esh_setpoint)
    annotation (Placement(transformation(extent={{-153.0,-18.0},{-123.0,-6.0}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Sources.RealExpression capacity_setpoin(
    y=capacity_setpoint)
    annotation (Placement(transformation(extent={{-153.0,-48.42226578845363},{-123.0,-35.57773421154637}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Sources.RealExpression dT_dsh_min_limi(
    y=dT_dsh_min_limit)
    annotation (Placement(transformation(extent={{-153.0,-35.0},{-123.0,-21.0}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Sources.RealExpression fanSpeed_setpoin(
    y=fanSpeed_setpoint)
    annotation (Placement(transformation(extent={{-153.0,-69.0},{-123.0,-55.0}},rotation=0.0,origin={0.0,0.0})));
  .Workspace.Interfaces.LimitsBus limitsBusA
    annotation (Placement(transformation(extent={{-65.17076629245895,30.82923370754105},{-42.82923370754105,53.17076629245895}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.MeasurementBus measurementBusA
    annotation (Placement(transformation(extent={{42.0,50.0},{82.0,90.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression rel_cooler_level_setPoin(
    y=rel_cooler_level_setPoint)
    annotation (Placement(transformation(extent={{-153.03965515075723,-112.0},{-122.96034484924277,-92.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression ecoEXV_max_openin(
    y=ecoEXV_max_opening)
    annotation (Placement(transformation(extent={{-153.03965515075723,-94.0},{-122.96034484924277,-74.0}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Controller.Tests.y_equalto_kx_plus_b y_equalto_kx_plus_b
    annotation (Placement(transformation(extent={{42.75508482356885,2.755084823568847},{69.24491517643115,29.244915176431153}},origin={0.0,0.0},rotation=0.0)));
equation
  connect(limitsBusA,controller.limitsBus_crkA)
    annotation (Line(points={{-54,42},{0,42},{0,17}},color={255,204,51}));
  connect(controller.measurementBus_crkA,measurementBusA)
    annotation (Line(points={{0,21},{0,70},{62,70}},color={255,204,51}));
  connect(T_sst_max_limi.y,limitsBusA.T_sst_max_limit)
    annotation (Line(points={{-117.5,138},{-85.75,138},{-85.75,42},{-54,42}},color={0,0,127}));
  connect(T_sst_min_limit_com.y,limitsBusA.T_sst_min_limit_comp)
    annotation (Line(points={{-117.5,124},{-85.75,124},{-85.75,42},{-54,42}},color={0,0,127}));
  connect(T_sst_min_limit_ex.y,limitsBusA.T_sst_min_limit_exv)
    annotation (Line(points={{-117.5,112},{-85.75,112},{-85.75,42},{-54,42}},color={0,0,127}));
  connect(T_dgt_max_limit_com.y,limitsBusA.T_dgt_max_limit_comp)
    annotation (Line(points={{-117.5,94},{-85.75,94},{-85.75,42},{-54,42}},color={0,0,127}));
  connect(T_dgt_max_limit_ex.y,limitsBusA.T_dgt_max_limit_exv)
    annotation (Line(points={{-117.5,78},{-85.75,78},{-85.75,42},{-54,42}},color={0,0,127}));
  connect(T_dgt_max_limit_fa.y,limitsBusA.T_dgt_max_limit_fan)
    annotation (Line(points={{-119.5,64},{-86.75,64},{-86.75,42},{-54,42}},color={0,0,127}));
  connect(T_sdt_max_limit_com.y,limitsBusA.T_sdt_max_limit_comp)
    annotation (Line(points={{-119.5,48},{-86.75,48},{-86.75,42},{-54,42}},color={0,0,127}));
  connect(T_sdt_max_limit_fa.y,limitsBusA.T_sdt_max_limit_fan)
    annotation (Line(points={{-119.5,32},{-86.75,32},{-86.75,42},{-54,42}},color={0,0,127}));
  connect(T_sdt_min_limi.y,limitsBusA.T_sdt_min_limit)
    annotation (Line(points={{-119.5,16},{-86.75,16},{-86.75,42},{-54,42}},color={0,0,127}));
  connect(dT_sbc_setpoin.y,limitsBusA.dT_sbc_setpoint)
    annotation (Line(points={{-121.5,0},{-87.75,0},{-87.75,42},{-54,42}},color={0,0,127}));
  connect(dT_esh_setpoin.y,limitsBusA.dT_esh_setpoint)
    annotation (Line(points={{-121.5,-12},{-87.75,-12},{-87.75,42},{-54,42}},color={0,0,127}));
  connect(dT_dsh_min_limi.y,limitsBusA.dT_dsh_min_limt)
    annotation (Line(points={{-121.5,-28},{-87.75,-28},{-87.75,42},{-54,42}},color={0,0,127}));
  connect(capacity_setpoin.y,limitsBusA.capacity_setpoint)
    annotation (Line(points={{-121.5,-42},{-87.75,-42},{-87.75,42},{-54,42}},color={0,0,127}));
  connect(fanSpeed_setpoin.y,limitsBusA.fanSpeed_setpoint)
    annotation (Line(points={{-121.5,-62},{-87.75,-62},{-87.75,42},{-54,42}},color={0,0,127}));
  connect(ecoEXV_max_openin.y,limitsBusA.ecoEXV_max_opening)
    annotation (Line(points={{-121.45637933416705,-84},{-87.72818966708353,-84},{-87.72818966708353,42},{-54,42}},color={0,0,127}));
  connect(rel_cooler_level_setPoin.y,limitsBusA.rel_cooler_level_setpoint)
    annotation (Line(points={{-121.45637933416705,-102},{-87.72818966708353,-102},{-87.72818966708353,42},{-54,42}},color={0,0,127}));
  connect(controller.exv_crkA,y_equalto_kx_plus_b.x[2])
    annotation (Line(points={{20.8,21.200000000000003},{33.15499578177437,21.200000000000003},{33.15499578177437,15.86755084823569},{49.50999156354874,15.86755084823569}},color={0,0,127}));
  connect(controller.ecoExv_crkA,y_equalto_kx_plus_b.x[3])
    annotation (Line(points={{20.8,17.4},{33.15499578177437,17.4},{33.15499578177437,15.86755084823569},{49.50999156354874,15.86755084823569}},color={0,0,127}));
  connect(controller.compressor_crkA,y_equalto_kx_plus_b.x[1])
    annotation (Line(points={{20.8,14.976},{33.15499578177437,14.976},{33.15499578177437,15.86755084823569},{49.50999156354874,15.86755084823569}},color={0,0,127}));
  connect(controller.fan_crkA,y_equalto_kx_plus_b.x[4])
    annotation (Line(points={{20.8,22.8},{33.15499578177437,22.8},{33.15499578177437,15.86755084823569},{49.50999156354874,15.86755084823569}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[1],measurementBusA.T_oat)
    annotation (Line(points={{62.75490673997989,15.86755084823569},{68.75490673997989,15.86755084823569},{68.75490673997989,70},{62,70}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[2],measurementBusA.T_ewt)
    annotation (Line(points={{62.75490673997989,15.86755084823569},{68.75490673997989,15.86755084823569},{68.75490673997989,70},{62,70}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[3],measurementBusA.T_lwt)
    annotation (Line(points={{62.75490673997989,15.86755084823569},{68.75490673997989,15.86755084823569},{68.75490673997989,70},{62,70}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[4],measurementBusA.T_sst)
    annotation (Line(points={{62.75490673997989,15.86755084823569},{68.75490673997989,15.86755084823569},{68.75490673997989,70},{62,70}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[5],measurementBusA.T_sdt)
    annotation (Line(points={{62.75490673997989,15.86755084823569},{68.75490673997989,15.86755084823569},{68.75490673997989,70},{62,70}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[6],measurementBusA.T_dgt)
    annotation (Line(points={{62.75490673997989,15.86755084823569},{68.75490673997989,15.86755084823569},{68.75490673997989,70},{62,70}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[7],measurementBusA.dT_ssh)
    annotation (Line(points={{62.75490673997989,15.86755084823569},{68.75490673997989,15.86755084823569},{68.75490673997989,70},{62,70}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[8],measurementBusA.dT_esh)
    annotation (Line(points={{62.75490673997989,15.86755084823569},{68.75490673997989,15.86755084823569},{68.75490673997989,70},{62,70}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[9],measurementBusA.dT_dsh)
    annotation (Line(points={{62.75490673997989,15.86755084823569},{68.75490673997989,15.86755084823569},{68.75490673997989,70},{62,70}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[10],measurementBusA.dT_sbc)
    annotation (Line(points={{62.75490673997989,15.86755084823569},{68.75490673997989,15.86755084823569},{68.75490673997989,70},{62,70}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[11],measurementBusA.capacity)
    annotation (Line(points={{62.75490673997989,15.86755084823569},{68.75490673997989,15.86755084823569},{68.75490673997989,70},{62,70}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[12],measurementBusA.compressorFrequency)
    annotation (Line(points={{62.75490673997989,15.86755084823569},{68.75490673997989,15.86755084823569},{68.75490673997989,70},{62,70}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[13],measurementBusA.rel_cooler_level)
    annotation (Line(points={{62.75490673997989,15.86755084823569},{68.75490673997989,15.86755084823569},{68.75490673997989,70},{62,70}},color={0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          fillColor={130,108,108},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end Test_Controller_1C_Zenith;
