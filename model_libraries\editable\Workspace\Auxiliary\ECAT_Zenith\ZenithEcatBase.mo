within Workspace.Auxiliary.ECAT_Zenith;
model Zenith<PERSON>catBase
  extends BOLT.ECATBlock.ChillerBase(
    nbrCircuit=2,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable CondCoilAirPressDrop_Pa,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable RefrigerantSST_K,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable RefrigerantSDT_K,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable RefrigerantSET_K,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable RefrigerantSCT_K,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable RefrigerantDGT_K,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable SuctionSuperheat_K,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable CondSubcooling_K,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable DischargeSuperheat_K,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATFixedVariable CompressorSpeed_rpm,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATFixedVariable CompressorFrequency_Hz,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable CondFanAirflowRate_m3s,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable CompressorPower_W,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable FanPower_W,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATFixedVariable Altitude_m,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATFixedVariable AmbientAirDBTemp_K,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable HeatingAmbientAirWBTemp_K,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATFixedVariable HeatingAmbientAirDBTemp_K,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATFixedVariable AmbientAirRH_nd,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATFixedVariable CondBrineConcentration_nd,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATFixedVariable CondFoulingFactor_m2KW,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATFixedVariable EvapBrineConcentration_nd,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATFixedVariable EvapFoulingFactor_m2KW,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATFixedVariable TargetCoolingCapacity_W,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATFixedVariable TargetHeatingCapacity_W,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable TotalRefrigerantCharge_kg,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable TotalOilCharge_kg,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable PubUnitPower_W,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable PubCoolingCapacity_W,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable CondCoilHeatingCapacity_W,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable PubHeatingCapacity_W,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable PubHeatingCapacityInstantaneous_W,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable TotalCompressorPower_W,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable TotalFanPower_W,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable EvapPumpPower_W,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable CondPumpPower_W,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable EvapBrineIntPressDrop_Pa,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable EvapPumpTotalHead_m,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable EvapBrineDensity_kgm3,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable CondBrineIntPressDrop_Pa,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable CondPumpTotalHead_m,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable CondBrineDensity_kgm3,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable EvapBrineVelocity_mps,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable CondBrineVelocity_mps,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable EN14511PumpPowerCorrectionWithPump_W,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable EN14511PumpPowerCorrectionWithoutPump_W,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable CondEN14511PumpPowerCorrectionWithPump_W,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable CondEN14511PumpPowerCorrectionWithoutPump_W,
    redeclare BOLT.InternalLibrary.ECATBlock.ECATUnfixedVariable CoolantFreezingTemp_K);
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end ZenithEcatBase;
