within Workspace.Controller.Tests;
model test_map_Cp_SST_max
  .Modelica.Blocks.Sources.RealExpression freq(
    y=frequence)
    annotation (Placement(transformation(extent={{-84,-60},{-64,-40}},origin={0,0},rotation=0)));
  .Modelica.Blocks.Tables.CombiTable1D combiTable1D(
    table={{30,15},{50,25},{100,25},{120,20},{140,15}})
    annotation (Placement(transformation(extent={{-32.0,-54.0},{-12.0,-34.0}},origin={0.0,0.0},rotation=0.0)));
  parameter Real frequence=140;
equation
  connect(freq.y,combiTable1D.u[1])
    annotation (Line(points={{-63,-50},{-48.5,-50},{-48.5,-44},{-34,-44}},color={0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end test_map_Cp_SST_max;
