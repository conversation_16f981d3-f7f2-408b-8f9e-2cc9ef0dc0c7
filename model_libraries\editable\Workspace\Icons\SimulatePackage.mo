within Workspace.Icons;
partial class SimulatePackage
  extends Workspace.Icons.PackageIcon;
  annotation (
    Icon(
      graphics={
        Line(
          points={{-76,18},{-66,18},{-58,16},{-46,10},{-40,0},{-28,-30},{-8,-80},{11.9628,-10.1048},{20,0},{28,6},{34,6}},
          color={0,0,0}),
        Ellipse(
          extent={{24,24},{62,-14}},
          pattern=LinePattern.None,
          fillColor={255,0,0},
          fillPattern=FillPattern.Sphere,
          lineColor={0,0,0}),
        Ellipse(
          extent={{40,4},{46,-2}},
          pattern=LinePattern.None,
          fillPattern=FillPattern.Solid,
          fillColor={255,255,255},
          lineColor={0,0,0}),
        Line(
          points={{-58,-80},{50,-80}},
          color={0,0,0})}));
end SimulatePackage;
