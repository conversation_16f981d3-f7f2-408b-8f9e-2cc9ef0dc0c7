within Workspace.Auxiliary.OptionBlock;
model ChoiceBlock
  parameter.Workspace.Auxiliary.OptionBlock.Record_Base.Selector Selector_Block_A=.Workspace.Auxiliary.OptionBlock.Record_Base.Selector.Unit_Z_040
    annotation (Dialog(group="Selector Unit"));
  parameter Boolean is_monobloc=true
    annotation (Dialog(group="Selector Unit"));
  parameter.Workspace.Auxiliary.OptionBlock.Record_Base.Selector Selector_Block_B=.Workspace.Auxiliary.OptionBlock.Record_Base.Selector.Unit_Z_040
    annotation (Dialog(group="Selector Unit"));
  final parameter.Workspace.Auxiliary.OptionBlock.Record_Base.UnitBase_1C Unit_Block_A=.Workspace.Auxiliary.OptionBlock.Record_Base.getSelector(
    Selector_Block_A);
  final parameter.Workspace.Auxiliary.OptionBlock.Record_Base.UnitBase_1C Unit_Block_B=.Workspace.Auxiliary.OptionBlock.Record_Base.getSelector(
    Selector_Block_B);
  parameter.Workspace.Auxiliary.OptionBlock.PumpOption.Pump_Option Pump_selector=.Workspace.Auxiliary.OptionBlock.PumpOption.Pump_Option.STANDARD
    annotation (Dialog(group="Options"),choicesAllMatching=true,Evaluate=false);
  final parameter Boolean is_Pump=.Workspace.Auxiliary.OptionBlock.PumpOption.getSelector(
    Pump_selector)
    annotation (Dialog(tab="Options",group="Options"));
  parameter Workspace.Auxiliary.OptionBlock.SoundOption.Selector 
  SoundOption_selector=Workspace.Auxiliary.OptionBlock.SoundOption.Selector.STANDARD
    annotation (Dialog(group="Options"),choicesAllMatching=true,Evaluate=false);
  final parameter Boolean is_SoundOption=Workspace.Auxiliary.OptionBlock.SoundOption.getSelector(
   SoundOption_selector)
    annotation (Dialog(tab="Options",group="Options"));
  parameter.Workspace.Auxiliary.OptionBlock.CoatingOption.Coating_selector Coating_selector=.Workspace.Auxiliary.OptionBlock.CoatingOption.Coating_selector.STANDARD
    annotation (Dialog(group="Options"));
  final parameter Boolean isCoatingOption=.Workspace.Auxiliary.OptionBlock.CoatingOption.getSelector_bool(
    Coating_selector);
  annotation (
    Icon(
      graphics={
        Rectangle(
          extent={{-100,-100},{100,100}},
          fillColor={115,55,0},
          fillPattern=FillPattern.Solid),
        Text(
          textString="ChoiceBlock",
          origin={-1,3},
          extent={{-99,49},{99,-49}},
          lineColor={255,255,255})}));
end ChoiceBlock;
