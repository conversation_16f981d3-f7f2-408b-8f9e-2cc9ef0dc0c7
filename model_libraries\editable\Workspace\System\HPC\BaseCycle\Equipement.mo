within Workspace.System.HPC.BaseCycle;
model Equipement
  import CoolantCommonMedium=BOLT.InternalLibrary.Media.Coolant.CoolantCommon;
  .Workspace.System.HPC.BaseCycle.OL BlockA(
    EWT=EWT,
    LWT=LWT,
    OAT=OAT,
    Water_pressure=Water_pressure,
    isOff_ref=isOFFA,
    CoolantMedium=CoolantMedium,
    BrineConcentration=BrineConcentration,
    Use_EN=Use_EN,
    use_Z_in=false,
    selector_Comp=selector_Comp[1],
    capacity_design=capacity_design[1],
    CompVoltage=CompVoltage[1],
    fmax=fmax[1],
    fmin=fmin[1],
    EXV_main=EXV_main[1],
    Dport_coolant_a=Dport_coolant_a[1],
    Dport_coolant_b=Dport_coolant_b[1],
    Dport_ref_a=Dport_ref_a[1],
    Dport_ref_b=Dport_ref_b[1],
    nPlate=nPlate[1],
    selector_geo_BPHE=selector_geo_BPHE[1],
    nCoils=nCoils[1],
    Itube=Itube[1],
    nCir=nCir[1],
    Ntube=Ntube[1],
    Nrow=Nrow[1],
    Ltube=Ltube[1],
    Dotube=Dotube[1],
    Ttube=Ttube[1],
    Ptube=Ptube[1],
    Prow=Prow[1],
    Dfin=Dfin[1],
    Tfin=Tfin[1],
    Fw_fan=Fw_fan[1],
    max_fan_frequency=max_fan_frequency[1],
    fanCurveCoefficientsCooling=fanCurveCoefficientsCooling[1,:],
    fanCurveCoefficientsHeating=fanCurveCoefficientsHeating[1,:],
    Suction_line_diameter=Suction_line_diameter[1],
    Suction_line_length=Suction_line_length[1],
    coil_line_length=coil_line_length[1],
    liquid_line_diameter=liquid_line_diameter[1],
    liquid_line_length=liquid_line_length[1],
    discharge_line_diameter=discharge_line_diameter[1],
    discharge_line_length=discharge_line_length[1],
    EXV_in_line_diameter=EXV_in_line_diameter[1],
    EXV_in_line_length=EXV_in_line_length[1],
    Ac_duct=Ac_duct[1],
    Ka_duct=Ka_duct[1],
    UA_duct=UA_duct[1],
    coil_line_diameter=coil_line_diameter[1],
    selector_pump=selector_pump[1],
    EvapFoulingFactor=EvapFoulingFactor,
    isCoating=isCoating,
    relative_humidity=relative_humidity,
    PDC_4WV=PDC_4WV[1],
    FW=FW[1],
    use_Calib=use_Calib,
    Zflow_heatcap=Zflow_heatcap[1],
    Zpower_SH=Zpower_SH[1],
    Zpower_DGT=Zpower_DGT[1],
    Zcond_HPH_cst=Zcond_HPH_cst[1],
    Zcond_HPH_heatcap=Zcond_HPH_heatcap[1],
    Zcond_HPH_SST=Zcond_HPH_SST[1],
    Zcond_HPC_DGT=Zcond_HPC_DGT[1],
    Zcond_HPC_Ncomp=Zcond_HPC_Ncomp[1],
    Zcond_HPC_SDT=Zcond_HPC_SDT[1],
    Zcond_HPC_cst=Zcond_HPC_cst[1],
    Zevap_HPH_cst=Zevap_HPH_cst[1],
    Zevap_HPH_SST=Zevap_HPH_SST[1],
    Zevap_HPH_Ncomp=Zevap_HPH_Ncomp[1],
    Zevap_HPH_heatcap=Zevap_HPH_heatcap[1],
    Zevap_HPC_cst=Zevap_HPC_cst[1],
    Zevap_HPC_heatcap=Zevap_HPC_heatcap[1],
    Zevap_HPC_SST=Zevap_HPC_SST[1],
    Zevap_coated_HPH=Zevap_coated_HPH[1],
    Zflow_intercept=Zflow_intercept[1],
    Zflow_Ncomp=Zflow_Ncomp[1],
    Zflow_SST=Zflow_SST[1],
    Zflow_SDT=Zflow_SDT[1],
    Zpower_intercept=Zpower_intercept[1],
    Zpower_SST=Zpower_SST[1],
    Zpower_Zflow=Zpower_Zflow[1],
    Zcond_HPH_SDT=Zcond_HPH_SDT[1],
    ssh_setPoint=ssh_setPoint[1],
    SC_setpoint=SC_setpoint[1],
    SC_fixed=SC_fixed[1],
    Mref_fixed=Mref_fixed[1],
    Mref=Mref[1])
    annotation (Placement(transformation(extent={{-54.0,42.0},{-34.0,62.0}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.System.HPC.BaseCycle.OL BlockB(
    isOff=is_monobloc,
    EWT=EWT,
    LWT=LWT,
    OAT=OAT,
    Water_pressure=Water_pressure,
    isOff_ref=isOFFB or is_monobloc,
    CoolantMedium=CoolantMedium,
    BrineConcentration=BrineConcentration,
    Use_EN=Use_EN,
    use_Z_in=false,
    selector_Comp=selector_Comp[2],
    capacity_design=capacity_design[2],
    CompVoltage=CompVoltage[2],
    fmax=fmax[2],
    fmin=fmin[2],
    EXV_main=EXV_main[2],
    Dport_coolant_a=Dport_coolant_a[2],
    Dport_coolant_b=Dport_coolant_b[2],
    Dport_ref_a=Dport_ref_a[2],
    Dport_ref_b=Dport_ref_b[2],
    nPlate=nPlate[2],
    selector_geo_BPHE=selector_geo_BPHE[2],
    nCoils=nCoils[2],
    Itube=Itube[2],
    nCir=nCir[2],
    Ntube=Ntube[2],
    Nrow=Nrow[2],
    Ltube=Ltube[2],
    Dotube=Dotube[2],
    Ttube=Ttube[2],
    Ptube=Ptube[2],
    Prow=Prow[2],
    Dfin=Dfin[2],
    Tfin=Tfin[2],
    Fw_fan=Fw_fan[2],
    max_fan_frequency=max_fan_frequency[2],
    fanCurveCoefficientsCooling=fanCurveCoefficientsCooling[2,:],
    fanCurveCoefficientsHeating=fanCurveCoefficientsHeating[2,:],
    Suction_line_diameter=Suction_line_diameter[2],
    Suction_line_length=Suction_line_length[2],
    coil_line_diameter=coil_line_diameter[2],
    coil_line_length=coil_line_length[2],
    liquid_line_diameter=liquid_line_diameter[2],
    liquid_line_length=liquid_line_length[2],
    discharge_line_diameter=discharge_line_diameter[2],
    discharge_line_length=discharge_line_length[2],
    EXV_in_line_diameter=EXV_in_line_diameter[2],
    EXV_in_line_length=EXV_in_line_length[2],
    Ac_duct=Ac_duct[2],
    Ka_duct=Ka_duct[2],
    UA_duct=UA_duct[2],
    selector_pump=selector_pump[2],
    EvapFoulingFactor=EvapFoulingFactor,
    isCoating=isCoating,
    relative_humidity=relative_humidity,
    PDC_4WV=PDC_4WV[2],
    FW=FW[2],
    use_Calib=use_Calib,
    Zflow_heatcap=Zflow_heatcap[2],
    Zpower_SH=Zpower_SH[2],
    Zpower_DGT=Zpower_DGT[2],
    Zcond_HPH_cst=Zcond_HPH_cst[2],
    Zcond_HPH_heatcap=Zcond_HPH_heatcap[2],
    Zcond_HPH_SST=Zcond_HPH_SST[2],
    Zcond_HPC_DGT=Zcond_HPC_DGT[2],
    Zcond_HPC_Ncomp=Zcond_HPC_Ncomp[1],
    Zcond_HPC_SDT=Zcond_HPC_SDT[2],
    Zcond_HPC_cst=Zcond_HPC_cst[2],
    Zevap_HPH_cst=Zevap_HPH_cst[2],
    Zevap_HPH_SST=Zevap_HPH_SST[2],
    Zevap_HPH_Ncomp=Zevap_HPH_Ncomp[2],
    Zevap_HPH_heatcap=Zevap_HPH_heatcap[2],
    Zevap_HPC_cst=Zevap_HPC_cst[2],
    Zevap_HPC_heatcap=Zevap_HPC_heatcap[2],
    Zevap_HPC_SST=Zevap_HPC_SST[2],
    Zevap_coated_HPH=Zevap_coated_HPH[2],
    Zflow_intercept=Zflow_intercept[2],
    Zflow_Ncomp=Zflow_Ncomp[2],
    Zflow_SST=Zflow_SST[2],
    Zflow_SDT=Zflow_SDT[2],
    Zpower_intercept=Zpower_intercept[2],
    Zpower_SST=Zpower_SST[2],
    Zpower_Zflow=Zpower_Zflow[2],
    Zcond_HPH_SDT=Zcond_HPH_SDT[2],
    ssh_setPoint=ssh_setPoint[2],
    SC_setpoint=SC_setpoint[2],
    SC_fixed=SC_fixed[2],
    Mref_fixed=Mref_fixed[2],
    Mref=Mref[2])
    annotation (Placement(transformation(extent={{51.98448439167882,41.257918475027665},{31.98448439167882,61.257918475027665}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.Mixer mixer(
    fa_fixed=false,
    isOff_b=is_monobloc,
    T_start=LWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration)
    annotation (Placement(transformation(extent={{-10.0,-10.0},{10.0,10.0}},origin={-2.6003915644383007,66.06009079921068},rotation=90.0)));
  parameter Boolean Use_pump;
  parameter Boolean is_monobloc;
  parameter Boolean use_Calib;
  parameter Real Water_pressure=200000;
  .BOLT.CoolantMisc.Split split_pumpA(
    mDot_a_start=
      if not Use_pump then
        mdot_start
      else
        0,
    mDot_b_start=
      if Use_pump then
        mdot_start
      else
        0,
    p_start=Water_pressure,
    T_start=EWT,
    isOff_b=not Use_pump,
    isOff_a=Use_pump,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    fa_set=
      if Use_pump then
        0
      else
        1)
    annotation (Placement(transformation(extent={{-5.275067375849304,-5.275067375849304},{5.275067375849304,5.275067375849304}},origin={-99.73066594633582,-56.58705980812161},rotation=90.0)));
  .BOLT.CoolantMisc.PumpPoly pumpPolyA(
    T_start=EWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff=not Use_pump,
    selector=selector_pump[1],
    k_pow=1)
    annotation (Placement(transformation(extent={{8.39339786044772,-8.39339786044772},{-8.39339786044772,8.39339786044772}},origin={-65.08160928199607,-28.901150851821043},rotation=-90.0)));
  .BOLT.CoolantMisc.ReducedPipe check_valve(
    T_start=EWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    Ka_set=0.2322,
    Kb=1.5823)
    annotation (Placement(transformation(extent={{-6.386404559420782,-6.386404559420782},{6.386404559420782,6.386404559420782}},origin={-2.4147855791865496,-104.77814292215895},rotation=90.0)));
  .BOLT.CoolantMisc.Split split_bloc(
    mDot_a_start=mdot_start,
    mDot_b_start=
      if not is_monobloc then
        mdot_start
      else
        0,
    isOff_b=is_monobloc,
    T_start=EWT,
    p_start=Water_pressure,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration)
    annotation (Placement(transformation(extent={{-5.275067375849304,-5.275067375849304},{5.275067375849304,5.275067375849304}},origin={-2.168738601331569,-75.78324388608779},rotation=90.0)));
  .BOLT.CoolantMisc.ReducedPipe external_system(
    T_start=LWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    Ka_fixed=false)
    annotation (Placement(transformation(extent={{-6.386404559420779,-6.386404559420782},{6.386404559420779,6.386404559420782}},origin={-2.6003915644383007,102.06009079921068},rotation=90.0)));
  .BOLT.CoolantMisc.Split split_pumpB(
    mDot_a_start=
      if Use_pump and not is_monobloc then
        mdot_start
      else
        0,
    mDot_b_start=
      if not Use_pump and not is_monobloc then
        mdot_start
      else
        0,
    isOff_b=is_monobloc or Use_pump,
    T_start=EWT,
    p_start=Water_pressure,
    isOff_a=is_monobloc or not Use_pump,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    fa_set=
      if Use_pump then
        1
      else
        0)
    annotation (Placement(transformation(extent={{-5.275067375849304,-5.275067375849304},{5.275067375849304,5.275067375849304}},origin={97.91362258255033,-57.40960626581417},rotation=90.0)));
  .BOLT.CoolantMisc.PumpPoly pumpPolyB(
    isOff=not Use_pump or is_monobloc,
    T_start=EWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    selector=selector_pump[2],
    k_pow=1)
    annotation (Placement(transformation(extent={{-8.39339786044772,-8.39339786044772},{8.39339786044772,8.39339786044772}},origin={57.59422308194374,-29.0322955537304},rotation=90.0)));
  .BOLT.InternalLibrary.BuildingBlocks.Coolant.Ports.FluidPort_a coolant_in(
    CoolantMedium=CoolantMedium,
    Xi_set=BrineConcentration)
    annotation (Placement(transformation(extent={{-13.480089871014542,-156.7059464254537},{6.519910128985458,-136.7059464254537}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.InternalLibrary.BuildingBlocks.Coolant.Ports.FluidPort_b coolant_out(
    CoolantMedium=CoolantMedium,
    Xi_set=BrineConcentration)
    annotation (Placement(transformation(extent={{-12.6003915644383,114.06009079921068},{7.399608435561699,134.06009079921068}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput EXV_controller_A
    annotation (Placement(transformation(extent={{-95.37224004817512,65.18237406821589},{-103.73623419313878,73.54636821317959}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={-110,25})));
  .Modelica.Blocks.Interfaces.RealInput Fan_controller_A
    annotation (Placement(transformation(extent={{-93.68762778007806,55.21653743265198},{-103.01998396510851,64.54889361768241}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={-110,-25})));
  .Modelica.Blocks.Interfaces.RealInput Compressor_controller_B
    annotation (Placement(transformation(extent={{82.74141595718798,71.24016805630758},{90.48280695230586,78.98155905142546}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={110,-75})));
  .Modelica.Blocks.Interfaces.RealInput EXV_controller_B
    annotation (Placement(transformation(extent={{83.27294414718028,62.48467763450602},{90.60422313218908,69.81595661951482}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={110,25})));
  .Modelica.Blocks.Interfaces.RealInput Fan_controller_B
    annotation (Placement(transformation(extent={{82.93873487109778,54.18966501312022},{91.11881075280178,62.36974089482419}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={110,-25})));
  .Modelica.Blocks.Interfaces.RealInput Compressor_controller_A
    annotation (Placement(transformation(extent={{-94.52303104173927,74.80176781892371},{-103.35490732000406,83.63364409718852}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={-110,-75})));
  .Workspace.Interfaces.MeasurementBus measurementBusA
    annotation (Placement(transformation(extent={{-40.15608500378272,148.13769465476108},{-15.20881689978215,173.08496275876163}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={-80,100})));
  .Workspace.Interfaces.MeasurementBus measurementBusB
    annotation (Placement(transformation(extent={{9.939416093258004,147.5405819008838},{36.08090970501311,173.6820755126389}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={80,100})));
  parameter.Modelica.SIunits.Temperature EWT=285.15;
  parameter.Modelica.SIunits.Temperature LWT=280.15;
  parameter.Modelica.SIunits.Temperature OAT=308.15;
  parameter Real relative_humidity=0.87;
  .BOLT.BoundaryNode.Coolant.Node node_blocA_out(
    m_flow_start=mdot_start,
    T_start=LWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration)
    annotation (Placement(transformation(extent={{-22.619641313530828,45.72354917436189},{-14.619641313530828,53.72354917436189}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Node node_blocB_out(
    m_flow_start=
      if not is_monobloc then
        mdot_start
      else
        0,
    T_start=LWT,
    isOff=is_monobloc,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration)
    annotation (Placement(transformation(extent={{17.716900311317985,45.72354917436189},{9.716900311317985,53.72354917436189}},origin={0.0,0.0},rotation=0.0)));
  parameter Boolean isOFFA=false;
  parameter Boolean isOFFB=false;
  .BOLT.BoundaryNode.Coolant.Node node_coolant_in_pumpB(
    m_flow_start=
      if not is_monobloc then
        mdot_start
      else
        0,
    T_start=EWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff=is_monobloc)
    annotation (Placement(transformation(extent={{39.44772772364737,-69.23333333333332},{47.44772772364737,-61.23333333333332}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Node node_coolant_in_pumpA(
    m_flow_start=mdot_start,
    T_start=EWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration)
    annotation (Placement(transformation(extent={{-39.40982391781609,-68.02469182603615},{-47.40982391781609,-60.02469182603615}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Node node_coolant_in_splitbloc(
    m_flow_start=mdot_start,
    T_start=EWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration)
    annotation (Placement(transformation(extent={{4.0,-4.0},{-4.0,4.0}},origin={-2.502071934664909,-89.1165772194211},rotation=-90.0)));
  .BOLT.BoundaryNode.Coolant.Node node_coolant_source(
    m_flow_start=mdot_start,
    T_start=EWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration)
    annotation (Placement(transformation(extent={{4.0,-4.0},{-4.0,4.0}},origin={-3.0687386013315745,-121.88324388608778},rotation=-90.0)));
  // Declaration of Coolant Medium Package
  //package CoolantCommonMedium =
  //.BOLT.InternalLibrary.Media.Coolant.CoolantCommon                             "Coolant Medium Package" annotation ();
  // Declaration of Coolant Medium at Evaporator Circuit
  parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector CoolantMedium=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector.FW
    "Coolant Medium Selection"
    annotation (Dialog(group="Medium"));
  final parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Empty coolantInf=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.getSelector(
    CoolantMedium)
    "Coolant Medium"
    annotation ();
  CoolantCommonMedium.Temperature FreezTemp=CoolantCommonMedium.freezeTemp(
    coolantInf,
    BrineConcentration,
    1);
  parameter Real BrineConcentration=0.2
    annotation (Dialog(group="Medium"));
  .BOLT.CoolantMisc.ReducedPipe gaz_separatorA(
    T_start=EWT,
    Ka_set=4,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration)
    annotation (Placement(transformation(extent={{-6.076246165826419,-6.076246165826419},{6.076246165826419,6.076246165826419}},origin={-101.26278268171433,33.30635426390258},rotation=90.0)));
  .BOLT.CoolantMisc.ReducedPipe gaz_separatorB(
    T_start=EWT,
    Ka_set=4,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff=is_monobloc)
    annotation (Placement(transformation(extent={{-6.076246165826419,-6.076246165826419},{6.076246165826419,6.076246165826419}},origin={97.20855048825722,31.723549174361892},rotation=90.0)));
  .BOLT.CoolantMisc.ReducedPipe PipingA(
    T_start=EWT,
    Ka_set=0.738,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    Kb=2.3384)
    annotation (Placement(transformation(extent={{-6.076246165826419,-6.076246165826419},{6.076246165826419,6.076246165826419}},origin={-101.26278268171433,15.306354263902577},rotation=90.0)));
  .BOLT.CoolantMisc.ReducedPipe PipingB(
    T_start=EWT,
    Ka_set=0.738,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff=is_monobloc,
    Kb=2.3384)
    annotation (Placement(transformation(extent={{-6.076246165826419,-6.076246165826419},{6.076246165826419,6.076246165826419}},origin={97.57846085003577,13.7235491743619},rotation=90.0)));
  .BOLT.BoundaryNode.Shaft.Source shafA(
    isOff=not Use_pump,
    use_speed_in=true)
    annotation (Placement(transformation(extent={{-48.2918498052081,-7.32838118682638},{-40.2918498052081,0.6716188131736196}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Shaft.Source shafB(
    isOff=not Use_pump or is_monobloc,
    use_speed_in=true)
    annotation (Placement(transformation(extent={{41.23789886228211,-6.614264544162268},{33.23789886228211,1.3857354558377324}},origin={0.0,0.0},rotation=0.0)));
  parameter.Modelica.SIunits.Pressure Pdispo=50000;
  .BOLT.BoundaryNode.Coolant.Node node_out(
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    T_start=LWT)
    annotation (Placement(transformation(extent={{-4.0,-4.0},{4.0,4.0}},origin={-2.6003915644383007,86.06009079921068},rotation=90.0)));
  .Modelica.Blocks.Sources.RealExpression controlledCapacityA(
    y=
      if is_monobloc then
        controlledCapacity
      else
        controlledCapacity/2)
    annotation (Placement(transformation(extent={{-51.36273640817607,184.61132870676136},{-31.36273640817607,204.61132870676136}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression controlledCapacityB(
    y=controlledCapacity/2)
    annotation (Placement(transformation(extent={{45.010162899135565,184.61132870676136},{25.010162899135565,204.61132870676136}},origin={0.0,0.0},rotation=0.0)));
  Real controlledCapacity;
  Real controlledPower;
  parameter.Modelica.SIunits.Power Elec_box_power=350;
  .Modelica.Blocks.Sources.RealExpression External_Pressure(
    y=external_system.dp_value.y)
    annotation (Placement(transformation(extent={{-10.0,-10.0},{10.0,10.0}},origin={-4.75201389559944,219.19566392473865},rotation=-90.0)));
  .Modelica.Blocks.Interfaces.RealInput ActuatorPumpUser_A
    annotation (Placement(transformation(extent={{-46.864319381851224,10.298758953189513},{-56.19667556688167,19.631115138219947}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={-110,70})));
  .Modelica.Blocks.Interfaces.RealInput ActuatorPumpUser_B
    annotation (Placement(transformation(extent={{4.666178092515224,-4.666178092515217},{-4.666178092515224,4.666178092515217}},origin={43.02729244108584,15.820655037491726},rotation=-180.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={110,70})));
  .Workspace.Auxiliary.EN14511.EN14511_HPC_HPH eN14511(
    heating_mode=false,
    ie=0.88,
    isOffB=isOFFB or is_monobloc,
    isOffA=isOFFA,
    integrated_pump=Use_pump)
    annotation (Placement(transformation(extent={{-213.40195854854227,115.20463714623145},{-193.40195854854227,135.20463714623145}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression DPe_air_A_B(
    y=0)
    annotation (Placement(transformation(extent={{-268.2591014056851,155.78195115465286},{-248.2591014056851,175.78195115465286}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression DPe(
    y=external_system.summary.dp)
    annotation (Placement(transformation(extent={{-267.5940885603243,141.78691261857034},{-247.59408856032428,161.78691261857034}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression DPi(
    y=external_system.summary.dp)
    annotation (Placement(transformation(extent={{-266.92907571496346,127.09355202715827},{-246.92907571496346,147.9802732099824}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression q(
    y=node_out.summary.Vd)
    annotation (Placement(transformation(extent={{-266.29360380627423,111.83616679299033},{-246.29360380627423,132.72288797581453}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression q_air_A_B(
    y=0)
    annotation (Placement(transformation(extent={{-266.2591014056851,97.09355202715824},{-246.2591014056851,117.98027320998244}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression gross_cap(
    y=BlockA.systemVariables.summary.CoolCap_total+BlockB.systemVariables.summary.CoolCap_total)
    annotation (Placement(transformation(extent={{-266.2591014056851,83.78691261857034},{-246.2591014056851,103.78691261857034}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression gross_pow(
    y=BlockA.systemVariables.summary.pow_total+BlockB.systemVariables.summary.pow_total+systemVariablesPump.summary.pow_total+Elec_box_power)
    annotation (Placement(transformation(extent={{-266.51527475472784,70.50119833285603},{-246.51527475472784,90.50119833285603}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Auxiliary.Business_Factor.BusinessFactors_cooling businessFactors(
    use_business_factor=use_bf)
    annotation (Placement(transformation(extent={{-154.63140290261873,118.97383736620517},{-127.67582905801078,145.92941121081313}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression load(
    y=100)
    annotation (Placement(transformation(extent={{-267.***********,53.62668898431265},{-247.***********,73.62668898431265}},origin={0.0,0.0},rotation=0.0)));
  parameter BOLT.InternalLibrary.Compressors.PD_2Port.SubComponents.PD.DataBase.Polynomial.ARI_VS.Selector selector_Comp[2]
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Real capacity_design[2]
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Real CompVoltage[2]
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Real fmax[2]
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Real fmin[2]
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Real M_ref[2]
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter BOLT.InternalLibrary.Refrigerant.Valves.TwoPort.DataBase.Selector EXV_main[2]
    annotation (Dialog(group="EXV",tab="Unit Characteristics"));
  parameter Real Dport_coolant_a[2]
    annotation (Dialog(group="BPHE",tab="Unit Characteristics"));
  parameter Real Dport_coolant_b[2]
    annotation (Dialog(group="BPHE",tab="Unit Characteristics"));
  parameter Real Dport_ref_a[2]
    annotation (Dialog(group="BPHE",tab="Unit Characteristics"));
  parameter Real Dport_ref_b[2]
    annotation (Dialog(group="BPHE",tab="Unit Characteristics"));
  parameter Integer nPlate[2]
    annotation (Dialog(group="BPHE",tab="Unit Characteristics"));
  parameter Real EvapFoulingFactor
    annotation (Dialog(group="BPHE",tab="Unit Characteristics"));
  parameter BlackBoxLibrary.GeoSelector.GeoSelector_1C_Cond selector_geo_BPHE[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Integer nCoils[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Integer Itube[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Integer nCir[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Integer Ntube[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Integer Nrow[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Ltube[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Dotube[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Ttube[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Ptube[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Prow[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Dfin[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Tfin[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Boolean isCoating
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter BOLT.InternalLibrary.Coolant.Pumps.DataBase.PumpPoly.Selector selector_pump[2]
    annotation (Dialog(group="Pump",tab="Unit Characteristics"));
  parameter Real Fw_fan[2]
    annotation (Dialog(group="Fan",tab="Unit Characteristics"));
  parameter Real max_fan_frequency[2]
    annotation (Dialog(group="Fan",tab="Unit Characteristics"));
  parameter Real fanCurveCoefficientsCooling[2,9]
    annotation (Dialog(group="Fan",tab="Unit Characteristics"));
  parameter Real fanCurveCoefficientsHeating[2,9]
    annotation (Dialog(group="Fan",tab="Unit Characteristics"));
  parameter Real Suction_line_diameter[2]
    annotation (Dialog(group="Suction Line",tab="Unit Characteristics"));
  parameter Real Suction_line_length[2]
    annotation (Dialog(group="Suction Line",tab="Unit Characteristics"));
  parameter Real coil_line_diameter[2]
    annotation (Dialog(group="Coil Line",tab="Unit Characteristics"));
  parameter Real coil_line_length[2]
    annotation (Dialog(group="Coil Line",tab="Unit Characteristics"));
  parameter Real liquid_line_diameter[2]
    annotation (Dialog(group="Liquid Line",tab="Unit Characteristics"));
  parameter Real liquid_line_length[2]
    annotation (Dialog(group="Liquid Line",tab="Unit Characteristics"));
  parameter Real discharge_line_diameter[2]
    annotation (Dialog(group="Discharge Line",tab="Unit Characteristics"));
  parameter Real discharge_line_length[2]
    annotation (Dialog(group="Discharge Line",tab="Unit Characteristics"));
  parameter Real EXV_in_line_diameter[2]
    annotation (Dialog(group="EXV in Line",tab="Unit Characteristics"));
  parameter Real EXV_in_line_length[2]
    annotation (Dialog(group="EXV in Line",tab="Unit Characteristics"));
  parameter Real Ac_duct[2]
    annotation (Dialog(group="Air Duct",tab="Unit Characteristics"));
  parameter Real Ka_duct[2]
    annotation (Dialog(group="Air Duct",tab="Unit Characteristics"));
  parameter Real UA_duct[2]
    annotation (Dialog(group="Air Duct",tab="Unit Characteristics"));
  parameter Real PDC_4WV[2]
    annotation (Dialog(group="4WV",tab="Unit Characteristics"));
  parameter Real Zevap_HPH_cst[2]
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_HPH_SST[2]
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_HPH_Ncomp[2]
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_HPH_heatcap[2]
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_coated_HPH[2]
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_HPC_cst[2]
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_HPC_heatcap[2]
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_HPC_SST[2]
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zcond_HPH_cst[2]
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPH_SDT[2]
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPH_heatcap[2]
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPH_SST[2]
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPC_cst[2]
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPC_DGT[2]
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPC_SDT[2]
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPC_Ncomp[2]
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zflow_intercept[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zflow_Ncomp[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zflow_SST[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zflow_SDT[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zflow_heatcap[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zpower_intercept[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zpower_SH[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zpower_DGT[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zpower_SST[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zpower_Zflow[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real FW[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Boolean use_bf
    annotation (Dialog(tab="BusinessFactor&EN14511"));
  //"if false BF's are set to 1 and controlledCapacity = engineering capacity"    
  parameter Boolean Use_EN
    annotation (Dialog(tab="BusinessFactor&EN14511"));
  //"if false controlledCapacity = gross capacity" ;
  parameter Real c_load_bf_cap=0.1
    "capacity business factor coefficient"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real c_const_bf_cap=0.92
    "capacity business factor coefficient"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real c_load2_bf_cap=0.92
    "capacity business factor coefficient"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real c_load2_bf_pow=-0.114
    "power business factor coefficient"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real c_load_bf_pow=0.239
    "power business factor coefficient"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real c_const_bf_pow=0.876
    "power business factor coefficient"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real bf_cap_max=1
    "capacity business factor max value"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real bf_cap_min=1
    "capacity business factor min value"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real bf_pow_max=1
    "power business factor max value"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real bf_pow_min=1
    "power business factor min value"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real mdot_start
    annotation (Dialog(group="Pump ",tab="Initialization"));
  parameter Real[2] ssh_setPoint={5,5}
    "circuit A ssh set point"
    annotation (Dialog(group="Refrigerant"));
  parameter.Modelica.SIunits.TemperatureDifference[2] SC_setpoint={-2,-2}
    annotation (Dialog(group="Refrigerant"));
  parameter Boolean[2] SC_fixed={true,true}
    annotation (Dialog(group="Refrigerant"));
  parameter Boolean[2] Mref_fixed={false,false}
    annotation (Dialog(group="Refrigerant"));
  parameter.Modelica.SIunits.Mass[2] Mref={5,5}
    annotation (Dialog(group="Refrigerant"));
  inner.BOLT.InternalLibrary.Refrigerant.Aggregation.AggregateStreams_2 systemVariablesPump(
    isOff={Use_pump,Use_pump})
    annotation (Placement(transformation(extent={{53.64941913376437,125.42875105810268},{73.64941913376437,145.42875105810268}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.Mixer mixerPumpUserA(
    mDot_a_start=
      if not Use_pump then
        mdot_start
      else
        0,
    mDot_b_start=
      if Use_pump then
        mdot_start
      else
        0,
    p_start=Water_pressure,
    T_start=EWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff_a=Use_pump,
    isOff_b=not Use_pump,
    fa_set=
      if Use_pump then
        0
      else
        1)
    annotation (Placement(transformation(extent={{-5.319920743950348,-5.319920743950334},{5.319920743950348,5.319920743950334}},origin={-101.10972902044189,-7.854595463337819},rotation=90.0)));
  .BOLT.CoolantMisc.Mixer mixerPumpUserB(
    mDot_a_start=
      if Use_pump then
        mdot_start
      else
        0,
    mDot_b_start=
      if not Use_pump then
        mdot_start
      else
        0,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    fa_set=
      if Use_pump then
        1
      else
        0,
    isOff_b=is_monobloc or Use_pump,
    isOff_a=is_monobloc or not Use_pump,
    T_start=EWT,
    p_start=Water_pressure)
    annotation (Placement(transformation(extent={{-5.319920743950348,-5.319920743950334},{5.319920743950348,5.319920743950334}},origin={97.5852880366944,-8.222272438398097},rotation=90.0)));
equation
  controlledCapacity=
    if Use_EN then
      if use_bf then
        businessFactors.pub_net_cool_cap
      else
        eN14511.inst_net_cap
    else
      if use_bf then
        businessFactors.pub_gross_cool_cap
      else
        eN14511.inst_gross_cap;
  controlledPower=
    if Use_EN then
      if use_bf then
        businessFactors.pub_net_pow
      else
        eN14511.inst_net_pow
    else
      if use_bf then
        businessFactors.pub_gross_pow
      else
        eN14511.inst_gross_pow;
  connect(split_pumpB.port_a,pumpPolyB.port_a)
    annotation (Line(points={{95.1705875471087,-52.134538889964865},{95.1705875471087,-44.926847888774006},{57.59422308194374,-44.926847888774006},{57.59422308194374,-37.42569341417812}},color={0,127,0}));
  connect(split_pumpA.port_b,pumpPolyA.port_a)
    annotation (Line(points={{-96.98763091089418,-51.311992432272305},{-96.98763091089418,-44.55022524213727},{-65.08160928199607,-44.55022524213727},{-65.08160928199607,-37.294548712268764}},color={0,127,0}));
  connect(node_blocB_out.port_b,mixer.port_b)
    annotation (Line(points={{9.716900311317987,49.723549174361885},{2.599608435561697,49.723549174361885},{2.599608435561697,56.06009079921068}},color={0,127,0}));
  connect(node_blocA_out.port_b,mixer.port_a)
    annotation (Line(points={{-14.619641313530828,49.723549174361885},{-7.400391564438303,49.723549174361885},{-7.400391564438303,56.06009079921068}},color={0,127,0}));
  connect(split_bloc.port_b,node_coolant_in_pumpB.port_a)
    annotation (Line(points={{0.5742964341100705,-70.50817651023848},{6.5666666666666655,-70.50817651023848},{6.5666666666666655,-65.23333333333332},{39.44772772364737,-65.23333333333332}},color={0,127,0}));
  connect(node_coolant_in_pumpB.port_b,split_pumpB.port_c)
    annotation (Line(points={{47.44772772364737,-65.23333333333332},{98.01912393006732,-65.23333333333332},{98.01912393006732,-62.79017498918046}},color={0,127,0}));
  connect(split_bloc.port_a,node_coolant_in_pumpA.port_a)
    annotation (Line(points={{-4.911773636773205,-70.50817651023848},{-10.88581550923115,-70.50817651023848},{-10.88581550923115,-64.02469182603615},{-39.40982391781609,-64.02469182603615}},color={0,127,0}));
  connect(node_coolant_in_pumpA.port_b,split_pumpA.port_c)
    annotation (Line(points={{-47.40982391781609,-64.02469182603615},{-99.62516459881883,-64.02469182603615},{-99.62516459881883,-61.9676285314879}},color={0,127,0}));
  connect(node_coolant_in_splitbloc.port_b,split_bloc.port_c)
    annotation (Line(points={{-2.50207193466491,-85.1165772194211},{-2.0632372538145813,-81.16381260945407}},color={0,127,0}));
  connect(node_coolant_source.port_b,check_valve.port_a)
    annotation (Line(points={{-3.0687386013315754,-117.88324388608778},{-3.0687386013315754,-111.16454748157973},{-2.414785579186551,-111.16454748157973}},color={0,127,0}));
  connect(node_coolant_in_splitbloc.port_a,check_valve.port_b)
    annotation (Line(points={{-2.502071934664908,-93.1165772194211},{-2.502071934664908,-98.39173836273817},{-2.4147855791865482,-98.39173836273817}},color={0,127,0}));
  connect(shafA.flange,pumpPolyA.flange)
    annotation (Line(points={{-40.2918498052081,-3.3283811868263804},{-31.988777703356632,-3.3283811868263804},{-31.988777703356632,-28.90115085182104},{-56.688211421548345,-28.90115085182104}},color={127,0,0}));
  connect(shafB.flange,pumpPolyB.flange)
    annotation (Line(points={{33.23789886228211,-2.6142645441622676},{27.23789886228211,-2.6142645441622676},{27.23789886228211,-29.032295553730396},{49.20082522149602,-29.032295553730396}},color={127,0,0}));
  connect(mixer.port_c,node_out.port_a)
    annotation (Line(points={{-2.6003915644382984,76.46009079921068},{-2.6003915644383016,82.06009079921068}},color={0,127,0}));
  connect(node_out.port_b,external_system.port_a)
    annotation (Line(points={{-2.6003915644382998,90.06009079921068},{-2.6003915644383024,95.6736862397899}},color={0,127,0}));
  connect(controlledCapacityA.y,measurementBusA.capacity)
    annotation (Line(points={{-30.36273640817607,194.61132870676136},{-20.183305020500796,194.61132870676136},{-20.183305020500796,160.61132870676136},{-27.682450951782435,160.61132870676136}},color={0,0,127}));
  connect(controlledCapacityB.y,measurementBusB.capacity)
    annotation (Line(points={{24.01016289913556,194.61132870676136},{15.296853263385106,194.61132870676136},{15.296853263385106,160.61132870676136},{23.01016289913556,160.61132870676136}},color={0,0,127}));
  connect(load.y,businessFactors.load)
    annotation (Line(points={{-246.***********,63.62668898431265},{-172.64435788608338,63.62668898431265},{-172.64435788608338,118.7042816277591},{-151.93584551815792,118.7042816277591}},color={0,0,127}));
  connect(eN14511.inst_net_cap,businessFactors.net_cool_cap)
    annotation (Line(points={{-193.40195854854227,128.20463714623145},{-172.71774068764967,128.20463714623145},{-172.71774068764967,143.23385382635234},{-151.93584551815792,143.23385382635234}},color={0,0,127}));
  connect(eN14511.inst_net_pow,businessFactors.net_pow)
    annotation (Line(points={{-193.40195854854227,122.20463714623145},{-172.6689020333501,122.20463714623145},{-172.6689020333501,139.19051774966118},{-151.93584551815792,139.19051774966118}},color={0,0,127}));
  connect(gross_pow.y,businessFactors.gross_pow)
    annotation (Line(points={{-245.51527475472784,80.50119833285603},{-173.12230848155326,80.50119833285603},{-173.12230848155326,131.10384559627877},{-151.93584551815792,131.10384559627877}},color={0,0,127}));
  connect(businessFactors.gross_cool_cap,gross_cap.y)
    annotation (Line(points={{-151.93584551815792,127.06050951958757},{-172.99422180703186,127.06050951958757},{-172.99422180703186,93.78691261857034},{-245.25910140568513,93.78691261857034}},color={0,0,127}));
  connect(DPe_air_A_B.y,eN14511.DPe_air_A)
    annotation (Line(points={{-247.25910140568513,165.78195115465286},{-230.33052997711368,165.78195115465286},{-230.33052997711368,129.40463714623144},{-213.40195854854227,129.40463714623144}},color={0,0,127}));
  connect(eN14511.DPe,DPe.y)
    annotation (Line(points={{-213.40195854854227,131.40463714623144},{-229.99802355443327,131.40463714623144},{-229.99802355443327,151.78691261857034},{-246.59408856032428,151.78691261857034}},color={0,0,127}));
  connect(eN14511.DPe_air_B,DPe_air_A_B.y)
    annotation (Line(points={{-213.40195854854227,127.40463714623145},{-229.8559289699806,127.40463714623145},{-229.8559289699806,165.78195115465286},{-247.25910140568513,165.78195115465286}},color={0,0,127}));
  connect(eN14511.DPi,DPi.y)
    annotation (Line(points={{-213.40195854854227,125.60463714623145},{-230.37741864245248,125.60463714623145},{-230.37741864245248,137.53691261857034},{-245.92907571496343,137.53691261857034}},color={0,0,127}));
  connect(eN14511.q,q.y)
    annotation (Line(points={{-213.40195854854227,123.80463714623144},{-229.34778117740825,123.80463714623144},{-229.34778117740825,122.27952738440243},{-245.29360380627423,122.27952738440243}},color={0,0,127}));
  connect(eN14511.q_air_A,q_air_A_B.y)
    annotation (Line(points={{-213.40195854854227,121.80463714623144},{-229.33052997711368,121.80463714623144},{-229.33052997711368,107.53691261857034},{-245.25910140568513,107.53691261857034}},color={0,0,127}));
  connect(eN14511.q_air_B,q_air_A_B.y)
    annotation (Line(points={{-213.40195854854227,119.80463714623144},{-229.33052997711368,119.80463714623144},{-229.33052997711368,107.53691261857034},{-245.25910140568513,107.53691261857034}},color={0,0,127}));
  connect(eN14511.inst_gross_cap,gross_cap.y)
    annotation (Line(points={{-213.40195854854227,117.80463714623144},{-229.33052997711368,117.80463714623144},{-229.33052997711368,93.78691261857034},{-245.25910140568513,93.78691261857034}},color={0,0,127}));
  connect(eN14511.inst_gross_pow,gross_pow.y)
    annotation (Line(points={{-213.40195854854227,115.80463714623144},{-229.45861665163505,115.80463714623144},{-229.45861665163505,80.50119833285603},{-245.51527475472784,80.50119833285603}},color={0,0,127}));
  connect(External_Pressure.y,measurementBusA.External_Pressure)
    annotation (Line(points={{-4.752013895599437,208.19566392473865},{-4.752013895599437,160.61132870676136},{-27.682450951782435,160.61132870676136}},color={0,0,127}));
  connect(External_Pressure.y,measurementBusB.External_Pressure)
    annotation (Line(points={{-4.752013895599437,208.19566392473865},{-4.752013895599437,160.61132870676136},{23.01016289913556,160.61132870676136}},color={0,0,127}));
  connect(PipingB.port_b,gaz_separatorB.port_a)
    annotation (Line(points={{97.57846085003577,19.799795340188318},{97.20855048825722,19.799795340188318},{97.20855048825722,25.647303008535474}},color={0,127,0}));
  connect(PipingA.port_b,gaz_separatorA.port_a)
    annotation (Line(points={{-101.26278268171433,21.38260042972899},{-101.26278268171433,27.23010809807615}},color={0,127,0}));
  connect(ActuatorPumpUser_B,shafB.speed_in)
    annotation (Line(points={{43.02729244108585,15.820655037491711},{32.97848306969195,15.820655037491711},{32.97848306969195,-1.0142645441622733},{41.23789886228212,-1.0142645441622733}},color={0,0,127}));
  connect(ActuatorPumpUser_A,shafA.speed_in)
    annotation (Line(points={{-51.53049747436644,14.96493704570473},{-41.58287337345662,14.96493704570473},{-41.58287337345662,-1.728381186826379},{-48.291849805208116,-1.728381186826379}},color={0,0,127}));
  connect(mixerPumpUserA.port_c,PipingA.port_a)
    annotation (Line(points={{-101.10972902044189,-2.321877889629457},{-101.10972902044189,3.4541151042233507},{-101.26278268171433,3.4541151042233507},{-101.26278268171433,9.230108098076158}},color={0,127,0}));
  connect(mixerPumpUserB.port_c,PipingB.port_a)
    annotation (Line(points={{97.5852880366944,-2.689554864689735},{97.5852880366944,2.4788740719228692},{97.57846085003577,2.4788740719228692},{97.57846085003577,7.647303008535481}},color={0,127,0}));
  connect(split_pumpA.port_a,mixerPumpUserA.port_a)
    annotation (Line(points={{-102.47370098177745,-51.311992432272305},{-102.47370098177745,-13.174516207288168},{-103.66329097753805,-13.174516207288168}},color={0,127,0}));
  connect(pumpPolyA.port_b,mixerPumpUserA.port_b)
    annotation (Line(points={{-65.08160928199607,-20.507752991373323},{-65.08160928199607,-13.174516207288168},{-98.34337023358772,-13.174516207288168}},color={0,127,0}));
  connect(pumpPolyB.port_b,mixerPumpUserB.port_a)
    annotation (Line(points={{57.59422308194374,-20.63889769328268},{57.59422308194374,-13.542193182348445},{95.03172607959823,-13.542193182348445}},color={0,127,0}));
  connect(split_pumpB.port_b,mixerPumpUserB.port_b)
    annotation (Line(points={{100.65665761799197,-52.134538889964865},{100.65665761799197,-32.83836603615666},{100.35164682354856,-32.83836603615666},{100.35164682354856,-13.542193182348445}},color={0,127,0}));
  connect(coolant_in,node_coolant_source.port_a)
    annotation (Line(points={{-3.480089871014542,-146.7059464254537},{-3.480089871014542,-125.88324388608778},{-3.0687386013315736,-125.88324388608778}},color={0,127,0}));
  connect(external_system.port_b,coolant_out)
    annotation (Line(points={{-2.6003915644382993,108.44649535863147},{-2.6003915644383007,124.06009079921068}},color={0,127,0}));
  connect(BlockA.port_b,node_blocA_out.port_a)
    annotation (Line(points={{-32.4,50},{-32.4,49.723549174361885},{-22.619641313530828,49.723549174361885}},color={0,127,0}));
  connect(BlockA.actual_FSFanSpd_in,Fan_controller_A)
    annotation (Line(points={{-55,52},{-76.90288730001818,52},{-76.90288730001818,59.88271552516719},{-98.35380587259328,59.88271552516719}},color={0,0,127}));
  connect(BlockA.EXV_controller,EXV_controller_A)
    annotation (Line(points={{-55,58},{-76.50394290903301,58},{-76.50394290903301,69.36437114069773},{-99.55423712065695,69.36437114069773}},color={0,0,127}));
  connect(BlockA.compressor_controller,Compressor_controller_A)
    annotation (Line(points={{-55,62},{-76.18453903904617,62},{-76.18453903904617,79.21770595805611},{-98.93896918087167,79.21770595805611}},color={0,0,127}));
  connect(BlockA.measurementBus,measurementBusA)
    annotation (Line(points={{-33,58},{-27.682450951782435,58},{-27.682450951782435,160.61132870676136}},color={255,204,51}));
  connect(gaz_separatorA.port_b,BlockA.port_a)
    annotation (Line(points={{-101.26278268171433,39.38260042972899},{-101.26278268171433,50},{-55,50}},color={0,127,0}));
  connect(BlockB.port_b,node_blocB_out.port_a)
    annotation (Line(points={{30.384484391678818,49.257918475027665},{30.384484391678818,49.723549174361885},{17.716900311317985,49.723549174361885}},color={0,127,0}));
  connect(BlockB.measurementBus,measurementBusB)
    annotation (Line(points={{30.98448439167882,57.257918475027665},{23.01016289913556,57.257918475027665},{23.01016289913556,160.61132870676136}},color={255,204,51}));
  connect(gaz_separatorB.port_b,BlockB.port_a)
    annotation (Line(points={{97.20855048825722,37.79979534018831},{97.20855048825722,49.257918475027665},{52.98448439167882,49.257918475027665}},color={0,127,0}));
  connect(Fan_controller_B,BlockB.actual_FSFanSpd_in)
    annotation (Line(points={{87.02877281194978,58.2797029539722},{70.19975331193633,58.2797029539722},{70.19975331193633,51.257918475027665},{52.98448439167882,51.257918475027665}},color={0,0,127}));
  connect(EXV_controller_B,BlockB.EXV_controller)
    annotation (Line(points={{86.93858363968468,66.15031712701042},{70.15465872580378,66.15031712701042},{70.15465872580378,57.257918475027665},{52.98448439167882,57.257918475027665}},color={0,0,127}));
  connect(Compressor_controller_B,BlockB.compressor_controller)
    annotation (Line(points={{86.61211145474692,75.11086355386652},{86.61211145474692,68.78444357635209},{70.2636455748654,68.78444357635209},{70.2636455748654,61.257918475027665},{52.98448439167882,61.257918475027665}},color={0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={49,49,49},
          fillColor={181,124,237},
          fillPattern=FillPattern.Solid,
          extent={{-100,-119},{100,119}},
          origin={0,-19}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name"),
        Text(
          textString="MODULE",
          origin={0,-20},
          extent={{-100,24},{100,-24}})}));
end Equipement;
