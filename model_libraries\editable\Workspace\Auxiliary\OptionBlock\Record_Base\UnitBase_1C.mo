within Workspace.Auxiliary.OptionBlock.Record_Base;
record UnitBase_1C
  extends Modelica.Icons.Record;
  parameter Modelica.SIunits.HeatFlowRate capacity_design "Design cooling capacity";
  parameter Modelica.SIunits.HeatFlowRate capacity_design_LN "Design cooling capacity";
  parameter Real m_ref_chaud "system charge heating mode";
  parameter Real m_ref_froid "system charge cooling mode";
  parameter BOLT.InternalLibrary.Compressors.PD_2Port.SubComponents.PD.DataBase.Polynomial.ARI_VS.Selector selector_Comp "Compressor type";
  parameter Real CompVoltage "Compressor voltage";
  parameter Real fmax "Maximum frequency for compressor";
  parameter Real fmin "Minimal frequency for compressor";
  parameter BOLT.InternalLibrary.Refrigerant.Valves.TwoPort.DataBase.Selector EXV_main_A "Main EXV type";
  parameter Real ZUev1 "BPHE_Z_factor";
  parameter Modelica.SIunits.Length cond_diameter_brine_port_in "Brine inlet diameter";
  parameter Modelica.SIunits.Length cond_diameter_brine_port_out "Brine outlet diameter";
  parameter Modelica.SIunits.Length cond_diameter_ref_port_in "Refrigerant inlet diameter";
  parameter Modelica.SIunits.Length cond_diameter_ref_port_out "Refrigerant outlet diameter";
  parameter BlackBoxLibrary.GeoSelector.GeoSelector_1C_Cond cond_select_geo "BPHE type";
  parameter Integer nplate "Number of plate BPHE";
  parameter Integer nCoils "Number of coil ";
  parameter Integer Itube "None";
  parameter Integer nCir "None";
  parameter Integer Nrow "None";
  parameter Integer Ntube "None";
  parameter Modelica.SIunits.Length Ltube "None";
  parameter Modelica.SIunits.Length Dotube "None";
  parameter Modelica.SIunits.Length Ttube "None";
  parameter Modelica.SIunits.Length Ptube "None";
  parameter Modelica.SIunits.Length Prow "None";
  parameter Integer Dfin "None";
  parameter Modelica.SIunits.Length Tfin "None";
  parameter Modelica.SIunits.Area duct_Ac "None";
  parameter Real duct_Ka "None";
  parameter Modelica.SIunits.ThermalConductance duct_UA "None";
  parameter Real pumpPoly_Fw "None";
  parameter Integer max_fan_frequency "None";
  parameter Integer max_fan_frequency2 "None";
  parameter Real Fan_FW "None";
  parameter Modelica.SIunits.Length discharge_line_length "None";
  parameter Modelica.SIunits.Length discharge_line_diameter "None";
  parameter Modelica.SIunits.Length BPHE_line_length "None";
  parameter Modelica.SIunits.Length BPHE_line_diameter "None";
  parameter Modelica.SIunits.Length Liquid_line_length "None";
  parameter Modelica.SIunits.Length Liquid_line_diameter "None";
  parameter Modelica.SIunits.Length Coil_line_length "None";
  parameter Modelica.SIunits.Length Coil_line_diameter "None";
  parameter Modelica.SIunits.Length Suction_line_length "None";
  parameter Modelica.SIunits.Length Suction_line_diameter "None";
  parameter Modelica.SIunits.Length EXV_in_line_length "None";
  parameter Modelica.SIunits.Length EXV_in_line_diameter "None";
  parameter Real[9] fanCurveCoefficientsCooling "None";
  parameter Real[9] fanCurveCoefficientsHeating "None";
  parameter Real Oil_charge "None";
  parameter BOLT.InternalLibrary.Coolant.Pumps.DataBase.PumpPoly.Selector Pump_type "None";
  parameter Modelica.SIunits.Pressure PDC_4WV "None";
  parameter Real Zflow_intercept "None";
  parameter Real Zflow_SST "None";
  parameter Real Zflow_SDT "None";
  parameter Real Zflow_Heatcap "None";
  parameter Real Zflow_Ncomp "None";
  parameter Real Zflow_SST2 "None";
  parameter Real Zflow_SST3 "None";
  parameter Real Zpower_intercept "None";
  parameter Real Zpower_DGT "None";
  parameter Real Zpower_SDT "None";
  parameter Real Zpower_SST "None";
  parameter Real Zpower_SST2 "None";
  parameter Real Zpower_Ncomp "None";
  parameter Real Zpower_heatcap "None";
  parameter Real FW "None";
  parameter Real Zcond_chaud_Heatcap "None";
  parameter Real Zcond_chaud_SDT "None";
  parameter Real Zcond_chaud_SST "None";
  parameter Real Zcond_chaud_intercept "None";
  parameter Real Zcond_chaud_coated "None";
  parameter Real Zevap_chaud_intercept "None";
  parameter Real Zevap_chaud_Coolcap "None";
  parameter Real Zevap_chaud_SST "None";
  parameter Real Zevap_coated_chaud "None";
  parameter Real Zcond_froid_intercept "None";
  parameter Real Zcond_froid_SDT "None";
  parameter Real Zevap_froid_intercept "None";
  parameter Real Zevap_froid_Coolcap "None";
  parameter Real Zevap_froid_SST "None";
  parameter Integer Heatcap_Tbiv "None";
  parameter Real SEER_A_cap "None";
  parameter Real Frequence_degi "None";
  parameter Real OAT_bf_cap_chaud "None";
  parameter Real load_bf_cap_chaud "None";
  parameter Real const_bf_cap_chaud "None";
  parameter Real OAT_bf_pow_chaud "None";
  parameter Real load_bf_pow_chaud "None";
  parameter Real const_bf_pow_chaud "None";
  parameter Real max_bf_cap_chaud "None";
  parameter Real min_bf_cap_chaud "None";
  parameter Real max_bf_pow_chaud "None";
  parameter Real min_bf_pow_chaud "None";
  parameter Real OAT_bf_cap_froid "None";
  parameter Real load_bf_cap_froid "None";
  parameter Real const_bf_cap_froid "None";
  parameter Real OAT_bf_pow_froid "None";
  parameter Real load_bf_pow_froid "None";
  parameter Real const_bf_pow_froid "None";
  parameter Real max_bf_cap_froid "None";
  parameter Real min_bf_cap_froid "None";
  parameter Real max_bf_pow_froid "None";
  parameter Real min_bf_pow_froid "None";
  parameter Real OAT_target_cap "None";
  parameter Real intercept_target_cap "None";
  parameter Real Max_max_target_cap "None";
  parameter Real OAT_target_cap_LN "None";
  parameter Real intercept_target_cap_LN "None";
  parameter Real Max_max_target_cap_LN "None";
  parameter Real max_fan_ULN_heating "None";
  parameter Real max_cp_ULN_heating "None";
  parameter Real max_fan_ULN_cooling "None";
  parameter Real max_cp_ULN_cooling "None";
  parameter Integer Heatcap_Tbiv_LN_option "None";
  parameter Real SEER_A_cap_LN_option "None";
  parameter Real Z_cond_HPH_min "None";
  parameter Real Z_cond_HPH_max "None";
  parameter Real Z_cond_HPC_min "None";
  parameter Real Z_cond_HPC_max "None";
  parameter Real Z_evap_HPH_min "None";
  parameter Real Z_evap_HPH_max "None";
  parameter Real Z_evap_HPC_min "None";
  parameter Real Z_evap_HPC_max "None";
  parameter Real Z_flow_min "None";
  parameter Real Z_flow_max "None";
  parameter Real Z_power_min "None";
  parameter Real Z_power_max "None";
end UnitBase_1C;