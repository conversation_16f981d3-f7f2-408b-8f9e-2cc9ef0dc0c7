within Workspace.Controller.SubSystems;
model CompleteCompressorControl_1C_cooling
  extends.Workspace.Controller.SubSystems.BaseClasses.CompleteCompressorControlBase_1C(
    redeclare replaceable.Workspace.Controller.SubSystems.CompressorControl_cooling compressorControl,
    capacity_controller(
      AV_start=0.9,
      isOff=isOff),
    capacity_error(
      isOff=isOff,
      gain=1 / (((Nominal_capacity)))),
    max_speed=max_speed,
    min_speed=min_speed);
  parameter.Modelica.SIunits.Power Nominal_capacity=60000;
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end CompleteCompressorControl_1C_cooling;
