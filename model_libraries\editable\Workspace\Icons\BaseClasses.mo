within Workspace.Icons;
partial class BaseClasses
  extends Workspace.Icons.PackageIcon;
  annotation (
    Icon(
      graphics={
        Rectangle(
          extent={{-60,6},{-34,-18}},
          pattern=LinePattern.None,
          fillColor={0,0,0},
          fillPattern=FillPattern.Solid,
          lineColor={0,0,0}),
        Rectangle(
          extent={{-12,6},{14,-18}},
          pattern=LinePattern.None,
          fillColor={0,0,0},
          fillPattern=FillPattern.Solid,
          lineColor={0,0,0}),
        Rectangle(
          extent={{36,6},{62,-18}},
          pattern=LinePattern.None,
          fillColor={0,0,0},
          fillPattern=FillPattern.Solid,
          lineColor={0,0,0})}));
end BaseClasses;
