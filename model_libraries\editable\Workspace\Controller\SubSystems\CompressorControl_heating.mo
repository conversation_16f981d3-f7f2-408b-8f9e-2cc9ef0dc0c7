within Workspace.Controller.SubSystems;
model CompressorControl_heating
  extends.Workspace.Controller.SubSystems.BaseClasses.CompressorControlBase(
    compressorFrequency_max=140,
    compressorFrequency_min=30);
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation T_sst_min_error(
    measurement=T_sst,
    setPoint=T_sst_min_limit_comp,
    ID=2,
    gain=-1/((((243-220)))),
    isOff=isOffSSTmin or crkIsOff)
    annotation (Placement(transformation(extent={{-87.38285368190994,6.0},{-32.617146318090064,26.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation Freq_max_sdt_error(
    measurement=actuatorSignal,
    setPoint=Freq_max_SDT_limit_comp,
    ID=6,
    gain=1/(((compressorFrequency_max-compressorFrequency_min))),
    isOff=isOffFreq_max_SDT or crkIsOff)
    annotation (Placement(transformation(extent={{-87.48717874970518,-60.0},{-32.51282125029482,-40.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation compressorFrequency_error(
    ID=1,
    measurement=actuatorSignal,
    setPoint=compressorFrequency,
    isOff=crkIsOff,
    gain=1/((compressorFrequency_max-compressorFrequency_min)))
    annotation (Placement(transformation(extent={{-87.00341338378533,21.17723432450272},{-32.23770601996543,41.17723432450272}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Min min
    annotation (Placement(transformation(extent={{-19.12580576364739,11.20752756968594},{-5.540860903019269,24.79247243031406}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Min min3
    annotation (Placement(transformation(extent={{74.11839839041747,-12.15560010854108},{89.42267206318152,3.1486735642229817}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointController(
    AV_max=compressorFrequency_max,
    isOff=crkIsOff,
    AV_min=compressorFrequency_min,
    AV_start=110,
    manualOff=manualOff,
    AV_value_off=AV_value_off)
    annotation (Placement(transformation(extent={{178.0,-12.0},{198.0,8.0}},origin={0.0,0.0},rotation=0.0)));
  parameter Boolean isOffSSTmin=false;
  parameter Boolean isOffSDTmax=false;
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation Freq_min_error(
    isOff=isOffMinFrequency or crkIsOff,
    gain=1/((compressorFrequency_max-compressorFrequency_min)),
    ID=4,
    setPoint=Min_frequency,
    measurement=actuatorSignal)
    annotation (Placement(transformation(extent={{-89.13090145685027,-28.686170090662316},{-34.156543957439936,-8.686170090662316}},origin={0.0,0.0},rotation=0.0)));
  parameter Boolean isOffMinFrequency=false;
  parameter Boolean isOffMaxFrequency=false;
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation Freq_max_error(
    measurement=actuatorSignal,
    setPoint=Max_frequency,
    ID=5,
    gain=1/((compressorFrequency_max-compressorFrequency_min)),
    isOff=isOffMaxFrequency or crkIsOff)
    annotation (Placement(transformation(extent={{-87.48717874970517,-44.0},{-32.512821250294834,-24.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Min min2
    annotation (Placement(transformation(extent={{51.85804236289526,-7.7532361857922965},{67.16231603565932,7.551037486971758}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Max max2
    annotation (Placement(transformation(extent={{28.98638701843352,-3.790236188763771},{44.290660691197544,11.514037484000283}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation T_DGT_max_error(
    isOff=isOffDGTmax or crkIsOff,
    gain=-1/((((423-300)))),
    ID=3,
    setPoint=T_DGT_max_limit_comp,
    measurement=DGT)
    annotation (Placement(transformation(extent={{-85.15423060744695,-11.667051857741797},{-30.179873108036617,8.332948142258203}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Max max
    annotation (Placement(transformation(extent={{8.171155871775973,6.805267205082075},{22.218637823988722,20.852749157294824}},origin={0.0,0.0},rotation=0.0)));
  parameter Boolean isOffDGTmin=false;
  parameter Boolean isOffDGTmax=false;
  parameter Boolean isOffFreq_max_SDT=false;
  parameter Boolean isOffFreq_min_SDT=false;
  parameter Boolean manualOff=false;
  parameter Real AV_value_off=720;
  parameter.Modelica.SIunits.Power Nominal_capacity=60000;
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation Freq_min_sdt_error(
    isOff=isOffFreq_min_SDT or crkIsOff,
    gain=1/(compressorFrequency_max-compressorFrequency_min),
    ID=7,
    setPoint=Freq_min_SDT_limit_comp,
    measurement=actuatorSignal)
    annotation (Placement(transformation(extent={{-86.34644423336965,-76.95943446072144},{-31.372086733959296,-56.95943446072144}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Max max4
    annotation (Placement(transformation(extent={{104.65816697859975,-13.824854560503226},{119.9624406513638,1.4794191122608353}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation SDT_max_error(
    ID=8,
    measurement=T_sdt,
    gain=1/((((348-300)))),
    setPoint=T_sdt_max_limit_comp,
    isOff=isOffSDTmax or crkIsOff)
    annotation (Placement(transformation(extent={{-86.96697884114381,-94.0},{-33.03302115885619,-74.0}},origin={0.0,0.0},rotation=0.0)));

  .BOLT.Control.SteadyState.SetpointControl.Min min4
    annotation (Placement(transformation(extent={{131.84255469070524,-16.15744530929475},{148.15744530929476,0.15744530929475076}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation SDT_min_error(
    isOff=isOffSDTmin or crkIsOff,
    setPoint=T_sdt_min_limit_comp,
    gain=1/(348-300),
    measurement=T_sdt,
    ID=9)
    annotation (Placement(transformation(extent={{-86.96697884114381,-110.0},{-33.03302115885619,-90.0}},origin={0.0,0.0},rotation=0.0)));

  parameter Boolean isOffSDTmin=false;
  .BOLT.Control.SteadyState.SetpointControl.Max max5
    annotation (Placement(transformation(extent={{153.84255469070524,-16.15744530929475},{170.15744530929476,0.15744530929475076}},origin={0.0,0.0},rotation=0.0)));
protected
      .Modelica.Blocks.Interfaces.RealOutput T_sdt_max_limit_comp
    annotation (Placement(transformation(extent={{-58.361420494991705,-210.3614204949917},{-37.638579505008295,-189.6385795050083}},origin={0.0,0.0},rotation=0.0)));
      .Modelica.Blocks.Interfaces.RealOutput T_sdt_min_limit_comp
    annotation (Placement(transformation(extent={{-58.361420494991705,-238.3614204949917},{-37.638579505008295,-217.6385795050083}},origin={0.0,0.0},rotation=0.0)));
      .Modelica.Blocks.Interfaces.RealOutput Freq_max_SDT_limit_comp
    annotation (Placement(transformation(extent={{-61.04070239238498,-147.06658588839872},{-39.67636591302166,-125.7022494090354}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Freq_min_SDT_limit_comp
    annotation (Placement(transformation(extent={{-60.40364815870569,-161.01689739196675},{-39.039311679342376,-139.65256091260343}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput T_sst_min_limit_comp
    annotation (Placement(transformation(extent={{-61.04070239238498,-130.62063763853533},{-39.67636591302166,-109.25630115917201}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput T_sst
    annotation (Placement(transformation(extent={{-56.68216823968166,63.31783176031834},{-35.31783176031834,84.68216823968166}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput T_sdt
    annotation (Placement(transformation(extent={{-60.68216823968166,49.31783176031834},{-39.31783176031834,70.68216823968166}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Max_frequency
    annotation (Placement(transformation(extent={{-59.749989773839424,-192.58675052846198},{-38.38565329447611,-171.22241404909866}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Min_frequency
    annotation (Placement(transformation(extent={{-59.5468820824739,-177.3147768800621},{-38.18254560311058,-155.9504404006988}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput DGT
    annotation (Placement(transformation(extent={{-61.09878835255423,33.857781964448314},{-39.73445187319091,55.22211844381163}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput T_DGT_max_limit_comp
    annotation (Placement(transformation(extent={{-58.68216823968166,-224.68216823968166},{-37.31783176031834,-203.31783176031834}},origin={0.0,0.0},rotation=0.0)));
equation
  connect(compressorFrequency_error.sensor,min.u1)
    annotation (Line(points={{-32.78536309360362,30.37723432450272},{-19.12580576364739,30.37723432450272},{-19.12580576364739,21.803784560975874}},color={28,108,200}));
  connect(T_sst_min_error.sensor,min.u2)
    annotation (Line(points={{-33.16480339172826,15.2},{-26,15.2},{-26,13.924516541811563},{-19.12580576364739,13.924516541811563}},color={28,108,200}));
  connect(T_sst_min_limit_comp,limitsBus.T_sst_min_limit_comp)
    annotation (Line(points={{-50.35853415270332,-119.93846939885367},{-92,-119.93846939885367},{-92,-120}},color={0,0,127}));
  connect(T_sst,measurementBus.T_sst)
    annotation (Line(points={{-46,74},{-100,74},{-100,60}},color={0,0,127}));
  connect(T_sdt,measurementBus.T_sdt)
    annotation (Line(points={{-50,60},{-100,60}},color={0,0,127}));
  connect(Min_frequency,limitsBus.Min_frequency)
    annotation (Line(points={{-48.86471384279224,-166.63260864038045},{-92,-166.63260864038045},{-92,-120}},color={0,0,127}));
  connect(Max_frequency,limitsBus.Max_frequency)
    annotation (Line(points={{-49.067821534157765,-181.90458228878032},{-92,-181.90458228878032},{-92,-120}},color={0,0,127}));
  connect(DGT,measurementBus.T_dgt)
    annotation (Line(points={{-50.41662011287257,44.53995020412997},{-100,44.53995020412997},{-100,60}},color={0,0,127}));
  connect(T_DGT_max_limit_comp,limitsBus.T_dgt_max_limit_comp)
    annotation (Line(points={{-48,-214},{-92,-214},{-92,-120}},color={0,0,127}));
  connect(Freq_max_SDT_limit_comp,limitsBus.Freq_max_SDT_limit_comp)
    annotation (Line(points={{-50.35853415270332,-136.38441764871706},{-92,-136.38441764871706},{-92,-120}},color={0,0,127}));
  connect(Freq_min_SDT_limit_comp,limitsBus.Freq_min_SDT_limit_comp)
    annotation (Line(points={{-49.721479919024034,-150.3347291522851},{-92,-150.3347291522851},{-92,-120}},color={0,0,127}));
  connect(Freq_min_sdt_error.sensor,max4.u2)
    annotation (Line(points={{-31.921830308953396,-67.75943446072144},{97.40502873578743,-67.75943446072144},{97.40502873578743,-10.763999825950414},{104.65816697859975,-10.763999825950414}},color={28,108,200}));
  connect(min.y,max.u1)
    annotation (Line(points={{-5.540860903019269,18},{0.07606550898942288,18},{0.07606550898942288,17.762303127808018},{8.171155871775973,17.762303127808018}},color={28,108,200}));
  connect(T_DGT_max_error.sensor,max.u2)
    annotation (Line(points={{-30.72961668303072,-2.4670518577417964},{-2.805193166564953,-2.4670518577417964},{-2.805193166564953,9.614763595524625},{8.171155871775973,9.614763595524625}},color={28,108,200}));
  connect(Freq_min_error.sensor,max2.u2)
    annotation (Line(points={{-34.70628753243405,-19.486170090662316},{10.296116121439464,-19.486170090662316},{10.296116121439464,-0.7293814542109605},{28.98638701843352,-0.7293814542109605}},color={28,108,200}));
  connect(max.y,max2.u1)
    annotation (Line(points={{22.218637823988722,13.829008181188449},{25.60251242121112,13.829008181188449},{25.60251242121112,8.147097275992191},{28.98638701843352,8.147097275992191}},color={28,108,200}));
  connect(Freq_max_error.sensor,min2.u2)
    annotation (Line(points={{-33.06256482528894,-34.8},{32.967883847038905,-34.8},{32.967883847038905,-4.692381451239486},{51.85804236289526,-4.692381451239486}},color={28,108,200}));
  connect(max2.y,min2.u1)
    annotation (Line(points={{44.290660691197544,3.8619006476182562},{48.0743515270464,3.8619006476182562},{48.0743515270464,4.184097278963666},{51.85804236289526,4.184097278963666}},color={28,108,200}));
  connect(Freq_max_sdt_error.sensor,min3.u2)
    annotation (Line(points={{-33.06256482528892,-50.8},{67.02402033672928,-50.8},{67.02402033672928,-9.094745373988268},{74.11839839041747,-9.094745373988268}},color={28,108,200}));
  connect(min3.y,max4.u1)
    annotation (Line(points={{89.42267206318152,-4.503463272159049},{97.25804805494566,-4.503463272159049},{97.25804805494566,-1.8875210957472577},{104.65816697859975,-1.8875210957472577}},color={28,108,200}));
  connect(min2.y,min3.u1)
    annotation (Line(points={{67.16231603565932,-0.10109934941026921},{70.85798574709341,-0.10109934941026921},{70.85798574709341,-0.2182666437851113},{74.11839839041747,-0.2182666437851113}},color={28,108,200}));
  connect(T_sdt_max_limit_comp,limitsBus.T_sdt_max_limit_comp)
    annotation (Line(points={{-48,-200},{-92,-200},{-92,-120}},color={0,0,127}));
  connect(SDT_max_error.sensor,min4.u2)
    annotation (Line(points={{-33.57236073567906,-84.8},{124,-84.8},{124,-12.89446718557685},{131.84255469070524,-12.89446718557685}},color={28,108,200}));
  connect(max4.y,min4.u1)
    annotation (Line(points={{119.9624406513638,-6.1727177241211955},{125.90249767103452,-6.1727177241211955},{125.90249767103452,-3.43183062679494},{131.84255469070524,-3.43183062679494}},color={28,108,200}));
  connect(setpointController.actuatorSignal,actuatorSignal)
    annotation (Line(points={{198.6,-2},{203.3,-2},{203.3,0},{216,0}},color={0,0,127}));
  connect(T_sdt_min_limit_comp,limitsBus.T_sdt_min_limit)
    annotation (Line(points={{-48,-228},{-92,-228},{-92,-120}},color={0,0,127}));
  connect(max5.u2,SDT_min_error.sensor)
    annotation (Line(points={{153.84255469070524,-12.89446718557685},{153.84255469070524,-100.8},{-33.57236073567906,-100.8}},color={28,108,200}));
  connect(max5.u1,min4.y)
    annotation (Line(points={{153.84255469070524,-3.43183062679494},{151,-3.43183062679494},{151,-8},{148.15744530929476,-8}},color={28,108,200}));
  connect(max5.y,setpointController.errorSignal)
    annotation (Line(points={{170.15744530929476,-8},{174.07872265464738,-8},{174.07872265464738,-2},{178,-2}},color={28,108,200}));
end CompressorControl_heating;
