within Workspace.Controller.StateMachine;
model Test_StateMachine
  extends.Workspace.Controller.StateMachine.StateMachine61AQ;
  parameter Boolean Module_2_on=true;
  parameter Boolean Module_3_on=false;
  parameter Boolean Module_4_on=false;
equation
  Module_2_ON=Module_2_on;
  Module_3_ON=Module_3_on;
  Module_4_ON=Module_4_on;
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end Test_StateMachine;
