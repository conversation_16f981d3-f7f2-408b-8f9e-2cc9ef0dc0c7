within Workspace.Controller.Tests;
model control_Pump
  parameter.Modelica.SIunits.HeatFlowRate TargetCoolingCapacity=215000;
  parameter Real Gain=-0.0001;
  parameter Real Gain_LWT=0.01;
  parameter.Modelica.SIunits.Temperature Heating_Temp=313.15;
  parameter.Modelica.SIunits.Temperature LWT_sp=308.15;
  parameter.Modelica.SIunits.Temperature EWT_sp=303.15;
  output.Modelica.SIunits.HeatFlowRate TotalCapacity;
  .BOLT.CoolantMisc.ReducedPipe PdC_Cool_3(
    Ka_set=0.01,
    use_Ka_in=true)
    annotation (Placement(transformation(extent={{25.40576483308562,-19.40576483308562},{14.59423516691438,-8.59423516691438}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.ReducedPipe PdC_Cool_4(
    Ka_set=0.01,
    use_Ka_in=true)
    annotation (Placement(transformation(extent={{24.873681474297932,-40.87368147429793},{15.126318525702068,-31.126318525702068}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Source MainInlet(
    Vd_set=WaterFlow_sp,
    T_set=EWT_sp,
    Vd_fixed=Vd_fixed,
    p_fixed=true,
    p_set=0,
    T_fixed=EWT_fixed and not Vd_fixed)
    annotation (Placement(transformation(extent={{-104.0,-2.7901562868938985},{-96.0,5.2098437131061015}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.ReducedPipe PdC_Cool_1(
    Ka_set=0.01,
    use_Ka_in=true)
    annotation (Placement(transformation(extent={{24.723607357042866,35.276392642957134},{15.276392642957134,44.723607357042866}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.ReducedPipe PdC_Cool_2(
    Ka_set=0.01,
    use_Ka_in=true)
    annotation (Placement(transformation(extent={{24.98029306711596,15.019706932884034},{15.019706932884041,24.980293067115966}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Node In4
    annotation (Placement(transformation(extent={{-44.0,-48.0},{-36.0,-40.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Node Out2(
    T_set=LWT_sp)
    annotation (Placement(transformation(extent={{52.229269539405564,7.231914367685817},{60.229269539405564,15.231914367685818}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Node Out1(
    T_set=LWT_sp)
    annotation (Placement(transformation(extent={{51.94760414796825,28.668583584811266},{59.94760414796825,36.668583584811266}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Sink MainOutlet(
    p_set=0,
    p_fixed=true,
    T_set=LWT_sp)
    annotation (Placement(transformation(extent={{146.1985681752938,-41.4738113224593},{138.1985681752938,-33.4738113224593}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.Split split_1
    annotation (Placement(transformation(extent={{-64.0,24.0},{-56.0,32.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Node Out3(
    T_set=LWT_sp)
    annotation (Placement(transformation(extent={{52.229269539405564,-24.768085632314182},{60.229269539405564,-16.768085632314182}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Node In3
    annotation (Placement(transformation(extent={{-44.0,-24.0},{-36.0,-16.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.Split split_0
    annotation (Placement(transformation(extent={{-66.0,-24.0},{-58.0,-16.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.Mixer mixer_1
    annotation (Placement(transformation(extent={{72.727474815075,21.730119643355245},{83.73106426373613,32.733709092016376}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Node In2
    annotation (Placement(transformation(extent={{-44.0,6.535164243730826},{-36.0,14.535164243730826}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Node In1
    annotation (Placement(transformation(extent={{-44.0,30.0},{-36.0,38.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.Mixer mixer
    annotation (Placement(transformation(extent={{87.2034643977177,-6.582035669721024},{98.83136447253138,5.045864405092659}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.Split split
    annotation (Placement(transformation(extent={{-84.0,-2.0},{-76.0,6.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.Mixer mixer_0
    annotation (Placement(transformation(extent={{71.17546214709672,-37.82189302462302},{81.2830769317144,-27.714278240005342}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Node Out4(
    T_set=LWT_sp)
    annotation (Placement(transformation(extent={{52.229269539405564,-48.76808563231418},{60.229269539405564,-40.76808563231418}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.HeatExchanger.CoolantCoolant.BriBriHX_Reduced briBriHX_Reduced_1(
    UA=15000)
    annotation (Placement(transformation(extent={{8.314142754597441,46.314142754597434},{-7.77897851086659,30.2210214891334}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.HeatExchanger.CoolantCoolant.BriBriHX_Reduced briBriHX_Reduced_2(
    UA=12000)
    annotation (Placement(transformation(extent={{8.287198343686516,24.287198343686516},{-8.287198343686516,7.712801656313484}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.HeatExchanger.CoolantCoolant.BriBriHX_Reduced briBriHX_Reduced_3(
    UA=10000)
    annotation (Placement(transformation(extent={{7.604660789993614,-8.395339210006387},{-7.604660789993614,-23.604660789993613}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.HeatExchanger.CoolantCoolant.BriBriHX_Reduced briBriHX_Reduced_4(
    UA=11000)
    annotation (Placement(transformation(extent={{7.920628599030769,-32.07937140096923},{-7.920628599030769,-47.92062859903077}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Source source_1(
    T_set=Heating_Temp,
    p_set=340000,
    Vd_fixed=false,
    p_fixed=true)
    annotation (Placement(transformation(extent={{44.0,48.0},{36.0,56.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Source source_2(
    T_set=Heating_Temp,
    p_set=340000,
    Vd_fixed=false,
    p_fixed=true)
    annotation (Placement(transformation(extent={{43.732417878134655,21.87307485305788},{35.732417878134655,29.87307485305788}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Source source_4(
    T_set=Heating_Temp,
    p_set=340000,
    Vd_fixed=false,
    p_fixed=true)
    annotation (Placement(transformation(extent={{43.464835756269196,-34.126925146942106},{35.464835756269196,-26.126925146942106}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Source source_3(
    T_set=Heating_Temp,
    p_set=340000,
    Vd_fixed=false,
    p_fixed=true)
    annotation (Placement(transformation(extent={{43.7324178781346,-9.71868605015341},{35.7324178781346,-1.7186860501534102}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Sink sink1
    annotation (Placement(transformation(extent={{-24.0,42.0},{-16.0,50.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Sink sink2
    annotation (Placement(transformation(extent={{-24.0,16.0},{-16.0,24.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Sink sink3
    annotation (Placement(transformation(extent={{-24.0,-16.0},{-16.0,-8.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Sink sink4
    annotation (Placement(transformation(extent={{-24.0,-40.0},{-16.0,-32.0}},origin={0.0,0.0},rotation=0.0)));
  parameter.Modelica.SIunits.VolumeFlowRate WaterFlow_sp=0.01;
  parameter Real Gain_FlowRate=10;
  parameter Real Ka_min=-1000;
  parameter Real Ka_max=-0.0001;
  .BOLT.CoolantMisc.ReducedPipe Unit_pdc1(
    Ka_set=0.02)
    annotation (Placement(transformation(extent={{16.180574390561848,28.750278012954965},{24.8249140836423,37.39461770603542}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.ReducedPipe Unit_pdc4(
    Ka_set=0.025)
    annotation (Placement(transformation(extent={{15.497808199562954,-50.16410178945995},{24.142147892643408,-41.519762096379495}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.ReducedPipe Unit_pdc2(
    Ka_set=0.018)
    annotation (Placement(transformation(extent={{17.109224225907596,6.5076874531306},{25.75356391898805,15.152027146211054}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.ReducedPipe Unit_pdc3(
    Ka_set=0.015)
    annotation (Placement(transformation(extent={{15.50878514797127,-25.37595689374099},{24.15312484105173,-16.73161720066055}},origin={0.0,0.0},rotation=0.0)));
  parameter.Modelica.SIunits.PressureDifference Pdispo=150000;
  .Workspace.Controller.SubSystems.PumpUserControl pumpUserControl1(
    is_LWT_control=LWT_fixed,
    is_EWT_control=EWT_fixed and not LWT_fixed)
    annotation (Placement(transformation(extent={{-65.53478915785603,62.745730538071854},{-51.68580406523155,76.59471563069633}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression LWT_measure(
    y=Out1.summary.T)
    annotation (Placement(transformation(extent={{-109.77946108322328,84.64587278337727},{-97.12028171574534,97.30505215085522}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression LWT_setpoint(
    y=LWT_sp)
    annotation (Placement(transformation(extent={{-107.16132265858162,50.30555996023921},{-94.72940674840483,62.737475870416006}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression ExtP_measure(
    y=Out1.summary.p)
    annotation (Placement(transformation(extent={{-109.52936685244198,93.44701263108261},{-96.87018748496403,106.10619199856056}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression ExtP_setpoint(
    y=Pdispo)
    annotation (Placement(transformation(extent={{-107.44639267153116,57.772473554295075},{-95.01447676135436,71.00743180773462}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.PumpPoly pumpPoly1(
    selector=.BOLT.InternalLibrary.Coolant.Pumps.DataBase.PumpPoly.Selector.CIE_210_4_HP)
    annotation (Placement(transformation(extent={{-31.006499721134144,26.69435799329063},{-18.67601946190838,39.024838252516396}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Shaft.Source source(
    use_speed_in=true)
    annotation (Placement(transformation(extent={{-38.23020236552709,43.051406401551894},{-30.23020236552709,51.051406401551894}},origin={0,0},rotation=0)));
  .BOLT.CoolantMisc.PumpPoly pumpPoly2(
    selector=.BOLT.InternalLibrary.Coolant.Pumps.DataBase.PumpPoly.Selector.CIE_210_4_HP)
    annotation (Placement(transformation(extent={{-30.38560581440477,15.830923313224105},{-18.055125555179004,3.500443053998339}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Shaft.Source source2(
    use_speed_in=true)
    annotation (Placement(transformation(extent={{-46.17193635849093,-7.703467089877865},{-38.17193635849093,0.29653291012214256}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Controller.SubSystems.PumpUserControl pumpUserControl2(
    is_LWT_control=LWT_fixed,
    is_EWT_control=EWT_fixed and not LWT_fixed)
    annotation (Placement(transformation(extent={{-93.81276441259116,16.171571086058893},{-79.96377931996669,30.020556178683368}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.PumpPoly pumpPoly(
    selector=.BOLT.InternalLibrary.Coolant.Pumps.DataBase.PumpPoly.Selector.CIE_210_4_HP)
    annotation (Placement(transformation(extent={{-29.497129785809996,-38.67009988432143},{-17.16664952658423,-51.0005801435472}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Shaft.Source source4(
    use_speed_in=true)
    annotation (Placement(transformation(extent={{-39.39665364885708,-66.48580423727003},{-31.396653648857082,-58.48580423727004}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.PumpPoly pumpPoly3(
    selector=.BOLT.InternalLibrary.Coolant.Pumps.DataBase.PumpPoly.Selector.CIE_210_4_HP)
    annotation (Placement(transformation(extent={{-29.41000079800657,-14.235796456375155},{-16.011990700928813,-27.633806553452914}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Shaft.Source source3(
    use_speed_in=true)
    annotation (Placement(transformation(extent={{-40.64883459518561,-36.43087692534523},{-32.64883459518561,-28.43087692534523}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Controller.SubSystems.PumpUserControl pumpUserControl3(
    is_LWT_control=LWT_fixed,
    is_EWT_control=EWT_fixed and not LWT_fixed)
    annotation (Placement(transformation(extent={{-80.88309290005283,-48.129390710077054},{-67.03410780742836,-34.28040561745258}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Controller.SubSystems.PumpUserControl pumpUserControl4(
    is_LWT_control=LWT_fixed,
    is_EWT_control=EWT_fixed and not LWT_fixed)
    annotation (Placement(transformation(extent={{-76.2484671653422,-95.67327873912191},{-62.39948207271773,-81.82429364649744}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression LWT_measure2(
    y=Out2.summary.T)
    annotation (Placement(transformation(extent={{-123.5659312168436,10.163908123491787},{-110.90675184936565,22.823087490969733}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression LWT_setpoint2(
    y=LWT_sp)
    annotation (Placement(transformation(extent={{-123.6656845512386,0.888597078136506},{-111.2337686410618,13.320512988313304}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression ExtP_setpoint2(
    y=Pdispo)
    annotation (Placement(transformation(extent={{-123.04479064450923,22.648478612233482},{-110.61287473433244,35.08039452241028}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression ExtP_measure2(
    y=Out2.summary.T)
    annotation (Placement(transformation(extent={{-123.99928345178495,34.01414667729716},{-111.340104084307,46.67332604477511}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression LWT_measure3(
    y=Out3.summary.T)
    annotation (Placement(transformation(extent={{-108.22802060751654,-52.899742973746974},{-95.5688412400386,-40.24056360626903}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression LWT_setpoint3(
    y=LWT_sp)
    annotation (Placement(transformation(extent={{-108.32777394191154,-62.175054019102255},{-95.89585803173475,-49.74313810892546}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression ExtP_setpoint3(
    y=Pdispo)
    annotation (Placement(transformation(extent={{-107.70688003518218,-40.41517248500528},{-95.27496412500538,-27.98325657482848}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression ExtP_measure3(
    y=Out3.summary.p)
    annotation (Placement(transformation(extent={{-107.07196245705634,-29.311299081277685},{-94.4127830895784,-16.65211971379974}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression LWT_measure4(
    y=Out4.summary.T)
    annotation (Placement(transformation(extent={{-103.05823062907515,-106.59801980569625},{-90.39905126159721,-93.9388404382183}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression LWT_setpoint4(
    y=LWT_sp)
    annotation (Placement(transformation(extent={{-103.15798396347016,-115.87333085105152},{-90.72606805329336,-103.44141494087472}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression ExtP_setpoint4(
    y=Pdispo)
    annotation (Placement(transformation(extent={{-102.5370900567408,-94.11344931695456},{-90.105174146564,-81.68153340677776}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression ExtP_measure4(
    y=Out4.summary.p)
    annotation (Placement(transformation(extent={{-101.59007786206395,-82.69748129667596},{-88.930898494586,-70.03830192919804}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.ReducedPipe reducedPipe(
    Ka_fixed=false)
    annotation (Placement(transformation(extent={{108.72565080211747,-42.428529430020475},{128.72565080211746,-22.428529430020475}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Node node_out(
    p_fixed=true,
    p_set=Pdispo,
    T_set=LWT_sp)
    annotation (Placement(transformation(extent={{-4.0,-4.0},{4.0,4.0}},origin={109.21836376693625,-11.690961116101946},rotation=-90.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation errorCalculation_(
    gain=Gain,
    setPoint=TargetCoolingCapacity,
    measurement=TotalCapacity)
    annotation (Placement(transformation(extent={{73,78},{65,86}},origin={0,0},rotation=0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointController(
    AV_max=100,
    AV_start=10,
    AV_min=0.0001)
    annotation (Placement(transformation(extent={{55,78},{47,86}},origin={0,0},rotation=0)));
  parameter Boolean EWT_fixed=true;
  parameter Boolean LWT_fixed=false;
  parameter Boolean Vd_fixed=true;
  .Modelica.Blocks.Sources.RealExpression EWT_measure(
    y=In1.summary.T)
    annotation (Placement(transformation(extent={{-109.72792070292611,77.73635910766164},{-96.99974030874422,88.18962602302632}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression EWT_setpoint(
    y=EWT_sp)
    annotation (Placement(transformation(extent={{-107.64840724908858,69.2156329276794},{-95.14872905063085,79.48123794883148}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression EWT_measure2(
    y=In2.summary.T)
    annotation (Placement(transformation(extent={{-148.87780399075464,24.39304107819708},{-136.14962359657275,34.84630799356176}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression EWT_setpoint2(
    y=EWT_sp)
    annotation (Placement(transformation(extent={{-149.5161822959538,14.664363005309639},{-137.01650409749607,24.929968026461722}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression EWT_setpoint3(
    y=EWT_sp)
    annotation (Placement(transformation(extent={{-131.8923266172382,-47.1346182744913},{-119.39264841878047,-36.86901325333922}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression EWT_measure3(
    y=In3.summary.T)
    annotation (Placement(transformation(extent={{-131.25394831203903,-37.70792817483016},{-118.52576791785715,-27.25466125946548}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression EWT_setpoint4(
    y=EWT_sp)
    annotation (Placement(transformation(extent={{-132.38774933210055,-102.8938400897663},{-119.88807113364282,-92.62823506861422}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression EWT_measure4(
    y=In4.summary.T)
    annotation (Placement(transformation(extent={{-131.7493710269014,-92.56118607042626},{-119.02119063271951,-82.10791915506158}},origin={0.0,0.0},rotation=0.0)));
equation
  TotalCapacity=briBriHX_Reduced_1.summary.Q_flow_H+briBriHX_Reduced_2.summary.Q_flow_H+briBriHX_Reduced_3.summary.Q_flow_H+briBriHX_Reduced_4.summary.Q_flow_H;
  connect(MainInlet.port,split.port_c)
    annotation (Line(points={{-96,1.2098437131061015},{-90.03999999999999,1.2098437131061015},{-90.03999999999999,1.92},{-84.08,1.92}},color={0,127,0}));
  connect(split.port_a,split_1.port_c)
    annotation (Line(points={{-76,4.08},{-70,4.08},{-70,16},{-70,16},{-70,27.92},{-64.08,27.92}},color={0,127,0}));
  connect(split_1.port_a,In1.port_a)
    annotation (Line(points={{-56,30.08},{-50,30.08},{-50,34},{-44,34}},color={0,127,0}));
  connect(Out1.port_b,mixer_1.port_a)
    annotation (Line(points={{59.94760414796825,32.66858358481126},{66.47837217724027,32.66858358481126},{66.47837217724027,29.87277583536449},{72.727474815075,29.87277583536449}},color={0,127,0}));
  connect(mixer_1.port_c,mixer.port_a)
    annotation (Line(points={{83.95113605270936,27.231914367685818},{87.2034643977177,27.231914367685818},{87.2034643977177,2.0226103856411015}},color={0,127,0}));
  connect(split.port_b,split_0.port_c)
    annotation (Line(points={{-76,-0.08000000000000007},{-70,-0.08000000000000007},{-70,-10.079999999999998},{-72,-10.079999999999998},{-72,-20.08},{-66.08,-20.08}},color={0,127,0}));
  connect(split_0.port_a,In3.port_a)
    annotation (Line(points={{-58,-17.92},{-51,-17.92},{-51,-20},{-44,-20}},color={0,127,0}));
  connect(Out3.port_b,mixer_0.port_a)
    annotation (Line(points={{60.229269539405564,-20.768085632314182},{65.70236584325114,-20.768085632314182},{65.70236584325114,-30.34225808400594},{71.17546214709672,-30.34225808400594}},color={0,127,0}));
  connect(mixer_0.port_c,mixer.port_b)
    annotation (Line(points={{81.48522922740676,-32.76808563231418},{87.2034643977177,-32.76808563231418},{87.2034643977177,-3.79133965176574}},color={0,127,0}));
  connect(split_0.port_b,In4.port_a)
    annotation (Line(points={{-58,-22.08},{-51,-22.08},{-51,-44},{-44,-44}},color={0,127,0}));
  connect(Out4.port_b,mixer_0.port_b)
    annotation (Line(points={{60.229269539405564,-44.76808563231418},{65.70236584325114,-44.76808563231418},{65.70236584325114,-35.39606547631478},{71.17546214709672,-35.39606547631478}},color={0,127,0}));
  connect(PdC_Cool_4.port_b,briBriHX_Reduced_4.Bri_L_in)
    annotation (Line(points={{15.126318525702068,-36},{11.061436894089624,-36},{11.061436894089624,-37.095769513688715},{6.99655526247718,-37.095769513688715}},color={0,127,0}));
  connect(briBriHX_Reduced_4.Bri_L_out,sink4.port)
    annotation (Line(points={{-7.3925866924287185,-37.095769513688715},{-11.69629334621436,-37.095769513688715},{-11.69629334621436,-36},{-16,-36}},color={0,127,0}));
  connect(briBriHX_Reduced_3.Bri_L_out,sink3.port)
    annotation (Line(points={{-7.097683403994039,-13.211624377002341},{-11.54884170199702,-13.211624377002341},{-11.54884170199702,-12},{-16,-12}},color={0,127,0}));
  connect(PdC_Cool_3.port_b,briBriHX_Reduced_3.Bri_L_in)
    annotation (Line(points={{14.59423516691438,-14},{10.655842765704369,-14},{10.655842765704369,-13.211624377002341},{6.717450364494359,-13.211624377002341}},color={0,127,0}));
  connect(split_1.port_b,In2.port_a)
    annotation (Line(points={{-56,25.92},{-50,25.92},{-50,10.535164243730826},{-44,10.535164243730826}},color={0,127,0}));
  connect(Out2.port_b,mixer_1.port_b)
    annotation (Line(points={{60.229269539405564,11.231914367685818},{66.47837217724027,11.231914367685818},{66.47837217724027,24.370981111033924},{72.727474815075,24.370981111033924}},color={0,127,0}));
  connect(PdC_Cool_2.port_b,briBriHX_Reduced_2.Bri_L_in)
    annotation (Line(points={{15.019706932884041,20},{7.32035853692309,20},{7.32035853692309,19.038639392685056}},color={0,127,0}));
  connect(briBriHX_Reduced_2.Bri_L_out,sink2.port)
    annotation (Line(points={{-7.734718454107416,19.038639392685056},{-11.867359227053708,19.038639392685056},{-11.867359227053708,20},{-16,20}},color={0,127,0}));
  connect(sink1.port,briBriHX_Reduced_1.Bri_L_out)
    annotation (Line(points={{-16,46},{-11.755061628608274,46},{-11.755061628608274,41.21798768720049},{-7.2425411353511215,41.21798768720049}},color={0,127,0}));
  connect(briBriHX_Reduced_1.Bri_L_in,PdC_Cool_1.port_b)
    annotation (Line(points={{7.375377347445372,41.21798768720049},{11.737940452267278,41.21798768720049},{11.737940452267278,40},{15.276392642957134,40}},color={0,127,0}));
  connect(PdC_Cool_1.port_a,source_1.port)
    annotation (Line(points={{24.723607357042866,40},{32.0835392823881,40},{32.0835392823881,52},{36,52}},color={0,127,0}));
  connect(briBriHX_Reduced_1.Bri_H_out,Unit_pdc1.port_a)
    annotation (Line(points={{7.442432019384805,34.44546582131771},{11.677712144040614,34.44546582131771},{11.677712144040614,33.07244785949519},{16.180574390561848,33.07244785949519}},color={0,127,0}));
  connect(briBriHX_Reduced_4.Bri_H_out,Unit_pdc4.port_a)
    annotation (Line(points={{7.062560500802436,-43.762298584539614},{11.280184350182695,-43.762298584539614},{11.280184350182695,-45.84193194291972},{15.497808199562954,-45.84193194291972}},color={0,127,0}));
  connect(briBriHX_Reduced_3.Bri_H_out,Unit_pdc3.port_a)
    annotation (Line(points={{6.780822537744305,-19.612213875246965},{11.144803842857787,-19.612213875246965},{11.144803842857787,-21.05378704720077},{15.50878514797127,-21.05378704720077}},color={0,127,0}));
  connect(briBriHX_Reduced_2.Bri_H_out,Unit_pdc2.port_a)
    annotation (Line(points={{7.389418523120478,12.063580786748904},{12.249321374514036,12.063580786748904},{12.249321374514036,10.829857299670827},{17.109224225907596,10.829857299670827}},color={0,127,0}));
  connect(PdC_Cool_4.port_a,source_4.port)
    annotation (Line(points={{24.873681474297932,-36},{28.831348005956528,-36},{28.831348005956528,-30.126925146942106},{35.464835756269196,-30.126925146942106}},color={0,127,0}));
  connect(source_3.port,PdC_Cool_3.port_a)
    annotation (Line(points={{35.7324178781346,-5.71868605015341},{29.632553929081162,-5.71868605015341},{29.632553929081162,-14},{25.40576483308562,-14}},color={0,127,0}));
  connect(source_2.port,PdC_Cool_2.port_a)
    annotation (Line(points={{35.732417878134655,25.87307485305788},{31.292892899154225,25.87307485305788},{31.292892899154225,20},{24.98029306711596,20}},color={0,127,0}));
  connect(source2.flange,pumpPoly2.flange)
    annotation (Line(points={{-38.17193635849093,-3.7034670898778614},{-24.220365684791886,-3.7034670898778614},{-24.220365684791886,3.500443053998339}},color={127,0,0}));
  connect(pumpUserControl2.actuatorSignal,source2.speed_in)
    annotation (Line(points={{-79.27133006533546,23.09606363237113},{-56.875221233107396,23.09606363237113},{-56.875221233107396,-2.10346708987786},{-46.171936358490925,-2.10346708987786}},color={0,0,127}));
  connect(In2.port_b,pumpPoly2.port_a)
    annotation (Line(points={{-36,10.535164243730826},{-33.192802907202385,10.535164243730826},{-33.192802907202385,9.665683183611222},{-30.38560581440477,9.665683183611222}},color={0,127,0}));
  connect(pumpPoly2.port_b,briBriHX_Reduced_2.Bri_H_in)
    annotation (Line(points={{-18.055125555179004,9.665683183611222},{-12.825862018445822,9.665683183611222},{-12.825862018445822,12.132640772946292},{-7.59659848171264,12.132640772946292}},color={0,127,0}));
  connect(In1.port_b,pumpPoly1.port_a)
    annotation (Line(points={{-36,34},{-31.006499721134144,34},{-31.006499721134144,32.85959812290351}},color={0,127,0}));
  connect(pumpPoly1.port_b,briBriHX_Reduced_1.Bri_H_in)
    annotation (Line(points={{-18.67601946190838,32.85959812290351},{-18.67601946190838,34.51252049325714},{-7.108431791472255,34.51252049325714}},color={0,127,0}));
  connect(source.flange,pumpPoly1.flange)
    annotation (Line(points={{-30.23020236552709,47.051406401551894},{-24.84125959152126,47.051406401551894},{-24.84125959152126,39.024838252516396}},color={127,0,0}));
  connect(LWT_measure.y,pumpUserControl1.measurementBus.T_lwt)
    annotation (Line(points={{-96.48732274737144,90.97546246711624},{-80.03021187271837,90.97546246711624},{-80.03021187271837,73.82491861217143},{-65.53478915785603,73.82491861217143}},color={0,0,127}));
  connect(LWT_setpoint.y,pumpUserControl1.limitsBus.LWT_setpoint)
    annotation (Line(points={{-94.10781095289599,56.52151791532761},{-65.53478915785603,56.52151791532761},{-65.53478915785603,65.51552755659675}},color={0,0,127}));
  connect(ExtP_setpoint.y,pumpUserControl1.limitsBus.extPressure_SetPoint)
    annotation (Line(points={{-94.39288096584552,64.38995268101485},{-65.53478915785603,64.38995268101485},{-65.53478915785603,65.51552755659675}},color={0,0,127}));
  connect(ExtP_measure.y,pumpUserControl1.measurementBus.External_Pressure)
    annotation (Line(points={{-96.23722851659014,99.77660231482159},{-80.53385390882266,99.77660231482159},{-80.53385390882266,73.82491861217143},{-65.53478915785603,73.82491861217143}},color={0,0,127}));
  connect(pumpUserControl1.actuatorSignal,source.speed_in)
    annotation (Line(points={{-50.99335481060033,69.67022308438409},{-44.477987527131006,69.67022308438409},{-44.477987527131006,48.651406401551895},{-38.23020236552709,48.651406401551895}},color={0,0,127}));
  connect(In4.port_b,pumpPoly.port_a)
    annotation (Line(points={{-36,-44},{-32.748564892904994,-44},{-32.748564892904994,-44.835340013934314},{-29.497129785809996,-44.835340013934314}},color={0,127,0}));
  connect(pumpPoly.port_b,briBriHX_Reduced_4.Bri_H_in)
    annotation (Line(points={{-17.16664952658423,-44.835340013934314},{-12.213612871181217,-44.835340013934314},{-12.213612871181217,-43.69629334621436},{-7.2605762157782054,-43.69629334621436}},color={0,127,0}));
  connect(In3.port_b,pumpPoly3.port_a)
    annotation (Line(points={{-36,-20},{-32.70500039900328,-20},{-32.70500039900328,-20.934801504914034},{-29.41000079800657,-20.934801504914034}},color={0,127,0}));
  connect(pumpPoly3.port_b,briBriHX_Reduced_3.Bri_H_in)
    annotation (Line(points={{-16.011990700928813,-20.934801504914034},{-11.49146487921148,-20.934801504914034},{-11.49146487921148,-19.54884170199702},{-6.970939057494145,-19.54884170199702}},color={0,127,0}));
  connect(source3.flange,pumpPoly3.flange)
    annotation (Line(points={{-32.648834595185605,-32.43087692534523},{-22.710995749467685,-32.43087692534523},{-22.710995749467685,-27.633806553452914}},color={127,0,0}));
  connect(source4.flange,pumpPoly.flange)
    annotation (Line(points={{-31.396653648857082,-62.48580423727004},{-23.331889656197113,-62.48580423727004},{-23.331889656197113,-51.0005801435472}},color={127,0,0}));
  connect(pumpUserControl4.actuatorSignal,source4.speed_in)
    annotation (Line(points={{-61.70703281808651,-88.74878619280967},{-50.284261111606384,-88.74878619280967},{-50.284261111606384,-60.88580423727003},{-39.39665364885708,-60.88580423727003}},color={0,0,127}));
  connect(pumpUserControl3.actuatorSignal,source3.speed_in)
    annotation (Line(points={{-66.34165855279713,-41.20489816376482},{-58.98068007223232,-41.20489816376482},{-58.98068007223232,-30.83087692534523},{-40.64883459518561,-30.83087692534523}},color={0,0,127}));
  connect(ExtP_measure3.y,pumpUserControl3.measurementBus.External_Pressure)
    annotation (Line(points={{-93.7798241212045,-22.98170939753871},{-87.33145851062866,-22.98170939753871},{-87.33145851062866,-37.05020263597747},{-80.88309290005283,-37.05020263597747}},color={0,0,127}));
  connect(ExtP_setpoint3.y,pumpUserControl3.limitsBus.extPressure_SetPoint)
    annotation (Line(points={{-94.65336832949654,-34.19921452991688},{-87.7682306147747,-34.19921452991688},{-87.7682306147747,-45.35959369155216},{-80.88309290005283,-45.35959369155216}},color={0,0,127}));
  connect(LWT_measure3.y,pumpUserControl3.measurementBus.T_lwt)
    annotation (Line(points={{-94.9358822716647,-46.570153290008},{-87.90948758585876,-46.570153290008},{-87.90948758585876,-37.05020263597747},{-80.88309290005283,-37.05020263597747}},color={0,0,127}));
  connect(LWT_setpoint3.y,pumpUserControl3.limitsBus.LWT_setpoint)
    annotation (Line(points={{-95.27426223622591,-55.959096064013856},{-88.07867756813937,-55.959096064013856},{-88.07867756813937,-45.35959369155216},{-80.88309290005283,-45.35959369155216}},color={0,0,127}));
  connect(LWT_setpoint2.y,pumpUserControl2.limitsBus.LWT_setpoint)
    annotation (Line(points={{-110.61217284555296,7.104555033224905},{-102.21246862907206,7.104555033224905},{-102.21246862907206,18.941368104583788},{-93.81276441259116,18.941368104583788}},color={0,0,127}));
  connect(ExtP_setpoint2.y,pumpUserControl2.limitsBus.extPressure_SetPoint)
    annotation (Line(points={{-109.9912789388236,28.86443656732188},{-101.90202167570737,28.86443656732188},{-101.90202167570737,18.941368104583788},{-93.81276441259116,18.941368104583788}},color={0,0,127}));
  connect(LWT_measure2.y,pumpUserControl2.measurementBus.T_lwt)
    annotation (Line(points={{-110.27379288099175,16.49349780723076},{-102.04327864679146,16.49349780723076},{-102.04327864679146,27.250759160158474},{-93.81276441259116,27.250759160158474}},color={0,0,127}));
  connect(ExtP_measure2.y,pumpUserControl2.measurementBus.External_Pressure)
    annotation (Line(points={{-110.7071451159331,40.343736361036136},{-101.46524957156136,40.343736361036136},{-101.46524957156136,27.250759160158474},{-93.81276441259116,27.250759160158474}},color={0,0,127}));
  connect(ExtP_measure4.y,pumpUserControl4.measurementBus.External_Pressure)
    annotation (Line(points={{-88.2979395262121,-76.367891612937},{-82.42925065405265,-76.367891612937},{-82.42925065405265,-84.59409066502234},{-76.2484671653422,-84.59409066502234}},color={0,0,127}));
  connect(ExtP_setpoint4.y,pumpUserControl4.limitsBus.extPressure_SetPoint)
    annotation (Line(points={{-89.48357835105516,-87.89749136186616},{-82.86602275819868,-87.89749136186616},{-82.86602275819868,-92.90348172059701},{-76.2484671653422,-92.90348172059701}},color={0,0,127}));
  connect(LWT_measure4.y,pumpUserControl4.measurementBus.T_lwt)
    annotation (Line(points={{-89.76609229322331,-100.26843012195728},{-83.00727972928276,-100.26843012195728},{-83.00727972928276,-84.59409066502234},{-76.2484671653422,-84.59409066502234}},color={0,0,127}));
  connect(LWT_setpoint4.y,pumpUserControl4.limitsBus.LWT_setpoint)
    annotation (Line(points={{-90.10447225778452,-109.65737289596312},{-83.17646971156336,-109.65737289596312},{-83.17646971156336,-92.90348172059701},{-76.2484671653422,-92.90348172059701}},color={0,0,127}));
  connect(Unit_pdc4.port_b,Out4.port_a)
    annotation (Line(points={{24.142147892643408,-45.84193194291972},{44.78809064891939,-45.84193194291972},{44.78809064891939,-44.76808563231418},{52.229269539405564,-44.76808563231418}},color={0,127,0}));
  connect(Unit_pdc3.port_b,Out3.port_a)
    annotation (Line(points={{24.15312484105173,-21.05378704720077},{44.79357912312356,-21.05378704720077},{44.79357912312356,-20.768085632314182},{52.229269539405564,-20.768085632314182}},color={0,127,0}));
  connect(Unit_pdc1.port_b,Out1.port_a)
    annotation (Line(points={{24.8249140836423,33.07244785949519},{44.98864104870018,33.07244785949519},{44.98864104870018,32.668583584811266},{51.94760414796825,32.668583584811266}},color={0,127,0}));
  connect(Unit_pdc2.port_b,Out2.port_a)
    annotation (Line(points={{25.75356391898805,10.829857299670827},{45.593798662091714,10.829857299670827},{45.593798662091714,11.231914367685818},{52.229269539405564,11.231914367685818}},color={0,127,0}));
  connect(mixer.port_c,node_out.port_a)
    annotation (Line(points={{99.06392247402765,-0.7680856323141825},{109.21836376693625,-0.7680856323141825},{109.21836376693625,-7.6909611161019455}},color={0,127,0}));
  connect(node_out.port_b,reducedPipe.port_a)
    annotation (Line(points={{109.21836376693625,-15.690961116101946},{109.21836376693625,-21.690961116101946},{102.02153967746284,-21.690961116101946},{102.02153967746284,-32.428529430020475},{108.72565080211747,-32.428529430020475}},color={0,127,0}));
  connect(reducedPipe.port_b,MainOutlet.port)
    annotation (Line(points={{128.72565080211746,-32.428529430020475},{133.2541312175091,-32.428529430020475},{133.2541312175091,-37.4738113224593},{138.1985681752938,-37.4738113224593}},color={0,127,0}));
  connect(errorCalculation_.sensor,setpointController.errorSignal)
    annotation (Line(points={{65.08,81.68},{59,81.68},{59,82},{55,82}},color={28,108,200}));
  connect(setpointController.actuatorSignal,PdC_Cool_1.Ka_in)
    annotation (Line(points={{46.76,82},{21.700498648535433,82},{21.700498648535433,44.534663062761155}},color={0,0,127}));
  connect(setpointController.actuatorSignal,PdC_Cool_2.Ka_in)
    annotation (Line(points={{46.76,82},{21.792905504161745,82},{21.792905504161745,24.78108134443133}},color={0,0,127}));
  connect(setpointController.actuatorSignal,PdC_Cool_3.Ka_in)
    annotation (Line(points={{46.76,82},{21.946075339910823,82},{21.946075339910823,-8.810465760237804}},color={0,0,127}));
  connect(setpointController.actuatorSignal,PdC_Cool_4.Ka_in)
    annotation (Line(points={{46.76,82},{21.754525330747256,82},{21.754525330747256,-31.321265784673983}},color={0,0,127}));
  connect(EWT_setpoint.y,pumpUserControl1.limitsBus.EWT_setpoint)
    annotation (Line(points={{-94.52374514070796,74.34843543825544},{-94.52374514070796,65.51552755659675},{-65.53478915785603,65.51552755659675}},color={0,0,127}));
  connect(EWT_measure.y,pumpUserControl1.measurementBus.T_ewt)
    annotation (Line(points={{-96.36333128903513,82.96299256534398},{-80.35796870804131,82.96299256534398},{-80.35796870804131,73.82491861217143},{-65.53478915785603,73.82491861217143}},color={0,0,127}));
  connect(EWT_measure2.y,pumpUserControl2.measurementBus.T_ewt)
    annotation (Line(points={{-135.51321457686365,29.61967453587942},{-114.66298949472741,29.61967453587942},{-114.66298949472741,27.25075916015847},{-93.81276441259116,27.25075916015847}},color={0,0,127}));
  connect(EWT_setpoint2.y,pumpUserControl2.limitsBus.EWT_setpoint)
    annotation (Line(points={{-136.39152018757318,19.79716551588568},{-115.10214230008216,19.79716551588568},{-115.10214230008216,18.941368104583784},{-93.81276441259116,18.941368104583784}},color={0,0,127}));
  connect(EWT_measure3.y,pumpUserControl3.measurementBus.T_ewt)
    annotation (Line(points={{-117.88935889814805,-32.48129471714782},{-99.38622589910044,-32.48129471714782},{-99.38622589910044,-37.05020263597747},{-80.88309290005283,-37.05020263597747}},color={0,0,127}));
  connect(EWT_setpoint3.y,pumpUserControl3.limitsBus.EWT_setpoint)
    annotation (Line(points={{-118.76766450885759,-42.00181576391526},{-99.8253787044552,-42.00181576391526},{-99.8253787044552,-45.35959369155215},{-80.88309290005283,-45.35959369155215}},color={0,0,127}));
  connect(EWT_measure4.y,pumpUserControl4.measurementBus.T_ewt)
    annotation (Line(points={{-118.38478161301042,-87.33455261274392},{-97.31662438917631,-87.33455261274392},{-97.31662438917631,-84.59409066502234},{-76.2484671653422,-84.59409066502234}},color={0,0,127}));
  connect(EWT_setpoint4.y,pumpUserControl4.limitsBus.EWT_setpoint)
    annotation (Line(points={{-119.26308722371994,-97.76103757919026},{-97.75577719453108,-97.76103757919026},{-97.75577719453108,-92.90348172059701},{-76.2484671653422,-92.90348172059701}},color={0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end control_Pump;
