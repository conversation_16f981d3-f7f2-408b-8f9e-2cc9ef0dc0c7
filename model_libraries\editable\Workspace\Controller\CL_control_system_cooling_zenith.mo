within Workspace.Controller;
model CL_control_system_cooling_zenith
  .Workspace.Controller.Controller_1C_cooling controller_crkA
    annotation (Placement(transformation(extent={{-70.91004239405333,-3.615720975156578},{-50.910042394053335,16.384279024843423}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Controller.ControllerSettings_cooling controllerSettings_crkA
    annotation (Placement(transformation(extent={{-83.0342640278964,31.365063591079974},{-103.0342640278964,51.36506359107997}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Controller.ControllerSettings_cooling controllerSettings_crkB
    annotation (Placement(transformation(extent={{51.33512311382792,31.533603273047675},{71.33512311382792,51.533603273047675}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Controller.Controller_1C_cooling controller_crkB
    annotation (Placement(transformation(extent={{41.87759675683532,-3.233652961145765},{21.877596756835317,16.766347038854235}},origin={0.0,0.0},rotation=0.0)));
equation
  connect(controller_crkA.measurementBus,controllerSettings_crkA.measurementBus)
    annotation (Line(points={{-70.91004239405333,13.384279024843423},{-77.99054515188132,13.384279024843423},{-77.99054515188132,40.82205673936891},{-81.48150413305495,40.82205673936891}},color={255,204,51}));
  connect(controller_crkB.measurementBus,controllerSettings_crkB.measurementBus)
    annotation (Line(points={{41.87759675683532,13.766347038854239},{49.78236321898647,13.766347038854239},{49.78236321898647,40.99059642133661}},color={255,204,51}));
  connect(controller_crkA.limitsBus,controllerSettings_crkA.limitsBus)
    annotation (Line(points={{-70.91004239405333,1.3842790248434218},{-111.49050485420159,1.3842790248434218},{-111.49050485420159,38.344247192157866},{-101.81620868821133,38.344247192157866}},color={255,204,51}));
  connect(controller_crkB.limitsBus,controllerSettings_crkB.limitsBus)
    annotation (Line(points={{41.87759675683532,1.7663470388542386},{75.30703742757396,1.7663470388542386},{75.30703742757396,38.533603273047675},{70.13512311382792,38.533603273047675}},color={255,204,51}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end CL_control_system_cooling_zenith;
