within Workspace.Controller.Tests;
model Test_control_2C_cooling
  replaceable.Workspace.Controller.Controller_1C_cooling controllerBase_1C
    constrainedby.Workspace.Controller.SubSystems.BaseClasses.ControllerBase_1C
    annotation (Placement(transformation(extent={{-31.66012466015556,-33.66012466015556},{-11.66012466015556,-13.660124660155557}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression SST_A
    annotation (Placement(transformation(extent={{-80.0,54.0},{-60.0,74.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression ssh_A
    annotation (Placement(transformation(extent={{-80.0,38.0},{-60.0,58.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression SDT_A
    annotation (Placement(transformation(extent={{-80.0,22.34712768663706},{-60.0,41.65287231336294}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression Capacity_A
    annotation (Placement(transformation(extent={{-80.0,-6.0},{-60.0,14.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression DGT_A
    annotation (Placement(transformation(extent={{-80.0,8.0},{-60.0,28.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression SST_max
    annotation (Placement(transformation(extent={{-144.0,-11.2},{-124.0,8.8}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression SDTmax_limit_comp
    annotation (Placement(transformation(extent={{-144.0,-26.0},{-124.0,-6.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression SDT_min
    annotation (Placement(transformation(extent={{-146.0,-68.85287231336295},{-126.0,-49.547127686637076}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression DGT_max_limit_comp
    annotation (Placement(transformation(extent={{-146.0,-99.0},{-126.0,-79.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression SST_min
    annotation (Placement(transformation(extent={{-146.0,-113.0},{-126.0,-93.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression SDTmax_limit_fan
    annotation (Placement(transformation(extent={{-146.0,-40.0},{-126.0,-20.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression DGT_max_limit_fan
    annotation (Placement(transformation(extent={{-146.0,-82.0},{-126.0,-62.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression fanspeed_setpoint
    annotation (Placement(transformation(extent={{-140.0,30.0},{-120.0,50.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression capacity_setpoint
    annotation (Placement(transformation(extent={{-140.0,44.0},{-120.0,64.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression ssh_setpoint
    annotation (Placement(transformation(extent={{-140.0,14.0},{-120.0,34.0}},origin={0.0,0.0},rotation=0.0)));
equation
  connect(ssh_A.y,controllerBase_1C.measurementBus_crkA.dT_ssh)
    annotation (Line(points={{-59,48},{-46,48},{-46,-17},{-32,-17}},color={0,0,127}));
  connect(SST_A.y,controllerBase_1C.measurementBus_crkA.T_sst)
    annotation (Line(points={{-59,64},{-46,64},{-46,-17},{-32,-17}},color={0,0,127}));
  connect(SDT_A.y,controllerBase_1C.measurementBus_crkA.T_sdt)
    annotation (Line(points={{-59,32},{-46,32},{-46,-17},{-32,-17}},color={0,0,127}));
  connect(DGT_A.y,controllerBase_1C.measurementBus_crkA.T_dgt)
    annotation (Line(points={{-59,18},{-46,18},{-46,-17},{-32,-17}},color={0,0,127}));
  connect(Capacity_A.y,controllerBase_1C.measurementBus_crkA.capacity)
    annotation (Line(points={{-59,4},{-46,4},{-46,-17},{-32,-17}},color={0,0,127}));
  connect(capacity_setpoint.y,controllerBase_1C.limitsBus.capacity_setpoint)
    annotation (Line(points={{-119,54},{-75.5,54},{-75.5,-20.660124660155557},{-31.66012466015556,-20.660124660155557}},color={0,0,127}));
  connect(fanspeed_setpoint.y,controllerBase_1C.limitsBus.fanSpeed_setpoint)
    annotation (Line(points={{-119,40},{-75.5,40},{-75.5,-20.660124660155557},{-31.66012466015556,-20.660124660155557}},color={0,0,127}));
  connect(ssh_setpoint.y,controllerBase_1C.limitsBus.dT_ssh_setpoint)
    annotation (Line(points={{-119,24},{-75.5,24},{-75.5,-20.660124660155557},{-31.66012466015556,-20.660124660155557}},color={0,0,127}));
  connect(SST_max.y,controllerBase_1C.limitsBus.T_sst_max_limit_EXV)
    annotation (Line(points={{-123,-1.1999999999999993},{-77.5,-1.1999999999999993},{-77.5,-20.660124660155557},{-31.66012466015556,-20.660124660155557}},color={0,0,127}));
  connect(SDTmax_limit_comp.y,controllerBase_1C.limitsBus.T_sdt_max_limit_comp)
    annotation (Line(points={{-123,-16},{-77.5,-16},{-77.5,-20.660124660155557},{-31.66012466015556,-20.660124660155557}},color={0,0,127}));
  connect(SDTmax_limit_fan.y,controllerBase_1C.limitsBus.T_sdt_max_limit_fan)
    annotation (Line(points={{-125,-30},{-78.5,-30},{-78.5,-20.660124660155557},{-31.66012466015556,-20.660124660155557}},color={0,0,127}));
  connect(SDT_min.y,controllerBase_1C.limitsBus.T_sdt_min_limit)
    annotation (Line(points={{-125,-59.20000000000002},{-78.5,-59.20000000000002},{-78.5,-20.660124660155557},{-31.66012466015556,-20.660124660155557}},color={0,0,127}));
  connect(DGT_max_limit_fan.y,controllerBase_1C.limitsBus.T_dgt_max_limit_fan)
    annotation (Line(points={{-125,-72},{-78.5,-72},{-78.5,-20.660124660155557},{-31.66012466015556,-20.660124660155557}},color={0,0,127}));
  connect(DGT_max_limit_comp.y,controllerBase_1C.limitsBus.T_dgt_max_limit_comp)
    annotation (Line(points={{-125,-89},{-78.5,-89},{-78.5,-20.660124660155557},{-31.66012466015556,-20.660124660155557}},color={0,0,127}));
  connect(SST_min.y,controllerBase_1C.limitsBus.T_sst_min_limit_comp)
    annotation (Line(points={{-125,-103},{-78.5,-103},{-78.5,-20.660124660155557},{-31.66012466015556,-20.660124660155557}},color={0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end Test_control_2C_cooling;
