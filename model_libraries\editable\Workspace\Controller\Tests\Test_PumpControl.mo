within Workspace.Controller.Tests;
model Test_PumpControl
  .Workspace.Controller.SubSystems.PumpUserControl pumpUserControl(
    is_LWT_control=false)
    annotation (Placement(transformation(extent={{-25.150339476236667,5.897187196896223},{-5.150339476236667,25.897187196896223}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression LWT_measure(
    y=LWT_output)
    annotation (Placement(transformation(extent={{-63.34626576139669,26.80892337536372},{-43.34626576139669,46.80892337536372}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression LWT_sp(
    y=LWT_input)
    annotation (Placement(transformation(extent={{-63.559650824442286,0.13579049466537896},{-43.559650824442286,20.13579049466538}},origin={0.0,0.0},rotation=0.0)));
  parameter.Modelica.SIunits.Temperature LWT_input=581.3;
  parameter.Modelica.SIunits.Temperature LWT_output=582.3;
  .Modelica.Blocks.Sources.RealExpression ExtP_measure(
    y=ExtP_output)
    annotation (Placement(transformation(extent={{-62.88574445717426,15.777870285417404},{-44.23355719171031,32.62271167384543}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression ExtP_sp(
    y=ExtP_input)
    annotation (Placement(transformation(extent={{-63.773035887487865,-12.468163912208585},{-43.773035887487865,5.108319101345355}},origin={0.0,0.0},rotation=0.0)));
  parameter.Modelica.SIunits.Temperature ExtP_input=150273.15;
  parameter.Modelica.SIunits.Temperature ExtP_output=140273.15;
equation
  connect(LWT_measure.y,pumpUserControl.measurementBus.T_lwt)
    annotation (Line(points={{-42.34626576139669,36.80892337536372},{-33.32153249272551,36.80892337536372},{-33.32153249272551,21.897187196896223},{-25.150339476236667,21.897187196896223}},color={0,0,127}));
  connect(LWT_sp.y,pumpUserControl.limitsBus.LWT_setpoint)
    annotation (Line(points={{-42.559650824442286,10.135790494665379},{-33.85499515033948,10.135790494665379},{-33.85499515033948,9.897187196896223},{-25.150339476236667,9.897187196896223}},color={0,0,127}));
  connect(ExtP_measure.y,pumpUserControl.measurementBus.External_Pressure)
    annotation (Line(points={{-43.30094782843712,24.20029097963142},{-34.22564365233689,24.20029097963142},{-34.22564365233689,21.897187196896223},{-25.150339476236667,21.897187196896223}},color={0,0,127}));
  connect(ExtP_sp.y,pumpUserControl.limitsBus.extPressure_SetPoint)
    annotation (Line(points={{-42.773035887487865,-3.679922405431615},{-33.961687681862266,-3.679922405431615},{-33.961687681862266,9.897187196896223},{-25.150339476236667,9.897187196896223}},color={0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end Test_PumpControl;
