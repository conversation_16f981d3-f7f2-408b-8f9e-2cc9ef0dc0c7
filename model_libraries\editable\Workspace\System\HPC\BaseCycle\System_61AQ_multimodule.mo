within Workspace.System.HPC.BaseCycle;
model System_61AQ_multimodule
  extends.Workspace.Controller.CL_control_system_cooling_zenith(
    controllerSettings_crkA(
      Capacity_setpoint=ECAT.TargetCoolingCapacity_W.setPoint,
      SST_min=274.15,
      fancoefficients=Module.fanCurveCoefficientsCooling[1,:],
      extPressure_setpoint=ECAT.ExternalSystemPressureDrop_Pa.setPoint,
      maxfanfreq=Module.max_fan_frequency[1]),
    controllerSettings_crkB(
      Capacity_setpoint=ECAT.TargetCoolingCapacity_W.setPoint,
      SST_min=274.15,
      fancoefficients=Module.fanCurveCoefficientsCooling[2,:],
      extPressure_setpoint=ECAT.ExternalSystemPressureDrop_Pa.setPoint,
      maxfanfreq=Module.max_fan_frequency[2]),
    controller_crkA(
      isOff=isOFFA,
      isOffSDTmin_fan=false,
      isOffSDTmax_fan=false,
      isOffDGTmax_fan=false,
      isOffSSTmin_EXV=false,
      isOffSSTmax_EXV=false,
      isOffDSHmin_EXV=false,
      isOffDGTmax_EXV=false,
      isOffSSTmin_comp=false,
      isOffSDTmax_comp=false,
      isOffDGTmax_comp=false,
      Fan_MaxFrequency=controllerSettings_crkA.maxfanfreq,
      completeCompressorControl_base(
        compressorControl(
          manualOff=controller_crkA.manualOff_compressor_block_A,
          AV_value_off=controller_crkA.frq_comp_sp_manual_block_A)),
      Use_pump=Module.Use_pump,
      fanControl(
        manualOff=controller_crkA.manualOff_fan_block_A,
        AV_value_off=controller_crkA.frq_fan_sp_manual_block_A),
      manualOff_fan_block_A=false,
      manualOff_fan_block_B=false,
      manualOff_compressor_block_A=false,
      manualOff_compressor_block_B=false,
      Fan_MinFrequency=controllerSettings_crkA.minfanfreq,
      min_speed=Module.fmin[1],
      max_speed=Module.fmax[1],
      load_ratio=ECAT.LoadRatio_nd.setPoint,
      is_load_ratio=ECAT.LoadRatio_nd.fixed),
    controller_crkB(
      isOff=Module.is_monobloc or isOFFB,
      isOffSDTmin_fan=false,
      isOffSDTmax_fan=false,
      isOffDGTmax_fan=false,
      isOffSSTmin_EXV=false,
      isOffSSTmax_EXV=false,
      isOffDSHmin_EXV=false,
      isOffDGTmax_EXV=false,
      isOffSSTmin_comp=false,
      isOffSDTmax_comp=false,
      isOffDGTmax_comp=false,
      Fan_MaxFrequency=controllerSettings_crkB.maxfanfreq,
      Use_pump=Module.Use_pump,
      fanControl(
        manualOff=controller_crkB.manualOff_fan_block_B,
        AV_value_off=controller_crkB.frq_fan_sp_manual_block_B),
      completeCompressorControl_base(
        compressorControl(
          manualOff=controller_crkB.manualOff_compressor_block_B,
          AV_value_off=controller_crkB.frq_comp_sp_manual_block_B)),
      Fan_MinFrequency=controllerSettings_crkB.minfanfreq,
      min_speed=Module.fmin[2],
      max_speed=Module.fmax[2],
      load_ratio=ECAT.LoadRatio_nd.setPoint,
      is_load_ratio=ECAT.LoadRatio_nd.fixed));
  .Workspace.System.HPC.BaseCycle.Equipement Module(
    CoolantMedium=CoolantMedium,
    isOFFB=isOFFB,
    isOFFA=isOFFA,
    OAT=OAT,
    LWT=LWT,
    EWT=EWT,
    Use_pump=choiceBlock.is_Pump,
    capacity_design={choiceBlock.Unit_Block_A.capacity_design,choiceBlock.Unit_Block_B.capacity_design},
    fmax={choiceBlock.Unit_Block_A.fmax,choiceBlock.Unit_Block_B.fmax},
    fmin={choiceBlock.Unit_Block_A.fmin,choiceBlock.Unit_Block_B.fmin},
    Dport_coolant_a={choiceBlock.Unit_Block_A.cond_diameter_brine_port_in,choiceBlock.Unit_Block_B.cond_diameter_brine_port_in},
    Dport_coolant_b={choiceBlock.Unit_Block_A.cond_diameter_brine_port_out,choiceBlock.Unit_Block_B.cond_diameter_brine_port_out},
    Dport_ref_a={choiceBlock.Unit_Block_A.cond_diameter_ref_port_in,choiceBlock.Unit_Block_B.cond_diameter_ref_port_in},
    Dport_ref_b={choiceBlock.Unit_Block_A.cond_diameter_ref_port_out,choiceBlock.Unit_Block_B.cond_diameter_ref_port_out},
    nPlate={choiceBlock.Unit_Block_A.nplate,choiceBlock.Unit_Block_B.nplate},
    nCoils={choiceBlock.Unit_Block_A.nCoils,choiceBlock.Unit_Block_B.nCoils},
    Itube={choiceBlock.Unit_Block_A.Itube,choiceBlock.Unit_Block_B.Itube},
    nCir={choiceBlock.Unit_Block_A.nCir,choiceBlock.Unit_Block_B.nCir},
    Ntube={choiceBlock.Unit_Block_A.Ntube,choiceBlock.Unit_Block_B.Ntube},
    Nrow={choiceBlock.Unit_Block_A.Nrow,choiceBlock.Unit_Block_B.Nrow},
    Ltube={choiceBlock.Unit_Block_A.Ltube,choiceBlock.Unit_Block_B.Ltube},
    Dotube={choiceBlock.Unit_Block_A.Dotube,choiceBlock.Unit_Block_B.Dotube},
    Ttube={choiceBlock.Unit_Block_A.Ttube,choiceBlock.Unit_Block_B.Ttube},
    Ptube={choiceBlock.Unit_Block_A.Ptube,choiceBlock.Unit_Block_B.Ptube},
    Prow={choiceBlock.Unit_Block_A.Prow,choiceBlock.Unit_Block_B.Prow},
    Dfin={choiceBlock.Unit_Block_A.Dfin,choiceBlock.Unit_Block_B.Dfin},
    Tfin={choiceBlock.Unit_Block_A.Tfin,choiceBlock.Unit_Block_B.Tfin},
    Fw_fan={choiceBlock.Unit_Block_A.Fan_FW,choiceBlock.Unit_Block_B.Fan_FW},
    max_fan_frequency={choiceBlock.Unit_Block_A.max_fan_frequency,choiceBlock.Unit_Block_B.max_fan_frequency},
    Suction_line_diameter={choiceBlock.Unit_Block_A.Suction_line_diameter,choiceBlock.Unit_Block_B.Suction_line_diameter},
    Suction_line_length={choiceBlock.Unit_Block_A.Suction_line_length,choiceBlock.Unit_Block_B.Suction_line_length},
    coil_line_diameter={choiceBlock.Unit_Block_A.Coil_line_diameter,choiceBlock.Unit_Block_B.Coil_line_diameter},
    coil_line_length={choiceBlock.Unit_Block_A.Coil_line_length,choiceBlock.Unit_Block_B.Coil_line_length},
    liquid_line_diameter={choiceBlock.Unit_Block_A.Liquid_line_diameter,choiceBlock.Unit_Block_B.Liquid_line_diameter},
    liquid_line_length={choiceBlock.Unit_Block_A.Liquid_line_length,choiceBlock.Unit_Block_B.Liquid_line_length},
    discharge_line_diameter={choiceBlock.Unit_Block_A.discharge_line_diameter,choiceBlock.Unit_Block_B.discharge_line_diameter},
    discharge_line_length={choiceBlock.Unit_Block_A.discharge_line_length,choiceBlock.Unit_Block_B.discharge_line_length},
    EXV_in_line_diameter={choiceBlock.Unit_Block_A.EXV_in_line_diameter,choiceBlock.Unit_Block_B.EXV_in_line_diameter},
    EXV_in_line_length={choiceBlock.Unit_Block_A.EXV_in_line_length,choiceBlock.Unit_Block_B.EXV_in_line_length},
    Ac_duct={choiceBlock.Unit_Block_A.duct_Ac,choiceBlock.Unit_Block_B.duct_Ac},
    Ka_duct={choiceBlock.Unit_Block_A.duct_Ka,choiceBlock.Unit_Block_B.duct_Ka},
    UA_duct={choiceBlock.Unit_Block_A.duct_UA,choiceBlock.Unit_Block_B.duct_UA},
    selector_Comp={choiceBlock.Unit_Block_A.selector_Comp,choiceBlock.Unit_Block_B.selector_Comp},
    EXV_main={choiceBlock.Unit_Block_A.EXV_main_A,choiceBlock.Unit_Block_B.EXV_main_A},
    selector_geo_BPHE={choiceBlock.Unit_Block_A.cond_select_geo,choiceBlock.Unit_Block_B.cond_select_geo},
    selector_pump={choiceBlock.Unit_Block_A.Pump_type,choiceBlock.Unit_Block_B.Pump_type},
    fanCurveCoefficientsHeating={choiceBlock.Unit_Block_A.fanCurveCoefficientsHeating,choiceBlock.Unit_Block_B.fanCurveCoefficientsHeating},
    CompVoltage={choiceBlock.Unit_Block_A.CompVoltage,choiceBlock.Unit_Block_B.CompVoltage},
    is_monobloc=choiceBlock.is_monobloc,
    fanCurveCoefficientsCooling={choiceBlock.Unit_Block_A.fanCurveCoefficientsCooling,choiceBlock.Unit_Block_B.fanCurveCoefficientsCooling},
    Use_EN=Use_EN14511,
    use_bf=use_bf,
    BrineConcentration=BrineConcentration,
    EvapFoulingFactor=ECAT.EvapFoulingFactor_m2KW.setPoint,
    mdot_start=Module.capacity_design[1]/(((4180*(Module.EWT-Module.LWT)))),
    isCoating=choiceBlock.isCoatingOption,
    relative_humidity=ECAT.AmbientAirRH_nd.setPoint,
    BlockA(
      systemVariables(
        mRef_set={ECAT.RefrigerantCharge_kg[1].setPoint},
        mRef_fixed={ECAT.RefrigerantCharge_kg[1].fixed}),
      node_liquid(
        dTsh_fixed=not ECAT.RefrigerantCharge_kg[1].fixed),
      compressor(
        isLoopBreaker=true)),
    BlockB(
      systemVariables(
        mRef_set={ECAT.RefrigerantCharge_kg[2].setPoint},
        mRef_fixed={ECAT.RefrigerantCharge_kg[2].fixed}),
      node_liquid(
        dTsh_fixed=not ECAT.RefrigerantCharge_kg[2].fixed)),
    PDC_4WV={choiceBlock.Unit_Block_A.PDC_4WV,choiceBlock.Unit_Block_B.PDC_4WV},
    Zevap_HPH={choiceBlock.Unit_Block_A.Zevap_HPH,choiceBlock.Unit_Block_B.Zevap_HPH},
    Zevap_coated_HPH={choiceBlock.Unit_Block_A.Zevap_coated_HPH,choiceBlock.Unit_Block_B.Zevap_coated_HPH},
    Zevap_A_HPC={choiceBlock.Unit_Block_A.Zevap_A_HPC,choiceBlock.Unit_Block_B.Zevap_A_HPC},
    Zevap_B_HPC={choiceBlock.Unit_Block_A.Zevap_B_HPC,choiceBlock.Unit_Block_B.Zevap_B_HPC},
    Zcond_A_HPH={choiceBlock.Unit_Block_A.Zcond_A_HPH,choiceBlock.Unit_Block_B.Zcond_A_HPH},
    Zcond_B_HPH={choiceBlock.Unit_Block_A.Zcond_B_HPH,choiceBlock.Unit_Block_B.Zcond_B_HPH},
    Zcond_A_HPC={choiceBlock.Unit_Block_A.Zcond_A_HPC,choiceBlock.Unit_Block_B.Zcond_A_HPC},
    Zcond_B_HPC={choiceBlock.Unit_Block_A.Zcond_B_HPC,choiceBlock.Unit_Block_B.Zcond_B_HPC},
    Zflow_intercept={choiceBlock.Unit_Block_A.Zflow_intercept,choiceBlock.Unit_Block_B.Zflow_intercept},
    Zflow_Ncomp={choiceBlock.Unit_Block_A.Zflow_Ncomp,choiceBlock.Unit_Block_B.Zflow_Ncomp},
    Zflow_SST={choiceBlock.Unit_Block_A.Zflow_SST,choiceBlock.Unit_Block_B.Zflow_SST},
    Zflow_SDT={choiceBlock.Unit_Block_A.Zflow_SDT,choiceBlock.Unit_Block_B.Zflow_SDT},
    Zflow_OAT={choiceBlock.Unit_Block_A.Zflow_OAT,choiceBlock.Unit_Block_B.Zflow_OAT},
    Zpower_intercept={choiceBlock.Unit_Block_A.Zpower_intercept,choiceBlock.Unit_Block_B.Zpower_intercept},
    Zpower_Ncomp={choiceBlock.Unit_Block_A.Zpower_Ncomp,choiceBlock.Unit_Block_B.Zpower_Ncomp},
    Zpower_OAT={choiceBlock.Unit_Block_A.Zpower_OAT,choiceBlock.Unit_Block_B.Zpower_OAT},
    Zpower_SDT={choiceBlock.Unit_Block_A.Zpower_SDT,choiceBlock.Unit_Block_B.Zpower_SDT},
    Zpower_SST={choiceBlock.Unit_Block_A.Zpower_SST,choiceBlock.Unit_Block_B.Zpower_SST},
    Zpower_Zflow={choiceBlock.Unit_Block_A.Zpower_Zflow,choiceBlock.Unit_Block_B.Zpower_Zflow},
    FW={choiceBlock.Unit_Block_A.FW,choiceBlock.Unit_Block_B.FW},
    use_Calib=use_Calib,
    external_system(
      isOff=true))
    annotation (Placement(transformation(extent={{-24.0,8.0},{-4.0,28.0}},origin={0.0,0.0},rotation=0.0)));
  parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector CoolantMedium=ECAT.EvapBrineType_nd
    "Coolant Medium Selection"
    annotation (Dialog(group="Medium"));
  final parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Empty coolantInf=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.getSelector(
    CoolantMedium)
    "Coolant Medium"
    annotation ();
  .BOLT.InternalLibrary.Media.Coolant.CoolantCommon.Temperature FreezTemp=.BOLT.InternalLibrary.Media.Coolant.CoolantCommon.freezeTemp(
    coolantInf,
    BrineConcentration,
    1);
  parameter Real BrineConcentration=ECAT.EvapBrineConcentration_nd.setPoint
    annotation (Dialog(group="Medium"));
  .BOLT.BoundaryNode.Coolant.Source sourceBrine(
    Vd_fixed=ECAT.EvapBrineFlowRate_m3s.fixed,
    T_set=EWT,
    p_fixed=true,
    CoolantMedium=CoolantMedium,
    T_fixed=ECAT.EvapBrineEWT_K.fixed,
    Vd_set=ECAT.EvapBrineFlowRate_m3s.setPoint,
    X=BrineConcentration)
    annotation (Placement(transformation(extent={{-6.546045643035143,-6.546045643035143},{6.546045643035143,6.546045643035143}},origin={270.0,-220.0},rotation=90.0)));
  .BOLT.BoundaryNode.Coolant.Sink sinkBrine(
    p_fixed=true,
    T_fixed=ECAT.EvapBrineLWT_K.fixed,
    T_set=LWT,
    CoolantMedium=CoolantMedium,
    p_set=sourceBrine.p_set,
    X=BrineConcentration,
    Vd_fixed=false)
    annotation (Placement(transformation(extent={{-5.468581959242897,-5.468581959242897},{5.468581959242897,5.468581959242897}},origin={270.0,318.6666666666667},rotation=-90.0)));
  parameter.Modelica.SIunits.Temperature LWT=ECAT.EvapBrineLWT_K.setPoint
    annotation (Dialog(group="Conditions"));
  parameter.Modelica.SIunits.Temperature EWT=ECAT.EvapBrineEWT_K.setPoint
    annotation (Dialog(group="Conditions"));
  parameter.Modelica.SIunits.Temperature OAT=ECAT.AmbientAirDBTemp_K.setPoint
    annotation (Dialog(group="Conditions"));
  parameter.Modelica.SIunits.Power TargetCapacityA=
    if Module.is_monobloc then
      ECAT.TargetCoolingCapacity_W.setPoint
    else
      ECAT.TargetCoolingCapacity_W.setPoint/2
    annotation (Dialog(group="Cp_control"));
  parameter.Modelica.SIunits.Power TargetCapacityB=ECAT.TargetCoolingCapacity_W.setPoint/2
    annotation (Dialog(group="Cp_control"));
  parameter Boolean isOFFB=false
    annotation (Dialog(group="Cp_control"));
  parameter Boolean isOFFA=false
    annotation (Dialog(group="Cp_control"));
  parameter Boolean use_bf=false
    annotation (Dialog(group="Use Parameters"));
  parameter Boolean Use_EN14511=false
    annotation (Dialog(group="Use Parameters"));
  parameter Boolean use_Calib=false
    annotation (Dialog(group="Use Parameters"));
  .Workspace.Auxiliary.ECAT_Zenith.ECATBase ECAT(
    EvapPumpSpeed_rpm(
      value=Module.pumpPolyA.summary.speed,
      setPoint=1800),
    EvapPumpSpeed_rpm_2(
      value=Module.pumpPolyB.summary.speed,
      setPoint=2100),
    EvapPumpPower_W_2(
      value=Module.pumpPolyB.summary.P_motor),
    EvapPumpPower_W(
      value=Module.pumpPolyA.summary.P_motor),
    nbrCircuit=2,
    CondCoilAirPressDrop_Pa(
      value={Module.BlockA.condAir.summary.dPa,Module.BlockB.condAir.summary.dPa}),
    RefrigerantSST_K(
      value={Module.BlockA.node_suction.Tsat,Module.BlockB.node_suction.Tsat}),
    RefrigerantSDT_K(
      value={Module.BlockA.node_discharge.Tsat,Module.BlockB.node_discharge.Tsat}),
    RefrigerantSET_K(
      value={Module.BlockA.node_evapout.Tsat,Module.BlockB.node_evapout.Tsat}),
    RefrigerantSCT_K(
      value={Module.BlockA.node_condin.Tsat,Module.BlockB.node_condin.Tsat}),
    RefrigerantDGT_K(
      value={Module.BlockA.node_discharge.T,Module.BlockB.node_discharge.T}),
    SuctionSuperheat_K(
      value={Module.BlockA.node_suction.dTsh,Module.BlockB.node_suction.dTsh}),
    CondSubcooling_K(
      value={Module.BlockA.node_liquid.dTsh,Module.BlockB.node_liquid.dTsh}),
    DischargeSuperheat_K(
      value={Module.BlockA.node_discharge.dTsh,Module.BlockB.node_discharge.dTsh}),
    CondFanAirflowRate_m3s(
      value={2*Module.BlockA.sourceAir.Vd_flow,2*Module.BlockB.sourceAir.Vd_flow}),
    CompressorFrequency_Hz(
      value={Module.BlockA.compressor.summary.Ncomp,Module.BlockB.compressor.summary.Ncomp},
      setPoint={Module.BlockA.compressor.summary.Ncomp,Module.BlockB.compressor.summary.Ncomp}),
    CompressorSpeed_rpm(
      value={Module.BlockA.compressor.summary.Ncomp*60,Module.BlockB.compressor.summary.Ncomp*60}),
    CompressorPower_W(
      value={Module.BlockA.compressor.summary.P_compression,Module.BlockB.compressor.summary.P_compression}),
    FanPower_W(
      value={Module.BlockA.motor.summary.power_VFD,Module.BlockB.motor.summary.power_VFD}),
    AmbientAirDBTemp_K(
      setPoint=308.15),
    EvapBrineConcentration_nd(
      setPoint=0.4),
    EvapFoulingFactor_m2KW(
      setPoint=0),
    EvapBrineLWT_K(
      value=sinkBrine.summary.T,
      setPoint=280.15),
    EvapBrineEWT_K(
      value=sourceBrine.summary.T,
      setPoint=285.15),
    EvapBrineFlowRate_m3s(
      value=sourceBrine.summary.Vd),
    TargetCoolingCapacity_W(
      setPoint=320e3),
    TotalRefrigerantCharge_kg(
      value=choiceBlock.Unit_Block_A.Design_mRef_set_A+choiceBlock.Unit_Block_B.Design_mRef_set_A),
    TotalOilCharge_kg(
      value=choiceBlock.Unit_Block_A.Oil_charge+choiceBlock.Unit_Block_B.Oil_charge),
    TotalCompressorPower_W(
      value=sum(
        ECAT.CompressorPower_W.value)),
    TotalFanPower_W(
      value=sum(
        ECAT.FanPower_W.value)),
    EvapBrineIntPressDrop_Pa(
      value=Module.BlockA.evapBPHE.summary.dp_coolant),
    EvapBrineDensity_kgm3(
      value=1/sourceBrine.summary.v),
    PubCoolingCapacity_W(
      value=Module.controlledCapacity),
    PubUnitPower_W(
      value=Module.controlledPower),
    ExternalSystemPressureDrop_Pa(
      value=8000),
    AmbientAirRH_nd(
      setPoint=0.87),
    RefrigerantCharge_kg(
      fixed={true,true},
      value={Module.BlockA.systemVariables.mRef[1],Module.BlockB.systemVariables.mRef[1]}),
    LoadRatio_nd(
      value=controller_crkA.completeCompressorControl_base.capacity_controller.summary.AV*100,
      fixed=false))
    annotation (Placement(transformation(extent={{-60.75411426282274,69.24588573717726},{-39.24588573717726,90.75411426282274}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Auxiliary.OptionBlock.ChoiceBlock choiceBlock(
    is_monobloc=false)
    annotation (Placement(transformation(extent={{-90.38432114310902,69.61567885689098},{-69.61567885689098,90.38432114310902}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Controller.Controller_1C_cooling controller_crkA2(
    is_load_ratio=ECAT.LoadRatio_nd.fixed,
    load_ratio=ECAT.LoadRatio_nd.setPoint,
    max_speed=Module.fmax[1],
    min_speed=Module.fmin[1],
    Fan_MinFrequency=controllerSettings_crkA.minfanfreq,
    manualOff_compressor_block_B=false,
    manualOff_compressor_block_A=false,
    manualOff_fan_block_B=false,
    manualOff_fan_block_A=false,
    fanControl(
      manualOff=controller_crkA.manualOff_fan_block_A,
      AV_value_off=controller_crkA.frq_fan_sp_manual_block_A),
    Use_pump=Module.Use_pump,
    completeCompressorControl_base(
      compressorControl(
        manualOff=controller_crkA.manualOff_compressor_block_A,
        AV_value_off=controller_crkA.frq_comp_sp_manual_block_A)),
    Fan_MaxFrequency=controllerSettings_crkA.maxfanfreq,
    isOffDGTmax_comp=false,
    isOffSDTmax_comp=false,
    isOffSSTmin_comp=false,
    isOffDGTmax_EXV=false,
    isOffDSHmin_EXV=false,
    isOffSSTmax_EXV=false,
    isOffSSTmin_EXV=false,
    isOffDGTmax_fan=false,
    isOffSDTmax_fan=false,
    isOffSDTmin_fan=false,
    isOff=isOFFA)
    annotation (Placement(transformation(extent={{125.47951766464104,-5.498536485807815},{145.47951766464104,14.501463514192185}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Controller.ControllerSettings_cooling controllerSettings_crkA2(
    maxfanfreq=Module.max_fan_frequency[1],
    extPressure_setpoint=ECAT.ExternalSystemPressureDrop_Pa.setPoint,
    fancoefficients=Module.fanCurveCoefficientsCooling[1,:],
    SST_min=274.15,
    Capacity_setpoint=ECAT.TargetCoolingCapacity_W.setPoint)
    annotation (Placement(transformation(extent={{113.35529603079797,29.72374094541228},{93.35529603079797,49.72374094541228}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Controller.ControllerSettings_cooling controllerSettings_crkB2(
    maxfanfreq=Module.max_fan_frequency[2],
    extPressure_setpoint=ECAT.ExternalSystemPressureDrop_Pa.setPoint,
    fancoefficients=Module.fanCurveCoefficientsCooling[2,:],
    SST_min=274.15,
    Capacity_setpoint=ECAT.TargetCoolingCapacity_W.setPoint)
    annotation (Placement(transformation(extent={{244.34378306275278,29.892280627379982},{264.3437830627528,49.89228062737998}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Controller.Controller_1C_cooling controller_crkB2(
    is_load_ratio=ECAT.LoadRatio_nd.fixed,
    load_ratio=ECAT.LoadRatio_nd.setPoint,
    max_speed=Module.fmax[2],
    min_speed=Module.fmin[2],
    Fan_MinFrequency=controllerSettings_crkB.minfanfreq,
    completeCompressorControl_base(
      compressorControl(
        manualOff=controller_crkB.manualOff_compressor_block_B,
        AV_value_off=controller_crkB.frq_comp_sp_manual_block_B)),
    fanControl(
      manualOff=controller_crkB.manualOff_fan_block_B,
      AV_value_off=controller_crkB.frq_fan_sp_manual_block_B),
    Use_pump=Module.Use_pump,
    Fan_MaxFrequency=controllerSettings_crkB.maxfanfreq,
    isOffDGTmax_comp=false,
    isOffSDTmax_comp=false,
    isOffSSTmin_comp=false,
    isOffDGTmax_EXV=false,
    isOffDSHmin_EXV=false,
    isOffSSTmax_EXV=false,
    isOffSSTmin_EXV=false,
    isOffDGTmax_fan=false,
    isOffSDTmax_fan=false,
    isOffSDTmin_fan=false,
    isOff=Module.is_monobloc or isOFFB)
    annotation (Placement(transformation(extent={{238.2671568155297,-4.874975606813457},{218.2671568155297,15.125024393186543}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.System.HPC.BaseCycle.Equipement Module2(
    use_Calib=use_Calib,
    FW={choiceBlock.Unit_Block_A.FW,choiceBlock.Unit_Block_B.FW},
    Zpower_Zflow={choiceBlock.Unit_Block_A.Zpower_Zflow,choiceBlock.Unit_Block_B.Zpower_Zflow},
    Zpower_SST={choiceBlock.Unit_Block_A.Zpower_SST,choiceBlock.Unit_Block_B.Zpower_SST},
    Zpower_SDT={choiceBlock.Unit_Block_A.Zpower_SDT,choiceBlock.Unit_Block_B.Zpower_SDT},
    Zpower_OAT={choiceBlock.Unit_Block_A.Zpower_OAT,choiceBlock.Unit_Block_B.Zpower_OAT},
    Zpower_Ncomp={choiceBlock.Unit_Block_A.Zpower_Ncomp,choiceBlock.Unit_Block_B.Zpower_Ncomp},
    Zpower_intercept={choiceBlock.Unit_Block_A.Zpower_intercept,choiceBlock.Unit_Block_B.Zpower_intercept},
    Zflow_OAT={choiceBlock.Unit_Block_A.Zflow_OAT,choiceBlock.Unit_Block_B.Zflow_OAT},
    Zflow_SDT={choiceBlock.Unit_Block_A.Zflow_SDT,choiceBlock.Unit_Block_B.Zflow_SDT},
    Zflow_SST={choiceBlock.Unit_Block_A.Zflow_SST,choiceBlock.Unit_Block_B.Zflow_SST},
    Zflow_Ncomp={choiceBlock.Unit_Block_A.Zflow_Ncomp,choiceBlock.Unit_Block_B.Zflow_Ncomp},
    Zflow_intercept={choiceBlock.Unit_Block_A.Zflow_intercept,choiceBlock.Unit_Block_B.Zflow_intercept},
    Zcond_B_HPC={choiceBlock.Unit_Block_A.Zcond_B_HPC,choiceBlock.Unit_Block_B.Zcond_B_HPC},
    Zcond_A_HPC={choiceBlock.Unit_Block_A.Zcond_A_HPC,choiceBlock.Unit_Block_B.Zcond_A_HPC},
    Zcond_B_HPH={choiceBlock.Unit_Block_A.Zcond_B_HPH,choiceBlock.Unit_Block_B.Zcond_B_HPH},
    Zcond_A_HPH={choiceBlock.Unit_Block_A.Zcond_A_HPH,choiceBlock.Unit_Block_B.Zcond_A_HPH},
    Zevap_B_HPC={choiceBlock.Unit_Block_A.Zevap_B_HPC,choiceBlock.Unit_Block_B.Zevap_B_HPC},
    Zevap_A_HPC={choiceBlock.Unit_Block_A.Zevap_A_HPC,choiceBlock.Unit_Block_B.Zevap_A_HPC},
    Zevap_coated_HPH={choiceBlock.Unit_Block_A.Zevap_coated_HPH,choiceBlock.Unit_Block_B.Zevap_coated_HPH},
    Zevap_HPH={choiceBlock.Unit_Block_A.Zevap_HPH,choiceBlock.Unit_Block_B.Zevap_HPH},
    PDC_4WV={choiceBlock.Unit_Block_A.PDC_4WV,choiceBlock.Unit_Block_B.PDC_4WV},
    BlockB(
      systemVariables(
        mRef_set={ECAT.RefrigerantCharge_kg[2].setPoint},
        mRef_fixed={ECAT.RefrigerantCharge_kg[2].fixed}),
      node_liquid(
        dTsh_fixed=not ECAT.RefrigerantCharge_kg[2].fixed)),
    BlockA(
      systemVariables(
        mRef_set={ECAT.RefrigerantCharge_kg[1].setPoint},
        mRef_fixed={ECAT.RefrigerantCharge_kg[1].fixed}),
      node_liquid(
        dTsh_fixed=not ECAT.RefrigerantCharge_kg[1].fixed)),
    relative_humidity=ECAT.AmbientAirRH_nd.setPoint,
    isCoating=choiceBlock.isCoatingOption,
    mdot_start=Module.capacity_design[1]/(((4180*(Module.EWT-Module.LWT)))),
    EvapFoulingFactor=ECAT.EvapFoulingFactor_m2KW.setPoint,
    BrineConcentration=BrineConcentration,
    use_bf=use_bf,
    Use_EN=Use_EN14511,
    fanCurveCoefficientsCooling={choiceBlock.Unit_Block_A.fanCurveCoefficientsCooling,choiceBlock.Unit_Block_B.fanCurveCoefficientsCooling},
    is_monobloc=choiceBlock.is_monobloc,
    CompVoltage={choiceBlock.Unit_Block_A.CompVoltage,choiceBlock.Unit_Block_B.CompVoltage},
    fanCurveCoefficientsHeating={choiceBlock.Unit_Block_A.fanCurveCoefficientsHeating,choiceBlock.Unit_Block_B.fanCurveCoefficientsHeating},
    selector_pump={choiceBlock.Unit_Block_A.Pump_type,choiceBlock.Unit_Block_B.Pump_type},
    selector_geo_BPHE={choiceBlock.Unit_Block_A.cond_select_geo,choiceBlock.Unit_Block_B.cond_select_geo},
    EXV_main={choiceBlock.Unit_Block_A.EXV_main_A,choiceBlock.Unit_Block_B.EXV_main_A},
    selector_Comp={choiceBlock.Unit_Block_A.selector_Comp,choiceBlock.Unit_Block_B.selector_Comp},
    UA_duct={choiceBlock.Unit_Block_A.duct_UA,choiceBlock.Unit_Block_B.duct_UA},
    Ka_duct={choiceBlock.Unit_Block_A.duct_Ka,choiceBlock.Unit_Block_B.duct_Ka},
    Ac_duct={choiceBlock.Unit_Block_A.duct_Ac,choiceBlock.Unit_Block_B.duct_Ac},
    EXV_in_line_length={choiceBlock.Unit_Block_A.EXV_in_line_length,choiceBlock.Unit_Block_B.EXV_in_line_length},
    EXV_in_line_diameter={choiceBlock.Unit_Block_A.EXV_in_line_diameter,choiceBlock.Unit_Block_B.EXV_in_line_diameter},
    discharge_line_length={choiceBlock.Unit_Block_A.discharge_line_length,choiceBlock.Unit_Block_B.discharge_line_length},
    discharge_line_diameter={choiceBlock.Unit_Block_A.discharge_line_diameter,choiceBlock.Unit_Block_B.discharge_line_diameter},
    liquid_line_length={choiceBlock.Unit_Block_A.Liquid_line_length,choiceBlock.Unit_Block_B.Liquid_line_length},
    liquid_line_diameter={choiceBlock.Unit_Block_A.Liquid_line_diameter,choiceBlock.Unit_Block_B.Liquid_line_diameter},
    coil_line_length={choiceBlock.Unit_Block_A.Coil_line_length,choiceBlock.Unit_Block_B.Coil_line_length},
    coil_line_diameter={choiceBlock.Unit_Block_A.Coil_line_diameter,choiceBlock.Unit_Block_B.Coil_line_diameter},
    Suction_line_length={choiceBlock.Unit_Block_A.Suction_line_length,choiceBlock.Unit_Block_B.Suction_line_length},
    Suction_line_diameter={choiceBlock.Unit_Block_A.Suction_line_diameter,choiceBlock.Unit_Block_B.Suction_line_diameter},
    max_fan_frequency={choiceBlock.Unit_Block_A.max_fan_frequency,choiceBlock.Unit_Block_B.max_fan_frequency},
    Fw_fan={choiceBlock.Unit_Block_A.Fan_FW,choiceBlock.Unit_Block_B.Fan_FW},
    Tfin={choiceBlock.Unit_Block_A.Tfin,choiceBlock.Unit_Block_B.Tfin},
    Dfin={choiceBlock.Unit_Block_A.Dfin,choiceBlock.Unit_Block_B.Dfin},
    Prow={choiceBlock.Unit_Block_A.Prow,choiceBlock.Unit_Block_B.Prow},
    Ptube={choiceBlock.Unit_Block_A.Ptube,choiceBlock.Unit_Block_B.Ptube},
    Ttube={choiceBlock.Unit_Block_A.Ttube,choiceBlock.Unit_Block_B.Ttube},
    Dotube={choiceBlock.Unit_Block_A.Dotube,choiceBlock.Unit_Block_B.Dotube},
    Ltube={choiceBlock.Unit_Block_A.Ltube,choiceBlock.Unit_Block_B.Ltube},
    Nrow={choiceBlock.Unit_Block_A.Nrow,choiceBlock.Unit_Block_B.Nrow},
    Ntube={choiceBlock.Unit_Block_A.Ntube,choiceBlock.Unit_Block_B.Ntube},
    nCir={choiceBlock.Unit_Block_A.nCir,choiceBlock.Unit_Block_B.nCir},
    Itube={choiceBlock.Unit_Block_A.Itube,choiceBlock.Unit_Block_B.Itube},
    nCoils={choiceBlock.Unit_Block_A.nCoils,choiceBlock.Unit_Block_B.nCoils},
    nPlate={choiceBlock.Unit_Block_A.nplate,choiceBlock.Unit_Block_B.nplate},
    Dport_ref_b={choiceBlock.Unit_Block_A.cond_diameter_ref_port_out,choiceBlock.Unit_Block_B.cond_diameter_ref_port_out},
    Dport_ref_a={choiceBlock.Unit_Block_A.cond_diameter_ref_port_in,choiceBlock.Unit_Block_B.cond_diameter_ref_port_in},
    Dport_coolant_b={choiceBlock.Unit_Block_A.cond_diameter_brine_port_out,choiceBlock.Unit_Block_B.cond_diameter_brine_port_out},
    Dport_coolant_a={choiceBlock.Unit_Block_A.cond_diameter_brine_port_in,choiceBlock.Unit_Block_B.cond_diameter_brine_port_in},
    fmin={choiceBlock.Unit_Block_A.fmin,choiceBlock.Unit_Block_B.fmin},
    fmax={choiceBlock.Unit_Block_A.fmax,choiceBlock.Unit_Block_B.fmax},
    capacity_design={choiceBlock.Unit_Block_A.capacity_design,choiceBlock.Unit_Block_B.capacity_design},
    Use_pump=choiceBlock.is_Pump,
    EWT=EWT,
    LWT=LWT,
    OAT=OAT,
    isOFFA=isOFFA,
    isOFFB=isOFFB,
    CoolantMedium=CoolantMedium,
    external_system(
      isOff=true))
    annotation (Placement(transformation(extent={{172.38956005869437,6.358677354332308},{192.38956005869437,26.358677354332308}},origin={0.0,0.0},rotation=0.0)));
  inner.BOLT.GlobalParameters globalParameters(
    varLevel=BOLT.InternalLibrary.BuildingBlocks.Types.Var_level.Advanced,
    oilMode=false)
    annotation (Placement(transformation(extent={{-120.0,70.0},{-100.0,90.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.Split split(
    X=BrineConcentration,
    CoolantMedium=CoolantMedium)
    annotation (Placement(transformation(extent={{-10.0,-10.0},{10.0,10.0}},origin={270.0,-176.0},rotation=90.0)));
  .BOLT.CoolantMisc.Mixer mixer(
    X=BrineConcentration,
    CoolantMedium=CoolantMedium,
    fa_fixed=false)
    annotation (Placement(transformation(extent={{-10.0,-10.0},{10.0,10.0}},origin={270.0,256.6666666666667},rotation=90.0)));
  .BOLT.CoolantMisc.ReducedPipe external_system(
    Ka_fixed=false,
    X=BrineConcentration,
    CoolantMedium=CoolantMedium,
    T_start=LWT,
    isOff=false)
    annotation (Placement(transformation(extent={{-6.386404559420782,-6.386404559420782},{6.386404559420782,6.386404559420782}},origin={270.0,288.6666666666667},rotation=90.0)));
  .Workspace.Controller.Controller_1C_cooling controller_crkA3(
    isOff=isOFFA,
    isOffSDTmin_fan=false,
    isOffSDTmax_fan=false,
    isOffDGTmax_fan=false,
    isOffSSTmin_EXV=false,
    isOffSSTmax_EXV=false,
    isOffDSHmin_EXV=false,
    isOffDGTmax_EXV=false,
    isOffSSTmin_comp=false,
    isOffSDTmax_comp=false,
    isOffDGTmax_comp=false,
    Fan_MaxFrequency=controllerSettings_crkA.maxfanfreq,
    completeCompressorControl_base(
      compressorControl(
        manualOff=controller_crkA.manualOff_compressor_block_A,
        AV_value_off=controller_crkA.frq_comp_sp_manual_block_A)),
    Use_pump=Module.Use_pump,
    fanControl(
      manualOff=controller_crkA.manualOff_fan_block_A,
      AV_value_off=controller_crkA.frq_fan_sp_manual_block_A),
    manualOff_fan_block_A=false,
    manualOff_fan_block_B=false,
    manualOff_compressor_block_A=false,
    manualOff_compressor_block_B=false,
    Fan_MinFrequency=controllerSettings_crkA.minfanfreq,
    min_speed=Module.fmin[1],
    max_speed=Module.fmax[1],
    load_ratio=ECAT.LoadRatio_nd.setPoint,
    is_load_ratio=ECAT.LoadRatio_nd.fixed)
    annotation (Placement(transformation(extent={{320.71245493815786,-4.6187738527084825},{340.71245493815786,15.381226147291517}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Controller.ControllerSettings_cooling controllerSettings_crkA3(
    Capacity_setpoint=ECAT.TargetCoolingCapacity_W.setPoint,
    SST_min=274.15,
    fancoefficients=Module.fanCurveCoefficientsCooling[1,:],
    extPressure_setpoint=ECAT.ExternalSystemPressureDrop_Pa.setPoint,
    maxfanfreq=Module.max_fan_frequency[1])
    annotation (Placement(transformation(extent={{308.5882333043148,30.603503578511614},{288.5882333043148,50.603503578511614}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Controller.ControllerSettings_cooling controllerSettings_crkB3(
    Capacity_setpoint=ECAT.TargetCoolingCapacity_W.setPoint,
    SST_min=274.15,
    fancoefficients=Module.fanCurveCoefficientsCooling[2,:],
    extPressure_setpoint=ECAT.ExternalSystemPressureDrop_Pa.setPoint,
    maxfanfreq=Module.max_fan_frequency[2])
    annotation (Placement(transformation(extent={{439.5767203362696,30.772043260479315},{459.5767203362696,50.772043260479315}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Controller.Controller_1C_cooling controller_crkB3(
    isOff=Module.is_monobloc or isOFFB,
    isOffSDTmin_fan=false,
    isOffSDTmax_fan=false,
    isOffDGTmax_fan=false,
    isOffSSTmin_EXV=false,
    isOffSSTmax_EXV=false,
    isOffDSHmin_EXV=false,
    isOffDGTmax_EXV=false,
    isOffSSTmin_comp=false,
    isOffSDTmax_comp=false,
    isOffDGTmax_comp=false,
    Fan_MaxFrequency=controllerSettings_crkB.maxfanfreq,
    Use_pump=Module.Use_pump,
    fanControl(
      manualOff=controller_crkB.manualOff_fan_block_B,
      AV_value_off=controller_crkB.frq_fan_sp_manual_block_B),
    completeCompressorControl_base(
      compressorControl(
        manualOff=controller_crkB.manualOff_compressor_block_B,
        AV_value_off=controller_crkB.frq_comp_sp_manual_block_B)),
    Fan_MinFrequency=controllerSettings_crkB.minfanfreq,
    min_speed=Module.fmin[2],
    max_speed=Module.fmax[2],
    load_ratio=ECAT.LoadRatio_nd.setPoint,
    is_load_ratio=ECAT.LoadRatio_nd.fixed)
    annotation (Placement(transformation(extent={{433.5000940890465,-3.995212973714125},{413.5000940890465,16.004787026285875}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.System.HPC.BaseCycle.Equipement Module3(
    external_system(
      isOff=true),
    CoolantMedium=CoolantMedium,
    isOFFB=isOFFB,
    isOFFA=isOFFA,
    OAT=OAT,
    LWT=LWT,
    EWT=EWT,
    Use_pump=choiceBlock.is_Pump,
    capacity_design={choiceBlock.Unit_Block_A.capacity_design,choiceBlock.Unit_Block_B.capacity_design},
    fmax={choiceBlock.Unit_Block_A.fmax,choiceBlock.Unit_Block_B.fmax},
    fmin={choiceBlock.Unit_Block_A.fmin,choiceBlock.Unit_Block_B.fmin},
    Dport_coolant_a={choiceBlock.Unit_Block_A.cond_diameter_brine_port_in,choiceBlock.Unit_Block_B.cond_diameter_brine_port_in},
    Dport_coolant_b={choiceBlock.Unit_Block_A.cond_diameter_brine_port_out,choiceBlock.Unit_Block_B.cond_diameter_brine_port_out},
    Dport_ref_a={choiceBlock.Unit_Block_A.cond_diameter_ref_port_in,choiceBlock.Unit_Block_B.cond_diameter_ref_port_in},
    Dport_ref_b={choiceBlock.Unit_Block_A.cond_diameter_ref_port_out,choiceBlock.Unit_Block_B.cond_diameter_ref_port_out},
    nPlate={choiceBlock.Unit_Block_A.nplate,choiceBlock.Unit_Block_B.nplate},
    nCoils={choiceBlock.Unit_Block_A.nCoils,choiceBlock.Unit_Block_B.nCoils},
    Itube={choiceBlock.Unit_Block_A.Itube,choiceBlock.Unit_Block_B.Itube},
    nCir={choiceBlock.Unit_Block_A.nCir,choiceBlock.Unit_Block_B.nCir},
    Ntube={choiceBlock.Unit_Block_A.Ntube,choiceBlock.Unit_Block_B.Ntube},
    Nrow={choiceBlock.Unit_Block_A.Nrow,choiceBlock.Unit_Block_B.Nrow},
    Ltube={choiceBlock.Unit_Block_A.Ltube,choiceBlock.Unit_Block_B.Ltube},
    Dotube={choiceBlock.Unit_Block_A.Dotube,choiceBlock.Unit_Block_B.Dotube},
    Ttube={choiceBlock.Unit_Block_A.Ttube,choiceBlock.Unit_Block_B.Ttube},
    Ptube={choiceBlock.Unit_Block_A.Ptube,choiceBlock.Unit_Block_B.Ptube},
    Prow={choiceBlock.Unit_Block_A.Prow,choiceBlock.Unit_Block_B.Prow},
    Dfin={choiceBlock.Unit_Block_A.Dfin,choiceBlock.Unit_Block_B.Dfin},
    Tfin={choiceBlock.Unit_Block_A.Tfin,choiceBlock.Unit_Block_B.Tfin},
    Fw_fan={choiceBlock.Unit_Block_A.Fan_FW,choiceBlock.Unit_Block_B.Fan_FW},
    max_fan_frequency={choiceBlock.Unit_Block_A.max_fan_frequency,choiceBlock.Unit_Block_B.max_fan_frequency},
    Suction_line_diameter={choiceBlock.Unit_Block_A.Suction_line_diameter,choiceBlock.Unit_Block_B.Suction_line_diameter},
    Suction_line_length={choiceBlock.Unit_Block_A.Suction_line_length,choiceBlock.Unit_Block_B.Suction_line_length},
    coil_line_diameter={choiceBlock.Unit_Block_A.Coil_line_diameter,choiceBlock.Unit_Block_B.Coil_line_diameter},
    coil_line_length={choiceBlock.Unit_Block_A.Coil_line_length,choiceBlock.Unit_Block_B.Coil_line_length},
    liquid_line_diameter={choiceBlock.Unit_Block_A.Liquid_line_diameter,choiceBlock.Unit_Block_B.Liquid_line_diameter},
    liquid_line_length={choiceBlock.Unit_Block_A.Liquid_line_length,choiceBlock.Unit_Block_B.Liquid_line_length},
    discharge_line_diameter={choiceBlock.Unit_Block_A.discharge_line_diameter,choiceBlock.Unit_Block_B.discharge_line_diameter},
    discharge_line_length={choiceBlock.Unit_Block_A.discharge_line_length,choiceBlock.Unit_Block_B.discharge_line_length},
    EXV_in_line_diameter={choiceBlock.Unit_Block_A.EXV_in_line_diameter,choiceBlock.Unit_Block_B.EXV_in_line_diameter},
    EXV_in_line_length={choiceBlock.Unit_Block_A.EXV_in_line_length,choiceBlock.Unit_Block_B.EXV_in_line_length},
    Ac_duct={choiceBlock.Unit_Block_A.duct_Ac,choiceBlock.Unit_Block_B.duct_Ac},
    Ka_duct={choiceBlock.Unit_Block_A.duct_Ka,choiceBlock.Unit_Block_B.duct_Ka},
    UA_duct={choiceBlock.Unit_Block_A.duct_UA,choiceBlock.Unit_Block_B.duct_UA},
    selector_Comp={choiceBlock.Unit_Block_A.selector_Comp,choiceBlock.Unit_Block_B.selector_Comp},
    EXV_main={choiceBlock.Unit_Block_A.EXV_main_A,choiceBlock.Unit_Block_B.EXV_main_A},
    selector_geo_BPHE={choiceBlock.Unit_Block_A.cond_select_geo,choiceBlock.Unit_Block_B.cond_select_geo},
    selector_pump={choiceBlock.Unit_Block_A.Pump_type,choiceBlock.Unit_Block_B.Pump_type},
    fanCurveCoefficientsHeating={choiceBlock.Unit_Block_A.fanCurveCoefficientsHeating,choiceBlock.Unit_Block_B.fanCurveCoefficientsHeating},
    CompVoltage={choiceBlock.Unit_Block_A.CompVoltage,choiceBlock.Unit_Block_B.CompVoltage},
    is_monobloc=choiceBlock.is_monobloc,
    fanCurveCoefficientsCooling={choiceBlock.Unit_Block_A.fanCurveCoefficientsCooling,choiceBlock.Unit_Block_B.fanCurveCoefficientsCooling},
    Use_EN=Use_EN14511,
    use_bf=use_bf,
    BrineConcentration=BrineConcentration,
    EvapFoulingFactor=ECAT.EvapFoulingFactor_m2KW.setPoint,
    mdot_start=Module.capacity_design[1]/(((4180*(Module.EWT-Module.LWT)))),
    isCoating=choiceBlock.isCoatingOption,
    relative_humidity=ECAT.AmbientAirRH_nd.setPoint,
    BlockA(
      systemVariables(
        mRef_set={ECAT.RefrigerantCharge_kg[1].setPoint},
        mRef_fixed={ECAT.RefrigerantCharge_kg[1].fixed}),
      node_liquid(
        dTsh_fixed=not ECAT.RefrigerantCharge_kg[1].fixed)),
    BlockB(
      systemVariables(
        mRef_set={ECAT.RefrigerantCharge_kg[2].setPoint},
        mRef_fixed={ECAT.RefrigerantCharge_kg[2].fixed}),
      node_liquid(
        dTsh_fixed=not ECAT.RefrigerantCharge_kg[2].fixed)),
    PDC_4WV={choiceBlock.Unit_Block_A.PDC_4WV,choiceBlock.Unit_Block_B.PDC_4WV},
    Zevap_HPH={choiceBlock.Unit_Block_A.Zevap_HPH,choiceBlock.Unit_Block_B.Zevap_HPH},
    Zevap_coated_HPH={choiceBlock.Unit_Block_A.Zevap_coated_HPH,choiceBlock.Unit_Block_B.Zevap_coated_HPH},
    Zevap_A_HPC={choiceBlock.Unit_Block_A.Zevap_A_HPC,choiceBlock.Unit_Block_B.Zevap_A_HPC},
    Zevap_B_HPC={choiceBlock.Unit_Block_A.Zevap_B_HPC,choiceBlock.Unit_Block_B.Zevap_B_HPC},
    Zcond_A_HPH={choiceBlock.Unit_Block_A.Zcond_A_HPH,choiceBlock.Unit_Block_B.Zcond_A_HPH},
    Zcond_B_HPH={choiceBlock.Unit_Block_A.Zcond_B_HPH,choiceBlock.Unit_Block_B.Zcond_B_HPH},
    Zcond_A_HPC={choiceBlock.Unit_Block_A.Zcond_A_HPC,choiceBlock.Unit_Block_B.Zcond_A_HPC},
    Zcond_B_HPC={choiceBlock.Unit_Block_A.Zcond_B_HPC,choiceBlock.Unit_Block_B.Zcond_B_HPC},
    Zflow_intercept={choiceBlock.Unit_Block_A.Zflow_intercept,choiceBlock.Unit_Block_B.Zflow_intercept},
    Zflow_Ncomp={choiceBlock.Unit_Block_A.Zflow_Ncomp,choiceBlock.Unit_Block_B.Zflow_Ncomp},
    Zflow_SST={choiceBlock.Unit_Block_A.Zflow_SST,choiceBlock.Unit_Block_B.Zflow_SST},
    Zflow_SDT={choiceBlock.Unit_Block_A.Zflow_SDT,choiceBlock.Unit_Block_B.Zflow_SDT},
    Zflow_OAT={choiceBlock.Unit_Block_A.Zflow_OAT,choiceBlock.Unit_Block_B.Zflow_OAT},
    Zpower_intercept={choiceBlock.Unit_Block_A.Zpower_intercept,choiceBlock.Unit_Block_B.Zpower_intercept},
    Zpower_Ncomp={choiceBlock.Unit_Block_A.Zpower_Ncomp,choiceBlock.Unit_Block_B.Zpower_Ncomp},
    Zpower_OAT={choiceBlock.Unit_Block_A.Zpower_OAT,choiceBlock.Unit_Block_B.Zpower_OAT},
    Zpower_SDT={choiceBlock.Unit_Block_A.Zpower_SDT,choiceBlock.Unit_Block_B.Zpower_SDT},
    Zpower_SST={choiceBlock.Unit_Block_A.Zpower_SST,choiceBlock.Unit_Block_B.Zpower_SST},
    Zpower_Zflow={choiceBlock.Unit_Block_A.Zpower_Zflow,choiceBlock.Unit_Block_B.Zpower_Zflow},
    FW={choiceBlock.Unit_Block_A.FW,choiceBlock.Unit_Block_B.FW},
    use_Calib=use_Calib)
    annotation (Placement(transformation(extent={{367.6224973322112,7.23843998743164},{387.6224973322112,27.23843998743164}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Controller.Controller_1C_cooling controller_crkA4(
    isOff=isOFFA,
    isOffSDTmin_fan=false,
    isOffSDTmax_fan=false,
    isOffDGTmax_fan=false,
    isOffSSTmin_EXV=false,
    isOffSSTmax_EXV=false,
    isOffDSHmin_EXV=false,
    isOffDGTmax_EXV=false,
    isOffSSTmin_comp=false,
    isOffSDTmax_comp=false,
    isOffDGTmax_comp=false,
    Fan_MaxFrequency=controllerSettings_crkA.maxfanfreq,
    completeCompressorControl_base(
      compressorControl(
        manualOff=controller_crkA.manualOff_compressor_block_A,
        AV_value_off=controller_crkA.frq_comp_sp_manual_block_A)),
    Use_pump=Module.Use_pump,
    fanControl(
      manualOff=controller_crkA.manualOff_fan_block_A,
      AV_value_off=controller_crkA.frq_fan_sp_manual_block_A),
    manualOff_fan_block_A=false,
    manualOff_fan_block_B=false,
    manualOff_compressor_block_A=false,
    manualOff_compressor_block_B=false,
    Fan_MinFrequency=controllerSettings_crkA.minfanfreq,
    min_speed=Module.fmin[1],
    max_speed=Module.fmax[1],
    load_ratio=ECAT.LoadRatio_nd.setPoint,
    is_load_ratio=ECAT.LoadRatio_nd.fixed)
    annotation (Placement(transformation(extent={{520.7124549381579,-6.618773852708486},{540.7124549381579,13.381226147291514}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Controller.ControllerSettings_cooling controllerSettings_crkA4(
    Capacity_setpoint=ECAT.TargetCoolingCapacity_W.setPoint,
    SST_min=274.15,
    fancoefficients=Module.fanCurveCoefficientsCooling[1,:],
    extPressure_setpoint=ECAT.ExternalSystemPressureDrop_Pa.setPoint,
    maxfanfreq=Module.max_fan_frequency[1])
    annotation (Placement(transformation(extent={{508.5882333043148,28.60350357851162},{488.5882333043148,48.60350357851162}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Controller.ControllerSettings_cooling controllerSettings_crkB4(
    Capacity_setpoint=ECAT.TargetCoolingCapacity_W.setPoint,
    SST_min=274.15,
    fancoefficients=Module.fanCurveCoefficientsCooling[2,:],
    extPressure_setpoint=ECAT.ExternalSystemPressureDrop_Pa.setPoint,
    maxfanfreq=Module.max_fan_frequency[2])
    annotation (Placement(transformation(extent={{639.5767203362695,28.77204326047932},{659.5767203362695,48.77204326047932}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Controller.Controller_1C_cooling controller_crkB4(
    isOff=Module.is_monobloc or isOFFB,
    isOffSDTmin_fan=false,
    isOffSDTmax_fan=false,
    isOffDGTmax_fan=false,
    isOffSSTmin_EXV=false,
    isOffSSTmax_EXV=false,
    isOffDSHmin_EXV=false,
    isOffDGTmax_EXV=false,
    isOffSSTmin_comp=false,
    isOffSDTmax_comp=false,
    isOffDGTmax_comp=false,
    Fan_MaxFrequency=controllerSettings_crkB.maxfanfreq,
    Use_pump=Module.Use_pump,
    fanControl(
      manualOff=controller_crkB.manualOff_fan_block_B,
      AV_value_off=controller_crkB.frq_fan_sp_manual_block_B),
    completeCompressorControl_base(
      compressorControl(
        manualOff=controller_crkB.manualOff_compressor_block_B,
        AV_value_off=controller_crkB.frq_comp_sp_manual_block_B)),
    Fan_MinFrequency=controllerSettings_crkB.minfanfreq,
    min_speed=Module.fmin[2],
    max_speed=Module.fmax[2],
    load_ratio=ECAT.LoadRatio_nd.setPoint,
    is_load_ratio=ECAT.LoadRatio_nd.fixed)
    annotation (Placement(transformation(extent={{633.5000940890466,-5.995212973714125},{613.5000940890466,14.004787026285875}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.System.HPC.BaseCycle.Equipement Module4(
    external_system(
      isOff=true),
    CoolantMedium=CoolantMedium,
    isOFFB=isOFFB,
    isOFFA=isOFFA,
    OAT=OAT,
    LWT=LWT,
    EWT=EWT,
    Use_pump=choiceBlock.is_Pump,
    capacity_design={choiceBlock.Unit_Block_A.capacity_design,choiceBlock.Unit_Block_B.capacity_design},
    fmax={choiceBlock.Unit_Block_A.fmax,choiceBlock.Unit_Block_B.fmax},
    fmin={choiceBlock.Unit_Block_A.fmin,choiceBlock.Unit_Block_B.fmin},
    Dport_coolant_a={choiceBlock.Unit_Block_A.cond_diameter_brine_port_in,choiceBlock.Unit_Block_B.cond_diameter_brine_port_in},
    Dport_coolant_b={choiceBlock.Unit_Block_A.cond_diameter_brine_port_out,choiceBlock.Unit_Block_B.cond_diameter_brine_port_out},
    Dport_ref_a={choiceBlock.Unit_Block_A.cond_diameter_ref_port_in,choiceBlock.Unit_Block_B.cond_diameter_ref_port_in},
    Dport_ref_b={choiceBlock.Unit_Block_A.cond_diameter_ref_port_out,choiceBlock.Unit_Block_B.cond_diameter_ref_port_out},
    nPlate={choiceBlock.Unit_Block_A.nplate,choiceBlock.Unit_Block_B.nplate},
    nCoils={choiceBlock.Unit_Block_A.nCoils,choiceBlock.Unit_Block_B.nCoils},
    Itube={choiceBlock.Unit_Block_A.Itube,choiceBlock.Unit_Block_B.Itube},
    nCir={choiceBlock.Unit_Block_A.nCir,choiceBlock.Unit_Block_B.nCir},
    Ntube={choiceBlock.Unit_Block_A.Ntube,choiceBlock.Unit_Block_B.Ntube},
    Nrow={choiceBlock.Unit_Block_A.Nrow,choiceBlock.Unit_Block_B.Nrow},
    Ltube={choiceBlock.Unit_Block_A.Ltube,choiceBlock.Unit_Block_B.Ltube},
    Dotube={choiceBlock.Unit_Block_A.Dotube,choiceBlock.Unit_Block_B.Dotube},
    Ttube={choiceBlock.Unit_Block_A.Ttube,choiceBlock.Unit_Block_B.Ttube},
    Ptube={choiceBlock.Unit_Block_A.Ptube,choiceBlock.Unit_Block_B.Ptube},
    Prow={choiceBlock.Unit_Block_A.Prow,choiceBlock.Unit_Block_B.Prow},
    Dfin={choiceBlock.Unit_Block_A.Dfin,choiceBlock.Unit_Block_B.Dfin},
    Tfin={choiceBlock.Unit_Block_A.Tfin,choiceBlock.Unit_Block_B.Tfin},
    Fw_fan={choiceBlock.Unit_Block_A.Fan_FW,choiceBlock.Unit_Block_B.Fan_FW},
    max_fan_frequency={choiceBlock.Unit_Block_A.max_fan_frequency,choiceBlock.Unit_Block_B.max_fan_frequency},
    Suction_line_diameter={choiceBlock.Unit_Block_A.Suction_line_diameter,choiceBlock.Unit_Block_B.Suction_line_diameter},
    Suction_line_length={choiceBlock.Unit_Block_A.Suction_line_length,choiceBlock.Unit_Block_B.Suction_line_length},
    coil_line_diameter={choiceBlock.Unit_Block_A.Coil_line_diameter,choiceBlock.Unit_Block_B.Coil_line_diameter},
    coil_line_length={choiceBlock.Unit_Block_A.Coil_line_length,choiceBlock.Unit_Block_B.Coil_line_length},
    liquid_line_diameter={choiceBlock.Unit_Block_A.Liquid_line_diameter,choiceBlock.Unit_Block_B.Liquid_line_diameter},
    liquid_line_length={choiceBlock.Unit_Block_A.Liquid_line_length,choiceBlock.Unit_Block_B.Liquid_line_length},
    discharge_line_diameter={choiceBlock.Unit_Block_A.discharge_line_diameter,choiceBlock.Unit_Block_B.discharge_line_diameter},
    discharge_line_length={choiceBlock.Unit_Block_A.discharge_line_length,choiceBlock.Unit_Block_B.discharge_line_length},
    EXV_in_line_diameter={choiceBlock.Unit_Block_A.EXV_in_line_diameter,choiceBlock.Unit_Block_B.EXV_in_line_diameter},
    EXV_in_line_length={choiceBlock.Unit_Block_A.EXV_in_line_length,choiceBlock.Unit_Block_B.EXV_in_line_length},
    Ac_duct={choiceBlock.Unit_Block_A.duct_Ac,choiceBlock.Unit_Block_B.duct_Ac},
    Ka_duct={choiceBlock.Unit_Block_A.duct_Ka,choiceBlock.Unit_Block_B.duct_Ka},
    UA_duct={choiceBlock.Unit_Block_A.duct_UA,choiceBlock.Unit_Block_B.duct_UA},
    selector_Comp={choiceBlock.Unit_Block_A.selector_Comp,choiceBlock.Unit_Block_B.selector_Comp},
    EXV_main={choiceBlock.Unit_Block_A.EXV_main_A,choiceBlock.Unit_Block_B.EXV_main_A},
    selector_geo_BPHE={choiceBlock.Unit_Block_A.cond_select_geo,choiceBlock.Unit_Block_B.cond_select_geo},
    selector_pump={choiceBlock.Unit_Block_A.Pump_type,choiceBlock.Unit_Block_B.Pump_type},
    fanCurveCoefficientsHeating={choiceBlock.Unit_Block_A.fanCurveCoefficientsHeating,choiceBlock.Unit_Block_B.fanCurveCoefficientsHeating},
    CompVoltage={choiceBlock.Unit_Block_A.CompVoltage,choiceBlock.Unit_Block_B.CompVoltage},
    is_monobloc=choiceBlock.is_monobloc,
    fanCurveCoefficientsCooling={choiceBlock.Unit_Block_A.fanCurveCoefficientsCooling,choiceBlock.Unit_Block_B.fanCurveCoefficientsCooling},
    Use_EN=Use_EN14511,
    use_bf=use_bf,
    BrineConcentration=BrineConcentration,
    EvapFoulingFactor=ECAT.EvapFoulingFactor_m2KW.setPoint,
    mdot_start=Module.capacity_design[1]/(((4180*(Module.EWT-Module.LWT)))),
    isCoating=choiceBlock.isCoatingOption,
    relative_humidity=ECAT.AmbientAirRH_nd.setPoint,
    BlockA(
      systemVariables(
        mRef_set={ECAT.RefrigerantCharge_kg[1].setPoint},
        mRef_fixed={ECAT.RefrigerantCharge_kg[1].fixed}),
      node_liquid(
        dTsh_fixed=not ECAT.RefrigerantCharge_kg[1].fixed)),
    BlockB(
      systemVariables(
        mRef_set={ECAT.RefrigerantCharge_kg[2].setPoint},
        mRef_fixed={ECAT.RefrigerantCharge_kg[2].fixed}),
      node_liquid(
        dTsh_fixed=not ECAT.RefrigerantCharge_kg[2].fixed)),
    PDC_4WV={choiceBlock.Unit_Block_A.PDC_4WV,choiceBlock.Unit_Block_B.PDC_4WV},
    Zevap_HPH={choiceBlock.Unit_Block_A.Zevap_HPH,choiceBlock.Unit_Block_B.Zevap_HPH},
    Zevap_coated_HPH={choiceBlock.Unit_Block_A.Zevap_coated_HPH,choiceBlock.Unit_Block_B.Zevap_coated_HPH},
    Zevap_A_HPC={choiceBlock.Unit_Block_A.Zevap_A_HPC,choiceBlock.Unit_Block_B.Zevap_A_HPC},
    Zevap_B_HPC={choiceBlock.Unit_Block_A.Zevap_B_HPC,choiceBlock.Unit_Block_B.Zevap_B_HPC},
    Zcond_A_HPH={choiceBlock.Unit_Block_A.Zcond_A_HPH,choiceBlock.Unit_Block_B.Zcond_A_HPH},
    Zcond_B_HPH={choiceBlock.Unit_Block_A.Zcond_B_HPH,choiceBlock.Unit_Block_B.Zcond_B_HPH},
    Zcond_A_HPC={choiceBlock.Unit_Block_A.Zcond_A_HPC,choiceBlock.Unit_Block_B.Zcond_A_HPC},
    Zcond_B_HPC={choiceBlock.Unit_Block_A.Zcond_B_HPC,choiceBlock.Unit_Block_B.Zcond_B_HPC},
    Zflow_intercept={choiceBlock.Unit_Block_A.Zflow_intercept,choiceBlock.Unit_Block_B.Zflow_intercept},
    Zflow_Ncomp={choiceBlock.Unit_Block_A.Zflow_Ncomp,choiceBlock.Unit_Block_B.Zflow_Ncomp},
    Zflow_SST={choiceBlock.Unit_Block_A.Zflow_SST,choiceBlock.Unit_Block_B.Zflow_SST},
    Zflow_SDT={choiceBlock.Unit_Block_A.Zflow_SDT,choiceBlock.Unit_Block_B.Zflow_SDT},
    Zflow_OAT={choiceBlock.Unit_Block_A.Zflow_OAT,choiceBlock.Unit_Block_B.Zflow_OAT},
    Zpower_intercept={choiceBlock.Unit_Block_A.Zpower_intercept,choiceBlock.Unit_Block_B.Zpower_intercept},
    Zpower_Ncomp={choiceBlock.Unit_Block_A.Zpower_Ncomp,choiceBlock.Unit_Block_B.Zpower_Ncomp},
    Zpower_OAT={choiceBlock.Unit_Block_A.Zpower_OAT,choiceBlock.Unit_Block_B.Zpower_OAT},
    Zpower_SDT={choiceBlock.Unit_Block_A.Zpower_SDT,choiceBlock.Unit_Block_B.Zpower_SDT},
    Zpower_SST={choiceBlock.Unit_Block_A.Zpower_SST,choiceBlock.Unit_Block_B.Zpower_SST},
    Zpower_Zflow={choiceBlock.Unit_Block_A.Zpower_Zflow,choiceBlock.Unit_Block_B.Zpower_Zflow},
    FW={choiceBlock.Unit_Block_A.FW,choiceBlock.Unit_Block_B.FW},
    use_Calib=use_Calib)
    annotation (Placement(transformation(extent={{567.6224973322112,5.23843998743164},{587.6224973322112,25.23843998743164}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.Split split2(
    CoolantMedium=CoolantMedium,
    X=BrineConcentration)
    annotation (Placement(transformation(extent={{-10.0,-10.0},{10.0,10.0}},origin={74.0,-88.0},rotation=90.0)));
  .BOLT.CoolantMisc.Split split3(
    X=BrineConcentration,
    CoolantMedium=CoolantMedium)
    annotation (Placement(transformation(extent={{-10.0,-10.0},{10.0,10.0}},origin={470.0,-100.0},rotation=90.0)));
  .BOLT.CoolantMisc.Mixer mixer2(
    fa_fixed=false,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration)
    annotation (Placement(transformation(extent={{-10.0,-10.0},{10.0,10.0}},origin={74.0,154.0},rotation=90.0)));
  .BOLT.CoolantMisc.Mixer mixer3(
    X=BrineConcentration,
    CoolantMedium=CoolantMedium,
    fa_fixed=false)
    annotation (Placement(transformation(extent={{-10.0,-10.0},{10.0,10.0}},origin={472.0,142.0},rotation=90.0)));
equation
  connect(Module.Compressor_controller_A,controller_crkA.compressor)
    annotation (Line(points={{-25,10.5},{-35.99161292982694,10.5},{-35.99161292982694,11.142786159859877},{-49.910042394053335,11.142786159859877}},color={0,0,127}));
  connect(controller_crkA.exv,Module.EXV_controller_A)
    annotation (Line(points={{-49.910042394053335,8.642786159859877},{-35.99161292982694,8.642786159859877},{-35.99161292982694,20.5},{-25,20.5}},color={0,0,127}));
  connect(controller_crkA.fan,Module.Fan_controller_A)
    annotation (Line(points={{-49.910042394053335,6.142786159859877},{-35.99161292982694,6.142786159859877},{-35.99161292982694,15.5},{-25,15.5}},color={0,0,127}));
  connect(controller_crkA.pump,Module.ActuatorPumpUser_A)
    annotation (Line(points={{-49.910042394053335,3.6427861598598774},{-35.99161292982694,3.6427861598598774},{-35.99161292982694,25},{-25,25}},color={0,0,127}));
  connect(controller_crkB.pump,Module.ActuatorPumpUser_B)
    annotation (Line(points={{20.877596756835317,4.266347038854235},{10.40220664561738,4.266347038854235},{10.40220664561738,25},{-3,25}},color={0,0,127}));
  connect(Module.Fan_controller_B,controller_crkB.fan)
    annotation (Line(points={{-3,15.5},{10.40220664561738,15.5},{10.40220664561738,6.766347038854235},{20.877596756835317,6.766347038854235}},color={0,0,127}));
  connect(Module.EXV_controller_B,controller_crkB.exv)
    annotation (Line(points={{-3,20.5},{10.40220664561738,20.5},{10.40220664561738,9.266347038854235},{20.877596756835317,9.266347038854235}},color={0,0,127}));
  connect(Module.Compressor_controller_B,controller_crkB.compressor)
    annotation (Line(points={{-3,10.5},{10.40220664561738,10.5},{10.40220664561738,11.766347038854235},{20.877596756835317,11.766347038854235}},color={0,0,127}));
  connect(Module2.Compressor_controller_A,controller_crkA2.compressor)
    annotation (Line(points={{171.38956005869437,8.858677354332308},{160.39794712886743,8.858677354332308},{160.39794712886743,9.501463514192185},{146.47951766464104,9.501463514192185}},color={0,0,127}));
  connect(controller_crkA2.exv,Module2.EXV_controller_A)
    annotation (Line(points={{146.47951766464104,7.001463514192185},{160.39794712886743,7.001463514192185},{160.39794712886743,18.858677354332308},{171.38956005869437,18.858677354332308}},color={0,0,127}));
  connect(controller_crkA2.fan,Module2.Fan_controller_A)
    annotation (Line(points={{146.47951766464104,4.501463514192185},{160.39794712886743,4.501463514192185},{160.39794712886743,13.858677354332308},{171.38956005869437,13.858677354332308}},color={0,0,127}));
  connect(controller_crkA2.pump,Module2.ActuatorPumpUser_A)
    annotation (Line(points={{146.47951766464104,2.001463514192185},{160.39794712886743,2.001463514192185},{160.39794712886743,23.358677354332308},{171.38956005869437,23.358677354332308}},color={0,0,127}));
  connect(controller_crkB2.pump,Module2.ActuatorPumpUser_B)
    annotation (Line(points={{217.2671568155297,2.6250243931865427},{206.79176670431175,2.6250243931865427},{206.79176670431175,23.358677354332308},{193.38956005869437,23.358677354332308}},color={0,0,127}));
  connect(Module2.Fan_controller_B,controller_crkB2.fan)
    annotation (Line(points={{193.38956005869437,13.858677354332308},{206.79176670431175,13.858677354332308},{206.79176670431175,5.125024393186543},{217.2671568155297,5.125024393186543}},color={0,0,127}));
  connect(Module2.EXV_controller_B,controller_crkB2.exv)
    annotation (Line(points={{193.38956005869437,18.858677354332308},{206.79176670431175,18.858677354332308},{206.79176670431175,7.625024393186543},{217.2671568155297,7.625024393186543}},color={0,0,127}));
  connect(Module2.Compressor_controller_B,controller_crkB2.compressor)
    annotation (Line(points={{193.38956005869437,8.858677354332308},{206.79176670431175,8.858677354332308},{206.79176670431175,10.125024393186543},{217.2671568155297,10.125024393186543}},color={0,0,127}));
  connect(controller_crkA2.limitsBus,controllerSettings_crkA2.limitsBus)
    annotation (Line(points={{125.47951766464104,-0.49853648580781496},{84.89905520449278,-0.49853648580781496},{84.89905520449278,36.70292454649017},{94.57335137048304,36.70292454649017}},color={255,204,51}));
  connect(controller_crkB2.limitsBus,controllerSettings_crkB2.limitsBus)
    annotation (Line(points={{238.2671568155297,0.12502439318654623},{271.69659748626833,0.12502439318654623},{271.69659748626833,36.871464228457874},{263.1257277230677,36.871464228457874}},color={255,204,51}));
  connect(sourceBrine.port,split.port_c)
    annotation (Line(points={{270,-213.45395435696486},{270,-207.82697717848242},{270.2,-207.82697717848242},{270.2,-186.2}},color={0,127,0}));
  connect(mixer.port_c,external_system.port_a)
    annotation (Line(points={{270,267.06666666666666},{270,282.2802621072459}},color={0,127,0}));
  connect(external_system.port_b,sinkBrine.port)
    annotation (Line(points={{270,295.05307122608747},{270,313.1980847074238}},color={0,127,0}));
  connect(Module3.Compressor_controller_A,controller_crkA3.compressor)
    annotation (Line(points={{366.6224973322112,9.73843998743164},{355.63088440238425,9.73843998743164},{355.63088440238425,10.381226147291517},{341.71245493815786,10.381226147291517}},color={0,0,127}));
  connect(controller_crkA3.exv,Module3.EXV_controller_A)
    annotation (Line(points={{341.71245493815786,7.8812261472915175},{355.63088440238425,7.8812261472915175},{355.63088440238425,19.73843998743164},{366.6224973322112,19.73843998743164}},color={0,0,127}));
  connect(controller_crkA3.fan,Module3.Fan_controller_A)
    annotation (Line(points={{341.71245493815786,5.3812261472915175},{355.63088440238425,5.3812261472915175},{355.63088440238425,14.73843998743164},{366.6224973322112,14.73843998743164}},color={0,0,127}));
  connect(controller_crkA3.pump,Module3.ActuatorPumpUser_A)
    annotation (Line(points={{341.71245493815786,2.8812261472915175},{355.63088440238425,2.8812261472915175},{355.63088440238425,24.23843998743164},{366.6224973322112,24.23843998743164}},color={0,0,127}));
  connect(controller_crkB3.pump,Module3.ActuatorPumpUser_B)
    annotation (Line(points={{412.5000940890465,3.504787026285875},{402.0247039778286,3.504787026285875},{402.0247039778286,24.23843998743164},{388.6224973322112,24.23843998743164}},color={0,0,127}));
  connect(Module3.Fan_controller_B,controller_crkB3.fan)
    annotation (Line(points={{388.6224973322112,14.73843998743164},{402.0247039778286,14.73843998743164},{402.0247039778286,6.004787026285875},{412.5000940890465,6.004787026285875}},color={0,0,127}));
  connect(Module3.EXV_controller_B,controller_crkB3.exv)
    annotation (Line(points={{388.6224973322112,19.73843998743164},{402.0247039778286,19.73843998743164},{402.0247039778286,8.504787026285875},{412.5000940890465,8.504787026285875}},color={0,0,127}));
  connect(Module3.Compressor_controller_B,controller_crkB3.compressor)
    annotation (Line(points={{388.6224973322112,9.73843998743164},{402.0247039778286,9.73843998743164},{402.0247039778286,11.004787026285875},{412.5000940890465,11.004787026285875}},color={0,0,127}));
  connect(controller_crkA3.limitsBus,controllerSettings_crkA3.limitsBus)
    annotation (Line(points={{320.71245493815786,0.3812261472915175},{280.1319924780096,0.3812261472915175},{280.1319924780096,37.582687179589506},{289.8062886439999,37.582687179589506}},color={255,204,51}));
  connect(controller_crkB3.limitsBus,controllerSettings_crkB3.limitsBus)
    annotation (Line(points={{433.5000940890465,1.0047870262858787},{466.92953475978516,1.0047870262858787},{466.92953475978516,37.75122686155721},{458.35866499658454,37.75122686155721}},color={255,204,51}));
  connect(Module4.Compressor_controller_A,controller_crkA4.compressor)
    annotation (Line(points={{566.6224973322112,7.73843998743164},{555.6308844023843,7.73843998743164},{555.6308844023843,8.381226147291514},{541.7124549381579,8.381226147291514}},color={0,0,127}));
  connect(controller_crkA4.exv,Module4.EXV_controller_A)
    annotation (Line(points={{541.7124549381579,5.881226147291514},{555.6308844023843,5.881226147291514},{555.6308844023843,17.73843998743164},{566.6224973322112,17.73843998743164}},color={0,0,127}));
  connect(controller_crkA4.fan,Module4.Fan_controller_A)
    annotation (Line(points={{541.7124549381579,3.381226147291514},{555.6308844023843,3.381226147291514},{555.6308844023843,12.73843998743164},{566.6224973322112,12.73843998743164}},color={0,0,127}));
  connect(controller_crkA4.pump,Module4.ActuatorPumpUser_A)
    annotation (Line(points={{541.7124549381579,0.8812261472915175},{555.6308844023843,0.8812261472915175},{555.6308844023843,22.23843998743164},{566.6224973322112,22.23843998743164}},color={0,0,127}));
  connect(controller_crkB4.pump,Module4.ActuatorPumpUser_B)
    annotation (Line(points={{612.5000940890466,1.5047870262858751},{602.0247039778286,1.5047870262858751},{602.0247039778286,22.23843998743164},{588.6224973322112,22.23843998743164}},color={0,0,127}));
  connect(Module4.Fan_controller_B,controller_crkB4.fan)
    annotation (Line(points={{588.6224973322112,12.73843998743164},{602.0247039778286,12.73843998743164},{602.0247039778286,4.004787026285875},{612.5000940890466,4.004787026285875}},color={0,0,127}));
  connect(Module4.EXV_controller_B,controller_crkB4.exv)
    annotation (Line(points={{588.6224973322112,17.73843998743164},{602.0247039778286,17.73843998743164},{602.0247039778286,6.504787026285875},{612.5000940890466,6.504787026285875}},color={0,0,127}));
  connect(Module4.Compressor_controller_B,controller_crkB4.compressor)
    annotation (Line(points={{588.6224973322112,7.73843998743164},{602.0247039778286,7.73843998743164},{602.0247039778286,9.004787026285875},{612.5000940890466,9.004787026285875}},color={0,0,127}));
  connect(controller_crkA4.limitsBus,controllerSettings_crkA4.limitsBus)
    annotation (Line(points={{520.7124549381579,-1.6187738527084825},{480.1319924780096,-1.6187738527084825},{480.1319924780096,35.58268717958951},{489.8062886439999,35.58268717958951}},color={255,204,51}));
  connect(controller_crkB4.limitsBus,controllerSettings_crkB4.limitsBus)
    annotation (Line(points={{633.5000940890466,-0.9952129737141213},{666.9295347597852,-0.9952129737141213},{666.9295347597852,35.751226861557214},{658.3586649965846,35.751226861557214}},color={255,204,51}));
  connect(split.port_a,split2.port_c)
    annotation (Line(points={{264.8,-166},{264.8,-143.1},{74.2,-143.1},{74.2,-98.2}},color={0,127,0}));
  connect(split2.port_a,Module.coolant_in)
    annotation (Line(points={{68.8,-78},{68.8,-49.335297321272684},{-14.348008987101455,-49.335297321272684},{-14.348008987101455,3.329405357454629}},color={0,127,0}));
  connect(split2.port_b,Module2.coolant_in)
    annotation (Line(points={{79.2,-78},{79.2,-50.155958644106526},{182.04155107159292,-50.155958644106526},{182.04155107159292,1.6880827117869437}},color={0,127,0}));
  connect(split.port_b,split3.port_c)
    annotation (Line(points={{275.2,-166},{275.2,-144},{470.2,-144},{470.2,-110.2}},color={0,127,0}));
  connect(split3.port_a,Module3.coolant_in)
    annotation (Line(points={{464.8,-90},{464.8,-43.71607732755687},{377.2744883451097,-43.71607732755687},{377.2744883451097,2.567845344886269}},color={0,127,0}));
  connect(split3.port_b,Module4.coolant_in)
    annotation (Line(points={{475.2,-90},{475.2,-44.71607732755687},{577.2744883451097,-44.71607732755687},{577.2744883451097,0.567845344886269}},color={0,127,0}));
  connect(Module.coolant_out,mixer2.port_a)
    annotation (Line(points={{-14.26003915644383,30.40600907992107},{-14.26003915644383,87.20300453996053},{69.2,87.20300453996053},{69.2,144}},color={0,127,0}));
  connect(Module2.coolant_out,mixer2.port_b)
    annotation (Line(points={{182.12952090225053,28.764686434253385},{186,28.764686434253385},{186,88},{79.2,88},{79.2,144}},color={0,127,0}));
  connect(mixer2.port_c,mixer.port_a)
    annotation (Line(points={{74,164.4},{74,206.53333333333336},{265.2,206.53333333333336},{265.2,246.66666666666669}},color={0,127,0}));
  connect(mixer3.port_a,Module3.coolant_out)
    annotation (Line(points={{467.2,132},{467.2,92.82222453367635},{377.36245817576736,92.82222453367635},{377.36245817576736,29.64444906735271}},color={0,127,0}));
  connect(Module4.coolant_out,mixer3.port_b)
    annotation (Line(points={{577.3624581757674,27.64444906735271},{577.3624581757674,91.82222453367635},{477.2,91.82222453367635},{477.2,132}},color={0,127,0}));
  connect(mixer3.port_c,mixer.port_b)
    annotation (Line(points={{472,152.4},{472,208},{275.2,208},{275.2,246.66666666666669}},color={0,127,0}));
  connect(controller_crkA2.measurementBus,controllerSettings_crkA2.measurementBus)
    annotation (Line(points={{125.47951766464104,11.501463514192185},{119.47951766464104,11.501463514192185},{119.47951766464104,25.3410988039467},{120.90805592563942,25.3410988039467},{120.90805592563942,39.18073409370122},{114.90805592563942,39.18073409370122}},color={255,204,51}));
  connect(Module2.measurementBusA,controllerSettings_crkA2.measurementBus)
    annotation (Line(points={{174.38956005869437,26.358677354332308},{174.38956005869437,39.18073409370122},{114.90805592563942,39.18073409370122}},color={255,204,51}));
  connect(Module2.measurementBusB,controllerSettings_crkB2.measurementBus)
    annotation (Line(points={{190.38956005869437,26.358677354332308},{190.38956005869437,39.34927377566892},{242.79102316791133,39.34927377566892}},color={255,204,51}));
  connect(controller_crkB2.measurementBus,controllerSettings_crkB2.measurementBus)
    annotation (Line(points={{238.2671568155297,12.125024393186543},{244.2671568155297,12.125024393186543},{244.2671568155297,25.73714908442773},{238.34378306275278,25.73714908442773},{238.34378306275278,39.34927377566892},{242.79102316791133,39.34927377566892}},color={255,204,51}));
  connect(controllerSettings_crkA3.measurementBus,controller_crkA3.measurementBus)
    annotation (Line(points={{310.1409931991563,40.06049672680055},{316.1409931991563,40.06049672680055},{316.1409931991563,26.220861437046032},{314.71245493815786,26.220861437046032},{314.71245493815786,12.381226147291517},{320.71245493815786,12.381226147291517}},color={255,204,51}));
  connect(controllerSettings_crkA3.measurementBus,Module3.measurementBusA)
    annotation (Line(points={{310.1409931991563,40.06049672680055},{369.6224973322112,40.06049672680055},{369.6224973322112,27.23843998743164}},color={255,204,51}));
  connect(controllerSettings_crkB3.measurementBus,controller_crkB3.measurementBus)
    annotation (Line(points={{438.02396044142813,40.22903640876825},{433.5767203362696,40.22903640876825},{433.5767203362696,26.616911717527064},{439.5000940890465,26.616911717527064},{439.5000940890465,13.004787026285875},{433.5000940890465,13.004787026285875}},color={255,204,51}));
  connect(Module3.measurementBusB,controller_crkB3.measurementBus)
    annotation (Line(points={{385.6224973322112,27.23843998743164},{385.6224973322112,33.23843998743164},{439.5000940890465,33.23843998743164},{439.5000940890465,13.004787026285875},{433.5000940890465,13.004787026285875}},color={255,204,51}));
  connect(controllerSettings_crkA4.measurementBus,controller_crkA4.measurementBus)
    annotation (Line(points={{510.1409931991563,38.06049672680056},{516.1409931991564,38.06049672680056},{516.1409931991564,24.220861437046036},{514.7124549381579,24.220861437046036},{514.7124549381579,10.381226147291514},{520.7124549381579,10.381226147291514}},color={255,204,51}));
  connect(controllerSettings_crkA4.measurementBus,Module4.measurementBusA)
    annotation (Line(points={{510.1409931991563,38.06049672680056},{569.6224973322112,38.06049672680056},{569.6224973322112,25.23843998743164}},color={255,204,51}));
  connect(controllerSettings_crkB4.measurementBus,Module4.measurementBusB)
    annotation (Line(points={{638.0239604414281,38.22903640876826},{585.6224973322112,38.22903640876826},{585.6224973322112,25.23843998743164}},color={255,204,51}));
  connect(controllerSettings_crkB4.measurementBus,controller_crkB4.measurementBus)
    annotation (Line(points={{638.0239604414281,38.22903640876826},{633.5767203362695,38.22903640876826},{633.5767203362695,24.616911717527067},{639.5000940890466,24.616911717527067},{639.5000940890466,11.004787026285875},{633.5000940890466,11.004787026285875}},color={255,204,51}));
  connect(controllerSettings_crkA.measurementBus,controller_crkA.measurementBus)
    annotation (Line(points={{-81.48150413305495,40.82205673936891},{-75.48150413305495,40.82205673936891},{-75.48150413305495,26.982421449614392},{-76.91004239405333,26.982421449614392},{-76.91004239405333,13.142786159859877},{-70.91004239405333,13.142786159859877}},color={255,204,51}));
  connect(controllerSettings_crkB.measurementBus,controller_crkB.measurementBus)
    annotation (Line(points={{46.40146310921697,40.99059642133661},{41.95422300405842,40.99059642133661},{41.95422300405842,27.378471730095423},{47.87759675683532,27.378471730095423},{47.87759675683532,13.766347038854235},{41.87759675683532,13.766347038854235}},color={255,204,51}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          fillColor={229,152,23},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end System_61AQ_multimodule;
