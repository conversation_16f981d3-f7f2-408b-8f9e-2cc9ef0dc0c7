within Workspace.Icons;
partial model CompressorIcon
  //extends VFD30XW.Icons.InitializationCycle2;
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100,-100},{100,100}}),
      graphics={
        Rectangle(
          extent={{-100,100},{100,-100}},
          lineColor={0,0,0},
          fillColor={255,255,255},
          fillPattern=FillPattern.Solid),
        Text(
          extent={{-134,-128},{134,-146}},
          lineColor={0,0,0},
          fillColor={255,0,0},
          fillPattern=FillPattern.Solid,
          textString="%name"),
        Ellipse(
          extent={{-82,82},{82,-82}},
          lineColor={0,0,0},
          fillColor={255,255,255},
          fillPattern=FillPattern.Solid),
        Polygon(
          points={{-66,-48},{-40,72},{40,72},{66,-48},{-66,-48}},
          lineColor={0,0,0},
          smooth=Smooth.None,
          fillColor={255,0,0},
          fillPattern=FillPattern.Solid)}));
end CompressorIcon;
