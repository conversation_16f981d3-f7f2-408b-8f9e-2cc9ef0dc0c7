within Workspace.Icons;
partial class CyclesPackage
  extends Workspace.Icons.PackageIcon;
  annotation (
    Icon(
      graphics={
        Rectangle(
          extent={{-24,46},{32,32}},
          lineColor={255,0,0},
          lineThickness=0.5,
          fillColor={0,127,0},
          fillPattern=FillPattern.HorizontalCylinder),
        Line(
          points={{48,14},{36,-26},{66,-26},{54,14},{48,14}},
          color={255,0,0},
          smooth=Smooth.None),
        Line(
          points={{-62,10},{-38,10},{-62,-26},{-38,-26},{-62,10}},
          color={255,0,0},
          smooth=Smooth.None),
        Rectangle(
          extent={{-22,-48},{34,-62}},
          lineColor={255,0,0},
          lineThickness=0.5,
          fillColor={0,127,0},
          fillPattern=FillPattern.HorizontalCylinder),
        Line(
          points={{50,14},{50,40},{32,40}},
          color={255,0,0},
          smooth=Smooth.None),
        Line(
          points={{-24,40},{-50,40},{-50,10}},
          color={255,0,0},
          smooth=Smooth.None),
        Line(
          points={{-50,-26},{-50,-56},{-22,-56}},
          color={255,0,0},
          smooth=Smooth.None),
        Line(
          points={{34,-56},{52,-56},{52,-26}},
          color={255,0,0},
          smooth=Smooth.None)}));
end CyclesPackage;
