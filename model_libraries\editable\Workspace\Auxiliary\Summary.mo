within Workspace.Auxiliary;
package Summary
  record Summary_CO
    extends Modelica.Icons.Record;
    Real charge
      "system charge";
    Modelica.SIunits.HeatFlowRate capacity_design(
      displayUnit="kW")
      "designed system capacity";
    Modelica.SIunits.Volume V_dis
      "compressor discharge volume";
    Modelica.SIunits.Volume V_suc
      "compressor suction volume";
    BOLT.InternalLibrary.Compressors.PD_2Port.SubComponents.PD.DataBase.Polynomial.ARI_FS.Selector selector_CompARI
      "compressor type";
    Integer nCompmax
      "max compressor number";
    Integer nCoils
      "Number of coils of MCHX";
    Integer n_inlet
      "Number of inlet tubes feeding inlet header of MCHX";
    Integer n_passes
      "Number of passes of each slab";
    Integer n_tubes_1
      "Number of tubes of pass 1";
    Integer n_tubes_2
      "Number of tubes of pass 2";
    Modelica.SIunits.Length length_tube
      "length between tube sheets of MCHX";
    Real diameter_in_header
      "Inside diameter of the headers at each end of MCHX";
    Real length_tube_inlet
      "Length of each inlet tube of MCHX";
    Real diameter_in_inletTube
      "inside diameter of inlet tubes of MCHX";
    Real length_outlet
      "length of each outlet of MCHX";
    Real diameter_in_outlet
      "Inside diameter of outlets";
    BOLT.InternalLibrary.HeatExchangers.RefAir.DataBase.MCHXGeo.SelectorCurve selector_curve_MCHX
      "curve data of MCHX";
    Modelica.SIunits.Length DportB_a
      "Brine side inlet connection port diameter of BPHE";
    Modelica.SIunits.Length DportB_b
      "Brine side outlet connection port diameter of BPHE";
    Modelica.SIunits.Length DportR_a
      "Refrigerant side inlet connection port diameter of BPHE";
    Modelica.SIunits.Length DportR_b
      "Refrigerant side outlet connection port diameter of BPHE";
    Integer nPlate
      "Number of plates of BPHE";
    RBBP_1C_BlackBoxLibrary.GeoSelector selectGeo_BPHE
      "BPHE geometry type";
    BOLT.InternalLibrary.Refrigerant.Valves.TwoPort.DataBase.Selector selector_flowData
      "EXV flow data";
    Workspace.SystemHorizon_P2.ControllerHorizon_P2.Components.Types.StagingSelector_1C stagingSelector
      "staging selector for controller";
    Modelica.SIunits.Conversions.NonSIunits.AngularVelocity_rpm max_fan_rpm
      "maximum fan speed ";
    Modelica.SIunits.Frequency max_motor_frequency
      "maximum motor frequency";
    Modelica.SIunits.Length suctionLine_Di
      "diameter of suction line";
    Modelica.SIunits.Length suctionLine_length
      "length of suction line";
    Modelica.SIunits.Length dischargeLine_Di
      "diameter of discharge line";
    Modelica.SIunits.Length dischargeLine_length
      "length of discharge line";
    Modelica.SIunits.Length liquidLine_Di
      "diameter of liquid line";
    Modelica.SIunits.Length liquidLine_length
      "length of liquid line";
    Modelica.SIunits.Area duct_Ac
      "cross sectional area of air duct";
    Real duct_Ka
      "air pressure drop coefficient of air duct";
    Modelica.SIunits.ThermalConductance duct_UA
      "overall thermal conductance of air duct";
    Modelica.SIunits.Area externalDuct_Ac
      "cross sectional area of external air duct";
    Modelica.SIunits.ThermalConductance externalDuct_UA
      "overall thermal conductance of external air duct";
    Modelica.SIunits.Temperature duct_T_amb
      "ambient temperature when calculating heat loss from air to ambient";
    Real pumpPoly_Fw
      "fraction factor of power into luid stream of pump";
    Real a_p
      "calibration factor";
    Real b_p
      "calibration factor";
    Real a_uev
      "calibration factor";
    Real b_uev
      "calibration factor";
    Real a_ucd
      "calibration factor";
    Real b_ucd
      "calibration factor";
    Real Z_Power_fl
      "calibration factor";
    Real ZU_Evap_fl
      "calibration factor";
    Real ZU_Cond_fl
      "calibration factor";
    Real a_dp_ref_suc
      "calibration factor";
    Real b_dp_ref_suc
      "calibration factor";
    Real a_flow
      "calibration factor";
    Real b_flow
      "calibration factor";
    Real Fw
      "calibration factor";
    Real minimalLoad
      "calibration factor";
    Real Z_dpr
      "calibration factor";
    Real dp_reference_discharge
      "calibration factor";
    Real dp_reference_liquid
      "calibration factor";
    Real dp_reference_suction_min
      "calibration factor";
    Real c_oat
      "calibration factor";
    Real c_lwt
      "calibration factor";
    Real c_tpcoil
      "calibration factor";
    Real c_load
      "calibration factor";
    Real c_oat2
      "calibration factor";
    Real c_lwt_oat
      "calibration factor";
    Real c_oat_tpcoil
      "calibration factor";
    Real c_oat_load
      "calibration factor";
    Real c_lwt2
      "calibration factor";
    Real c_lwt_tpcoil
      "calibration factor";
    Real c_lwt_load
      "calibration factor";
    Real c_tpcoil2
      "calibration factor";
    Real c_load_tpcoil
      "calibration factor";
    Real c_load2
      "calibration factor";
    Real c_const
      "calibration factor";
    Real coilcoating
      "calibration factor";
    BOLT.InternalLibrary.Coolant.Pumps.DataBase.PumpPoly.Selector selectorPump
      "selector pump";
    annotation (
      Icon(
        coordinateSystem(
          preserveAspectRatio=false)),
      Diagram(
        coordinateSystem(
          preserveAspectRatio=false)));
  end Summary_CO;
  record Summary_CO_2C
    extends Modelica.Icons.Record;
    Real charge_A
      "system charge";
    Real charge_B
      "system charge";
    Modelica.SIunits.HeatFlowRate capacity_design(
      displayUnit="kW")
      "designed system capacity";
    Modelica.SIunits.Volume V_dis_A
      "compressor discharge volume";
    Modelica.SIunits.Volume V_suc_A
      "compressor suction volume";
    Modelica.SIunits.Volume V_dis_B
      "compressor discharge volume";
    Modelica.SIunits.Volume V_suc_B
      "compressor suction volume";
    BOLT.InternalLibrary.Compressors.PD_2Port.SubComponents.PD.DataBase.Polynomial.ARI_FS.Selector selector_CompARI_A
      "compressor type";
    BOLT.InternalLibrary.Compressors.PD_2Port.SubComponents.PD.DataBase.Polynomial.ARI_FS.Selector selector_CompARI_B
      "compressor type";
    Integer nCompmax_A
      "max compressor number";
    Integer nCoils_A
      "Number of coils of MCHX";
    Integer nCompmax_B
      "max compressor number";
    Integer nCoils_B
      "Number of coils of MCHX";
    Integer n_inlet_A
      "Number of inlet tubes feeding inlet header of MCHX";
    Integer n_passes_A
      "Number of passes of each slab";
    Integer n_tubes_1_A
      "Number of tubes of pass 1";
    Integer n_tubes_2_A
      "Number of tubes of pass 2";
    Modelica.SIunits.Length length_tube_A
      "length between tube sheets of MCHX";
    Real diameter_in_header_A
      "Inside diameter of the headers at each end of MCHX";
    Real length_tube_inlet_A
      "Length of each inlet tube of MCHX";
    Real diameter_in_inletTube_A
      "inside diameter of inlet tubes of MCHX";
    Real length_outlet_A
      "length of each outlet of MCHX";
    Real diameter_in_outlet_A
      "Inside diameter of outlets";
    Integer n_inlet_B
      "Number of inlet tubes feeding inlet header of MCHX";
    Integer n_passes_B
      "Number of passes of each slab";
    Integer n_tubes_1_B
      "Number of tubes of pass 1";
    Integer n_tubes_2_B
      "Number of tubes of pass 2";
    Modelica.SIunits.Length length_tube_B
      "length between tube sheets of MCHX";
    Real diameter_in_header_B
      "Inside diameter of the headers at each end of MCHX";
    Real length_tube_inlet_B
      "Length of each inlet tube of MCHX";
    Real diameter_in_inletTube_B
      "inside diameter of inlet tubes of MCHX";
    Real length_outlet_B
      "length of each outlet of MCHX";
    Real diameter_in_outlet_B
      "Inside diameter of outlets";
    BOLT.InternalLibrary.HeatExchangers.RefAir.DataBase.MCHXGeo.SelectorCurve selector_curve_MCHX_A
      "curve data of MCHX";
    BOLT.InternalLibrary.HeatExchangers.RefAir.DataBase.MCHXGeo.SelectorCurve selector_curve_MCHX_B
      "curve data of MCHX";
    Modelica.SIunits.Length DportB_a
      "Brine side inlet connection port diameter of BPHE";
    Modelica.SIunits.Length DportB_b
      "Brine side outlet connection port diameter of BPHE";
    Modelica.SIunits.Length DportR_a
      "Refrigerant side inlet connection port diameter of BPHE";
    Modelica.SIunits.Length DportR_b
      "Refrigerant side outlet connection port diameter of BPHE";
    Integer nPlate
      "Number of plates of BPHE";
    RBBP_2C_BlackBoxLibrary.GeoSelector selectGeo_BPHE
      "BPHE geometry type";
    BOLT.InternalLibrary.Refrigerant.Valves.TwoPort.DataBase.Selector selector_flowData_A
      "EXV flow data";
    BOLT.InternalLibrary.Refrigerant.Valves.TwoPort.DataBase.Selector selector_flowData_B
      "EXV flow data";
    Workspace.SystemHorizon_P2.ControllerHorizon_P2.Components.Types.StagingSelector_2C stagingSelector
      "staging selector for controller";
    Modelica.SIunits.Conversions.NonSIunits.AngularVelocity_rpm max_fan_rpm
      "standard maximum fan speed ";
    Modelica.SIunits.Frequency max_motor_frequency
      "standard maximum motor frequency";
    Modelica.SIunits.Length suctionLine_Di_A
      "diameter of suction line";
    Modelica.SIunits.Length suctionLine_length_A
      "length of suction line";
    Modelica.SIunits.Length suctionLine_Di_B
      "diameter of suction line";
    Modelica.SIunits.Length suctionLine_length_B
      "length of suction line";
    Modelica.SIunits.Length dischargeLine_Di_A
      "diameter of discharge line";
    Modelica.SIunits.Length dischargeLine_length_A
      "length of discharge line";
    Modelica.SIunits.Length dischargeLine_Di_B
      "diameter of discharge line";
    Modelica.SIunits.Length dischargeLine_length_B
      "length of discharge line";
    Modelica.SIunits.Length liquidLine_Di_A
      "diameter of liquid line";
    Modelica.SIunits.Length liquidLine_length_A
      "length of liquid line";
    Modelica.SIunits.CoefficientOfHeatTransfer liquidLine_U_A
      "overall heat trnsfer conefficient of liquid line";
    Modelica.SIunits.Length liquidLine_Di_B
      "diameter of liquid line";
    Modelica.SIunits.Length liquidLine_length_B
      "length of liquid line";
    Modelica.SIunits.CoefficientOfHeatTransfer liquidLine_U_B
      "overall heat trnsfer conefficient of liquid line";
    Modelica.SIunits.Area duct_Ac_A
      "cross sectional area of air duct";
    Real duct_Ka_A
      "air pressure drop coefficient of air duct";
    Modelica.SIunits.ThermalConductance duct_UA_A
      "overall thermal conductance of air duct";
    Modelica.SIunits.Area duct_Ac_B
      "cross sectional area of air duct";
    Real duct_Ka_B
      "air pressure drop coefficient of air duct";
    Modelica.SIunits.ThermalConductance duct_UA_B
      "overall thermal conductance of air duct";
    Modelica.SIunits.Area externalDuct_Ac_A
      "cross sectional area of external air duct";
    Modelica.SIunits.ThermalConductance externalDuct_UA_A
      "overall thermal conductance of external air duct";
    Modelica.SIunits.Area externalDuct_Ac_B
      "cross sectional area of external air duct";
    Modelica.SIunits.ThermalConductance externalDuct_UA_B
      "overall thermal conductance of external air duct";
    Modelica.SIunits.Temperature duct_T_amb_A
      "ambient temperature when calculating heat loss from air to ambient";
    Modelica.SIunits.Temperature duct_T_amb_B
      "ambient temperature when calculating heat loss from air to ambient";
    Real pumpPoly_Fw
      "fraction factor of power into luid stream of pump";
    Real a_p
      "calibration factor";
    Real b_p
      "calibration factor";
    Real a_uev
      "calibration factor";
    Real b_uev
      "calibration factor";
    Real a_ucd
      "calibration factor";
    Real b_ucd
      "calibration factor";
    Real Z_Power_fl
      "calibration factor";
    Real ZU_Evap_fl
      "calibration factor";
    Real ZU_Cond_fl
      "calibration factor";
    Real a_dp_ref_suc_A
      "calibration factor";
    Real b_dp_ref_suc_A
      "calibration factor";
    Real a_dp_ref_suc_B
      "calibration factor";
    Real b_dp_ref_suc_B
      "calibration factor";
    Real a_flow
      "calibration factor";
    Real b_flow
      "calibration factor";
    Real Fw_A
      "calibration factor";
    Real Fw_B
      "calibration factor";
    Real minimalLoad
      "calibration factor";
    Real Z_dpr
      "calibration factor";
    Real dp_reference_discharge_A
      "calibration factor";
    Real dp_reference_liquid_A
      "calibration factor";
    Real dp_reference_suction_min_A
      "calibration factor";
    Real dp_reference_discharge_B
      "calibration factor";
    Real dp_reference_liquid_B
      "calibration factor";
    Real dp_reference_suction_min_B
      "calibration factor";
    Real c_oat
      "calibration factor";
    Real c_lwt
      "calibration factor";
    Real c_tpcoil
      "calibration factor";
    Real c_load
      "calibration factor";
    Real c_oat2
      "calibration factor";
    Real c_lwt_oat
      "calibration factor";
    Real c_oat_tpcoil
      "calibration factor";
    Real c_oat_load
      "calibration factor";
    Real c_lwt2
      "calibration factor";
    Real c_lwt_tpcoil
      "calibration factor";
    Real c_lwt_load
      "calibration factor";
    Real c_tpcoil2
      "calibration factor";
    Real c_load_tpcoil
      "calibration factor";
    Real c_load2
      "calibration factor";
    Real c_const
      "calibration factor";
    Real coilcoating_A
      "calibration factor";
    Real coilcoating_B
      "calibration factor";
    BOLT.InternalLibrary.Coolant.Pumps.DataBase.PumpPoly.Selector selectorPump
      "selector pump";
    annotation (
      Icon(
        coordinateSystem(
          preserveAspectRatio=false)),
      Diagram(
        coordinateSystem(
          preserveAspectRatio=false)));
  end Summary_CO_2C;
  record Summary_CO_Surr
    extends Modelica.Icons.Record;
    Real charge
      "system charge";
    Modelica.SIunits.HeatFlowRate capacity_design(
      displayUnit="kW")
      "designed system capacity";
    Modelica.SIunits.Volume V_dis
      "compressor discharge volume";
    Modelica.SIunits.Volume V_suc
      "compressor suction volume";
    BOLT.InternalLibrary.Compressors.PD_2Port.SubComponents.PD.DataBase.Polynomial.ARI_FS.Selector selector_CompARI
      "compressor type";
    Integer nCompmax
      "max compressor number";
    Integer nCoils
      "Number of coils of MCHX";
    Real ie;
    BOLT.InternalLibrary.Refrigerant.Valves.TwoPort.DataBase.Selector selector_flowData
      "EXV flow data";
    Workspace.SystemHorizon_P2.ControllerHorizon_P2.Components.Types.StagingSelector_1C stagingSelector
      "staging selector for controller";
    Modelica.SIunits.Conversions.NonSIunits.AngularVelocity_rpm max_fan_rpm
      "maximum fan speed ";
    Modelica.SIunits.Frequency max_motor_frequency
      "maximum motor frequency";
    Modelica.SIunits.Length suctionLine_Di
      "diameter of suction line";
    Modelica.SIunits.Length suctionLine_length
      "length of suction line";
    Modelica.SIunits.Length dischargeLine_Di
      "diameter of discharge line";
    Modelica.SIunits.Length dischargeLine_length
      "length of discharge line";
    Modelica.SIunits.Length liquidLine_Di
      "diameter of liquid line";
    Modelica.SIunits.Length liquidLine_length
      "length of liquid line";
    Modelica.SIunits.Area duct_Ac
      "cross sectional area of air duct";
    Real duct_Ka
      "air pressure drop coefficient of air duct";
    Modelica.SIunits.ThermalConductance duct_UA
      "overall thermal conductance of air duct";
    Modelica.SIunits.Area externalDuct_Ac
      "cross sectional area of external air duct";
    Modelica.SIunits.ThermalConductance externalDuct_UA
      "overall thermal conductance of external air duct";
    Modelica.SIunits.Temperature duct_T_amb
      "ambient temperature when calculating heat loss from air to ambient";
    Real pumpPoly_Fw
      "fraction factor of power into luid stream of pump";
    Real a_p
      "calibration factor";
    Real b_p
      "calibration factor";
    Real a_uev
      "calibration factor";
    Real b_uev
      "calibration factor";
    Real a_ucd
      "calibration factor";
    Real b_ucd
      "calibration factor";
    Real Z_Power_fl
      "calibration factor";
    Real ZU_Evap_fl
      "calibration factor";
    Real ZU_Cond_fl
      "calibration factor";
    Real a_dp_ref_suc
      "calibration factor";
    Real b_dp_ref_suc
      "calibration factor";
    Real a_flow
      "calibration factor";
    Real b_flow
      "calibration factor";
    Real Fw
      "calibration factor";
    Real minimalLoad
      "calibration factor";
    Real Z_dpr
      "calibration factor";
    Real dp_reference_discharge
      "calibration factor";
    Real dp_reference_liquid
      "calibration factor";
    Real dp_reference_suction_min
      "calibration factor";
    Real c_oat
      "calibration factor";
    Real c_lwt
      "calibration factor";
    Real c_tpcoil
      "calibration factor";
    Real c_load
      "calibration factor";
    Real c_oat2
      "calibration factor";
    Real c_lwt_oat
      "calibration factor";
    Real c_oat_tpcoil
      "calibration factor";
    Real c_oat_load
      "calibration factor";
    Real c_lwt2
      "calibration factor";
    Real c_lwt_tpcoil
      "calibration factor";
    Real c_lwt_load
      "calibration factor";
    Real c_tpcoil2
      "calibration factor";
    Real c_load_tpcoil
      "calibration factor";
    Real c_load2
      "calibration factor";
    Real c_const
      "calibration factor";
    Real coilcoating
      "calibration factor";
    BOLT.InternalLibrary.Coolant.Pumps.DataBase.PumpPoly.Selector selectorPump
      "selector pump";
    annotation (
      Icon(
        coordinateSystem(
          preserveAspectRatio=false)),
      Diagram(
        coordinateSystem(
          preserveAspectRatio=false)));
  end Summary_CO_Surr;
  record Summary_CO_Surr_2C
    extends Modelica.Icons.Record;
    Real charge_A
      "system charge";
    Real charge_B
      "system charge";
    Modelica.SIunits.HeatFlowRate capacity_design(
      displayUnit="kW")
      "designed system capacity";
    Modelica.SIunits.Volume V_dis_A
      "compressor discharge volume";
    Modelica.SIunits.Volume V_suc_A
      "compressor suction volume";
    Modelica.SIunits.Volume V_dis_B
      "compressor discharge volume";
    Modelica.SIunits.Volume V_suc_B
      "compressor suction volume";
    BOLT.InternalLibrary.Compressors.PD_2Port.SubComponents.PD.DataBase.Polynomial.ARI_FS.Selector selector_CompARI_A
      "compressor type";
    BOLT.InternalLibrary.Compressors.PD_2Port.SubComponents.PD.DataBase.Polynomial.ARI_FS.Selector selector_CompARI_B
      "compressor type";
    Integer nCompmax_A
      "max compressor number";
    Integer nCoils_A
      "Number of coils of MCHX";
    Integer nCompmax_B
      "max compressor number";
    Integer nCoils_B
      "Number of coils of MCHX";
    Real ie;
    BOLT.InternalLibrary.Refrigerant.Valves.TwoPort.DataBase.Selector selector_flowData_A
      "EXV flow data";
    BOLT.InternalLibrary.Refrigerant.Valves.TwoPort.DataBase.Selector selector_flowData_B
      "EXV flow data";
    Workspace.SystemHorizon_P2.ControllerHorizon_P2.Components.Types.StagingSelector_2C stagingSelector
      "staging selector for controller";
    Modelica.SIunits.Conversions.NonSIunits.AngularVelocity_rpm max_fan_rpm
      "standard maximum fan speed ";
    Modelica.SIunits.Frequency max_motor_frequency
      "standard maximum motor frequency";
    Modelica.SIunits.Length suctionLine_Di_A
      "diameter of suction line";
    Modelica.SIunits.Length suctionLine_length_A
      "length of suction line";
    Modelica.SIunits.Length suctionLine_Di_B
      "diameter of suction line";
    Modelica.SIunits.Length suctionLine_length_B
      "length of suction line";
    Modelica.SIunits.Length dischargeLine_Di_A
      "diameter of discharge line";
    Modelica.SIunits.Length dischargeLine_length_A
      "length of discharge line";
    Modelica.SIunits.Length dischargeLine_Di_B
      "diameter of discharge line";
    Modelica.SIunits.Length dischargeLine_length_B
      "length of discharge line";
    Modelica.SIunits.Length liquidLine_Di_A
      "diameter of liquid line";
    Modelica.SIunits.Length liquidLine_length_A
      "length of liquid line";
    Modelica.SIunits.CoefficientOfHeatTransfer liquidLine_U_A
      "overall heat trnsfer conefficient of liquid line";
    Modelica.SIunits.Length liquidLine_Di_B
      "diameter of liquid line";
    Modelica.SIunits.Length liquidLine_length_B
      "length of liquid line";
    Modelica.SIunits.CoefficientOfHeatTransfer liquidLine_U_B
      "overall heat trnsfer conefficient of liquid line";
    Modelica.SIunits.Area duct_Ac_A
      "cross sectional area of air duct";
    Real duct_Ka_A
      "air pressure drop coefficient of air duct";
    Modelica.SIunits.ThermalConductance duct_UA_A
      "overall thermal conductance of air duct";
    Modelica.SIunits.Area duct_Ac_B
      "cross sectional area of air duct";
    Real duct_Ka_B
      "air pressure drop coefficient of air duct";
    Modelica.SIunits.ThermalConductance duct_UA_B
      "overall thermal conductance of air duct";
    Modelica.SIunits.Area externalDuct_Ac_A
      "cross sectional area of external air duct";
    Modelica.SIunits.ThermalConductance externalDuct_UA_A
      "overall thermal conductance of external air duct";
    Modelica.SIunits.Area externalDuct_Ac_B
      "cross sectional area of external air duct";
    Modelica.SIunits.ThermalConductance externalDuct_UA_B
      "overall thermal conductance of external air duct";
    Modelica.SIunits.Temperature duct_T_amb_A
      "ambient temperature when calculating heat loss from air to ambient";
    Modelica.SIunits.Temperature duct_T_amb_B
      "ambient temperature when calculating heat loss from air to ambient";
    Real pumpPoly_Fw
      "fraction factor of power into luid stream of pump";
    Real a_p
      "calibration factor";
    Real b_p
      "calibration factor";
    Real a_uev
      "calibration factor";
    Real b_uev
      "calibration factor";
    Real a_ucd
      "calibration factor";
    Real b_ucd
      "calibration factor";
    Real Z_Power_fl
      "calibration factor";
    Real ZU_Evap_fl
      "calibration factor";
    Real ZU_Cond_fl
      "calibration factor";
    Real a_dp_ref_suc_A
      "calibration factor";
    Real b_dp_ref_suc_A
      "calibration factor";
    Real a_dp_ref_suc_B
      "calibration factor";
    Real b_dp_ref_suc_B
      "calibration factor";
    Real a_flow
      "calibration factor";
    Real b_flow
      "calibration factor";
    Real Fw_A
      "calibration factor";
    Real Fw_B
      "calibration factor";
    Real minimalLoad
      "calibration factor";
    Real Z_dpr
      "calibration factor";
    Real dp_reference_discharge_A
      "calibration factor";
    Real dp_reference_liquid_A
      "calibration factor";
    Real dp_reference_suction_min_A
      "calibration factor";
    Real dp_reference_discharge_B
      "calibration factor";
    Real dp_reference_liquid_B
      "calibration factor";
    Real dp_reference_suction_min_B
      "calibration factor";
    Real c_oat
      "calibration factor";
    Real c_lwt
      "calibration factor";
    Real c_tpcoil
      "calibration factor";
    Real c_load
      "calibration factor";
    Real c_oat2
      "calibration factor";
    Real c_lwt_oat
      "calibration factor";
    Real c_oat_tpcoil
      "calibration factor";
    Real c_oat_load
      "calibration factor";
    Real c_lwt2
      "calibration factor";
    Real c_lwt_tpcoil
      "calibration factor";
    Real c_lwt_load
      "calibration factor";
    Real c_tpcoil2
      "calibration factor";
    Real c_load_tpcoil
      "calibration factor";
    Real c_load2
      "calibration factor";
    Real c_const
      "calibration factor";
    Real coilcoating_A
      "calibration factor";
    Real coilcoating_B
      "calibration factor";
    BOLT.InternalLibrary.Coolant.Pumps.DataBase.PumpPoly.Selector selectorPump
      "selector pump";
    annotation (
      Icon(
        coordinateSystem(
          preserveAspectRatio=false)),
      Diagram(
        coordinateSystem(
          preserveAspectRatio=false)));
  end Summary_CO_Surr_2C;
  record Summary_HPC
    extends Modelica.Icons.Record;
    Real charge
      "system charge";
    Modelica.SIunits.HeatFlowRate capacity_design(
      displayUnit="kW")
      "designed system capacity";
    Modelica.SIunits.Volume V_dis
      "compressor discharge volume";
    Modelica.SIunits.Volume V_suc
      "compressor suction volume";
    BOLT.InternalLibrary.Compressors.PD_2Port.SubComponents.PD.DataBase.Polynomial.ARI_FS.Selector selector_CompARI
      "compressor type";
    Integer nCompmax
      "max compressor number";
    Integer nCoils
      "Number of coils of RTPF";
    Modelica.SIunits.Length DportB_a
      "Brine side inlet connection port diameter of BPHE";
    Modelica.SIunits.Length DportB_b
      "Brine side outlet connection port diameter of BPHE";
    Modelica.SIunits.Length DportR_a
      "Refrigerant side inlet connection port diameter of BPHE";
    Modelica.SIunits.Length DportR_b
      "Refrigerant side outlet connection port diameter of BPHE";
    Integer nPlate
      "Number of plates of BPHE";
    RBBP_1C_BlackBoxLibrary.GeoSelector selectGeo_BPHE
      "BPHE geometry type";
    BOLT.InternalLibrary.Refrigerant.Valves.TwoPort.DataBase.Selector selector_flowData
      "EXV flow data";
    Workspace.SystemHorizon_P2.ControllerHorizon_P2.Components.Types.StagingSelector_1C stagingSelector
      "staging selector for controller";
    Modelica.SIunits.Conversions.NonSIunits.AngularVelocity_rpm max_fan_rpm
      "maximum fan speed ";
    Modelica.SIunits.Frequency max_motor_frequency
      "maximum motor frequency";
    Modelica.SIunits.Length suctionLine_Di
      "diameter of suction line";
    Modelica.SIunits.Length suctionLine_length
      "length of suction line";
    Modelica.SIunits.Length dischargeLine_Di
      "diameter of discharge line";
    Modelica.SIunits.Length dischargeLine_length
      "length of discharge line";
    Modelica.SIunits.Length liquidLine_Di
      "diameter of liquid line";
    Modelica.SIunits.Length liquidLine_length
      "length of liquid line";
    Modelica.SIunits.Area duct_Ac
      "cross sectional area of air duct";
    Real duct_Ka
      "air pressure drop coefficient of air duct";
    Modelica.SIunits.ThermalConductance duct_UA
      "overall thermal conductance of air duct";
    Modelica.SIunits.Area externalDuct_Ac
      "cross sectional area of external air duct";
    Modelica.SIunits.ThermalConductance externalDuct_UA
      "overall thermal conductance of external air duct";
    Modelica.SIunits.Temperature duct_T_amb
      "ambient temperature when calculating heat loss from air to ambient";
    Real pumpPoly_Fw
      "fraction factor of power into luid stream of pump";
    Real a_p
      "calibration factor";
    Real b_p
      "calibration factor";
    Real a_uev
      "calibration factor";
    Real b_uev
      "calibration factor";
    Real a_ucd
      "calibration factor";
    Real b_ucd
      "calibration factor";
    Real Z_Power_fl
      "calibration factor";
    Real ZU_Evap_fl
      "calibration factor";
    Real ZU_Cond_fl
      "calibration factor";
    Real a_dp_ref_suc
      "calibration factor";
    Real b_dp_ref_suc
      "calibration factor";
    Real a_flow
      "calibration factor";
    Real b_flow
      "calibration factor";
    Real Fw
      "calibration factor";
    Real minimalLoad
      "calibration factor";
    Real Z_dpr
      "calibration factor";
    Real dp_reference_discharge
      "calibration factor";
    Real dp_reference_liquid
      "calibration factor";
    Real dp_reference_suction_min
      "calibration factor";
    Real c_oat
      "calibration factor";
    Real c_lwt
      "calibration factor";
    Real c_tpcoil
      "calibration factor";
    Real c_load
      "calibration factor";
    Real c_oat2
      "calibration factor";
    Real c_lwt_oat
      "calibration factor";
    Real c_oat_tpcoil
      "calibration factor";
    Real c_oat_load
      "calibration factor";
    Real c_lwt2
      "calibration factor";
    Real c_lwt_tpcoil
      "calibration factor";
    Real c_lwt_load
      "calibration factor";
    Real c_tpcoil2
      "calibration factor";
    Real c_load_tpcoil
      "calibration factor";
    Real c_load2
      "calibration factor";
    Real c_const
      "calibration factor";
    Real coilcoating
      "calibration factor";
    BOLT.InternalLibrary.Coolant.Pumps.DataBase.PumpPoly.Selector selectorPump
      "selector pump";
    Integer Itube
      "Tube number for inside surface performance";
    Integer nCr
      "Number of circuits";
    Integer Nrow
      "Number of tube rows";
    Integer Ntube
      "Number of tubes per row";
    Modelica.SIunits.Length Ltube
      "Tube length between tube sheets(RTPF)/headers(MCHX)";
    Modelica.SIunits.Length Dotube
      "Expanded tube outer diameter";
    Modelica.SIunits.Length Ttube
      "Expanded tube wall thickness";
    Modelica.SIunits.Length Ptube
      "Tube pitch, within rows";
    Modelica.SIunits.Length Prow
      "Row pitch, between rows";
    Real Dfin
      "Fin density (number of fins/inch)";
    Modelica.SIunits.Thickness Tfin
      "Fin thickness";
    Modelica.SIunits.Length CoilLine_Di
      "diameter of Coil line";
    Modelica.SIunits.Length CoilLine_length
      "length of Coil line";
    Integer CoilLine_nBends
      "length of Coil line";
    Modelica.SIunits.Length BPHELine_Di
      "diameter of BPHE line";
    Modelica.SIunits.Length BPHELine_length
      "length of BPHE line";
    Integer BPHELine_nBends
      "length of BPHE line";
    Real Load;
    Real ie_EN14511;
    annotation (
      Icon(
        coordinateSystem(
          preserveAspectRatio=false)),
      Diagram(
        coordinateSystem(
          preserveAspectRatio=false)));
  end Summary_HPC;
  record Summary_HPC_2C
    extends Modelica.Icons.Record;
    Real charge_A
      "system charge";
    Real charge_B
      "system charge";
    Modelica.SIunits.HeatFlowRate capacity_design(
      displayUnit="kW")
      "designed system capacity";
    Modelica.SIunits.Volume V_dis_A
      "compressor discharge volume";
    Modelica.SIunits.Volume V_suc_A
      "compressor suction volume";
    Modelica.SIunits.Volume V_dis_B
      "compressor discharge volume";
    Modelica.SIunits.Volume V_suc_B
      "compressor suction volume";
    BOLT.InternalLibrary.Compressors.PD_2Port.SubComponents.PD.DataBase.Polynomial.ARI_FS.Selector selector_CompARI_A
      "compressor type";
    BOLT.InternalLibrary.Compressors.PD_2Port.SubComponents.PD.DataBase.Polynomial.ARI_FS.Selector selector_CompARI_B
      "compressor type";
    Integer nCompmax_A
      "max compressor number";
    Integer nCoils_A
      "Number of coils ";
    Integer nCompmax_B
      "max compressor number";
    Integer nCoils_B
      "Number of coils ";
    Modelica.SIunits.Length DportB_a
      "Brine side inlet connection port diameter of BPHE";
    Modelica.SIunits.Length DportB_b
      "Brine side outlet connection port diameter of BPHE";
    Modelica.SIunits.Length DportR_a
      "Refrigerant side inlet connection port diameter of BPHE";
    Modelica.SIunits.Length DportR_b
      "Refrigerant side outlet connection port diameter of BPHE";
    Integer nPlate
      "Number of plates of BPHE";
    RBBP_2C_BlackBoxLibrary.GeoSelector selectGeo_BPHE
      "BPHE geometry type";
    BOLT.InternalLibrary.Refrigerant.Valves.TwoPort.DataBase.Selector selector_flowData_A
      "EXV flow data";
    BOLT.InternalLibrary.Refrigerant.Valves.TwoPort.DataBase.Selector selector_flowData_B
      "EXV flow data";
    Workspace.SystemHorizon_P2.ControllerHorizon_P2.Components.Types.StagingSelector_2C stagingSelector
      "staging selector for controller";
    Modelica.SIunits.Conversions.NonSIunits.AngularVelocity_rpm max_fan_rpm
      "standard maximum fan speed ";
    Modelica.SIunits.Frequency max_motor_frequency
      "standard maximum motor frequency";
    Modelica.SIunits.Length suctionLine_Di_A
      "diameter of suction line";
    Modelica.SIunits.Length suctionLine_length_A
      "length of suction line";
    Modelica.SIunits.Length suctionLine_Di_B
      "diameter of suction line";
    Modelica.SIunits.Length suctionLine_length_B
      "length of suction line";
    Modelica.SIunits.Length dischargeLine_Di_A
      "diameter of discharge line";
    Modelica.SIunits.Length dischargeLine_length_A
      "length of discharge line";
    Modelica.SIunits.Length dischargeLine_Di_B
      "diameter of discharge line";
    Modelica.SIunits.Length dischargeLine_length_B
      "length of discharge line";
    Modelica.SIunits.Length liquidLine_Di_A
      "diameter of liquid line";
    Modelica.SIunits.Length liquidLine_length_A
      "length of liquid line";
    Modelica.SIunits.CoefficientOfHeatTransfer liquidLine_U_A
      "overall heat trnsfer conefficient of liquid line";
    Modelica.SIunits.Length liquidLine_Di_B
      "diameter of liquid line";
    Modelica.SIunits.Length liquidLine_length_B
      "length of liquid line";
    Modelica.SIunits.CoefficientOfHeatTransfer liquidLine_U_B
      "overall heat trnsfer conefficient of liquid line";
    Modelica.SIunits.Area duct_Ac_A
      "cross sectional area of air duct";
    Real duct_Ka_A
      "air pressure drop coefficient of air duct";
    Modelica.SIunits.ThermalConductance duct_UA_A
      "overall thermal conductance of air duct";
    Modelica.SIunits.Area duct_Ac_B
      "cross sectional area of air duct";
    Real duct_Ka_B
      "air pressure drop coefficient of air duct";
    Modelica.SIunits.ThermalConductance duct_UA_B
      "overall thermal conductance of air duct";
    Modelica.SIunits.Area externalDuct_Ac_A
      "cross sectional area of external air duct";
    Modelica.SIunits.ThermalConductance externalDuct_UA_A
      "overall thermal conductance of external air duct";
    Modelica.SIunits.Area externalDuct_Ac_B
      "cross sectional area of external air duct";
    Modelica.SIunits.ThermalConductance externalDuct_UA_B
      "overall thermal conductance of external air duct";
    Modelica.SIunits.Temperature duct_T_amb_A
      "ambient temperature when calculating heat loss from air to ambient";
    Modelica.SIunits.Temperature duct_T_amb_B
      "ambient temperature when calculating heat loss from air to ambient";
    Real pumpPoly_Fw
      "fraction factor of power into luid stream of pump";
    Real a_p
      "calibration factor";
    Real b_p
      "calibration factor";
    Real a_uev
      "calibration factor";
    Real b_uev
      "calibration factor";
    Real a_ucd
      "calibration factor";
    Real b_ucd
      "calibration factor";
    Real Z_Power_fl
      "calibration factor";
    Real ZU_Evap_fl
      "calibration factor";
    Real ZU_Cond_fl
      "calibration factor";
    Real a_dp_ref_suc_A
      "calibration factor";
    Real b_dp_ref_suc_A
      "calibration factor";
    Real a_dp_ref_suc_B
      "calibration factor";
    Real b_dp_ref_suc_B
      "calibration factor";
    Real a_flow
      "calibration factor";
    Real b_flow
      "calibration factor";
    Real Fw_A
      "calibration factor";
    Real Fw_B
      "calibration factor";
    Real minimalLoad
      "calibration factor";
    Real Z_dpr
      "calibration factor";
    Real dp_reference_discharge_A
      "calibration factor";
    Real dp_reference_liquid_A
      "calibration factor";
    Real dp_reference_suction_min_A
      "calibration factor";
    Real dp_reference_discharge_B
      "calibration factor";
    Real dp_reference_liquid_B
      "calibration factor";
    Real dp_reference_suction_min_B
      "calibration factor";
    Real c_oat
      "calibration factor";
    Real c_lwt
      "calibration factor";
    Real c_tpcoil
      "calibration factor";
    Real c_load
      "calibration factor";
    Real c_oat2
      "calibration factor";
    Real c_lwt_oat
      "calibration factor";
    Real c_oat_tpcoil
      "calibration factor";
    Real c_oat_load
      "calibration factor";
    Real c_lwt2
      "calibration factor";
    Real c_lwt_tpcoil
      "calibration factor";
    Real c_lwt_load
      "calibration factor";
    Real c_tpcoil2
      "calibration factor";
    Real c_load_tpcoil
      "calibration factor";
    Real c_load2
      "calibration factor";
    Real c_const
      "calibration factor";
    Real coilcoating_A
      "calibration factor";
    Real coilcoating_B
      "calibration factor";
    BOLT.InternalLibrary.Coolant.Pumps.DataBase.PumpPoly.Selector selectorPump
      "selector pump";
    Integer Itube
      "Tube number for inside surface performance";
    Integer nCr
      "Number of circuits";
    Integer Nrow
      "Number of tube rows";
    Integer Ntube
      "Number of tubes per row";
    Modelica.SIunits.Length Ltube
      "Tube length between tube sheets(RTPF)/headers(MCHX)";
    Modelica.SIunits.Length Dotube
      "Expanded tube outer diameter";
    Modelica.SIunits.Length Ttube
      "Expanded tube wall thickness";
    Modelica.SIunits.Length Ptube
      "Tube pitch, within rows";
    Modelica.SIunits.Length Prow
      "Row pitch, between rows";
    Real Dfin
      "Fin density (number of fins/inch)";
    Modelica.SIunits.Thickness Tfin
      "Fin thickness";
    Modelica.SIunits.Length CoilLine_Di_a
      "diameter of Coil line of circuit a";
    Modelica.SIunits.Length CoilLine_length_a
      "length of Coil line of circuit a";
    Integer CoilLine_nBends_a
      "length of Coil line of circuit a";
    Modelica.SIunits.Length CoilLine_Di_b
      "diameter of Coil line of circuit b";
    Modelica.SIunits.Length CoilLine_length_b
      "length of Coil line of circuit b";
    Integer CoilLine_nBends_b
      "length of Coil line of circuit b";
    Modelica.SIunits.Length BPHELine_Di_a
      "diameter of BPHE line of circuit a";
    Modelica.SIunits.Length BPHELine_length_a
      "length of BPHE line of circuit a";
    Integer BPHELine_nBends_a
      "length of BPHE line of circuit a";
    Modelica.SIunits.Length BPHELine_Di_b
      "diameter of BPHE line of circuit b";
    Modelica.SIunits.Length BPHELine_length_b
      "length of BPHE line of circuit b";
    Integer BPHELine_nBends_b
      "length of BPHE line of circuit b";
    Real ie_EN14511;
    Real LoadA;
    Real LoadB;
    annotation (
      Icon(
        coordinateSystem(
          preserveAspectRatio=false)),
      Diagram(
        coordinateSystem(
          preserveAspectRatio=false)));
  end Summary_HPC_2C;
  record Summary_HPC_Surr
    extends Modelica.Icons.Record;
    Real charge
      "system charge";
    Modelica.SIunits.HeatFlowRate capacity_design(
      displayUnit="kW")
      "designed system capacity";
    Modelica.SIunits.Volume V_dis
      "compressor discharge volume";
    Modelica.SIunits.Volume V_suc
      "compressor suction volume";
    BOLT.InternalLibrary.Compressors.PD_2Port.SubComponents.PD.DataBase.Polynomial.ARI_FS.Selector selector_CompARI
      "compressor type";
    Integer nCompmax
      "max compressor number";
    Integer nCoils
      "Number of coils of RTPF";
    //   Modelica.SIunits.Length DportB_a
    //     "Brine side inlet connection port diameter of BPHE";
    //   Modelica.SIunits.Length DportB_b
    //     "Brine side outlet connection port diameter of BPHE";
    //   Modelica.SIunits.Length DportR_a
    //     "Refrigerant side inlet connection port diameter of BPHE";
    //   Modelica.SIunits.Length DportR_b
    //     "Refrigerant side outlet connection port diameter of BPHE";
    //   Integer nPlate "Number of plates of BPHE";
    //   RBBP_1C_BlackBoxLibrary.GeoSelector selectGeo_BPHE
    //     "BPHE geometry type";
    BOLT.InternalLibrary.Refrigerant.Valves.TwoPort.DataBase.Selector selector_flowData
      "EXV flow data";
    Workspace.SystemHorizon_P2.ControllerHorizon_P2.Components.Types.StagingSelector_1C stagingSelector
      "staging selector for controller";
    Modelica.SIunits.Conversions.NonSIunits.AngularVelocity_rpm max_fan_rpm
      "maximum fan speed ";
    Modelica.SIunits.Frequency max_motor_frequency
      "maximum motor frequency";
    Modelica.SIunits.Length suctionLine_Di
      "diameter of suction line";
    Modelica.SIunits.Length suctionLine_length
      "length of suction line";
    Modelica.SIunits.Length dischargeLine_Di
      "diameter of discharge line";
    Modelica.SIunits.Length dischargeLine_length
      "length of discharge line";
    Modelica.SIunits.Length liquidLine_Di
      "diameter of liquid line";
    Modelica.SIunits.Length liquidLine_length
      "length of liquid line";
    Modelica.SIunits.Area duct_Ac
      "cross sectional area of air duct";
    Real duct_Ka
      "air pressure drop coefficient of air duct";
    Modelica.SIunits.ThermalConductance duct_UA
      "overall thermal conductance of air duct";
    Modelica.SIunits.Area externalDuct_Ac
      "cross sectional area of external air duct";
    Modelica.SIunits.ThermalConductance externalDuct_UA
      "overall thermal conductance of external air duct";
    Modelica.SIunits.Temperature duct_T_amb
      "ambient temperature when calculating heat loss from air to ambient";
    Real pumpPoly_Fw
      "fraction factor of power into luid stream of pump";
    Real a_p
      "calibration factor";
    Real b_p
      "calibration factor";
    Real a_uev
      "calibration factor";
    Real b_uev
      "calibration factor";
    Real a_ucd
      "calibration factor";
    Real b_ucd
      "calibration factor";
    Real Z_Power_fl
      "calibration factor";
    Real ZU_Evap_fl
      "calibration factor";
    Real ZU_Cond_fl
      "calibration factor";
    Real a_dp_ref_suc
      "calibration factor";
    Real b_dp_ref_suc
      "calibration factor";
    Real a_flow
      "calibration factor";
    Real b_flow
      "calibration factor";
    Real Fw
      "calibration factor";
    Real minimalLoad
      "calibration factor";
    Real Z_dpr
      "calibration factor";
    Real dp_reference_discharge
      "calibration factor";
    Real dp_reference_liquid
      "calibration factor";
    Real dp_reference_suction_min
      "calibration factor";
    Real c_oat
      "calibration factor";
    Real c_lwt
      "calibration factor";
    Real c_tpcoil
      "calibration factor";
    Real c_load
      "calibration factor";
    Real c_oat2
      "calibration factor";
    Real c_lwt_oat
      "calibration factor";
    Real c_oat_tpcoil
      "calibration factor";
    Real c_oat_load
      "calibration factor";
    Real c_lwt2
      "calibration factor";
    Real c_lwt_tpcoil
      "calibration factor";
    Real c_lwt_load
      "calibration factor";
    Real c_tpcoil2
      "calibration factor";
    Real c_load_tpcoil
      "calibration factor";
    Real c_load2
      "calibration factor";
    Real c_const
      "calibration factor";
    Real coilcoating
      "calibration factor";
    BOLT.InternalLibrary.Coolant.Pumps.DataBase.PumpPoly.Selector selectorPump
      "selector pump";
    Real ie_EN14511;
    //   Integer Itube "Tube number for inside surface performance";
    //   Integer nCr "Number of circuits";
    //   Integer Nrow "Number of tube rows";
    //   Integer Ntube "Number of tubes per row";
    //   Modelica.SIunits.Length Ltube
    //     "Tube length between tube sheets(RTPF)/headers(MCHX)";
    //   Modelica.SIunits.Length Dotube "Expanded tube outer diameter";
    //   Modelica.SIunits.Length Ttube "Expanded tube wall thickness";
    //   Modelica.SIunits.Length Ptube "Tube pitch, within rows";
    //   Modelica.SIunits.Length Prow "Row pitch, between rows";
    //   Real Dfin "Fin density (number of fins/inch)";
    //   Modelica.SIunits.Thickness Tfin "Fin thickness";
    //   Modelica.SIunits.Length CoilLine_Di "diameter of Coil line";
    //   Modelica.SIunits.Length CoilLine_length "length of Coil line";
    //   Integer CoilLine_nBends "length of Coil line";
    //   Modelica.SIunits.Length BPHELine_Di "diameter of BPHE line";
    //   Modelica.SIunits.Length BPHELine_length "length of BPHE line";
    //   Integer BPHELine_nBends "length of BPHE line";
    annotation (
      Icon(
        coordinateSystem(
          preserveAspectRatio=false)),
      Diagram(
        coordinateSystem(
          preserveAspectRatio=false)));
  end Summary_HPC_Surr;
  record Summary_HPC_2C_Surr
    extends Modelica.Icons.Record;
    Real charge_A
      "system charge";
    Real charge_B
      "system charge";
    Modelica.SIunits.HeatFlowRate capacity_design(
      displayUnit="kW")
      "designed system capacity";
    Modelica.SIunits.Volume V_dis_A
      "compressor discharge volume";
    Modelica.SIunits.Volume V_suc_A
      "compressor suction volume";
    Modelica.SIunits.Volume V_dis_B
      "compressor discharge volume";
    Modelica.SIunits.Volume V_suc_B
      "compressor suction volume";
    BOLT.InternalLibrary.Compressors.PD_2Port.SubComponents.PD.DataBase.Polynomial.ARI_FS.Selector selector_CompARI_A
      "compressor type";
    BOLT.InternalLibrary.Compressors.PD_2Port.SubComponents.PD.DataBase.Polynomial.ARI_FS.Selector selector_CompARI_B
      "compressor type";
    Integer nCompmax_A
      "max compressor number";
    Integer nCoils_A
      "Number of coils ";
    Integer nCompmax_B
      "max compressor number";
    Integer nCoils_B
      "Number of coils ";
    //   Modelica.SIunits.Length DportB_a
    //     "Brine side inlet connection port diameter of BPHE";
    //   Modelica.SIunits.Length DportB_b
    //     "Brine side outlet connection port diameter of BPHE";
    //   Modelica.SIunits.Length DportR_a
    //     "Refrigerant side inlet connection port diameter of BPHE";
    //   Modelica.SIunits.Length DportR_b
    //     "Refrigerant side outlet connection port diameter of BPHE";
    //   Integer nPlate "Number of plates of BPHE";
    //   RBBP_2C_BlackBoxLibrary.GeoSelector selectGeo_BPHE
    //     "BPHE geometry type";
    BOLT.InternalLibrary.Refrigerant.Valves.TwoPort.DataBase.Selector selector_flowData_A
      "EXV flow data";
    BOLT.InternalLibrary.Refrigerant.Valves.TwoPort.DataBase.Selector selector_flowData_B
      "EXV flow data";
    Workspace.SystemHorizon_P2.ControllerHorizon_P2.Components.Types.StagingSelector_2C stagingSelector
      "staging selector for controller";
    Modelica.SIunits.Conversions.NonSIunits.AngularVelocity_rpm max_fan_rpm
      "standard maximum fan speed ";
    Modelica.SIunits.Frequency max_motor_frequency
      "standard maximum motor frequency";
    Modelica.SIunits.Length suctionLine_Di_A
      "diameter of suction line";
    Modelica.SIunits.Length suctionLine_length_A
      "length of suction line";
    Modelica.SIunits.Length suctionLine_Di_B
      "diameter of suction line";
    Modelica.SIunits.Length suctionLine_length_B
      "length of suction line";
    Modelica.SIunits.Length dischargeLine_Di_A
      "diameter of discharge line";
    Modelica.SIunits.Length dischargeLine_length_A
      "length of discharge line";
    Modelica.SIunits.Length dischargeLine_Di_B
      "diameter of discharge line";
    Modelica.SIunits.Length dischargeLine_length_B
      "length of discharge line";
    Modelica.SIunits.Length liquidLine_Di_A
      "diameter of liquid line";
    Modelica.SIunits.Length liquidLine_length_A
      "length of liquid line";
    Modelica.SIunits.CoefficientOfHeatTransfer liquidLine_U_A
      "overall heat trnsfer conefficient of liquid line";
    Modelica.SIunits.Length liquidLine_Di_B
      "diameter of liquid line";
    Modelica.SIunits.Length liquidLine_length_B
      "length of liquid line";
    Modelica.SIunits.CoefficientOfHeatTransfer liquidLine_U_B
      "overall heat trnsfer conefficient of liquid line";
    Modelica.SIunits.Area duct_Ac_A
      "cross sectional area of air duct";
    Real duct_Ka_A
      "air pressure drop coefficient of air duct";
    Modelica.SIunits.ThermalConductance duct_UA_A
      "overall thermal conductance of air duct";
    Modelica.SIunits.Area duct_Ac_B
      "cross sectional area of air duct";
    Real duct_Ka_B
      "air pressure drop coefficient of air duct";
    Modelica.SIunits.ThermalConductance duct_UA_B
      "overall thermal conductance of air duct";
    Modelica.SIunits.Area externalDuct_Ac_A
      "cross sectional area of external air duct";
    Modelica.SIunits.ThermalConductance externalDuct_UA_A
      "overall thermal conductance of external air duct";
    Modelica.SIunits.Area externalDuct_Ac_B
      "cross sectional area of external air duct";
    Modelica.SIunits.ThermalConductance externalDuct_UA_B
      "overall thermal conductance of external air duct";
    Modelica.SIunits.Temperature duct_T_amb_A
      "ambient temperature when calculating heat loss from air to ambient";
    Modelica.SIunits.Temperature duct_T_amb_B
      "ambient temperature when calculating heat loss from air to ambient";
    Real pumpPoly_Fw
      "fraction factor of power into luid stream of pump";
    Real a_p
      "calibration factor";
    Real b_p
      "calibration factor";
    Real a_uev
      "calibration factor";
    Real b_uev
      "calibration factor";
    Real a_ucd
      "calibration factor";
    Real b_ucd
      "calibration factor";
    Real Z_Power_fl
      "calibration factor";
    Real ZU_Evap_fl
      "calibration factor";
    Real ZU_Cond_fl
      "calibration factor";
    Real a_dp_ref_suc_A
      "calibration factor";
    Real b_dp_ref_suc_A
      "calibration factor";
    Real a_dp_ref_suc_B
      "calibration factor";
    Real b_dp_ref_suc_B
      "calibration factor";
    Real a_flow
      "calibration factor";
    Real b_flow
      "calibration factor";
    Real Fw_A
      "calibration factor";
    Real Fw_B
      "calibration factor";
    Real minimalLoad
      "calibration factor";
    Real Z_dpr
      "calibration factor";
    Real dp_reference_discharge_A
      "calibration factor";
    Real dp_reference_liquid_A
      "calibration factor";
    Real dp_reference_suction_min_A
      "calibration factor";
    Real dp_reference_discharge_B
      "calibration factor";
    Real dp_reference_liquid_B
      "calibration factor";
    Real dp_reference_suction_min_B
      "calibration factor";
    Real c_oat
      "calibration factor";
    Real c_lwt
      "calibration factor";
    Real c_tpcoil
      "calibration factor";
    Real c_load
      "calibration factor";
    Real c_oat2
      "calibration factor";
    Real c_lwt_oat
      "calibration factor";
    Real c_oat_tpcoil
      "calibration factor";
    Real c_oat_load
      "calibration factor";
    Real c_lwt2
      "calibration factor";
    Real c_lwt_tpcoil
      "calibration factor";
    Real c_lwt_load
      "calibration factor";
    Real c_tpcoil2
      "calibration factor";
    Real c_load_tpcoil
      "calibration factor";
    Real c_load2
      "calibration factor";
    Real c_const
      "calibration factor";
    Real coilcoating_A
      "calibration factor";
    Real coilcoating_B
      "calibration factor";
    BOLT.InternalLibrary.Coolant.Pumps.DataBase.PumpPoly.Selector selectorPump
      "selector pump";
    Real ie_EN14511;
    //   Integer Itube "Tube number for inside surface performance";
    //   Integer nCr "Number of circuits";
    //   Integer Nrow "Number of tube rows";
    //   Integer Ntube "Number of tubes per row";
    //   Modelica.SIunits.Length Ltube
    //     "Tube length between tube sheets(RTPF)/headers(MCHX)";
    //   Modelica.SIunits.Length Dotube "Expanded tube outer diameter";
    //   Modelica.SIunits.Length Ttube "Expanded tube wall thickness";
    //   Modelica.SIunits.Length Ptube "Tube pitch, within rows";
    //   Modelica.SIunits.Length Prow "Row pitch, between rows";
    //   Real Dfin "Fin density (number of fins/inch)";
    //   Modelica.SIunits.Thickness Tfin "Fin thickness";
    //   Modelica.SIunits.Length CoilLine_Di_a "diameter of Coil line of circuit a";
    //   Modelica.SIunits.Length CoilLine_length_a "length of Coil line of circuit a";
    //   Integer CoilLine_nBends_a "length of Coil line of circuit a";
    //   Modelica.SIunits.Length CoilLine_Di_b "diameter of Coil line of circuit b";
    //   Modelica.SIunits.Length CoilLine_length_b "length of Coil line of circuit b";
    //   Integer CoilLine_nBends_b "length of Coil line of circuit b";
    //   Modelica.SIunits.Length BPHELine_Di_a "diameter of BPHE line of circuit a";
    //   Modelica.SIunits.Length BPHELine_length_a "length of BPHE line of circuit a";
    //   Integer BPHELine_nBends_a "length of BPHE line of circuit a";
    //   Modelica.SIunits.Length BPHELine_Di_b "diameter of BPHE line of circuit b";
    //   Modelica.SIunits.Length BPHELine_length_b "length of BPHE line of circuit b";
    //   Integer BPHELine_nBends_b "length of BPHE line of circuit b";
    annotation (
      Icon(
        coordinateSystem(
          preserveAspectRatio=false)),
      Diagram(
        coordinateSystem(
          preserveAspectRatio=false)));
  end Summary_HPC_2C_Surr;
  record Summary_HPH_2C
    extends Modelica.Icons.Record;
    Real charge_A
      "system charge";
    Real charge_B
      "system charge";
    Modelica.SIunits.HeatFlowRate capacity_design(
      displayUnit="kW")
      "designed system capacity";
    Modelica.SIunits.Volume V_dis_A
      "compressor discharge volume";
    Modelica.SIunits.Volume V_suc_A
      "compressor suction volume";
    Modelica.SIunits.Volume V_dis_B
      "compressor discharge volume";
    Modelica.SIunits.Volume V_suc_B
      "compressor suction volume";
    BOLT.InternalLibrary.Compressors.PD_2Port.SubComponents.PD.DataBase.Polynomial.ARI_FS.Selector selector_CompARI_A
      "compressor type";
    BOLT.InternalLibrary.Compressors.PD_2Port.SubComponents.PD.DataBase.Polynomial.ARI_FS.Selector selector_CompARI_B
      "compressor type";
    Integer nCompmax_A
      "max compressor number";
    Integer nCoils_A
      "Number of coils ";
    Integer nCompmax_B
      "max compressor number";
    Integer nCoils_B
      "Number of coils ";
    Modelica.SIunits.Length DportB_a
      "Brine side inlet connection port diameter of BPHE";
    Modelica.SIunits.Length DportB_b
      "Brine side outlet connection port diameter of BPHE";
    Modelica.SIunits.Length DportR_a
      "Refrigerant side inlet connection port diameter of BPHE";
    Modelica.SIunits.Length DportR_b
      "Refrigerant side outlet connection port diameter of BPHE";
    Integer nPlate
      "Number of plates of BPHE";
    RBBP_2C_BlackBoxLibrary.GeoSelector selectGeo_BPHE
      "BPHE geometry type";
    BOLT.InternalLibrary.Refrigerant.Valves.TwoPort.DataBase.Selector selector_flowData_A
      "EXV flow data";
    BOLT.InternalLibrary.Refrigerant.Valves.TwoPort.DataBase.Selector selector_flowData_B
      "EXV flow data";
    Workspace.SystemHorizon_P2.ControllerHorizon_P2.Components.Types.StagingSelector_2C stagingSelector
      "staging selector for controller";
    Modelica.SIunits.Conversions.NonSIunits.AngularVelocity_rpm max_fan_rpm
      "standard maximum fan speed ";
    Modelica.SIunits.Frequency max_motor_frequency
      "standard maximum motor frequency";
    Modelica.SIunits.Length suctionLine_Di_A
      "diameter of suction line";
    Modelica.SIunits.Length suctionLine_length_A
      "length of suction line";
    Modelica.SIunits.Length suctionLine_Di_B
      "diameter of suction line";
    Modelica.SIunits.Length suctionLine_length_B
      "length of suction line";
    Modelica.SIunits.Length dischargeLine_Di_A
      "diameter of discharge line";
    Modelica.SIunits.Length dischargeLine_length_A
      "length of discharge line";
    Modelica.SIunits.Length dischargeLine_Di_B
      "diameter of discharge line";
    Modelica.SIunits.Length dischargeLine_length_B
      "length of discharge line";
    Modelica.SIunits.Length liquidLine_Di_A
      "diameter of liquid line";
    Modelica.SIunits.Length liquidLine_length_A
      "length of liquid line";
    Modelica.SIunits.CoefficientOfHeatTransfer liquidLine_U_A
      "overall heat trnsfer conefficient of liquid line";
    Modelica.SIunits.Length liquidLine_Di_B
      "diameter of liquid line";
    Modelica.SIunits.Length liquidLine_length_B
      "length of liquid line";
    Modelica.SIunits.CoefficientOfHeatTransfer liquidLine_U_B
      "overall heat trnsfer conefficient of liquid line";
    Modelica.SIunits.Area duct_Ac_A
      "cross sectional area of air duct";
    Real duct_Ka_A
      "air pressure drop coefficient of air duct";
    Modelica.SIunits.ThermalConductance duct_UA_A
      "overall thermal conductance of air duct";
    Modelica.SIunits.Area duct_Ac_B
      "cross sectional area of air duct";
    Real duct_Ka_B
      "air pressure drop coefficient of air duct";
    Modelica.SIunits.ThermalConductance duct_UA_B
      "overall thermal conductance of air duct";
    Modelica.SIunits.Area externalDuct_Ac_A
      "cross sectional area of external air duct";
    Modelica.SIunits.ThermalConductance externalDuct_UA_A
      "overall thermal conductance of external air duct";
    Modelica.SIunits.Area externalDuct_Ac_B
      "cross sectional area of external air duct";
    Modelica.SIunits.ThermalConductance externalDuct_UA_B
      "overall thermal conductance of external air duct";
    Modelica.SIunits.Temperature duct_T_amb_A
      "ambient temperature when calculating heat loss from air to ambient";
    Modelica.SIunits.Temperature duct_T_amb_B
      "ambient temperature when calculating heat loss from air to ambient";
    Real pumpPoly_Fw
      "fraction factor of power into luid stream of pump";
    Real a_p_a
      "calibration factor";
    Real b_p_a
      "calibration factor";
    Real a_p_b
      "calibration factor";
    Real b_p_b
      "calibration factor";
    Real a_uev_a
      "calibration factor";
    Real b_uev_a
      "calibration factor";
    Real a_ucd_a
      "calibration factor";
    Real b_ucd_a
      "calibration factor";
    Real a_uev_b
      "calibration factor";
    Real b_uev_b
      "calibration factor";
    Real a_ucd_b
      "calibration factor";
    Real b_ucd_b
      "calibration factor";
    Real Z_Power_fl
      "calibration factor";
    Real ZU_Evap_fl
      "calibration factor";
    Real ZU_Cond_fl
      "calibration factor";
    Real a_dp_ref_suc_A
      "calibration factor";
    Real b_dp_ref_suc_A
      "calibration factor";
    Real a_dp_ref_suc_B
      "calibration factor";
    Real b_dp_ref_suc_B
      "calibration factor";
    Real a_flow_a
      "calibration factor";
    Real b_flow_a
      "calibration factor";
    Real a_flow_b
      "calibration factor";
    Real b_flow_b
      "calibration factor";
    Real Fw_A
      "calibration factor";
    Real Fw_B
      "calibration factor";
    Real minimalLoad
      "calibration factor";
    Real Z_dpr
      "calibration factor";
    Real dp_reference_discharge_A
      "calibration factor";
    Real dp_reference_liquid_A
      "calibration factor";
    Real dp_reference_suction_min_A
      "calibration factor";
    Real dp_reference_discharge_B
      "calibration factor";
    Real dp_reference_liquid_B
      "calibration factor";
    Real dp_reference_suction_min_B
      "calibration factor";
    Real c_oat
      "calibration factor";
    Real c_lwt
      "calibration factor";
    Real c_tpcoil
      "calibration factor";
    Real c_load
      "calibration factor";
    Real c_oat2
      "calibration factor";
    Real c_lwt_oat
      "calibration factor";
    Real c_oat_tpcoil
      "calibration factor";
    Real c_oat_load
      "calibration factor";
    Real c_lwt2
      "calibration factor";
    Real c_lwt_tpcoil
      "calibration factor";
    Real c_lwt_load
      "calibration factor";
    Real c_tpcoil2
      "calibration factor";
    Real c_load_tpcoil
      "calibration factor";
    Real c_load2
      "calibration factor";
    Real c_const
      "calibration factor";
    Real coilcoating_A
      "calibration factor";
    Real coilcoating_B
      "calibration factor";
    BOLT.InternalLibrary.Coolant.Pumps.DataBase.PumpPoly.Selector selectorPump
      "selector pump";
    Integer Itube
      "Tube number for inside surface performance";
    Integer nCr
      "Number of circuits";
    Integer Nrow
      "Number of tube rows";
    Integer Ntube
      "Number of tubes per row";
    Modelica.SIunits.Length Ltube
      "Tube length between tube sheets(RTPF)/headers(MCHX)";
    Modelica.SIunits.Length Dotube
      "Expanded tube outer diameter";
    Modelica.SIunits.Length Ttube
      "Expanded tube wall thickness";
    Modelica.SIunits.Length Ptube
      "Tube pitch, within rows";
    Modelica.SIunits.Length Prow
      "Row pitch, between rows";
    Real Dfin
      "Fin density (number of fins/inch)";
    Modelica.SIunits.Thickness Tfin
      "Fin thickness";
    Modelica.SIunits.Length CoilLine_Di_a
      "diameter of Coil line of circuit a";
    Modelica.SIunits.Length CoilLine_length_a
      "length of Coil line of circuit a";
    Integer CoilLine_nBends_a
      "length of Coil line of circuit a";
    Modelica.SIunits.Length CoilLine_Di_b
      "diameter of Coil line of circuit b";
    Modelica.SIunits.Length CoilLine_length_b
      "length of Coil line of circuit b";
    Integer CoilLine_nBends_b
      "length of Coil line of circuit b";
    Modelica.SIunits.Length BPHELine_Di_a
      "diameter of BPHE line of circuit a";
    Modelica.SIunits.Length BPHELine_length_a
      "length of BPHE line of circuit a";
    Integer BPHELine_nBends_a
      "length of BPHE line of circuit a";
    Modelica.SIunits.Length BPHELine_Di_b
      "diameter of BPHE line of circuit b";
    Modelica.SIunits.Length BPHELine_length_b
      "length of BPHE line of circuit b";
    Integer BPHELine_nBends_b
      "length of BPHE line of circuit b";
    Real ie_EN14511;
    Real LoadA;
    Real LoadB;
    annotation (
      Icon(
        coordinateSystem(
          preserveAspectRatio=false)),
      Diagram(
        coordinateSystem(
          preserveAspectRatio=false)));
  end Summary_HPH_2C;
  record Summary_HPH_2C_Surr
    extends Modelica.Icons.Record;
    Real charge_A
      "system charge";
    Real charge_B
      "system charge";
    Modelica.SIunits.HeatFlowRate capacity_design(
      displayUnit="kW")
      "designed system capacity";
    Modelica.SIunits.Volume V_dis_A
      "compressor discharge volume";
    Modelica.SIunits.Volume V_suc_A
      "compressor suction volume";
    Modelica.SIunits.Volume V_dis_B
      "compressor discharge volume";
    Modelica.SIunits.Volume V_suc_B
      "compressor suction volume";
    BOLT.InternalLibrary.Compressors.PD_2Port.SubComponents.PD.DataBase.Polynomial.ARI_FS.Selector selector_CompARI_A
      "compressor type";
    BOLT.InternalLibrary.Compressors.PD_2Port.SubComponents.PD.DataBase.Polynomial.ARI_FS.Selector selector_CompARI_B
      "compressor type";
    Integer nCompmax_A
      "max compressor number";
    Integer nCoils_A
      "Number of coils ";
    Integer nCompmax_B
      "max compressor number";
    Integer nCoils_B
      "Number of coils ";
    //   Modelica.SIunits.Length DportB_a
    //     "Brine side inlet connection port diameter of BPHE";
    //   Modelica.SIunits.Length DportB_b
    //     "Brine side outlet connection port diameter of BPHE";
    //   Modelica.SIunits.Length DportR_a
    //     "Refrigerant side inlet connection port diameter of BPHE";
    //   Modelica.SIunits.Length DportR_b
    //     "Refrigerant side outlet connection port diameter of BPHE";
    //   Integer nPlate "Number of plates of BPHE";
    //   RBBP_2C_BlackBoxLibrary.GeoSelector selectGeo_BPHE
    //     "BPHE geometry type";
    BOLT.InternalLibrary.Refrigerant.Valves.TwoPort.DataBase.Selector selector_flowData_A
      "EXV flow data";
    BOLT.InternalLibrary.Refrigerant.Valves.TwoPort.DataBase.Selector selector_flowData_B
      "EXV flow data";
    Workspace.SystemHorizon_P2.ControllerHorizon_P2.Components.Types.StagingSelector_2C stagingSelector
      "staging selector for controller";
    Modelica.SIunits.Conversions.NonSIunits.AngularVelocity_rpm max_fan_rpm
      "standard maximum fan speed ";
    Modelica.SIunits.Frequency max_motor_frequency
      "standard maximum motor frequency";
    Modelica.SIunits.Length suctionLine_Di_A
      "diameter of suction line";
    Modelica.SIunits.Length suctionLine_length_A
      "length of suction line";
    Modelica.SIunits.Length suctionLine_Di_B
      "diameter of suction line";
    Modelica.SIunits.Length suctionLine_length_B
      "length of suction line";
    Modelica.SIunits.Length dischargeLine_Di_A
      "diameter of discharge line";
    Modelica.SIunits.Length dischargeLine_length_A
      "length of discharge line";
    Modelica.SIunits.Length dischargeLine_Di_B
      "diameter of discharge line";
    Modelica.SIunits.Length dischargeLine_length_B
      "length of discharge line";
    Modelica.SIunits.Length liquidLine_Di_A
      "diameter of liquid line";
    Modelica.SIunits.Length liquidLine_length_A
      "length of liquid line";
    Modelica.SIunits.CoefficientOfHeatTransfer liquidLine_U_A
      "overall heat trnsfer conefficient of liquid line";
    Modelica.SIunits.Length liquidLine_Di_B
      "diameter of liquid line";
    Modelica.SIunits.Length liquidLine_length_B
      "length of liquid line";
    Modelica.SIunits.CoefficientOfHeatTransfer liquidLine_U_B
      "overall heat trnsfer conefficient of liquid line";
    Modelica.SIunits.Area duct_Ac_A
      "cross sectional area of air duct";
    Real duct_Ka_A
      "air pressure drop coefficient of air duct";
    Modelica.SIunits.ThermalConductance duct_UA_A
      "overall thermal conductance of air duct";
    Modelica.SIunits.Area duct_Ac_B
      "cross sectional area of air duct";
    Real duct_Ka_B
      "air pressure drop coefficient of air duct";
    Modelica.SIunits.ThermalConductance duct_UA_B
      "overall thermal conductance of air duct";
    Modelica.SIunits.Area externalDuct_Ac_A
      "cross sectional area of external air duct";
    Modelica.SIunits.ThermalConductance externalDuct_UA_A
      "overall thermal conductance of external air duct";
    Modelica.SIunits.Area externalDuct_Ac_B
      "cross sectional area of external air duct";
    Modelica.SIunits.ThermalConductance externalDuct_UA_B
      "overall thermal conductance of external air duct";
    Modelica.SIunits.Temperature duct_T_amb_A
      "ambient temperature when calculating heat loss from air to ambient";
    Modelica.SIunits.Temperature duct_T_amb_B
      "ambient temperature when calculating heat loss from air to ambient";
    Real pumpPoly_Fw
      "fraction factor of power into luid stream of pump";
    Real a_p_a
      "calibration factor";
    Real b_p_a
      "calibration factor";
    Real a_p_b
      "calibration factor";
    Real b_p_b
      "calibration factor";
    Real a_uev_a
      "calibration factor";
    Real b_uev_a
      "calibration factor";
    Real a_uev_b
      "calibration factor";
    Real b_uev_b
      "calibration factor";
    Real a_ucd_a
      "calibration factor";
    Real b_ucd_a
      "calibration factor";
    Real a_ucd_b
      "calibration factor";
    Real b_ucd_b
      "calibration factor";
    Real Z_Power_fl
      "calibration factor";
    Real ZU_Evap_fl
      "calibration factor";
    Real ZU_Cond_fl
      "calibration factor";
    Real a_dp_ref_suc_A
      "calibration factor";
    Real b_dp_ref_suc_A
      "calibration factor";
    Real a_dp_ref_suc_B
      "calibration factor";
    Real b_dp_ref_suc_B
      "calibration factor";
    Real a_flow_a
      "calibration factor";
    Real b_flow_a
      "calibration factor";
    Real a_flow_b
      "calibration factor";
    Real b_flow_b
      "calibration factor";
    Real Fw_A
      "calibration factor";
    Real Fw_B
      "calibration factor";
    Real minimalLoad
      "calibration factor";
    Real Z_dpr
      "calibration factor";
    Real dp_reference_discharge_A
      "calibration factor";
    Real dp_reference_liquid_A
      "calibration factor";
    Real dp_reference_suction_min_A
      "calibration factor";
    Real dp_reference_discharge_B
      "calibration factor";
    Real dp_reference_liquid_B
      "calibration factor";
    Real dp_reference_suction_min_B
      "calibration factor";
    Real c_oat
      "calibration factor";
    Real c_lwt
      "calibration factor";
    Real c_tpcoil
      "calibration factor";
    Real c_load
      "calibration factor";
    Real c_oat2
      "calibration factor";
    Real c_lwt_oat
      "calibration factor";
    Real c_oat_tpcoil
      "calibration factor";
    Real c_oat_load
      "calibration factor";
    Real c_lwt2
      "calibration factor";
    Real c_lwt_tpcoil
      "calibration factor";
    Real c_lwt_load
      "calibration factor";
    Real c_tpcoil2
      "calibration factor";
    Real c_load_tpcoil
      "calibration factor";
    Real c_load2
      "calibration factor";
    Real c_const
      "calibration factor";
    Real coilcoating_A
      "calibration factor";
    Real coilcoating_B
      "calibration factor";
    BOLT.InternalLibrary.Coolant.Pumps.DataBase.PumpPoly.Selector selectorPump
      "selector pump";
    Real ie_EN14511;
    //   Integer Itube "Tube number for inside surface performance";
    //   Integer nCr "Number of circuits";
    //   Integer Nrow "Number of tube rows";
    //   Integer Ntube "Number of tubes per row";
    //   Modelica.SIunits.Length Ltube
    //     "Tube length between tube sheets(RTPF)/headers(MCHX)";
    //   Modelica.SIunits.Length Dotube "Expanded tube outer diameter";
    //   Modelica.SIunits.Length Ttube "Expanded tube wall thickness";
    //   Modelica.SIunits.Length Ptube "Tube pitch, within rows";
    //   Modelica.SIunits.Length Prow "Row pitch, between rows";
    //   Real Dfin "Fin density (number of fins/inch)";
    //   Modelica.SIunits.Thickness Tfin "Fin thickness";
    //   Modelica.SIunits.Length CoilLine_Di_a "diameter of Coil line of circuit a";
    //   Modelica.SIunits.Length CoilLine_length_a "length of Coil line of circuit a";
    //   Integer CoilLine_nBends_a "length of Coil line of circuit a";
    //   Modelica.SIunits.Length CoilLine_Di_b "diameter of Coil line of circuit b";
    //   Modelica.SIunits.Length CoilLine_length_b "length of Coil line of circuit b";
    //   Integer CoilLine_nBends_b "length of Coil line of circuit b";
    //   Modelica.SIunits.Length BPHELine_Di_a "diameter of BPHE line of circuit a";
    //   Modelica.SIunits.Length BPHELine_length_a "length of BPHE line of circuit a";
    //   Integer BPHELine_nBends_a "length of BPHE line of circuit a";
    //   Modelica.SIunits.Length BPHELine_Di_b "diameter of BPHE line of circuit b";
    //   Modelica.SIunits.Length BPHELine_length_b "length of BPHE line of circuit b";
    //   Integer BPHELine_nBends_b "length of BPHE line of circuit b";
    annotation (
      Icon(
        coordinateSystem(
          preserveAspectRatio=false)),
      Diagram(
        coordinateSystem(
          preserveAspectRatio=false)));
  end Summary_HPH_2C_Surr;
end Summary;
