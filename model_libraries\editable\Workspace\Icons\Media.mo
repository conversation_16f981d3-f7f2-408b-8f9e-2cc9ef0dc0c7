within Workspace.Icons;
partial class Media
  extends Workspace.Icons.PackageIcon;
  annotation (
    Icon(
      graphics={
        Ellipse(
          extent={{-56,38},{-2,-14}},
          lineColor={0,0,255},
          fillColor={0,0,255},
          fillPattern=FillPattern.Solid),
        Ellipse(
          extent={{-28,0},{26,-52}},
          lineColor={0,0,255},
          fillColor={255,255,0},
          fillPattern=FillPattern.Solid),
        Ellipse(
          extent={{-8,42},{46,-10}},
          lineColor={0,0,255},
          fillColor={255,0,0},
          fillPattern=FillPattern.Solid)}));
end Media;
