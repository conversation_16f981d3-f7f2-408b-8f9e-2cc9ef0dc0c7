within Workspace.Icons;
partial model FanIcon
  //extends VFD30XW.Icons.InitializationCycle2;
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100,-100},{100,100}}),
      graphics={
        Rectangle(
          extent={{-100,100},{100,-100}},
          lineColor={0,0,0},
          fillColor={255,255,255},
          fillPattern=FillPattern.Solid),
        Text(
          extent={{-134,-130},{134,-148}},
          lineColor={0,0,0},
          fillColor={255,0,0},
          fillPattern=FillPattern.Solid,
          textString="%name"),
        Polygon(
          points={{-60,20},{-60,-20},{0,0},{-60,20}},
          smooth=Smooth.None,
          fillColor={255,128,0},
          fillPattern=FillPattern.Solid,
          lineColor={0,0,0}),
        Polygon(
          points={{-20,-60},{20,-60},{0,0},{-20,-60}},
          lineColor={0,0,0},
          smooth=Smooth.None,
          fillColor={255,170,85},
          fillPattern=FillPattern.Solid),
        Polygon(
          points={{60,20},{60,-20},{0,0},{60,20}},
          lineColor={0,0,0},
          smooth=Smooth.None,
          fillColor={255,255,0},
          fillPattern=FillPattern.Solid),
        Polygon(
          points={{-20,60},{20,60},{0,0},{-20,60}},
          lineColor={0,0,0},
          smooth=Smooth.None,
          fillColor={255,0,0},
          fillPattern=FillPattern.Solid)}));
end FanIcon;
