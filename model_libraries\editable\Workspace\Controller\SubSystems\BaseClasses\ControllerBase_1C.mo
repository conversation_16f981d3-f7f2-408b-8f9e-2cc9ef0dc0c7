within Workspace.Controller.SubSystems.BaseClasses;
model ControllerBase_1C
  extends.BOLT.InternalLibrary.BuildingBlocks.Icons.ControllerPackage;
  parameter Boolean isOff=false
    "Specify whether the circuit A is on or off";
  parameter Real Fan_MaxFrequency=950
    "Maximum speed of Fan  (RPM)"
    annotation (Dialog(group="fan"));
  parameter Real Fan_MinFrequency=100
    "minim speed of Fan  (RPM)"
    annotation (Dialog(group="fan"));
  parameter Real min_speed=30
    "Minimum speed of compressor  (Hz)"
    annotation (Dialog(group="compressor"));
  parameter Real max_speed=140
    "Maximum speed of compressor  (Hz)"
    annotation (Dialog(group="compressor"));
  parameter Boolean Use_fake_pump=false
    "When true User Pump is not present"
    annotation (Dialog(group="pump"));
  parameter Boolean is_LWTcontrol=false
    "When true pump control the Leaving Water Temperature"
    annotation (Dialog(group="pump"));
  parameter Boolean is_EWTcontrol=false
    "When true pump control the Entering Water Temperature"
    annotation (Dialog(group="pump"));
  parameter Real Pump_speed_max=3000
    annotation (Dialog(group="Pump"));
  parameter Real Pump_speed_min=1200
    annotation (Dialog(group="Pump"));
  parameter Real Pump_speed_start=2000
    annotation (Dialog(group="Pump"));
  parameter Real Ka_max=-0.0001
    annotation (Dialog(group="Fake Pump"));
  parameter Real Ka_min=-100
    annotation (Dialog(group="Fake Pump"));
  parameter Real Ka_start=-10
    annotation (Dialog(group="Fake Pump"));
  parameter Real load_ratio=100
    "input percentage load"
    annotation (Dialog(group="compressor"));
  parameter Boolean is_load_ratio=false
    "When true set load_ration instead of target capacity"
    annotation (Dialog(group="compressor"));
  .Workspace.Interfaces.MeasurementBus measurementBus
    annotation (Placement(transformation(extent={{-120.0,50.0},{-80.0,90.0}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.LimitsBus limitsBus
    annotation (Placement(transformation(extent={{-110.53884282723261,19.461157172767386},{-89.46115717276739,40.538842827232614}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{90,-10},{110,10}},origin={-200,-50})));
  replaceable.Workspace.Controller.SubSystems.BaseClasses.CompleteCompressorControlBase_1C completeCompressorControl_base(
    isOff=isOff)
    annotation (Placement(transformation(extent={{-9.182098785124445,-1.6327178795452273},{10.817901512898779,18.367282418478}},origin={0.0,0.0},rotation=0.0)));
  replaceable.Workspace.Controller.SubSystems.BaseClasses.EXVControlBase eXVControl(
    crkIsOff=isOff)
    annotation (Placement(transformation(extent={{-10.0,41.184155300939985},{10.0,61.184155300939985}},origin={0.0,0.0},rotation=0.0)));
  replaceable.Workspace.Controller.SubSystems.BaseClasses.FanControlBase fanControl(
    crkIsOff=isOff)
    annotation (Placement(transformation(extent={{-10.0,80.0},{10.0,100.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput compressor
    annotation (Placement(transformation(extent={{96.20258170878881,-1.1391689874443358},{116.20258170878881,18.860831012555664}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{90,-10},{110,10}},origin={10,50})));
  .Modelica.Blocks.Interfaces.RealOutput exv
    annotation (Placement(transformation(extent={{95.31649860753342,41.09745883907104},{115.31649860753342,61.09745883907104}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{90,-10},{110,10}},origin={10,25})));
  .Modelica.Blocks.Interfaces.RealOutput fan
    annotation (Placement(transformation(extent={{94.43041550627771,80.085115294316},{114.43041550627771,100.085115294316}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{90,-10},{110,10}},origin={10,0})));
  .Modelica.Blocks.Interfaces.RealOutput pump
    annotation (Placement(transformation(extent={{96.2025817087889,-41.60363061144856},{116.2025817087889,-21.60363061144856}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{90,-10},{110,10}},origin={10,-25})));
  .Workspace.Controller.SubSystems.PumpUserControl pumpUserControl(
    crkIsOff=isOff,
    is_LWT_control=is_LWTcontrol,
    is_EWT_control=is_EWTcontrol)
    annotation (Placement(transformation(extent={{-9.264088823193974,-42.93225621459387},{12.95805451706041,-20.71011287433948}},origin={0.0,0.0},rotation=0.0)));
equation
  connect(measurementBus,fanControl.measurementBus)
    annotation (Line(points={{-100,70},{-56,70},{-56,96},{-10,96}},color={255,204,51}));
  connect(fanControl.limitsBus,limitsBus)
    annotation (Line(points={{-10,84},{-56.18542222102706,84},{-56.18542222102706,30},{-100,30}},color={255,204,51}));
  connect(completeCompressorControl_base.actuatorSignal,compressor)
    annotation (Line(points={{11.417901521839475,8.76728227542685},{106.20258170878881,8.76728227542685},{106.20258170878881,8.860831012555664}},color={0,0,127}));
  connect(eXVControl.actuatorSignal,exv)
    annotation (Line(points={{10,51.184155300939985},{59,51.184155300939985},{59,51.09745883907104},{105.31649860753342,51.09745883907104}},color={0,0,127}));
  connect(eXVControl.measurementBus,measurementBus)
    annotation (Line(points={{-10,57.184155300939985},{-56,57.184155300939985},{-56,70},{-100,70}},color={255,204,51}));
  connect(eXVControl.limitsBus,limitsBus)
    annotation (Line(points={{-10,45.184155300939985},{-56,45.184155300939985},{-56,30},{-100,30}},color={255,204,51}));
  connect(completeCompressorControl_base.measurementBus,measurementBus)
    annotation (Line(points={{-9.571135028435105,11.15939275239976},{-56.167114198456076,11.15939275239976},{-56.167114198456076,70},{-100,70}},color={255,204,51}));
  connect(completeCompressorControl_base.limitsBus,limitsBus)
    annotation (Line(points={{-9.19306254777425,6.375171798453937},{-56,6.375171798453937},{-56,30},{-100,30}},color={255,204,51}));
  connect(fanControl.actuatorSignal,fan)
    annotation (Line(points={{10,90},{104.43041550627771,90.085115294316}},color={0,0,127}));
  connect(measurementBus,pumpUserControl.measurementBus)
    annotation (Line(points={{-100,70},{-55.79716898298043,70},{-55.79716898298043,-25.15454154239036},{-9.264088823193974,-25.15454154239036}},color={255,204,51}));
  connect(limitsBus,pumpUserControl.limitsBus)
    annotation (Line(points={{-100,30},{-56.037835044717276,30},{-56.037835044717276,-38.487827546542995},{-9.264088823193974,-38.487827546542995}},color={255,204,51}));
  connect(pumpUserControl.actuatorSignal,pump)
    annotation (Line(points={{14.069161684073128,-31.821184544466675},{60.89607561894223,-31.821184544466675},{60.89607561894223,-31.60363061144856},{106.2025817087889,-31.60363061144856}},color={0,0,127}));
end ControllerBase_1C;
