within Workspace.System.HPC.R290;
model CL_61AQ
  extends.Workspace.System.HPC.BaseCycle.System_61AQ(
    choiceBlock(
      is_monobloc=false,
      Selector_Block_B=Workspace.Auxiliary.OptionBlock.Record_Base.Selector.Unit_Z_070,
      Selector_Block_A=Workspace.Auxiliary.OptionBlock.Record_Base.Selector.Unit_Z_070,
      Pump_selector=Workspace.Auxiliary.OptionBlock.PumpOption.Pump_Option.STANDARD),
    ECAT(
      TargetCoolingCapacity_W(
        setPoint=100000),
      LoadRatio_nd(
        setPoint=50)),
    controllerSettings_crkA(
      SST_min=243.15),
    controllerSettings_crkB(
      SST_min=243.15));
end CL_61AQ;
