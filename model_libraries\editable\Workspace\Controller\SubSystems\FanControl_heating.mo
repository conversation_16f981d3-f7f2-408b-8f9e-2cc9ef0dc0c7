within Workspace.Controller.SubSystems;
model FanControl_heating
  extends.Workspace.Controller.SubSystems.BaseClasses.FanControlBase;
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointController(
    AV_min=MinFrequency,
    isOff=crkIsOff,
    AV_max=MaxFrequency,
    manualOff=manualOff,
    AV_value_off=AV_value_off,
    AV_start=720)
    annotation (Placement(transformation(extent={{66.0,-10.0},{86.0,10.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation fanSpeed_error(
    ID=1,
    gain=1/(MaxFrequency),
    setPoint=fanSpeed_setpoint,
    measurement=actuatorSignal,
    isOff=crkIsOff or crkIsOff)
    annotation (Placement(transformation(extent={{-105.14623271598376,13.784890797495848},{-54.85376728401624,34.21510920250415}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation T_dgt_max_error(
    ID=3,
    gain=-1/(((((423))))),
    setPoint=T_dgt_max_limit_fan,
    measurement=T_dgt,
    isOff=isOffDGTmax or crkIsOff)
    annotation (Placement(transformation(extent={{-109.4621691565839,-22.215109202504152},{-59.16970372461638,-1.784890797495848}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Min min
    annotation (Placement(transformation(extent={{30.54233536527721,-10.710569649411875},{51.94713409208924,10.694229077400157}},origin={0.0,0.0},rotation=0.0)));
  parameter Real MaxFrequency=720;
  parameter Real MinFrequency=100;
  parameter Boolean isOffSSTmax=false;
  parameter Boolean isOffDGTmax=false;
  parameter Boolean isOffSSTmin=false;
  parameter Boolean manualOff=false;
  parameter Real AV_value_off=720;
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation T_sst_max_error(
    isOff=isOffSSTmax or crkIsOff,
    measurement=T_sst,
    setPoint=T_sst_max_limit_fan,
    gain=1/(((((((288-220))))))),
    ID=4)
    annotation (Placement(transformation(extent={{-109.14623271598376,-38.21510920250415},{-58.85376728401624,-17.784890797495848}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Max max
    annotation (Placement(transformation(extent={{-2.702399363406016,-2.702399363406016},{18.702399363406016,18.702399363406016}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation T_sst_min_error(
    isOff=isOffSSTmin or crkIsOff,
    measurement=T_sst,
    setPoint=T_sst_min_limit_fan,
    gain=1/((((270-220)))),
    ID=2)
    annotation (Placement(transformation(extent={{-106.51435983478346,-3.583236321303861},{-56.22189440281595,16.846982083704443}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Max max2
    annotation (Placement(transformation(extent={{-36.702399363406016,5.29760063659398},{-15.297600636593984,26.70239936340602}},origin={0.0,0.0},rotation=0.0)));
protected
  .Modelica.Blocks.Interfaces.RealOutput T_dgt
    annotation (Placement(transformation(extent={{-68.49941158124145,47.500588418758554},{-43.500588418758554,72.49941158124145}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput T_dgt_max_limit_fan
    annotation (Placement(transformation(extent={{-67.25829806776137,-93.60990347401922},{-42.25947490527848,-68.61108031153633}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput T_sst
    annotation (Placement(transformation(extent={{-68.49941158124145,71.56602116803167},{-43.500588418758554,96.56484433051456}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput T_sst_min_limit_fan
    annotation (Placement(transformation(extent={{-65.54572223984039,-129.9689156577725},{-40.5468990773575,-104.9700924952896}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput fanSpeed_setpoint
    annotation (Placement(transformation(extent={{-68.49941158124145,-72.49941158124145},{-43.500588418758554,-47.500588418758554}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput T_sst_max_limit_fan
    annotation (Placement(transformation(extent={{-66.58918387345051,-113.25896843418845},{-41.59036071096762,-88.26014527170555}},origin={0.0,0.0},rotation=0.0)));
equation
  connect(setpointController.actuatorSignal,actuatorSignal)
    annotation (Line(points={{86.6,0},{100,0}},color={0,0,127}));
  connect(fanSpeed_setpoint,limitsBus.fanSpeed_setpoint)
    annotation (Line(points={{-56,-60},{-78,-60},{-78,-59.95},{-99.95,-59.95}},color={0,0,127}));
  connect(T_dgt_max_limit_fan,limitsBus.T_dgt_max_limit_fan)
    annotation (Line(points={{-54.7589,-81.1105},{-99.95,-81.1105},{-99.95,-59.95}},color={0,0,127}));
  connect(T_dgt,measurementBus.T_dgt)
    annotation (Line(points={{-56,60},{-78,60},{-78,60.1},{-99.9,60.1}},color={0,0,127}));
  connect(T_sst,measurementBus.T_sst)
    annotation (Line(points={{-56,84.0654},{-77.9338,84.0654},{-77.9338,60.1},{-99.9,60.1}},color={0,0,127}));
  connect(T_sst_max_limit_fan,limitsBus.T_sst_max_limit_fan)
    annotation (Line(points={{-54.0898,-100.76},{-99.95,-100.76},{-99.95,-59.95}},color={0,0,127}));
  connect(T_sst_min_limit_fan,limitsBus.T_sst_min_limit_fan)
    annotation (Line(points={{-53.0463,-117.47},{-99.95,-117.47},{-99.95,-59.95}},color={0,0,127}));
  connect(fanSpeed_error.sensor,max2.u1)
    annotation (Line(points={{-55.35669193833592,23.18279126379967},{-46.0584083455302,23.18279126379967},{-46.0584083455302,21.993343643507373},{-36.702399363406016,21.993343643507373}},color={28,108,200}));
  connect(T_sst_min_error.sensor,max2.u2)
    annotation (Line(points={{-56.724819057135626,5.814664144999959},{-46.1750906645042,5.814664144999959},{-46.1750906645042,9.578560381956388},{-36.702399363406016,9.578560381956388}},color={28,108,200}));
  connect(max2.y,max.u1)
    annotation (Line(points={{-15.297600636593984,16},{-2.702399363406016,16},{-2.702399363406016,13.99334364350737}},color={28,108,200}));
  connect(T_dgt_max_error.sensor,max.u2)
    annotation (Line(points={{-59.67262837893606,-12.817208736200332},{-29.619862931435506,-12.817208736200332},{-29.619862931435506,1.5785603819563896},{-2.702399363406016,1.5785603819563896}},color={28,108,200}));
  connect(max.y,min.u1)
    annotation (Line(points={{18.702399363406016,8},{25.565740664762867,8},{25.565740664762867,5.985173357501511},{30.54233536527721,5.985173357501511}},color={28,108,200}));
  connect(T_sst_max_error.sensor,min.u2)
    annotation (Line(points={{-59.35669193833592,-28.81720873620033},{-14.068821079044202,-28.81720873620033},{-14.068821079044202,-6.429609904049469},{30.54233536527721,-6.429609904049469}},color={28,108,200}));
  connect(min.y,setpointController.errorSignal)
    annotation (Line(points={{51.94713409208924,-0.008170286005858785},{58.1436935936174,-0.008170286005858785},{58.1436935936174,0},{66,0}},color={28,108,200}));
end FanControl_heating;
