within Workspace.System.HPC.R290;
model CL_61AQ_FW092
  extends.Workspace.System.HPC.BaseCycle.System_61AQ_FW092(
    choiceBlock(
      is_monobloc=false,
      Selector_Block_B=Workspace.Auxiliary.OptionBlock.Record_Base.Selector.Unit_Z_060,
      Selector_Block_A=Workspace.Auxiliary.OptionBlock.Record_Base.Selector.Unit_Z_060,
      Pump_selector=Workspace.Auxiliary.OptionBlock.PumpOption.Pump_Option.STANDARD),
    ECAT(
      TargetCoolingCapacity_W(
        setPoint=600000),
      LoadRatio_nd(
        setPoint=50),
      AmbientAirDBTemp_K(
        setPoint=308.15),
      EvapBrineEWT_K(
        setPoint=285.15),
      EvapBrineLWT_K(
        setPoint=280.15),
      RefrigerantCharge_kg(
        setPoint={1.85,1.85},
        fixed={true,true})),
    controllerSettings_crkA(
      SST_min=243.15),
    controllerSettings_crkB(
      SST_min=243.15),
    controller_crkA(
      manualOff_fan_block_A=false,
      manualOff_fan_block_B=false,
      manualOff_compressor_block_A=false,
      frq_comp_sp_manual_block_A=110),
    Module(
      BlockA(
        node_suction(
          dTsh_set=5.27)),
      Elec_box_power=160),
    use_bf=true,
    Use_EN14511=true,
    use_Calib=true);
end CL_61AQ_FW092;
