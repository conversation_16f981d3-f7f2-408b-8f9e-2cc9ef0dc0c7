within Workspace.Controller.Components.Functions;
function Freq_Heating_Transition_1_2
  extends Modelica.Icons.Function;
  import SI=Modelica.SIunits;
  input SI.Temperature T_lwt;
  input SI.Temperature T_oat;
  input Real freq_compressor;
  input Real target_cap;
  input Real current_cap;
  input Integer N_module;
  output Real freq_Transition;
  output Boolean Freq_Heating_Transition_1_2;
  parameter Real intercept=128.6371668;
  parameter Real OAT_1=-7.435528694;
  parameter Real LWT_1=-1.98613424;
  parameter Real OAT_1_LWT_2=-0.00181187;
  parameter Real LWT_3=0.000412478;
  parameter Real OAT_2=0.311261338;
  parameter Real OAT_3=-0.004601083;
  parameter Real OAT_1_LWT_1=0.146226256;
  parameter Real clamp=90;
protected
  SI.Temp_C OAT=T_lwt-273.15
    "Leaving water temperature in centigrade";
  SI.Temp_C LWT=T_oat-273.15
    "Outside air temperature in centigrade";
algorithm
  freq_Transition := min(
    intercept+OAT_1*OAT+LWT_1*LWT+OAT_1_LWT_2*OAT*LWT^2+LWT_3*LWT^3+OAT_2*OAT^2+OAT_3*OAT^3+OAT_1_LWT_1*OAT*LWT,
    clamp);
  Freq_Heating_Transition_1_2 :=(freq_compressor >= freq_Transition or target_cap-10 > current_cap) and N_module > 1;
end Freq_Heating_Transition_1_2;
