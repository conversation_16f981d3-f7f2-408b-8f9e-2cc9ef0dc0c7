within Workspace.Controller.Tests;
model test_map_Cp_SDT_min
  parameter Real SST_input=-10;
  .Modelica.Blocks.Tables.CombiTable1D Table_SDT_min(
    table={{-30,10},{0,10},{25,35}})
    annotation (Placement(transformation(extent={{-28.0,-5.0},{-8.0,15.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression freq2(
    y=SST_input)
    annotation (Placement(transformation(extent={{-80.0,-11.0},{-60.0,9.0}},origin={0.0,0.0},rotation=0.0)));
equation
  connect(freq2.y,combiTable1D.u[1])
    annotation (Line(points={{-59,-1},{-44.5,-1},{-44.5,5},{-30,5}},color={0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end test_map_Cp_SDT_min;
