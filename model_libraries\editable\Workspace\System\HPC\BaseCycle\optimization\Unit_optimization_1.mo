within Workspace.System.HPC.BaseCycle.optimization;
model Unit_optimization_1
  .Workspace.System.HPC.BaseCycle.Equipement oL_modular(
    is_monobloc=is_monobloc,
    BlocA(
      evapBPHE(
        capacity_ref_fixed=not Use_compressor_actuator,
        capacity_ref=Target_capacityA),
      node_suction(
        dTsh_set=5)),
    BlocB(
      evapBPHE(
        capacity_ref_fixed=not Use_compressor_actuator,
        capacity_ref=Target_capacityB)))
    annotation (Placement(transformation(extent={{-2.0,24.0},{18.0,44.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Source sourceBrine(
    Vd_fixed=false,
    T_set=EWT,
    p_fixed=true)
    annotation (Placement(transformation(extent={{-4.0,-4.0},{4.0,4.0}},origin={8.0,-6.0},rotation=90.0)));
  .BOLT.BoundaryNode.Coolant.Sink sinkBrine(
    p_fixed=false,
    T_fixed=true,
    T_set=LWT)
    annotation (Placement(transformation(extent={{-4.0,-4.0},{4.0,4.0}},origin={8.0,64.0},rotation=-90.0)));
  .BOLT.Control.SteadyState.SetpointControl.Actuator EXV_actuatorA(
    setPoint=0.5,
    minBound=0,
    maxBound=1,
    fixed=false)
    annotation (Placement(transformation(extent={{-22.0,38.0},{-42.0,58.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Actuator EXV_actuatorB(
    fixed=false,
    maxBound=1,
    minBound=0,
    setPoint=0.5,
    isOff=is_monobloc)
    annotation (Placement(transformation(extent={{42.0,36.0},{62.0,56.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Actuator Fan_actuatorA(
    fixed=true,
    maxBound=950,
    minBound=0,
    setPoint=Fan_speedA)
    annotation (Placement(transformation(extent={{-38.0,24.0},{-58.0,44.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Actuator Fan_actuatorB(
    minBound=0,
    maxBound=950,
    isOff=is_monobloc,
    fixed=true,
    setPoint=Fan_speedB)
    annotation (Placement(transformation(extent={{50.0,22.0},{70.0,42.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Actuator Comp_actuatorA(
    fixed=Use_compressor_actuator,
    maxBound=140,
    minBound=30,
    setPoint=Cp_frequencyA)
    annotation (Placement(transformation(extent={{-28.0,10.0},{-48.0,30.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Actuator Comp_actuatorB(
    setPoint=Cp_frequencyB,
    minBound=30,
    maxBound=140,
    isOff=is_monobloc,
    fixed=Use_compressor_actuator)
    annotation (Placement(transformation(extent={{40.0,12.0},{60.0,32.0}},origin={0.0,0.0},rotation=0.0)));
  parameter Boolean is_monobloc=true;
  parameter Boolean Use_pump=false;
  parameter Boolean Use_tank=false;
  parameter Boolean Use_filter=false;
  parameter Boolean is0ff=false;
  output Real EERA;
  output Real EERB;
  output Real EER;
  parameter.Modelica.SIunits.Temperature LWT=280.15;
  parameter.Modelica.SIunits.Temperature EWT=285.15;
  output.Modelica.SIunits.Power CoolingCapacityA;
  output.Modelica.SIunits.Power CoolingCapacityB;
  output.Modelica.SIunits.Power CoolingCapacity;
  output.Modelica.SIunits.Power PcompressorA;
  output.Modelica.SIunits.Power PcompressorB;
  output.Modelica.SIunits.Power Pcompressor;
  output.Modelica.SIunits.Power PfanA;
  output.Modelica.SIunits.Power PfanB;
  output.Modelica.SIunits.Power Pfan;
  output.Modelica.SIunits.Power PinputA;
  output.Modelica.SIunits.Power PinputB;
  output.Modelica.SIunits.Power Pinput;
  output.Modelica.SIunits.Temperature SSTmaxA;
  output.Modelica.SIunits.Temperature SSTminA;
  output.Modelica.SIunits.Temperature SDTmaxA;
  output.Modelica.SIunits.Temperature SDTminA;
  output.Modelica.SIunits.Temperature DGTmaxA;
  output.Modelica.SIunits.Temperature SSTmaxB;
  output.Modelica.SIunits.Temperature SSTminB;
  output.Modelica.SIunits.Temperature SDTmaxB;
  output.Modelica.SIunits.Temperature SDTminB;
  output.Modelica.SIunits.Temperature DGTmaxB;
  parameter Boolean Use_compressor_actuator=false;
  parameter Real OAT=35;
  parameter.Modelica.SIunits.Power Target_capacityA=40000;
  parameter.Modelica.SIunits.Power Target_capacityB=60000;
  .Modelica.SIunits.Temperature SDTmax_varA=controllerSettingsA.limitsBus.SDTmax;
  .Modelica.SIunits.Temperature SDTmin_varA;
  .Modelica.SIunits.Temperature SSTmax_varA;
  .Modelica.SIunits.TemperatureDifference SH_sp_varA;
  .Modelica.SIunits.Temperature SDTmax_varB;
  .Modelica.SIunits.Temperature SDTmin_varB;
  .Modelica.SIunits.Temperature SSTmax_varB;
  .Modelica.SIunits.TemperatureDifference SH_sp_varB;
  parameter.Modelica.SIunits.Frequency Cp_frequencyA=100;
  parameter Real Fan_speedA=720;
  parameter.Modelica.SIunits.Frequency Cp_frequencyB=100;
  parameter Real Fan_speedB=720;
  .Workspace.Controller.ControllerSettings_cooling controllerSettingsA
    annotation (Placement(transformation(extent={{-12.0,70.0},{8.0,90.0}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Controller.ControllerSettings_cooling controllerSettingsB
    annotation (Placement(transformation(extent={{28.0,70.0},{48.0,90.0}},origin={0.0,0.0},rotation=0.0)));
equation
  CoolingCapacityA=oL_modular.BlocA.evapBPHE.summary.Q_flow_ref;
  CoolingCapacityB=oL_modular.BlocB.evapBPHE.summary.Q_flow_ref;
  CoolingCapacity=oL_modular.BlocA.evapBPHE.summary.Q_flow_ref+oL_modular.BlocB.evapBPHE.summary.Q_flow_ref;
  PcompressorA=oL_modular.BlocA.compressor.summary.P_compression;
  PcompressorB=oL_modular.BlocB.compressor.summary.P_compression;
  Pcompressor=oL_modular.BlocA.compressor.summary.P_compression+oL_modular.BlocB.compressor.summary.P_compression;
  PfanA=oL_modular.BlocA.motor.summary.power_shaft;
  PfanB=oL_modular.BlocB.motor.summary.power_shaft;
  Pfan=oL_modular.BlocA.motor.summary.power_shaft+oL_modular.BlocB.motor.summary.power_shaft;
  PinputA=PfanA+PcompressorA;
  PinputB=PfanB+PcompressorB;
  Pinput=Pfan+Pcompressor;
  EERA=CoolingCapacityA/PinputA;
  EERB=CoolingCapacityB/PinputB;
  EER=CoolingCapacity/Pinput;
  DGTmaxA=150;
  SSTminA=274.15;
  SDTmaxA=SDTmax_varA;
  SSTmaxA=SSTmax_varA;
  SDTminA=SDTmin_varA;
  DGTmaxB=150;
  SSTminB=274.15;
  SDTmaxB=SDTmax_varB;
  SSTmaxB=SSTmax_varB;
  SDTminB=SDTmin_varB;
  connect(sourceBrine.port,oL_modular.coolant_in)
    annotation (Line(points={{8,-2},{8,10},{7.6,10},{7.6,22}},color={0,127,0}));
  connect(oL_modular.coolant_out,sinkBrine.port)
    annotation (Line(points={{7.6,44.2},{7.6,52.1},{8,52.1},{8,60}},color={0,127,0}));
  connect(EXV_actuatorA.value,oL_modular.EXV_controller_A)
    annotation (Line(points={{-27,48},{-15,48},{-15,36.5},{-3,36.5}},color={0,0,127}));
  connect(Fan_actuatorA.value,oL_modular.Fan_controller_A)
    annotation (Line(points={{-43,34},{-29,34},{-29,31.5},{-3,31.5}},color={0,0,127}));
  connect(Comp_actuatorA.value,oL_modular.Compressor_controller_A)
    annotation (Line(points={{-33,20},{-18,20},{-18,26.5},{-3,26.5}},color={0,0,127}));
  connect(EXV_actuatorB.value,oL_modular.EXV_controller_B)
    annotation (Line(points={{47,46},{33,46},{33,36.5},{19,36.5}},color={0,0,127}));
  connect(Fan_actuatorB.value,oL_modular.Fan_controller_B)
    annotation (Line(points={{55,32},{37,32},{37,31.5},{19,31.5}},color={0,0,127}));
  connect(Comp_actuatorB.value,oL_modular.Compressor_controller_B)
    annotation (Line(points={{45,22},{32,22},{32,26.5},{19,26.5}},color={0,0,127}));
  connect(oL_modular.measurementBusB,controllerSettingsB.measurementBus)
    annotation (Line(points={{19,40},{26.4,40},{26.4,80.6}},color={255,204,51}));
  connect(oL_modular.measurementBusA,controllerSettingsA.measurementBus)
    annotation (Line(points={{-3,40},{-13.600000000000001,40},{-13.600000000000001,80.6}},color={255,204,51}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end Unit_optimization_1;
