within Workspace.System.Multimodule.HPC;
model System_61AQ_Modular
  parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector CoolantMedium=ECAT.EvapBrineType_nd
    "Coolant Medium Selection"
    annotation (Dialog(group="Medium"));
  final parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Empty coolantInf=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.getSelector(
    CoolantMedium)
    "Coolant Medium"
    annotation ();
  .BOLT.InternalLibrary.Media.Coolant.CoolantCommon.Temperature FreezTemp=.BOLT.InternalLibrary.Media.Coolant.CoolantCommon.freezeTemp(
    coolantInf,
    BrineConcentration,
    1);
  parameter Real BrineConcentration=ECAT.EvapBrineConcentration_nd.setPoint
    annotation (Dialog(group="Medium"));
  .BOLT.BoundaryNode.Coolant.Source sourceBrine(
    Vd_fixed=ECAT.EvapBrineFlowRate_m3s.fixed,
    T_set=EWT,
    p_fixed=true,
    CoolantMedium=CoolantMedium,
    T_fixed=ECAT.EvapBrineEWT_K.fixed and not ECAT.EvapBrineFlowRate_m3s.fixed,
    Vd_set=ECAT.EvapBrineFlowRate_m3s.setPoint,
    X=BrineConcentration)
    annotation (Placement(transformation(extent={{-6.307870201039421,-6.307870201039421},{6.307870201039421,6.307870201039421}},origin={6.118859653839639,-89.59898379798511},rotation=90.0)));
  .BOLT.BoundaryNode.Coolant.Sink sinkBrine(
    p_fixed=true,
    T_set=LWT,
    CoolantMedium=CoolantMedium,
    p_set=sourceBrine.p_set,
    X=BrineConcentration,
    Vd_fixed=false)
    annotation (Placement(transformation(extent={{-3.8483240695665124,-3.848324069566445},{3.8483240695665124,3.848324069566445}},origin={6.218204003021201,75.70329478819629},rotation=-90.0)));
  parameter.Modelica.SIunits.Temperature LWT=ECAT.EvapBrineLWT_K.setPoint
    annotation (Dialog(group="Conditions"));
  parameter.Modelica.SIunits.Temperature EWT=ECAT.EvapBrineEWT_K.setPoint
    annotation (Dialog(group="Conditions"));
  parameter.Modelica.SIunits.Temperature OAT=ECAT.AmbientAirDBTemp_K.setPoint
    annotation (Dialog(group="Conditions"));
  parameter Boolean use_bf=false
    annotation (Dialog(group="Use Parameters"));
  parameter Boolean Use_EN14511=true
    annotation (Dialog(group="Use Parameters"));
  parameter Boolean use_Calib=true
    annotation (Dialog(group="Use Parameters"));
  inner.BOLT.GlobalParameters globalParameters(
    varLevel=.BOLT.InternalLibrary.BuildingBlocks.Types.Var_level.Advanced,
    oilMode=false,
    RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R290)
    annotation (Placement(transformation(extent={{-71.07901718307973,81.88164298245607},{-52.58286101038492,100.37779915515088}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.Split split(
    X=BrineConcentration,
    CoolantMedium=CoolantMedium,
    isOff_a=IsOFF1 and IsOFF2,
    isOff_b=IsOFF3 and IsOFF4,
    fa_set=
      if(IsOFF3 and IsOFF4) then
        1
      else
        0.5,
    mDot_b_start=Module_4.Module.mdot_start*(1-.Workspace.Auxiliary.Tools.booleanToReal(
      IsOFF4))+Module_3.Module.mdot_start*(1-.Workspace.Auxiliary.Tools.booleanToReal(
      IsOFF3)),
    mDot_a_start=Module_1.Module.mdot_start+Module_2.Module.mdot_start*(1-.Workspace.Auxiliary.Tools.booleanToReal(
      IsOFF2)),
    p_start=sourceBrine.p_set,
    T_start=EWT)
    annotation (Placement(transformation(extent={{-2.609284077593937,-2.6092840775939408},{2.609284077593937,2.6092840775939408}},origin={6.035010396540265,-72.61479817138942},rotation=90.0)));
  .BOLT.CoolantMisc.Mixer mixer(
    X=BrineConcentration,
    CoolantMedium=CoolantMedium,
    fa_fixed=false,
    isOff_a=IsOFF1 and IsOFF2,
    isOff_b=IsOFF3 and IsOFF4,
    fa_set=
      if(IsOFF3 and IsOFF4) then
        1
      else
        0.5)
    annotation (Placement(transformation(extent={{-3.597086624961869,-3.597086624961875},{3.597086624961869,3.597086624961875}},origin={5.7833233234222226,42.22295922065787},rotation=90.0)));
  .BOLT.CoolantMisc.ReducedPipe external_system(
    Ka_fixed=true,
    X=BrineConcentration,
    CoolantMedium=CoolantMedium,
    T_start=LWT,
    isOff=false,
    Ka_set=0.01,
    use_Ka_in=true)
    annotation (Placement(transformation(extent={{-4.196959134473763,-4.196959134473701},{4.196959134473763,4.196959134473701}},origin={6.218204003021199,64.58299309703474},rotation=90.0)));
  .BOLT.CoolantMisc.Split split2(
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff_a=IsOFF1,
    isOff_b=IsOFF2,
    fa_set=
      if IsOFF2 then
        1
      else
        0.5,
    mDot_b_start=Module_2.Module.mdot_start*(1-.Workspace.Auxiliary.Tools.booleanToReal(
      IsOFF2)),
    mDot_a_start=Module_1.Module.mdot_start,
    T_start=EWT,
    p_start=sourceBrine.p_set)
    annotation (Placement(transformation(extent={{-2.6092840775939408,-2.6092840775939337},{2.6092840775939408,2.6092840775939337}},origin={-39.2597973957626,-55.78361344087423},rotation=90.0)));
  .BOLT.CoolantMisc.Mixer mixer2(
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff_a=IsOFF1,
    isOff_b=IsOFF2,
    fa_set=
      if IsOFF2 then
        1
      else
        0.5)
    annotation (Placement(transformation(extent={{-2.6092840775939408,-2.609284077593937},{2.6092840775939408,2.609284077593937}},origin={-37.5072945683571,22.24635271582144},rotation=90.0)));
  .BOLT.CoolantMisc.Split split3(
    X=BrineConcentration,
    CoolantMedium=CoolantMedium,
    isOff_a=IsOFF3,
    isOff_b=IsOFF4,
    fa_set=
      if IsOFF4 then
        1
      else
        0.5,
    mDot_b_start=Module_4.Module.mdot_start*(1-.Workspace.Auxiliary.Tools.booleanToReal(
      IsOFF4)),
    mDot_a_start=Module_3.Module.mdot_start*(1-.Workspace.Auxiliary.Tools.booleanToReal(
      IsOFF3)),
    T_start=EWT,
    p_start=sourceBrine.p_set)
    annotation (Placement(transformation(extent={{-2.6092840775939337,-2.6092840775939337},{2.6092840775939337,2.6092840775939337}},origin={53.61079871876068,-54.37774465108568},rotation=90.0)));
  .BOLT.CoolantMisc.Mixer mixer3(
    X=BrineConcentration,
    CoolantMedium=CoolantMedium,
    fa_fixed=false,
    isOff_a=IsOFF3,
    isOff_b=IsOFF4,
    fa_set=
      if IsOFF4 then
        1
      else
        0.5)
    annotation (Placement(transformation(extent={{-2.6092840775939337,-2.609284077593937},{2.6092840775939337,2.609284077593937}},origin={54.61642952139507,25.550635835197774},rotation=90.0)));
  .Workspace.System.Multimodule.HPC.Modular_61AQ Module_1(
    TargetCapacity=TargetCapacity,
    Selector_block_A=choiceBlock.Module_1.BlocA,
    Selector_block_B=choiceBlock.Module_1.BlocB,
    LWT_fixed=ECAT.EvapBrineLWT_K.fixed,
    LWT=ECAT.EvapBrineLWT_K.setPoint,
    EWT_fixed=ECAT.EvapBrineEWT_K.fixed,
    EWT=ECAT.EvapBrineEWT_K.setPoint,
    FlowRate_fixed=ECAT.EvapBrineFlowRate_m3s.fixed,
    FlowRate=ECAT.EvapBrineFlowRate_m3s.setPoint,
    OAT=ECAT.AmbientAirDBTemp_K.setPoint,
    Relative_humidity=ECAT.AmbientAirRH_nd.setPoint,
    Pdispo=ECAT.ExternalSystemPressureDrop_Pa.setPoint,
    CoolantMedium=CoolantMedium,
    Pump_selector=choiceBlock.Pump_selector,
    Coating_selector=choiceBlock.Coating_selector,
    use_en=Use_EN14511,
    Module(
      EvapFoulingFactor=ECAT.EvapFoulingFactor_m2KW.setPoint),
    SC_fixed={not ECAT.RefrigerantCharge_kg[1].fixed,not ECAT.RefrigerantCharge_kg[2].fixed},
    Mref_fixed={ECAT.RefrigerantCharge_kg[1].fixed,ECAT.RefrigerantCharge_kg[2].fixed},
    LWT=LWT,
    EWT=EWT,
    OAT=OAT,
    controller_crkA(
      is_load_ratio=ECAT.LoadRatio_nd.fixed,
      load_ratio=ECAT.LoadRatio_nd.setPoint),
    controller_crkB(
      is_load_ratio=ECAT.LoadRatio_nd.fixed,
      load_ratio=ECAT.LoadRatio_nd.setPoint),
    use_bf=use_bf,
    use_Calib=use_Calib,
    is_monobloc_db_initialization=false,
    isOFF=IsOFF1,
    BrineConcentration=BrineConcentration,
    is_monobloc=choiceBlock.Module_1.is_monobloc,
    isFilter=choiceBlock.isFilter,
    Coef_filter=choiceBlock.Module_1.Coef_filter,
    isBufferTank=choiceBlock.isBufferTank,
    Coef_bufferTank=choiceBlock.Module_1.Coef_BufferTank,Altitude = ECAT.Altitude_m.setPoint,Sound_selector = choiceBlock.SoundOption_selector)
    annotation (Placement(transformation(extent={{-66.73926117781534,-17.423777191267945},{-51.48793205915658,-2.172448072609214}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.System.Multimodule.HPC.Modular_61AQ Module_2(
    TargetCapacity=TargetCapacity,
    isOFF=IsOFF2,
    Selector_block_A=choiceBlock.Module_2.BlocA,
    is_monobloc=choiceBlock.Module_2.is_monobloc,
    Selector_block_B=choiceBlock.Module_2.BlocB,
    LWT_fixed=ECAT.EvapBrineLWT_K.fixed,
    LWT=ECAT.EvapBrineLWT_K.setPoint,
    EWT_fixed=ECAT.EvapBrineEWT_K.fixed,
    EWT=ECAT.EvapBrineEWT_K.setPoint,
    FlowRate_fixed=ECAT.EvapBrineFlowRate_m3s.fixed,
    FlowRate=ECAT.EvapBrineFlowRate_m3s.setPoint,
    OAT=ECAT.AmbientAirDBTemp_K.setPoint,
    Relative_humidity=ECAT.AmbientAirRH_nd.setPoint,
    Pdispo=ECAT.ExternalSystemPressureDrop_Pa.setPoint,
    CoolantMedium=CoolantMedium,
    BrineConcentration=BrineConcentration,
    Pump_selector=choiceBlock.Pump_selector,
    Coating_selector=choiceBlock.Coating_selector,
    use_en=Use_EN14511,
    Module(
      EvapFoulingFactor=ECAT.EvapFoulingFactor_m2KW.setPoint),
    SC_fixed={not ECAT.RefrigerantCharge_kg[3].fixed,not ECAT.RefrigerantCharge_kg[4].fixed},
    Mref_fixed={ECAT.RefrigerantCharge_kg[3].fixed,ECAT.RefrigerantCharge_kg[4].fixed},
    LWT=LWT,
    EWT=EWT,
    OAT=OAT,
    controller_crkA(
      is_load_ratio=ECAT.LoadRatio_nd.fixed,
      load_ratio=ECAT.LoadRatio_nd.setPoint),
    controller_crkB(
      is_load_ratio=ECAT.LoadRatio_nd.fixed,
      load_ratio=ECAT.LoadRatio_nd.setPoint),
    use_bf=use_bf,
    use_Calib=use_Calib,
    is_monobloc_db_initialization=false,
    isFilter=choiceBlock.isFilter,
    Coef_filter=choiceBlock.Module_2.Coef_filter,
    isBufferTank=choiceBlock.isBufferTank,
    Coef_bufferTank=choiceBlock.Module_2.Coef_BufferTank,Altitude = ECAT.Altitude_m.setPoint,Sound_selector = choiceBlock.SoundOption_selector)
    annotation (Placement(transformation(extent={{-20.35808651537588,-17.090022953860746},{-4.771811432909656,-1.5037478713945163}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.System.Multimodule.HPC.Modular_61AQ Module_3(
    TargetCapacity=TargetCapacity,
    isOFF=IsOFF3,
    Selector_block_A=choiceBlock.Module_3.BlocA,
    is_monobloc=choiceBlock.Module_3.is_monobloc,
    Selector_block_B=choiceBlock.Module_3.BlocB,
    LWT_fixed=ECAT.EvapBrineLWT_K.fixed,
    LWT=ECAT.EvapBrineLWT_K.setPoint,
    EWT_fixed=ECAT.EvapBrineEWT_K.fixed,
    EWT=ECAT.EvapBrineEWT_K.setPoint,
    FlowRate_fixed=ECAT.EvapBrineFlowRate_m3s.fixed,
    FlowRate=ECAT.EvapBrineFlowRate_m3s.setPoint,
    OAT=ECAT.AmbientAirDBTemp_K.setPoint,
    Relative_humidity=ECAT.AmbientAirRH_nd.setPoint,
    Pdispo=ECAT.ExternalSystemPressureDrop_Pa.setPoint,
    CoolantMedium=CoolantMedium,
    BrineConcentration=BrineConcentration,
    Pump_selector=choiceBlock.Pump_selector,
    Coating_selector=choiceBlock.Coating_selector,
    use_en=Use_EN14511,
    Module(
      EvapFoulingFactor=ECAT.EvapFoulingFactor_m2KW.setPoint),
    SC_fixed={not ECAT.RefrigerantCharge_kg[5].fixed,not ECAT.RefrigerantCharge_kg[6].fixed},
    Mref_fixed={ECAT.RefrigerantCharge_kg[5].fixed,ECAT.RefrigerantCharge_kg[6].fixed},
    LWT=LWT,
    EWT=EWT,
    OAT=OAT,
    controller_crkA(
      is_load_ratio=ECAT.LoadRatio_nd.fixed,
      load_ratio=ECAT.LoadRatio_nd.setPoint),
    controller_crkB(
      is_load_ratio=ECAT.LoadRatio_nd.fixed,
      load_ratio=ECAT.LoadRatio_nd.setPoint),
    use_bf=use_bf,
    use_Calib=use_Calib,
    is_monobloc_db_initialization=false,
    isFilter=choiceBlock.isFilter,
    Coef_filter=choiceBlock.Module_3.Coef_filter,
    isBufferTank=choiceBlock.isBufferTank,
    Coef_bufferTank=choiceBlock.Module_3.Coef_BufferTank,Altitude = ECAT.Altitude_m.setPoint,Sound_selector = choiceBlock.SoundOption_selector)
    annotation (Placement(transformation(extent={{26.709155741673946,-14.936464314175018},{41.74201907283756,0.09639901698858644}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.System.Multimodule.HPC.Modular_61AQ Module_4(
    TargetCapacity=TargetCapacity,
    isOFF=IsOFF4,
    Selector_block_A=choiceBlock.Module_4.BlocA,
    is_monobloc=choiceBlock.Module_4.is_monobloc,
    Selector_block_B=choiceBlock.Module_4.BlocB,
    LWT_fixed=ECAT.EvapBrineLWT_K.fixed,
    LWT=ECAT.EvapBrineLWT_K.setPoint,
    EWT_fixed=ECAT.EvapBrineEWT_K.fixed,
    EWT=ECAT.EvapBrineEWT_K.setPoint,
    FlowRate_fixed=ECAT.EvapBrineFlowRate_m3s.fixed,
    FlowRate=ECAT.EvapBrineFlowRate_m3s.setPoint,
    OAT=ECAT.AmbientAirDBTemp_K.setPoint,
    Relative_humidity=ECAT.AmbientAirRH_nd.setPoint,
    Pdispo=ECAT.ExternalSystemPressureDrop_Pa.setPoint,
    CoolantMedium=CoolantMedium,
    BrineConcentration=BrineConcentration,
    Pump_selector=choiceBlock.Pump_selector,
    Coating_selector=choiceBlock.Coating_selector,
    use_en=Use_EN14511,
    Module(
      EvapFoulingFactor=ECAT.EvapFoulingFactor_m2KW.setPoint),
    SC_fixed={not ECAT.RefrigerantCharge_kg[7].fixed,not ECAT.RefrigerantCharge_kg[8].fixed},
    Mref_fixed={ECAT.RefrigerantCharge_kg[7].fixed,ECAT.RefrigerantCharge_kg[8].fixed},
    LWT=LWT,
    EWT=EWT,
    OAT=OAT,
    controller_crkA(
      is_load_ratio=ECAT.LoadRatio_nd.fixed,
      load_ratio=ECAT.LoadRatio_nd.setPoint),
    controller_crkB(
      is_load_ratio=ECAT.LoadRatio_nd.fixed,
      load_ratio=ECAT.LoadRatio_nd.setPoint),
    use_bf=use_bf,
    use_Calib=use_Calib,
    is_monobloc_db_initialization=false,
    isFilter=choiceBlock.isFilter,
    Coef_filter=choiceBlock.Module_4.Coef_filter,
    isBufferTank=choiceBlock.isBufferTank,
    Coef_bufferTank=choiceBlock.Module_4.Coef_BufferTank,Altitude = ECAT.Altitude_m.setPoint,Sound_selector = choiceBlock.SoundOption_selector)
    annotation (Placement(transformation(extent={{67.11195426798463,-15.056490096441127},{82.59467175753042,0.42622739310464297}},origin={0.0,0.0},rotation=0.0)));
  parameter.Modelica.SIunits.Power TargetCapacity=min(ECAT.TargetCoolingCapacity_W.setPoint,Max_targetcap)
    annotation (Dialog(group="Cp_control"));
  .Modelica.Blocks.Sources.RealExpression ControlledCapacity(
    y=total_capacity)
    annotation (Placement(transformation(extent={{-116.32791932388885,-56.83675411606582},{-96.32791932388885,-36.83675411606582}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Auxiliary.ECAT_Zenith.ECATBase ECAT(
    EvapBrineDensity_kgm3(
      value=1/sourceBrine.summary.v),
    TargetCoolingCapacity_W(
      setPoint=320000),
    EvapBrineFlowRate_m3s(
      value=
        if ECAT.EvapBrineFlowRate_m3s.fixed then
          sourceBrine.summary.Vd
        else
          EvapFlowRate,
      fixed=false,
      setPoint=0.005),
    EvapBrineEWT_K(
      value=
        if ECAT.EvapBrineEWT_K.fixed then
          sourceBrine.summary.T
        else
          Evap_ewt,
      setPoint=285.15,
      fixed=true),
    EvapBrineLWT_K(
      value=
        if ECAT.EvapBrineLWT_K.fixed then
          sinkBrine.summary.T
        else
          Evap_lwt,
      setPoint=280.15,
      fixed=true),
    LoadRatio_nd(
      value=Module_1.controller_crkA.completeCompressorControl_base.capacity_controller.summary.AV*100,
      fixed=false),
    RefrigerantCharge_kg(
      value={Module_1.Module.BlockA.systemVariables.mRef[1],Module_1.Module.BlockB.systemVariables.mRef[1],Module_2.Module.BlockA.systemVariables.mRef[1],Module_2.Module.BlockB.systemVariables.mRef[1],Module_3.Module.BlockA.systemVariables.mRef[1],Module_3.Module.BlockB.systemVariables.mRef[1],Module_4.Module.BlockA.systemVariables.mRef[1],Module_4.Module.BlockB.systemVariables.mRef[1]}),
    EvapPumpSpeed_rpm(
      setPoint=1800),
    CondPumpSpeed_rpm(
      value=Module_1.Module.pumpPolyA.summary.speed,
      setPoint=1800),
    AmbientAirRH_nd(
      setPoint=0.87),
    PubUnitPower_W(
      value=total_power),
    TotalFanPower_W(
      value=sum(
        ECAT.FanPower_W.value)),
    TotalCompressorPower_W(
      value=sum(
        ECAT.CompressorPower_W.value)),
    TotalOilCharge_kg(
      value=Module_1.choiceBlock.Unit_Block_A.Oil_charge+Module_1.choiceBlock.Unit_Block_B.Oil_charge+Module_2.choiceBlock.Unit_Block_A.Oil_charge+Module_2.choiceBlock.Unit_Block_B.Oil_charge+Module_3.choiceBlock.Unit_Block_A.Oil_charge+Module_3.choiceBlock.Unit_Block_B.Oil_charge+Module_4.choiceBlock.Unit_Block_A.Oil_charge+Module_4.choiceBlock.Unit_Block_B.Oil_charge),
    TotalRefrigerantCharge_kg(
      value=Module_1.choiceBlock.Unit_Block_A.m_ref_froid+Module_1.choiceBlock.Unit_Block_B.m_ref_froid+Module_2.choiceBlock.Unit_Block_A.m_ref_froid+Module_2.choiceBlock.Unit_Block_B.m_ref_froid+Module_3.choiceBlock.Unit_Block_A.m_ref_froid+Module_3.choiceBlock.Unit_Block_B.m_ref_froid+Module_4.choiceBlock.Unit_Block_A.m_ref_froid+Module_4.choiceBlock.Unit_Block_B.m_ref_froid),
    EvapFoulingFactor_m2KW(
      setPoint=0),
    EvapBrineConcentration_nd(
      setPoint=0.4),
    AmbientAirDBTemp_K(
      setPoint=298.15),
    FanPower_W(
      value={Module_1.Module.BlockA.motor.summary.power_VFD,Module_1.Module.BlockB.motor.summary.power_VFD,Module_2.Module.BlockA.motor.summary.power_VFD,Module_2.Module.BlockB.motor.summary.power_VFD,Module_3.Module.BlockA.motor.summary.power_VFD,Module_3.Module.BlockB.motor.summary.power_VFD,Module_4.Module.BlockA.motor.summary.power_VFD,Module_4.Module.BlockB.motor.summary.power_VFD}),
    CompressorPower_W(
      value={Module_1.Module.BlockA.compressor.summary.P_compression,Module_1.Module.BlockB.compressor.summary.P_compression,Module_2.Module.BlockA.compressor.summary.P_compression,Module_2.Module.BlockB.compressor.summary.P_compression,Module_3.Module.BlockA.compressor.summary.P_compression,Module_3.Module.BlockB.compressor.summary.P_compression,Module_4.Module.BlockA.compressor.summary.P_compression,Module_4.Module.BlockB.compressor.summary.P_compression}),
    CompressorFrequency_Hz(
      value={Module_1.Module.BlockA.compressor.summary.Ncomp,Module_1.Module.BlockB.compressor.summary.Ncomp,Module_2.Module.BlockA.compressor.summary.Ncomp,Module_2.Module.BlockB.compressor.summary.Ncomp,Module_3.Module.BlockA.compressor.summary.Ncomp,Module_3.Module.BlockB.compressor.summary.Ncomp,Module_4.Module.BlockA.compressor.summary.Ncomp,Module_4.Module.BlockB.compressor.summary.Ncomp}),
    CondFanAirflowRate_m3s(
      value={2*Module_1.Module.BlockA.AirFlow,
             2*Module_1.Module.BlockB.AirFlow,
             2*Module_2.Module.BlockA.AirFlow,
             2*Module_2.Module.BlockB.AirFlow,
             2*Module_3.Module.BlockA.AirFlow,
             2*Module_3.Module.BlockB.AirFlow,
             2*Module_4.Module.BlockA.AirFlow,
             2*Module_4.Module.BlockB.AirFlow}),
    DischargeSuperheat_K(
      value={Module_1.Module.BlockA.node_discharge.dTsh,Module_1.Module.BlockB.node_discharge.dTsh,Module_2.Module.BlockA.node_discharge.dTsh,Module_2.Module.BlockB.node_discharge.dTsh,Module_3.Module.BlockA.node_discharge.dTsh,Module_3.Module.BlockB.node_discharge.dTsh,Module_4.Module.BlockA.node_discharge.dTsh,Module_4.Module.BlockB.node_discharge.dTsh}),
    CondSubcooling_K(
      value={Module_1.Module.BlockA.node_liquid.dTsh,Module_1.Module.BlockB.node_liquid.dTsh,Module_2.Module.BlockA.node_liquid.dTsh,Module_2.Module.BlockB.node_liquid.dTsh,Module_3.Module.BlockA.node_liquid.dTsh,Module_3.Module.BlockB.node_liquid.dTsh,Module_4.Module.BlockA.node_liquid.dTsh,Module_4.Module.BlockB.node_liquid.dTsh}),
    SuctionSuperheat_K(
      value={Module_1.Module.BlockA.node_suction.dTsh,Module_1.Module.BlockB.node_suction.dTsh,Module_2.Module.BlockA.node_suction.dTsh,Module_2.Module.BlockB.node_suction.dTsh,Module_3.Module.BlockA.node_suction.dTsh,Module_3.Module.BlockB.node_suction.dTsh,Module_4.Module.BlockA.node_suction.dTsh,Module_4.Module.BlockB.node_suction.dTsh}),
    RefrigerantDGT_K(
      value={Module_1.Module.BlockA.node_discharge.T,Module_1.Module.BlockB.node_discharge.T,Module_2.Module.BlockA.node_discharge.T,Module_2.Module.BlockB.node_discharge.T,Module_3.Module.BlockA.node_discharge.T,Module_3.Module.BlockB.node_discharge.T,Module_4.Module.BlockA.node_discharge.T,Module_4.Module.BlockB.node_discharge.T}),
    RefrigerantSCT_K(
      value={Module_1.Module.BlockA.node_condin.Tsat,Module_1.Module.BlockB.node_condin.Tsat,Module_2.Module.BlockA.node_condin.Tsat,Module_2.Module.BlockB.node_condin.Tsat,Module_3.Module.BlockA.node_condin.Tsat,Module_3.Module.BlockB.node_condin.Tsat,Module_4.Module.BlockA.node_condin.Tsat,Module_4.Module.BlockB.node_condin.Tsat}),
    RefrigerantSET_K(
      value={Module_1.Module.BlockA.node_evapout.Tsat,Module_1.Module.BlockB.node_evapout.Tsat,Module_2.Module.BlockA.node_evapout.Tsat,Module_2.Module.BlockB.node_evapout.Tsat,Module_3.Module.BlockA.node_evapout.Tsat,Module_3.Module.BlockB.node_evapout.Tsat,Module_4.Module.BlockA.node_evapout.Tsat,Module_4.Module.BlockB.node_evapout.Tsat}),
    RefrigerantSDT_K(
      value={Module_1.Module.BlockA.node_discharge.Tsat,Module_1.Module.BlockB.node_discharge.Tsat,Module_2.Module.BlockA.node_discharge.Tsat,Module_2.Module.BlockB.node_discharge.Tsat,Module_3.Module.BlockA.node_discharge.Tsat,Module_3.Module.BlockB.node_discharge.Tsat,Module_4.Module.BlockA.node_discharge.Tsat,Module_4.Module.BlockB.node_discharge.Tsat}),
    RefrigerantSST_K(
      value={Module_1.Module.BlockA.node_suction.Tsat,Module_1.Module.BlockB.node_suction.Tsat,Module_2.Module.BlockA.node_suction.Tsat,Module_2.Module.BlockB.node_suction.Tsat,Module_3.Module.BlockA.node_suction.Tsat,Module_3.Module.BlockB.node_suction.Tsat,Module_4.Module.BlockA.node_suction.Tsat,Module_4.Module.BlockB.node_suction.Tsat}),
    nbrCircuit=8,
    ExternalSystemPressureDrop_Pa(
      setPoint=80000,
      value=Module_1.AvailableStaticPressure),
    PubCoolingCapacity_W(
      value=total_capacity),
    FanSpeed_rpm(
      value={Module_1.Module.BlockA.fanCurve.summary.speed,Module_1.Module.BlockB.fanCurve.summary.speed,Module_2.Module.BlockA.fanCurve.summary.speed,Module_2.Module.BlockB.fanCurve.summary.speed,Module_3.Module.BlockA.fanCurve.summary.speed,Module_3.Module.BlockB.fanCurve.summary.speed,Module_4.Module.BlockA.fanCurve.summary.speed,Module_4.Module.BlockB.fanCurve.summary.speed}),
    FanFrequency_Hz(
      value={Module_1.Module.BlockA.motor.summary.Motor_freq,Module_1.Module.BlockB.motor.summary.Motor_freq,Module_2.Module.BlockA.motor.summary.Motor_freq,Module_2.Module.BlockB.motor.summary.Motor_freq,Module_3.Module.BlockA.motor.summary.Motor_freq,Module_3.Module.BlockB.motor.summary.Motor_freq,Module_4.Module.BlockA.motor.summary.Motor_freq,Module_4.Module.BlockB.motor.summary.Motor_freq}),
    CondPumpSpeed_Hz(
      value={Module_1.Module.pumpPolyA.pump.speed_Hz,Module_1.Module.pumpPolyB.pump.speed_Hz,Module_2.Module.pumpPolyA.pump.speed_Hz,Module_2.Module.pumpPolyB.pump.speed_Hz,Module_3.Module.pumpPolyA.pump.speed_Hz,Module_3.Module.pumpPolyB.pump.speed_Hz,Module_4.Module.pumpPolyA.pump.speed_Hz,Module_4.Module.pumpPolyB.pump.speed_Hz}),
    CondPumpSpeed_rpm(
      value={Module_1.Module.pumpPolyA.summary.speed,Module_1.Module.pumpPolyB.summary.speed,Module_2.Module.pumpPolyA.summary.speed,Module_2.Module.pumpPolyB.summary.speed,Module_3.Module.pumpPolyA.summary.speed,Module_3.Module.pumpPolyB.summary.speed,Module_4.Module.pumpPolyA.summary.speed,Module_4.Module.pumpPolyB.summary.speed}),
    EvapPumpSpeed_Hz(
      value={Module_1.Module.pumpPolyA.pump.speed_Hz,Module_1.Module.pumpPolyB.pump.speed_Hz,Module_2.Module.pumpPolyA.pump.speed_Hz,Module_2.Module.pumpPolyB.pump.speed_Hz,Module_3.Module.pumpPolyA.pump.speed_Hz,Module_3.Module.pumpPolyB.pump.speed_Hz,Module_4.Module.pumpPolyA.pump.speed_Hz,Module_4.Module.pumpPolyB.pump.speed_Hz}),
    EvapPumpSpeed_rpm(
      value={Module_1.Module.pumpPolyA.summary.speed,Module_1.Module.pumpPolyB.summary.speed,Module_2.Module.pumpPolyA.summary.speed,Module_2.Module.pumpPolyB.summary.speed,Module_3.Module.pumpPolyA.summary.speed,Module_3.Module.pumpPolyB.summary.speed,Module_4.Module.pumpPolyA.summary.speed,Module_4.Module.pumpPolyB.summary.speed}),
    CoolantFreezingTemp_K(
      value=Module_1.FreezTemp),
    ElecFanBoxFrequency_Hz(
      value={Module_1.controllerSettings_crkA.FanSpeedBox,Module_1.controllerSettings_crkB.FanSpeedBox,Module_2.controllerSettings_crkA.FanSpeedBox,Module_2.controllerSettings_crkB.FanSpeedBox,Module_3.controllerSettings_crkA.FanSpeedBox,Module_3.controllerSettings_crkB.FanSpeedBox,Module_4.controllerSettings_crkA.FanSpeedBox,Module_4.controllerSettings_crkB.FanSpeedBox}),
    EvapBrineIntPressDrop_Pa(
      value=InternalPressureDrop_eq),
    EvapPumpPower_W(
      value=Module_1.Module.pumpPolyA.summary.P_motor+Module_1.Module.pumpPolyB.summary.P_motor+Module_2.Module.pumpPolyA.summary.P_motor+Module_2.Module.pumpPolyB.summary.P_motor+Module_3.Module.pumpPolyA.summary.P_motor+Module_3.Module.pumpPolyB.summary.P_motor+Module_4.Module.pumpPolyA.summary.P_motor+Module_4.Module.pumpPolyB.summary.P_motor),IsMinimalCapacity(value = if Module_1.controller_crkA.completeCompressorControl_base.capacity_controller.summary.ID == -3 then 1 else 0))
    annotation (Placement(transformation(extent={{-50.23480772314652,82.12891949331699},{-31.920906071732347,100.44282114473116}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Node node_out(
    T_start=LWT,
    X=BrineConcentration,
    CoolantMedium=CoolantMedium,
    p_fixed=false,
    p_set=Pdispo+sourceBrine.p_set)
    annotation (Placement(transformation(extent={{-2.6571693963575083,-2.65716939635751},{2.6571693963575083,2.65716939635751}},origin={6.218204003021201,53.912786367442024},rotation=90.0)));
  parameter Boolean IsOFF1=false
    annotation (Dialog(group="isOff",tab="StateMachine"));
  parameter Boolean IsOFF2=true
    annotation (Dialog(group="isOff",tab="StateMachine"));
  parameter Boolean IsOFF3=true
    annotation (Dialog(group="isOff",tab="StateMachine"));
  parameter Boolean IsOFF4=true
    annotation (Dialog(group="isOff",tab="StateMachine"));
  .BOLT.BoundaryNode.Coolant.Node Inlet_1(
    isOff=IsOFF1,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    T_start=EWT)
    annotation (Placement(transformation(extent={{-2.96056502800689,-2.9605650280068865},{2.96056502800689,2.9605650280068865}},origin={-59.188360496227475,-26.29869342809592},rotation=90.0)));
  .BOLT.BoundaryNode.Coolant.Node Outlet_1(
    isOff=IsOFF1,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    T_start=LWT)
    annotation (Placement(transformation(extent={{-3.138802286365596,-3.1388022863655944},{3.138802286365596,3.1388022863655944}},origin={-59.40943026126568,6.89375334925699},rotation=90.0)));
  .BOLT.BoundaryNode.Coolant.Node Outlet_2(
    isOff=IsOFF2,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    T_start=LWT)
    annotation (Placement(transformation(extent={{-3.2306838028017673,-3.2306838028017673},{3.2306838028017673,3.2306838028017673}},origin={-13.285875940002597,6.597936324514301},rotation=90.0)));
  .BOLT.BoundaryNode.Coolant.Node Inlet_2(
    isOff=IsOFF2,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    T_start=EWT)
    annotation (Placement(transformation(extent={{-3.215173607215906,-3.215173607215906},{3.215173607215906,3.215173607215906}},origin={-12.960651229281716,-28.48788800791606},rotation=90.0)));
  .BOLT.BoundaryNode.Coolant.Node Outlet_3(
    isOff=IsOFF3,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    T_start=LWT)
    annotation (Placement(transformation(extent={{-2.813137670154717,-2.813137670154717},{2.813137670154717,2.813137670154717}},origin={34.785160078741,9.29934009779417},rotation=90.0)));
  .BOLT.BoundaryNode.Coolant.Node Inlet_3(
    isOff=IsOFF3,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    T_start=EWT)
    annotation (Placement(transformation(extent={{-3.100774381208595,-3.1007743812085984},{3.100774381208595,3.1007743812085984}},origin={34.43386111962755,-24.43634589327049},rotation=90.0)));
  .BOLT.BoundaryNode.Coolant.Node Outlet_4(
    isOff=IsOFF4,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    T_start=LWT)
    annotation (Placement(transformation(extent={{-2.5083187102476785,-2.5083187102476807},{2.5083187102476785,2.5083187102476807}},origin={74.60788415229285,9.24973096076083},rotation=90.0)));
  .BOLT.BoundaryNode.Coolant.Node Inlet_4(
    isOff=IsOFF4,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    T_start=EWT)
    annotation (Placement(transformation(extent={{-2.950739646623262,-2.9507396466232585},{2.950739646623262,2.9507396466232585}},origin={74.165159499364,-24.710174992364973},rotation=90.0)));
  .Workspace.Auxiliary.OptionBlock.ChoiceBlock_multi choiceBlock(
    Module_1_selector=.Workspace.Auxiliary.OptionBlock.Record_ModuleBase.Module_selector.Module_140,
    Number_of_modules=3,
    Pump_selector=.Workspace.Auxiliary.OptionBlock.PumpOption.Pump_Option.Pump,
    Module_2_selector=Workspace.Auxiliary.OptionBlock.Record_ModuleBase.Module_selector.Module_100,
    Module_3_selector=.Workspace.Auxiliary.OptionBlock.Record_ModuleBase.Module_selector.Module_120,SoundOption_selector = Workspace.Auxiliary.OptionBlock.SoundOption.Selector.STANDARD)
    annotation (Placement(transformation(extent={{-30.47216050586008,81.49389933538286},{-11.844064887978284,100.12199495326466}},origin={0.0,0.0},rotation=0.0)));
  parameter.Modelica.SIunits.PressureDifference Pdispo=
    if choiceBlock.is_Pump then
      ECAT.ExternalSystemPressureDrop_Pa.setPoint
    else
      0
    annotation (Dialog(group="Cp_control"));
  .Modelica.SIunits.HeatFlowRate total_capacity=Module_1.Module.controlledCapacity+Module_2.Module.controlledCapacity+Module_3.Module.controlledCapacity+Module_4.Module.controlledCapacity;
  .Modelica.SIunits.Power total_power=Module_1.Module.controlledPower+Module_2.Module.controlledPower+Module_3.Module.controlledPower+Module_4.Module.controlledPower;
  .Modelica.SIunits.PressureDifference InternalPressureDrop_eq=(Module_1.InternalPressureDrop*Inlet_1.Vd+Module_2.InternalPressureDrop*Inlet_2.Vd+Module_3.InternalPressureDrop*Inlet_3.Vd+Module_4.InternalPressureDrop*Inlet_4.Vd)/node_out.Vd
    "Equivalent pressure drop for one unit instead of sevral parallele units.";
  replaceable.Workspace.Controller.SubSystems.PressureControl_cooling pressureControl(
    Av_min=0,
    Av_max=300,
    AV_start=1,
    Pressure_Gain=1/200000,
    Pressure_Setpoint=Pdispo+sourceBrine.p_set,
    LWT_Setpoint=LWT-0.001,
    LWT_Gain=1/(25-3),
    IsOff=not choiceBlock.is_Pump)
    constrainedby.Workspace.Controller.SubSystems.PressureControl_cooling
    annotation (Placement(transformation(extent={{-34.56477487656127,57.9798328584621},{-21.738984341664278,70.80562339335908}},origin={0.0,0.0},rotation=0.0)));
  Real EvapFlowRate=Module_1.Module.EvapFlowRate+Module_2.Module.EvapFlowRate+Module_3.Module.EvapFlowRate+Module_4.Module.EvapFlowRate
    "Condenser flowrate, Busines factor impact included";
  Real Evap_ewt=(Module_1.Module.Evap_ewt*Module_1.Module.EvapFlowRate+Module_2.Module.Evap_ewt*Module_2.Module.EvapFlowRate+Module_3.Module.Evap_ewt*Module_3.Module.EvapFlowRate+Module_4.Module.Evap_ewt*Module_4.Module.EvapFlowRate)/EvapFlowRate
    "Condenser flowrate, Busines factor impact included";
  Real Evap_lwt=(Module_1.Module.Evap_lwt*Module_1.Module.EvapFlowRate+Module_2.Module.Evap_lwt*Module_2.Module.EvapFlowRate+Module_3.Module.Evap_lwt*Module_3.Module.EvapFlowRate+Module_4.Module.Evap_lwt*Module_4.Module.EvapFlowRate)/EvapFlowRate
    "Condenser flowrate, Busines factor impact included";
    parameter Real Max_targetcap = Module_1.Max_targetcap + Module_2.Max_targetcap + Module_3.Max_targetcap + Module_4.Max_targetcap annotation(Dialog(group = "Cp_control"));
equation
  connect(sourceBrine.port,split.port_c)
    annotation (Line(points={{6.118859653839641,-83.29111359694569},{6.087196078092143,-83.29111359694569},{6.087196078092143,-75.27626793053525}},color={0,127,0}));
  connect(split.port_a,split2.port_c)
    annotation (Line(points={{4.678182676191417,-70.00551409379548},{4.678182676191417,-62.527390553411806},{-39.20761171421072,-62.527390553411806},{-39.20761171421072,-58.44508320002004}},color={0,127,0}));
  connect(mixer2.port_c,mixer.port_a)
    annotation (Line(points={{-37.5072945683571,24.96000815651914},{-37.5072945683571,33.393321147257396},{4.056721743440521,33.393321147257396},{4.056721743440521,38.625872595696}},color={0,127,0}));
  connect(split.port_b,split3.port_c)
    annotation (Line(points={{7.391838116889115,-70.00551409379548},{7.391838116889115,-62.83720528728827},{53.66298440031255,-62.83720528728827},{53.66298440031255,-57.03921441023149}},color={0,127,0}));
  connect(mixer3.port_c,mixer.port_b)
    annotation (Line(points={{54.61642952139507,28.264291275895467},{54.61642952139507,33.39332114725741},{7.653808368402396,33.39332114725741},{7.653808368402396,38.625872595696}},color={0,127,0}));
  connect(external_system.port_b,sinkBrine.port)
    annotation (Line(points={{6.2182040030212,68.7799522315085},{6.218204003021202,68.7799522315085},{6.218204003021202,71.85497071862977}},color={0,127,0}));
  connect(node_out.port_a,mixer.port_c)
    annotation (Line(points={{6.2182040030212,51.25561697108451},{5.783323323422223,51.25561697108451},{5.783323323422223,45.96392931061822}},color={0,127,0}));
  connect(external_system.port_a,node_out.port_b)
    annotation (Line(points={{6.2182040030211985,60.38603396256098},{6.2182040030211985,56.56995576379953},{6.218204003021201,56.56995576379953}},color={0,127,0}));
  connect(Inlet_1.port_a,split2.port_a)
    annotation (Line(points={{-59.188360496227475,-29.25925845610281},{-59.188360496227475,-50},{-40.61662511611144,-50},{-40.61662511611144,-53.174329363280286}},color={0,127,0}));
  connect(Module_1.outlet,Outlet_1.port_a)
    annotation (Line(points={{-59.11359661848596,-2.172448072609215},{-59.40943026126568,-2.172448072609215},{-59.40943026126568,3.7549510628913936}},color={0,127,0}));
  connect(Outlet_1.port_b,mixer2.port_a)
    annotation (Line(points={{-59.40943026126568,10.032555635622586},{-59.40943026126568,14.46047343567474},{-38.75975092560219,14.46047343567474},{-38.75975092560219,19.6370686382275}},color={0,127,0}));
  connect(split2.port_b,Inlet_2.port_a)
    annotation (Line(points={{-37.90296967541375,-53.174329363280286},{-37.90296967541375,-50},{-12.960651229281716,-50},{-12.960651229281716,-31.703061615131965}},color={0,127,0}));
  connect(Inlet_2.port_b,Module_2.inlet)
    annotation (Line(points={{-12.960651229281716,-25.272714400700153},{-12.960651229281716,-17.090022953860746},{-12.564948974142768,-17.090022953860746}},color={0,127,0}));
  connect(Module_2.outlet,Outlet_2.port_a)
    annotation (Line(points={{-12.564948974142768,-1.5037478713945163},{-13.285875940002597,-1.5037478713945163},{-13.285875940002597,3.3672525217125333}},color={0,127,0}));
  connect(Outlet_2.port_b,mixer2.port_b)
    annotation (Line(points={{-13.285875940002597,9.828620127316068},{-13.285875940002597,14.283253668611508},{-36.15046684800826,14.283253668611508},{-36.15046684800826,19.6370686382275}},color={0,127,0}));
  connect(Outlet_3.port_a,Module_3.outlet)
    annotation (Line(points={{34.785160078740994,6.486202427639457},{34.785160078740994,0.09639901698858644},{34.22558740725575,0.09639901698858644}},color={0,127,0}));
  connect(Module_3.inlet,Inlet_3.port_b)
    annotation (Line(points={{34.22558740725575,-14.936464314175018},{34.43386111962755,-14.936464314175018},{34.43386111962755,-21.335571512061897}},color={0,127,0}));
  connect(Inlet_3.port_a,split3.port_a)
    annotation (Line(points={{34.43386111962755,-27.537120274479086},{34.43386111962755,-48},{52.25397099841183,-48},{52.25397099841183,-51.768460573491744}},color={0,127,0}));
  connect(split3.port_b,Inlet_4.port_a)
    annotation (Line(points={{54.96762643910952,-51.768460573491744},{54.96762643910952,-48},{74.165159499364,-48},{74.165159499364,-27.660914638988235}},color={0,127,0}));
  connect(Inlet_4.port_b,Module_4.inlet)
    annotation (Line(points={{74.165159499364,-21.75943534574171},{74.165159499364,-15.056490096441127},{74.85331301275752,-15.056490096441127}},color={0,127,0}));
  connect(Module_4.outlet,Outlet_4.port_a)
    annotation (Line(points={{74.85331301275752,0.42622739310464297},{74.60788415229285,0.42622739310464297},{74.60788415229285,6.741412250513152}},color={0,127,0}));
  connect(Outlet_4.port_b,mixer3.port_b)
    annotation (Line(points={{74.60788415229285,11.758049671008509},{74.60788415229285,17.422784944924175},{55.973257241743916,17.422784944924175},{55.973257241743916,22.94135175760384}},color={0,127,0}));
  connect(mixer3.port_a,Outlet_3.port_b)
    annotation (Line(points={{53.36397316414998,22.94135175760384},{53.36397316414998,17.89924275018748},{34.78516007874101,17.89924275018748},{34.78516007874101,12.112477767948883}},color={0,127,0}));
  connect(ControlledCapacity.y,Module_1.capacity_total)
    annotation (Line(points={{-95.32791932388885,-46.83675411606582},{-79.8688796359401,-46.83675411606582},{-79.8688796359401,-9.79811263193858},{-67.50182763374828,-9.79811263193858}},color={0,0,127}));
  connect(ControlledCapacity.y,Module_2.capacity_total)
    annotation (Line(points={{-95.32791932388885,-46.83675411606582},{-24.91047125462217,-46.83675411606582},{-24.91047125462217,-9.296885412627631},{-21.13740026949919,-9.296885412627631}},color={0,0,127}));
  connect(ControlledCapacity.y,Module_3.capacity_total)
    annotation (Line(points={{-95.32791932388885,-46.83675411606582},{22.126285932795064,-46.83675411606582},{22.126285932795064,-7.4200326485932155},{25.957512575115768,-7.4200326485932155}},color={0,0,127}));
  connect(ControlledCapacity.y,Module_4.capacity_total)
    annotation (Line(points={{-95.32791932388885,-46.83675411606582},{60.2920139537787,-46.83675411606582},{60.2920139537787,-7.315131351668242},{66.33781839350733,-7.315131351668242}},color={0,0,127}));
  connect(Inlet_1.port_b,Module_1.inlet)
    annotation (Line(points={{-59.188360496227475,-23.33812840008903},{-59.188360496227475,-17.423777191267945},{-59.11359661848596,-17.423777191267945}},color={0,127,0}));
  connect(Module_1.measurementBus,pressureControl.measurementBus)
    annotation (Line(points={{-66.9326946166055,-2.185251643470261},{-66.9326946166055,68.24046528637969},{-34.56477487656127,68.24046528637969}},color={255,204,51}));
    connect(pressureControl.actuatorSignal,external_system.Ka_in) annotation(Line(points = {{-21.09769481491943,64.3927281259106},{-9.454285790496492,64.3927281259106},{-9.454285790496492,63.07208780862419},{2.1891232339264466,63.07208780862419}},color = {0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          fillColor={229,152,23},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end System_61AQ_Modular;
