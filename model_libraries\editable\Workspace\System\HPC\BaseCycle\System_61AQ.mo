within Workspace.System.HPC.BaseCycle;
model System_61AQ
  import CoolantCommonMedium=BOLT.InternalLibrary.Media.Coolant.CoolantCommon;
  extends.Workspace.Controller.CL_control_system_cooling_zenith(
    controllerSettings_crkA(
      Capacity_setpoint=TargetCapacityA,
      SST_min=274.15,
      fancoefficients=Module.fanCurveCoefficientsCooling[1,:],
      extPressure_setpoint=ECAT.ExternalSystemPressureDrop_Pa.setPoint,
      maxfanfreq=Module.max_fan_frequency[1],
      Manual_ssh=Module.BlockA.node_suction.dTsh_set),
    controllerSettings_crkB(
      Capacity_setpoint=TargetCapacityB,
      SST_min=274.15,
      fancoefficients=Module.fanCurveCoefficientsCooling[2,:],
      extPressure_setpoint=ECAT.ExternalSystemPressureDrop_Pa.setPoint,
      maxfanfreq=Module.max_fan_frequency[2]),
    controller_crkA(
      isOff=isOFFA,
      isOffSDTmin_fan=false,
      isOffSDTmax_fan=false,
      isOffDGTmax_fan=false,
      isOffSSTmin_EXV=false,
      isOffSSTmax_EXV=false,
      isOffDSHmin_EXV=false,
      isOffDGTmax_EXV=false,
      isOffSSTmin_comp=false,
      isOffSDTmax_comp=false,
      isOffDGTmax_comp=false,
      Fan_MaxFrequency=controllerSettings_crkA.maxfanfreq,
      completeCompressorControl_base(
        compressorControl(
          manualOff=controller_crkA.manualOff_compressor_block_A,
          AV_value_off=controller_crkA.frq_comp_sp_manual_block_A)),
      fanControl(
        manualOff=controller_crkA.manualOff_fan_block_A,
        AV_value_off=controller_crkA.frq_fan_sp_manual_block_A),
      manualOff_fan_block_A=false,
      manualOff_fan_block_B=false,
      manualOff_compressor_block_A=false,
      manualOff_compressor_block_B=false,
      Fan_MinFrequency=controllerSettings_crkA.minfanfreq,
      min_speed=Module.fmin[1],
      max_speed=Module.fmax[1],
      load_ratio=ECAT.LoadRatio_nd.setPoint,
      is_load_ratio=ECAT.LoadRatio_nd.fixed),
    controller_crkB(
      isOff=Module.is_monobloc or isOFFB,
      isOffSDTmin_fan=false,
      isOffSDTmax_fan=false,
      isOffDGTmax_fan=false,
      isOffSSTmin_EXV=false,
      isOffSSTmax_EXV=false,
      isOffDSHmin_EXV=false,
      isOffDGTmax_EXV=false,
      isOffSSTmin_comp=false,
      isOffSDTmax_comp=false,
      isOffDGTmax_comp=false,
      Fan_MaxFrequency=controllerSettings_crkB.maxfanfreq,
      fanControl(
        manualOff=controller_crkB.manualOff_fan_block_B,
        AV_value_off=controller_crkB.frq_fan_sp_manual_block_B),
      completeCompressorControl_base(
        compressorControl(
          manualOff=controller_crkB.manualOff_compressor_block_B,
          AV_value_off=controller_crkB.frq_comp_sp_manual_block_B)),
      Fan_MinFrequency=controllerSettings_crkB.minfanfreq,
      min_speed=Module.fmin[2],
      max_speed=Module.fmax[2],
      load_ratio=ECAT.LoadRatio_nd.setPoint,
      is_load_ratio=ECAT.LoadRatio_nd.fixed));
  .Workspace.System.HPC.BaseCycle.Equipement Module(
    CoolantMedium=CoolantMedium,
    isOFFB=isOFFB,
    isOFFA=isOFFA,
    OAT=OAT,
    LWT=LWT,
    EWT=EWT,
    Use_pump=choiceBlock.is_Pump,
    capacity_design={choiceBlock.Unit_Block_A.capacity_design,choiceBlock.Unit_Block_B.capacity_design},
    fmax={choiceBlock.Unit_Block_A.fmax,choiceBlock.Unit_Block_B.fmax},
    fmin={choiceBlock.Unit_Block_A.fmin,choiceBlock.Unit_Block_B.fmin},
    Dport_coolant_a={choiceBlock.Unit_Block_A.cond_diameter_brine_port_in,choiceBlock.Unit_Block_B.cond_diameter_brine_port_in},
    Dport_coolant_b={choiceBlock.Unit_Block_A.cond_diameter_brine_port_out,choiceBlock.Unit_Block_B.cond_diameter_brine_port_out},
    Dport_ref_a={choiceBlock.Unit_Block_A.cond_diameter_ref_port_in,choiceBlock.Unit_Block_B.cond_diameter_ref_port_in},
    Dport_ref_b={choiceBlock.Unit_Block_A.cond_diameter_ref_port_out,choiceBlock.Unit_Block_B.cond_diameter_ref_port_out},
    nPlate={choiceBlock.Unit_Block_A.nplate,choiceBlock.Unit_Block_B.nplate},
    nCoils={choiceBlock.Unit_Block_A.nCoils,choiceBlock.Unit_Block_B.nCoils},
    Itube={choiceBlock.Unit_Block_A.Itube,choiceBlock.Unit_Block_B.Itube},
    nCir={choiceBlock.Unit_Block_A.nCir,choiceBlock.Unit_Block_B.nCir},
    Ntube={choiceBlock.Unit_Block_A.Ntube,choiceBlock.Unit_Block_B.Ntube},
    Nrow={choiceBlock.Unit_Block_A.Nrow,choiceBlock.Unit_Block_B.Nrow},
    Ltube={choiceBlock.Unit_Block_A.Ltube,choiceBlock.Unit_Block_B.Ltube},
    Dotube={choiceBlock.Unit_Block_A.Dotube,choiceBlock.Unit_Block_B.Dotube},
    Ttube={choiceBlock.Unit_Block_A.Ttube,choiceBlock.Unit_Block_B.Ttube},
    Ptube={choiceBlock.Unit_Block_A.Ptube,choiceBlock.Unit_Block_B.Ptube},
    Prow={choiceBlock.Unit_Block_A.Prow,choiceBlock.Unit_Block_B.Prow},
    Dfin={choiceBlock.Unit_Block_A.Dfin,choiceBlock.Unit_Block_B.Dfin},
    Tfin={choiceBlock.Unit_Block_A.Tfin,choiceBlock.Unit_Block_B.Tfin},
    Fw_fan={choiceBlock.Unit_Block_A.Fan_FW,choiceBlock.Unit_Block_B.Fan_FW},
    max_fan_frequency={choiceBlock.Unit_Block_A.max_fan_frequency,choiceBlock.Unit_Block_B.max_fan_frequency},
    Suction_line_diameter={choiceBlock.Unit_Block_A.Suction_line_diameter,choiceBlock.Unit_Block_B.Suction_line_diameter},
    Suction_line_length={choiceBlock.Unit_Block_A.Suction_line_length,choiceBlock.Unit_Block_B.Suction_line_length},
    coil_line_diameter={choiceBlock.Unit_Block_A.Coil_line_diameter,choiceBlock.Unit_Block_B.Coil_line_diameter},
    coil_line_length={choiceBlock.Unit_Block_A.Coil_line_length,choiceBlock.Unit_Block_B.Coil_line_length},
    liquid_line_diameter={choiceBlock.Unit_Block_A.Liquid_line_diameter,choiceBlock.Unit_Block_B.Liquid_line_diameter},
    liquid_line_length={choiceBlock.Unit_Block_A.Liquid_line_length,choiceBlock.Unit_Block_B.Liquid_line_length},
    discharge_line_diameter={choiceBlock.Unit_Block_A.discharge_line_diameter,choiceBlock.Unit_Block_B.discharge_line_diameter},
    discharge_line_length={choiceBlock.Unit_Block_A.discharge_line_length,choiceBlock.Unit_Block_B.discharge_line_length},
    EXV_in_line_diameter={choiceBlock.Unit_Block_A.EXV_in_line_diameter,choiceBlock.Unit_Block_B.EXV_in_line_diameter},
    EXV_in_line_length={choiceBlock.Unit_Block_A.EXV_in_line_length,choiceBlock.Unit_Block_B.EXV_in_line_length},
    Ac_duct={choiceBlock.Unit_Block_A.duct_Ac,choiceBlock.Unit_Block_B.duct_Ac},
    Ka_duct={choiceBlock.Unit_Block_A.duct_Ka,choiceBlock.Unit_Block_B.duct_Ka},
    UA_duct={choiceBlock.Unit_Block_A.duct_UA,choiceBlock.Unit_Block_B.duct_UA},
    selector_Comp={choiceBlock.Unit_Block_A.selector_Comp,choiceBlock.Unit_Block_B.selector_Comp},
    EXV_main={choiceBlock.Unit_Block_A.EXV_main_A,choiceBlock.Unit_Block_B.EXV_main_A},
    selector_geo_BPHE={choiceBlock.Unit_Block_A.cond_select_geo,choiceBlock.Unit_Block_B.cond_select_geo},
    selector_pump={choiceBlock.Unit_Block_A.Pump_type,choiceBlock.Unit_Block_B.Pump_type},
    fanCurveCoefficientsHeating={choiceBlock.Unit_Block_A.fanCurveCoefficientsHeating,choiceBlock.Unit_Block_B.fanCurveCoefficientsHeating},
    CompVoltage={choiceBlock.Unit_Block_A.CompVoltage,choiceBlock.Unit_Block_B.CompVoltage},
    is_monobloc=choiceBlock.is_monobloc,
    fanCurveCoefficientsCooling={choiceBlock.Unit_Block_A.fanCurveCoefficientsCooling,choiceBlock.Unit_Block_B.fanCurveCoefficientsCooling},
    Use_EN=Use_EN14511,
    use_bf=use_bf,
    BrineConcentration=BrineConcentration,
    EvapFoulingFactor=ECAT.EvapFoulingFactor_m2KW.setPoint,
    mdot_start=Module.capacity_design[1]/(((4180*(Module.EWT-Module.LWT)))),
    isCoating=choiceBlock.isCoatingOption,
    relative_humidity=ECAT.AmbientAirRH_nd.setPoint,
    PDC_4WV={choiceBlock.Unit_Block_A.PDC_4WV,choiceBlock.Unit_Block_B.PDC_4WV},
    Zflow_intercept={choiceBlock.Unit_Block_A.Zflow_intercept,choiceBlock.Unit_Block_B.Zflow_intercept},
    Zflow_Ncomp={choiceBlock.Unit_Block_A.Zflow_Ncomp,choiceBlock.Unit_Block_B.Zflow_Ncomp},
    Zflow_SST={choiceBlock.Unit_Block_A.Zflow_SST,choiceBlock.Unit_Block_B.Zflow_SST},
    Zflow_SDT={choiceBlock.Unit_Block_A.Zflow_SDT,choiceBlock.Unit_Block_B.Zflow_SDT},
    Zflow_heatcap={choiceBlock.Unit_Block_A.Zflow_Heatcap,choiceBlock.Unit_Block_B.Zflow_Heatcap},
    Zpower_intercept={choiceBlock.Unit_Block_A.Zpower_intercept,choiceBlock.Unit_Block_B.Zpower_intercept},
    Zpower_SH={choiceBlock.Unit_Block_A.Zpower_SH,choiceBlock.Unit_Block_B.Zpower_SH},
    Zpower_DGT={choiceBlock.Unit_Block_A.Zpower_DGT,choiceBlock.Unit_Block_B.Zpower_DGT},
    Zpower_SST={choiceBlock.Unit_Block_A.Zpower_SST,choiceBlock.Unit_Block_B.Zpower_SST},
    Zpower_Zflow={choiceBlock.Unit_Block_A.Zpower_Zflow,choiceBlock.Unit_Block_B.Zpower_Zflow},
    FW={choiceBlock.Unit_Block_A.FW,choiceBlock.Unit_Block_B.FW},
    use_Calib=use_Calib,
    Zevap_HPH_cst={choiceBlock.Unit_Block_A.Zevap_chaud_intercept,choiceBlock.Unit_Block_B.Zevap_chaud_intercept},
    Zevap_HPH_SST={choiceBlock.Unit_Block_A.Zevap_chaud_SST,choiceBlock.Unit_Block_B.Zevap_chaud_SST},
    Zevap_HPH_Ncomp={choiceBlock.Unit_Block_A.Zevap_chaud_Ncomp,choiceBlock.Unit_Block_B.Zevap_chaud_Ncomp},
    Zevap_HPH_heatcap={choiceBlock.Unit_Block_A.Zevap_chaud_heatcap,choiceBlock.Unit_Block_B.Zevap_chaud_heatcap},
    Zevap_HPC_cst={choiceBlock.Unit_Block_A.Zevap_froid_intercept,choiceBlock.Unit_Block_B.Zevap_froid_intercept},
    Zevap_HPC_heatcap={choiceBlock.Unit_Block_A.Zevap_froid_heatcap,choiceBlock.Unit_Block_B.Zevap_froid_heatcap},
    Zevap_HPC_SST={choiceBlock.Unit_Block_A.Zevap_froid_SST,choiceBlock.Unit_Block_B.Zevap_froid_SST},
    Zcond_HPH_cst={choiceBlock.Unit_Block_A.Zcond_chaud_intercept,choiceBlock.Unit_Block_B.Zcond_chaud_intercept},
    Zcond_HPH_heatcap={choiceBlock.Unit_Block_A.Zcond_chaud_Heatcap,choiceBlock.Unit_Block_B.Zcond_chaud_Heatcap},
    Zcond_HPH_SST={choiceBlock.Unit_Block_A.Zcond_chaud_SST,choiceBlock.Unit_Block_B.Zcond_chaud_SST},
    Zcond_HPC_cst={choiceBlock.Unit_Block_A.Zcond_froid_intercept,choiceBlock.Unit_Block_B.Zcond_froid_intercept},
    Zcond_HPC_DGT={choiceBlock.Unit_Block_A.Zcond_froid_DGT,choiceBlock.Unit_Block_B.Zcond_froid_DGT},
    Zcond_HPC_SDT={choiceBlock.Unit_Block_A.Zcond_froid_SDT,choiceBlock.Unit_Block_B.Zcond_froid_SDT},
    Zcond_HPC_Ncomp={choiceBlock.Unit_Block_A.Zcond_froid_Ncomp,choiceBlock.Unit_Block_B.Zcond_froid_Ncomp},
    Zcond_HPH_SDT={choiceBlock.Unit_Block_A.Zcond_chaud_SDT,choiceBlock.Unit_Block_B.Zcond_chaud_SDT},
    Mref_fixed={ECAT.RefrigerantCharge_kg[1].fixed,ECAT.RefrigerantCharge_kg[2].fixed},
    SC_fixed={not ECAT.RefrigerantCharge_kg[1].fixed,not ECAT.RefrigerantCharge_kg[2].fixed},
    Mref={ECAT.RefrigerantCharge_kg[1].setPoint,ECAT.RefrigerantCharge_kg[2].setPoint},
    M_ref={choiceBlock.Unit_Block_A.Design_mRef_set_A,choiceBlock.Unit_Block_B.Design_mRef_set_A})
    annotation (Placement(transformation(extent={{-23.999999999999993,8.000000000000014},{-3.999999999999993,28.000000000000014}},origin={0.0,0.0},rotation=0.0)));
  parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector CoolantMedium=ECAT.EvapBrineType_nd
    "Coolant Medium Selection"
    annotation (Dialog(group="Medium"));
  final parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Empty coolantInf=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.getSelector(
    CoolantMedium)
    "Coolant Medium"
    annotation ();
  CoolantCommonMedium.Temperature FreezTemp=CoolantCommonMedium.freezeTemp(
    coolantInf,
    BrineConcentration,
    1);
  parameter Real BrineConcentration=ECAT.EvapBrineConcentration_nd.setPoint
    annotation (Dialog(group="Medium"));
  .BOLT.BoundaryNode.Coolant.Source sourceBrine(
    Vd_fixed=ECAT.EvapBrineFlowRate_m3s.fixed,
    T_set=EWT,
    p_fixed=true,
    CoolantMedium=CoolantMedium,
    T_fixed=ECAT.EvapBrineEWT_K.fixed,
    Vd_set=ECAT.EvapBrineFlowRate_m3s.setPoint,
    X=BrineConcentration)
    annotation (Placement(transformation(extent={{-6.546045643035146,-6.546045643035143},{6.546045643035146,6.546045643035143}},origin={-11.40601200562805,-34.58656321958037},rotation=90.0)));
  .BOLT.BoundaryNode.Coolant.Sink sinkBrine(
    p_fixed=true,
    T_fixed=ECAT.EvapBrineLWT_K.fixed,
    T_set=LWT,
    CoolantMedium=CoolantMedium,
    p_set=sourceBrine.p_set,
    X=BrineConcentration)
    annotation (Placement(transformation(extent={{-5.468581959242896,-5.468581959242897},{5.468581959242896,5.468581959242897}},origin={-11.208421744176526,58.26802167641249},rotation=-90.0)));
  parameter.Modelica.SIunits.Temperature LWT=ECAT.EvapBrineLWT_K.setPoint
    annotation (Dialog(group="Conditions"));
  parameter.Modelica.SIunits.Temperature EWT=ECAT.EvapBrineEWT_K.setPoint
    annotation (Dialog(group="Conditions"));
  parameter.Modelica.SIunits.Temperature OAT=ECAT.AmbientAirDBTemp_K.setPoint
    annotation (Dialog(group="Conditions"));
  parameter.Modelica.SIunits.Power TargetCapacityA=
    if Module.is_monobloc then
      ECAT.TargetCoolingCapacity_W.setPoint
    else
      ECAT.TargetCoolingCapacity_W.setPoint/2
    annotation (Dialog(group="Cp_control"));
  parameter.Modelica.SIunits.Power TargetCapacityB=ECAT.TargetCoolingCapacity_W.setPoint/2
    annotation (Dialog(group="Cp_control"));
  parameter Boolean isOFFB=false
    annotation (Dialog(group="Cp_control"));
  parameter Boolean isOFFA=false
    annotation (Dialog(group="Cp_control"));
  parameter Boolean use_bf=true
    annotation (Dialog(group="Use Parameters"));
  parameter Boolean Use_EN14511=true
    annotation (Dialog(group="Use Parameters"));
  parameter Boolean use_Calib=true
    annotation (Dialog(group="Use Parameters"));
  .Workspace.Auxiliary.ECAT_Zenith.ECATBase ECAT(
    EvapPumpPower_W(
      value=Module.pumpPolyA.summary.P_motor),
    nbrCircuit=2,
    CondCoilAirPressDrop_Pa(
      value={Module.BlockA.condAir.summary.dPa,Module.BlockB.condAir.summary.dPa}),
    RefrigerantSST_K(
      value={Module.BlockA.node_suction.Tsat,Module.BlockB.node_suction.Tsat}),
    RefrigerantSDT_K(
      value={Module.BlockA.node_discharge.Tsat,Module.BlockB.node_discharge.Tsat}),
    RefrigerantSET_K(
      value={Module.BlockA.node_evapout.Tsat,Module.BlockB.node_evapout.Tsat}),
    RefrigerantSCT_K(
      value={Module.BlockA.node_condin.Tsat,Module.BlockB.node_condin.Tsat}),
    RefrigerantDGT_K(
      value={Module.BlockA.node_discharge.T,Module.BlockB.node_discharge.T}),
    SuctionSuperheat_K(
      value={Module.BlockA.node_suction.dTsh,Module.BlockB.node_suction.dTsh}),
    CondSubcooling_K(
      value={Module.BlockA.node_liquid.dTsh,Module.BlockB.node_liquid.dTsh}),
    DischargeSuperheat_K(
      value={Module.BlockA.node_discharge.dTsh,Module.BlockB.node_discharge.dTsh}),
    CondFanAirflowRate_m3s(
      value={2*Module.BlockA.sourceAir.Vd_flow,2*Module.BlockB.sourceAir.Vd_flow}),
    CompressorFrequency_Hz(
      value={Module.BlockA.compressor.summary.Ncomp,Module.BlockB.compressor.summary.Ncomp},
      setPoint={Module.BlockA.compressor.summary.Ncomp,Module.BlockB.compressor.summary.Ncomp}),
    CompressorSpeed_rpm(
      value={Module.BlockA.compressor.summary.Ncomp*60,Module.BlockB.compressor.summary.Ncomp*60}),
    CompressorPower_W(
      value={Module.BlockA.compressor.summary.P_compression,Module.BlockB.compressor.summary.P_compression}),
    FanPower_W(
      value={Module.BlockA.motor.summary.power_VFD,Module.BlockB.motor.summary.power_VFD}),
    AmbientAirDBTemp_K(
      setPoint=298.15),
    EvapBrineConcentration_nd(
      setPoint=0.4),
    EvapFoulingFactor_m2KW(
      setPoint=0),
    EvapBrineLWT_K(
      value=sinkBrine.summary.T,
      setPoint=280.15),
    EvapBrineEWT_K(
      value=sourceBrine.summary.T,
      setPoint=285.15),
    EvapBrineFlowRate_m3s(
      value=sourceBrine.summary.Vd),
    TargetCoolingCapacity_W(
      setPoint=160000),
    TotalRefrigerantCharge_kg(
      value=choiceBlock.Unit_Block_A.Design_mRef_set_A+choiceBlock.Unit_Block_B.Design_mRef_set_A),
    TotalOilCharge_kg(
      value=choiceBlock.Unit_Block_A.Oil_charge+choiceBlock.Unit_Block_B.Oil_charge),
    TotalCompressorPower_W(
      value=sum(
        ECAT.CompressorPower_W.value)),
    TotalFanPower_W(
      value=sum(
        ECAT.FanPower_W.value)),
    EvapBrineIntPressDrop_Pa(
      value=Module.BlockA.evapBPHE.summary.dp_coolant),
    EvapBrineDensity_kgm3(
      value=1/sourceBrine.summary.v),
    PubCoolingCapacity_W(
      value=Module.controlledCapacity),
    PubUnitPower_W(
      value=Module.controlledPower),
    AmbientAirRH_nd(
      setPoint=0.87),
    RefrigerantCharge_kg(
      fixed={true,true},
      value={Module.BlockA.systemVariables.mRef[1],Module.BlockB.systemVariables.mRef[1]},
      setPoint={Module.M_ref[1],Module.M_ref[2]}),
    LoadRatio_nd(
      value=controller_crkA.completeCompressorControl_base.capacity_controller.summary.AV*100,
      fixed=false),
    FanFrequency_Hz(
      value={Module.BlockA.motor.summary.Motor_freq,Module.BlockB.motor.summary.Motor_freq}),
    FanSpeed_rpm(
      value={Module.BlockA.fanCurve.summary.speed,Module.BlockB.fanCurve.summary.speed}),
    ElecFanBoxFrequency_Hz(
      value={50,50}),
    EvapPumpSpeed_Hz(
      value={Module.pumpPolyA.pump.speed_Hz,Module.pumpPolyB.pump.speed_Hz}),
    EvapPumpSpeed_rpm(
      value={Module.pumpPolyA.summary.speed,Module.pumpPolyB.summary.speed}))
    annotation (Placement(transformation(extent={{-63.2370999927898,67.79692854727604},{-41.72887146714432,89.30515707292152}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Auxiliary.OptionBlock.ChoiceBlock choiceBlock(
    is_monobloc=false,
    Selector_Block_A=Workspace.Auxiliary.OptionBlock.Record_Base.Selector.Unit_Z_070,
    Selector_Block_B=Workspace.Auxiliary.OptionBlock.Record_Base.Selector.Unit_Z_070)
    annotation (Placement(transformation(extent={{-89.33120367479577,68.33954495951075},{-68.56256138857773,89.10818724572879}},origin={0.0,0.0},rotation=0.0)));
equation
  connect(Module.Compressor_controller_A,controller_crkA.compressor)
    annotation (Line(points={{-24.999999999999993,10.500000000000014},{-35.99161292982694,10.500000000000014},{-35.99161292982694,11.142786159859877},{-49.910042394053335,11.142786159859877}},color={0,0,127}));
  connect(controller_crkA.exv,Module.EXV_controller_A)
    annotation (Line(points={{-49.910042394053335,8.642786159859877},{-35.99161292982694,8.642786159859877},{-35.99161292982694,20.500000000000014},{-24.999999999999993,20.500000000000014}},color={0,0,127}));
  connect(controller_crkA.fan,Module.Fan_controller_A)
    annotation (Line(points={{-49.910042394053335,6.142786159859877},{-35.99161292982694,6.142786159859877},{-35.99161292982694,15.500000000000014},{-24.999999999999993,15.500000000000014}},color={0,0,127}));
  connect(controller_crkA.pump,Module.ActuatorPumpUser_A)
    annotation (Line(points={{-49.910042394053335,3.6427861598598774},{-35.99161292982694,3.6427861598598774},{-35.99161292982694,25.000000000000014},{-24.999999999999993,25.000000000000014}},color={0,0,127}));
  connect(controller_crkB.pump,Module.ActuatorPumpUser_B)
    annotation (Line(points={{20.877596756835317,4.266347038854235},{10.40220664561738,4.266347038854235},{10.40220664561738,25.000000000000014},{-2.999999999999993,25.000000000000014}},color={0,0,127}));
  connect(Module.Fan_controller_B,controller_crkB.fan)
    annotation (Line(points={{-2.999999999999993,15.500000000000014},{10.40220664561738,15.500000000000014},{10.40220664561738,6.766347038854235},{20.877596756835317,6.766347038854235}},color={0,0,127}));
  connect(Module.EXV_controller_B,controller_crkB.exv)
    annotation (Line(points={{-2.999999999999993,20.500000000000014},{10.40220664561738,20.500000000000014},{10.40220664561738,9.266347038854235},{20.877596756835317,9.266347038854235}},color={0,0,127}));
  connect(Module.Compressor_controller_B,controller_crkB.compressor)
    annotation (Line(points={{-2.999999999999993,10.500000000000014},{10.40220664561738,10.500000000000014},{10.40220664561738,11.766347038854235},{20.877596756835317,11.766347038854235}},color={0,0,127}));
  connect(controllerSettings_crkA.measurementBus,Module.measurementBusA)
    annotation (Line(points={{-81.48150413305495,40.82205673936891},{-21.999999999999993,40.82205673936891},{-21.999999999999993,28.000000000000014}},color={255,204,51}));
  connect(sourceBrine.port,Module.coolant_in)
    annotation (Line(points={{-11.406012005628048,-28.040517576545223},{-11.406012005628048,3.329405357454643},{-14.348008987101448,3.329405357454643}},color={0,127,0}));
  connect(Module.coolant_out,sinkBrine.port)
    annotation (Line(points={{-14.260039156443822,30.406009079921084},{-11.208421744176524,30.406009079921084},{-11.208421744176524,52.79943971716959}},color={0,127,0}));
  connect(Module.measurementBusB,controllerSettings_crkB.measurementBus)
    annotation (Line(points={{-5.999999999999993,28.000000000000014},{-5.999999999999993,40.99059642133661},{49.78236321898647,40.99059642133661}},color={255,204,51}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          fillColor={229,152,23},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end System_61AQ;
