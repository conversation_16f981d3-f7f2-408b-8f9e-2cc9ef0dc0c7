within Workspace.System.Multimodule.HPC;
model Equipement_Modular
  .Workspace.System.HPC.BaseCycle.OL_FW092 BlockA(
    EWT=EWT,
    LWT=LWT,
    OAT=OAT,
    Water_pressure=Water_pressure,
    isOff_ref=isOFF,
    CoolantMedium=CoolantMedium,
    BrineConcentration=BrineConcentration,
    use_Z_in=false,
    selector_Comp=selector_Comp[1],
    capacity_design=capacity_design[1],
    CompVoltage=CompVoltage[1],
    fmax=fmax[1],
    fmin=fmin[1],
    EXV_main=EXV_main[1],
    Dport_coolant_a=Dport_coolant_a[1],
    Dport_coolant_b=Dport_coolant_b[1],
    Dport_ref_a=Dport_ref_a[1],
    Dport_ref_b=Dport_ref_b[1],
    nPlate=nPlate[1],
    selector_geo_BPHE=selector_geo_BPHE[1],
    nCoils=nCoils[1],
    Itube=Itube[1],
    nCir=nCir[1],
    Ntube=Ntube[1],
    Nrow=Nrow[1],
    Ltube=Ltube[1],
    Dotube=Dotube[1],
    Ttube=Ttube[1],
    Ptube=Ptube[1],
    Prow=Prow[1],
    Dfin=Dfin[1],
    Tfin=Tfin[1],
    Fw_fan=Fw_fan[1],
    max_fan_frequency=max_fan_frequency[1],
    fanCurveCoefficientsCooling=fanCurveCoefficientsCooling[1,:],
    fanCurveCoefficientsHeating=fanCurveCoefficientsHeating[1,:],
    Suction_line_diameter=Suction_line_diameter[1],
    Suction_line_length=Suction_line_length[1],
    coil_line_length=coil_line_length[1],
    liquid_line_diameter=liquid_line_diameter[1],
    liquid_line_length=liquid_line_length[1],
    discharge_line_diameter=discharge_line_diameter[1],
    discharge_line_length=discharge_line_length[1],
    EXV_in_line_diameter=EXV_in_line_diameter[1],
    EXV_in_line_length=EXV_in_line_length[1],
    Ac_duct=Ac_duct[1],
    Ka_duct=Ka_duct[1],
    UA_duct=UA_duct[1],
    coil_line_diameter=coil_line_diameter[1],
    selector_pump=selector_pump[1],
    EvapFoulingFactor=EvapFoulingFactor,
    isCoating=isCoating,
    relative_humidity=relative_humidity,
    PDC_4WV=PDC_4WV[1],
    Zflow_heatcap=Zflow_heatcap[1],
    Zflow_Ncomp=Zflow_Ncomp[1],
    Zpower_DGT=Zpower_DGT[1],
    Zcond_HPH_cst=Zcond_HPH_cst[1],
    Zcond_HPH_heatcap=Zcond_HPH_heatcap[1],
    Zcond_HPH_SST=Zcond_HPH_SST[1],
    Zcond_HPC_SDT=Zcond_HPC_SDT[1],
    Zcond_HPC_cst=Zcond_HPC_cst[1],
    Zevap_HPH_cst=Zevap_HPH_cst[1],
    Zevap_HPH_SST=Zevap_HPH_SST[1],
    Zevap_HPC_cst=Zevap_HPC_cst[1],
    Zevap_HPC_SST=Zevap_HPC_SST[1],
    Zevap_coated_HPH=Zevap_coated_HPH[1],
    Zflow_intercept=Zflow_intercept[1],
    Zflow_SST=Zflow_SST[1],
    Zflow_SST2=Zflow_SST2[1],
    Zflow_SST3=Zflow_SST3[1],
    Zflow_SDT=Zflow_SDT[1],
    Zpower_intercept=Zpower_intercept[1],
    Zpower_SST=Zpower_SST[1],
    Zpower_SST2=Zpower_SST2[1],
    Zcond_HPH_SDT=Zcond_HPH_SDT[1],
    FW=FW[1],
    ssh_setPoint=ssh_setPoint[1],
    SC_setpoint=SC_setpoint[1],
    SC_fixed=SC_fixed[1],
    Mref_fixed=Mref_fixed[1],
    Mref=M_ref[1],
    use_Calib=use_Calib,
    isOff=isOFF,
    Zevap_HPC_Coolcap=Zevap_froid_Coolcap[1],
    Zevap_HPH_Coolcap=Zevap_chaud_Coolcap[1],
    Zpower_SDT=Zpower_SDT[1],
    Zpower_Ncomp=Zpower_Ncomp[1],
    Zpower_heatcap=Zpower_heatcap[1],
    Zcond_HPC_coated=Zcond_HPC_coated[1],Altitude = Altitude,Zevap_HPH_min = Zevap_HPH_min[1],Zevap_HPH_max = Zevap_HPH_max[1],Zevap_HPC_min = Zevap_HPC_min[1],Zevap_HPC_max = Zevap_HPC_max[1],Zcond_HPH_min = Zcond_HPH_min[1],Zcond_HPH_max = Zcond_HPH_max[1],Zcond_HPC_min = Zcond_HPC_min[1],Zcond_HPC_max = Zcond_HPC_max[1],Zflow_min = Zflow_min[1],Zflow_max = Zflow_max[1],Zpower_min = Zpower_min[1],Zpower_max = Zpower_max[1])
    annotation (Placement(transformation(extent={{-54.51983879885063,44.84558962403017},{-34.51983879885063,64.84558962403017}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.System.HPC.BaseCycle.OL_FW092 BlockB(
    isOff=is_monobloc or isOFF,
    EWT=EWT,
    LWT=LWT,
    OAT=OAT,
    Water_pressure=Water_pressure,
    isOff_ref=is_monobloc or isOFF or isOff_ref,
    CoolantMedium=CoolantMedium,
    BrineConcentration=BrineConcentration,
    use_Z_in=false,
    selector_Comp=selector_Comp[2],
    capacity_design=capacity_design[2],
    CompVoltage=CompVoltage[2],
    fmax=fmax[2],
    fmin=fmin[2],
    EXV_main=EXV_main[2],
    Dport_coolant_a=Dport_coolant_a[2],
    Dport_coolant_b=Dport_coolant_b[2],
    Dport_ref_a=Dport_ref_a[2],
    Dport_ref_b=Dport_ref_b[2],
    nPlate=nPlate[2],
    selector_geo_BPHE=selector_geo_BPHE[2],
    nCoils=nCoils[2],
    Itube=Itube[2],
    nCir=nCir[2],
    Ntube=Ntube[2],
    Nrow=Nrow[2],
    Ltube=Ltube[2],
    Dotube=Dotube[2],
    Ttube=Ttube[2],
    Ptube=Ptube[2],
    Prow=Prow[2],
    Dfin=Dfin[2],
    Tfin=Tfin[2],
    Fw_fan=Fw_fan[2],
    max_fan_frequency=max_fan_frequency[2],
    fanCurveCoefficientsCooling=fanCurveCoefficientsCooling[2,:],
    fanCurveCoefficientsHeating=fanCurveCoefficientsHeating[2,:],
    Suction_line_diameter=Suction_line_diameter[2],
    Suction_line_length=Suction_line_length[2],
    coil_line_diameter=coil_line_diameter[2],
    coil_line_length=coil_line_length[2],
    liquid_line_diameter=liquid_line_diameter[2],
    liquid_line_length=liquid_line_length[2],
    discharge_line_diameter=discharge_line_diameter[2],
    discharge_line_length=discharge_line_length[2],
    EXV_in_line_diameter=EXV_in_line_diameter[2],
    EXV_in_line_length=EXV_in_line_length[2],
    Ac_duct=Ac_duct[2],
    Ka_duct=Ka_duct[2],
    UA_duct=UA_duct[2],
    selector_pump=selector_pump[2],
    EvapFoulingFactor=EvapFoulingFactor,
    isCoating=isCoating,
    relative_humidity=relative_humidity,
    PDC_4WV=PDC_4WV[2],
    Zflow_heatcap=Zflow_heatcap[2],
    Zflow_Ncomp=Zflow_Ncomp[2],
    Zpower_DGT=Zpower_DGT[2],
    Zcond_HPH_cst=Zcond_HPH_cst[2],
    Zcond_HPH_heatcap=Zcond_HPH_heatcap[2],
    Zcond_HPH_SST=Zcond_HPH_SST[2],
    Zcond_HPC_SDT=Zcond_HPC_SDT[2],
    Zcond_HPC_cst=Zcond_HPC_cst[2],
    Zevap_HPH_cst=Zevap_HPH_cst[2],
    Zevap_HPH_SST=Zevap_HPH_SST[2],
    Zevap_HPC_cst=Zevap_HPC_cst[2],
    Zevap_HPC_SST=Zevap_HPC_SST[2],
    Zevap_coated_HPH=Zevap_coated_HPH[2],
    Zflow_intercept=Zflow_intercept[2],
    Zflow_SST=Zflow_SST[2],
    Zflow_SST2=Zflow_SST2[2],
    Zflow_SST3=Zflow_SST3[2],
    Zflow_SDT=Zflow_SDT[2],
    Zpower_intercept=Zpower_intercept[2],
    Zpower_SST=Zpower_SST[2],
    Zpower_SST2=Zpower_SST2[2],
    Zcond_HPH_SDT=Zcond_HPH_SDT[2],
    FW=FW[2],
    ssh_setPoint=ssh_setPoint[2],
    SC_setpoint=SC_setpoint[2],
    SC_fixed=SC_fixed[2],
    Mref_fixed=Mref_fixed[2],
    Mref=M_ref[2],
    use_Calib=use_Calib,
    Zevap_HPH_Coolcap=Zevap_chaud_Coolcap[2],
    Zevap_HPC_Coolcap=Zevap_froid_Coolcap[2],
    Zpower_SDT=Zpower_SDT[2],
    Zpower_Ncomp=Zpower_Ncomp[2],
    Zpower_heatcap=Zpower_heatcap[2],
    Zcond_HPC_coated=Zcond_HPC_coated[2],Altitude = Altitude,
    Zevap_HPH_min = Zevap_HPH_min[2],Zevap_HPH_max = Zevap_HPH_max[2],Zevap_HPC_min = Zevap_HPC_min[2],Zevap_HPC_max = Zevap_HPC_max[2],Zcond_HPH_min = Zcond_HPH_min[2],Zcond_HPH_max = Zcond_HPH_max[2],Zcond_HPC_min = Zcond_HPC_min[2],Zcond_HPC_max = Zcond_HPC_max[2],Zflow_min = Zflow_min[2],Zflow_max = Zflow_max[2],Zpower_min = Zpower_min[2],Zpower_max = Zpower_max[2])
    annotation (Placement(transformation(extent={{52.51983879885067,42.971316805432906},{32.51983879885067,62.971316805432906}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.Mixer mixer(
    fa_fixed=false,
    isOff_b=is_monobloc or isOFF,
    T_start=LWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff_a=isOFF,
    fa_set=
      if is_monobloc then
        1
      else
        0.5)
    annotation (Placement(transformation(extent={{-10.0,-10.0},{10.0,10.0}},origin={-3.120230363289018,67.09976839691211},rotation=90.0)));
  parameter Boolean Use_pump=false;
  parameter Boolean Use_fake_pump=false;
  parameter Boolean use_Calib=false;
  parameter Real Water_pressure=200000;
  .BOLT.CoolantMisc.Split split_pumpA(
    mDot_a_start=
      if Use_fake_pump then
        mdot_start
      else
        0,
    mDot_b_start=
      if not Use_fake_pump then
        mdot_start
      else
        0,
    p_start=Water_pressure,
    T_start=EWT,
    isOff_b=not FakePumpA.isOff or isOFF,
    isOff_a=not Use_fake_pump or isOFF,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    fa_set=
      if Use_pump then
        0
      else
        1)
    annotation (Placement(transformation(extent={{-5.275067375849304,-5.275067375849304},{5.275067375849304,5.275067375849304}},origin={-99.73066594633582,-56.58705980812161},rotation=90.0)));
  .BOLT.CoolantMisc.PumpPoly pumpPolyA(
    T_start=EWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff=not FakePumpA.isOff or isOFF,
    selector=selector_pump[1],
    k_pow=1)
    annotation (Placement(transformation(extent={{8.39339786044772,-8.39339786044772},{-8.39339786044772,8.39339786044772}},origin={-64.17961562871741,-29.698159792787976},rotation=-90.0)));
  .BOLT.CoolantMisc.ReducedPipe filter(
    T_start=EWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    Ka_set=Coef_filter[1]*.Workspace.Auxiliary.Tools.booleanToReal(
      isFilter)+0.0001*(1-.Workspace.Auxiliary.Tools.booleanToReal(
      isFilter)),
    Kb=Coef_filter[2]*.Workspace.Auxiliary.Tools.booleanToReal(
      isFilter)+0,
    isOff=isOFF)
    annotation (Placement(transformation(extent={{-6.386404559420782,-6.386404559420782},{6.386404559420782,6.386404559420782}},origin={-2.4147855791865496,-104.77814292215895},rotation=90.0)));
  .BOLT.CoolantMisc.Split split_bloc(
    mDot_a_start=mdot_start,
    mDot_b_start=
      if not is_monobloc then
        mdot_start
      else
        0,
    isOff_b=is_monobloc or isOFF,
    T_start=EWT,
    p_start=Water_pressure,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff_a=isOFF,
    fa_set=
      if is_monobloc then
        1
      else
        0.5)
    annotation (Placement(transformation(extent={{-5.275067375849304,-5.275067375849304},{5.275067375849304,5.275067375849304}},origin={-2.168738601331569,-74.8812502328091},rotation=90.0)));
  .BOLT.CoolantMisc.Split split_pumpB(
    mDot_a_start=
      if not Use_fake_pump and not is_monobloc then
        mdot_start
      else
        0,
    mDot_b_start=
      if Use_fake_pump and not is_monobloc then
        mdot_start
      else
        0,
    isOff_b=not Use_fake_pump or is_monobloc or isOFF,
    T_start=EWT,
    p_start=Water_pressure,
    isOff_a=not FakePumpB.isOff or is_monobloc or isOFF,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    fa_set=
      if Use_pump then
        1
      else
        0)
    annotation (Placement(transformation(extent={{-5.275067375849304,-5.275067375849304},{5.275067375849304,5.275067375849304}},origin={97.91************,-56.700481499316574},rotation=90.0)));
  .BOLT.CoolantMisc.PumpPoly pumpPolyB(
    isOff=not FakePumpB.isOff or is_monobloc or isOFF,
    T_start=EWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    selector=selector_pump[2],
    k_pow=1)
    annotation (Placement(transformation(extent={{-8.393397860447724,-8.39339786044772},{8.393397860447724,8.39339786044772}},origin={58.114061880794424,-29.0322955537304},rotation=90.0)));
  .BOLT.InternalLibrary.BuildingBlocks.Coolant.Ports.FluidPort_a coolant_in(
    CoolantMedium=CoolantMedium,
    Xi_set=BrineConcentration)
    annotation (Placement(transformation(extent={{-13.931086697653875,-156.7059464254537},{6.068913302346126,-136.7059464254537}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.InternalLibrary.BuildingBlocks.Coolant.Ports.FluidPort_b coolant_out(
    CoolantMedium=CoolantMedium,
    Xi_set=BrineConcentration)
    annotation (Placement(transformation(extent={{-11.405481404682625,132.6738698914627},{8.594518595317375,152.6738698914627}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-11.900134225592929,101.51074217411269},{8.099865774407071,121.51074217411269}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput EXV_controller_A
    annotation (Placement(transformation(extent={{-95.37224004817512,65.18237406821589},{-103.73623419313878,73.54636821317959}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={-110,25})));
  .Modelica.Blocks.Interfaces.RealInput Fan_controller_A
    annotation (Placement(transformation(extent={{-93.68762778007806,55.21653743265198},{-103.01998396510851,64.54889361768241}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={-110,-25})));
  .Modelica.Blocks.Interfaces.RealInput Compressor_controller_B
    annotation (Placement(transformation(extent={{82.74141595718798,71.24016805630758},{90.48280695230586,78.98155905142546}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={110,-75})));
  .Modelica.Blocks.Interfaces.RealInput EXV_controller_B
    annotation (Placement(transformation(extent={{83.27294414718028,62.48467763450602},{90.60422313218908,69.81595661951482}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={110,25})));
  .Modelica.Blocks.Interfaces.RealInput Fan_controller_B
    annotation (Placement(transformation(extent={{82.93873487109778,54.18966501312022},{91.11881075280178,62.36974089482419}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={110,-25})));
  .Modelica.Blocks.Interfaces.RealInput Compressor_controller_A
    annotation (Placement(transformation(extent={{-94.52303104173927,74.80176781892371},{-103.35490732000406,83.63364409718852}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={-110,-75})));
  .Workspace.Interfaces.MeasurementBus measurementBusA
    annotation (Placement(transformation(extent={{-40.15608500378272,148.13769465476108},{-15.20881689978215,173.08496275876163}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={-80,100})));
  .Workspace.Interfaces.MeasurementBus measurementBusB
    annotation (Placement(transformation(extent={{9.939416093258004,147.5405819008838},{36.08090970501311,173.6820755126389}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={80,100})));
  parameter.Modelica.SIunits.Temperature EWT=285.15;
  parameter.Modelica.SIunits.Temperature LWT=280.15;
  parameter.Modelica.SIunits.Temperature OAT=308.15;
  parameter Real relative_humidity=0.87;
  .BOLT.BoundaryNode.Coolant.Node node_blocA_out(
    m_flow_start=mdot_start,
    T_start=LWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff=isOFF)
    annotation (Placement(transformation(extent={{-22.168644486891402,44.821555521083205},{-14.168644486891402,52.821555521083205}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Node node_blocB_out(
    m_flow_start=
      if not is_monobloc then
        mdot_start
      else
        0,
    T_start=LWT,
    isOff=is_monobloc or isOFF,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration)
    annotation (Placement(transformation(extent={{19.520887617875417,44.82155552108316},{11.520887617875417,52.82155552108316}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Node node_blocA_in(
    m_flow_start=mdot_start,
    T_start=EWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff=isOFF)
    annotation (Placement(transformation(extent={{-78.99424464345026,44.37055869444386},{-70.99424464345026,52.37055869444386}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Node node_blocB_in(
    m_flow_start=mdot_start,
    T_start=EWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff=is_monobloc or isOFF)
    annotation (Placement(transformation(extent={{74.2267335344223,45.27255234772253},{66.2267335344223,53.27255234772253}},origin={0.0,0.0},rotation=0.0)));
  parameter Boolean isOFF=false
    annotation (Dialog(group="isOff"));
  parameter Boolean isFilter=false
    annotation (Dialog(group="isOff"));
  parameter Boolean isBufferTank=false
    annotation (Dialog(group="isOff"));
  parameter Boolean is_monobloc=false
    annotation (Dialog(group="isOff"));
  .BOLT.BoundaryNode.Coolant.Node node_coolant_in_pumpB(
    m_flow_start=
      if not is_monobloc then
        mdot_start
      else
        0,
    T_start=EWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff=is_monobloc or isOFF)
    annotation (Placement(transformation(extent={{39.44772772364737,-69.68433015997266},{47.44772772364737,-61.68433015997266}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Node node_coolant_in_pumpA(
    m_flow_start=mdot_start,
    T_start=EWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff=isOFF)
    annotation (Placement(transformation(extent={{-39.76097449252692,-68.02469182603615},{-47.76097449252692,-60.02469182603615}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Node node_coolant_in_splitbloc(
    m_flow_start=mdot_start,
    T_start=EWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff=isOFF)
    annotation (Placement(transformation(extent={{4.0,-4.0},{-4.0,4.0}},origin={-2.502071934664909,-89.1165772194211},rotation=-90.0)));
  .BOLT.BoundaryNode.Coolant.Node node_coolant_source(
    m_flow_start=mdot_start,
    T_start=EWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff=isOFF)
    annotation (Placement(transformation(extent={{4.0,-4.0},{-4.0,4.0}},origin={-3.0687386013315745,-121.88324388608778},rotation=-90.0)));
  // Declaration of Coolant Medium Package
  //package CoolantCommonMedium =
  //.BOLT.InternalLibrary.Media.Coolant.CoolantCommon                             "Coolant Medium Package" annotation ();
  // Declaration of Coolant Medium at Evaporator Circuit
  parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector CoolantMedium=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector.FW
    "Coolant Medium Selection"
    annotation (Dialog(group="Medium"));
  final parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Empty coolantInf=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.getSelector(
    CoolantMedium)
    "Coolant Medium"
    annotation ();
  .BOLT.InternalLibrary.Media.Coolant.CoolantCommon.Temperature FreezTemp=.BOLT.InternalLibrary.Media.Coolant.CoolantCommon.freezeTemp(
    coolantInf,
    BrineConcentration,
    1);
  parameter Real BrineConcentration=0.2
    annotation (Dialog(group="Medium"));
  .BOLT.CoolantMisc.ReducedPipe gaz_separatorA(
    T_start=EWT,
    Ka_set=7,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff=isOFF)
    annotation (Placement(transformation(extent={{-6.076246165826419,-6.076246165826419},{6.076246165826419,6.076246165826419}},origin={-102.0,32.0},rotation=90.0)));
  .BOLT.CoolantMisc.ReducedPipe gaz_separatorB(
    T_start=EWT,
    Ka_set=7,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff=is_monobloc or isOFF)
    annotation (Placement(transformation(extent={{-6.076246165826419,-6.076246165826419},{6.076246165826419,6.076246165826419}},origin={97.20855048825722,31.723549174361892},rotation=90.0)));
  .BOLT.CoolantMisc.ReducedPipe PipingA(
    T_start=EWT,
    Ka_set=0.738,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    Kb=2.3384,
    isOff=isOFF)
    annotation (Placement(transformation(extent={{-6.076246165826419,-6.076246165826419},{6.076246165826419,6.076246165826419}},origin={-101.26278268171433,15.306354263902577},rotation=90.0)));
  .BOLT.CoolantMisc.ReducedPipe PipingB(
    T_start=EWT,
    Ka_set=0.738,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff=is_monobloc or isOFF,
    Kb=2.3384)
    annotation (Placement(transformation(extent={{-6.076246165826419,-6.076246165826419},{6.076246165826419,6.076246165826419}},origin={97.57846085003577,13.7235491743619},rotation=90.0)));
  .BOLT.BoundaryNode.Shaft.Source shafA(
    isOff=not FakePumpA.isOff or isOFF,
    use_speed_in=true)
    annotation (Placement(transformation(extent={{-48.2918498052081,-7.32838118682638},{-40.2918498052081,0.6716188131736196}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Shaft.Source shafB(
    isOff=not FakePumpB.isOff or is_monobloc or isOFF,
    use_speed_in=true)
    annotation (Placement(transformation(extent={{41.23789886228211,-6.614264544162268},{33.23789886228211,1.3857354558377324}},origin={0.0,0.0},rotation=0.0)));
  parameter.Modelica.SIunits.Pressure Pdispo=50000;
  .BOLT.BoundaryNode.Coolant.Node node_out(
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    T_start=LWT,
    isOff=isOFF)
    annotation (Placement(transformation(extent={{-4.0,-4.0},{4.0,4.0}},origin={-1.2853267807020963,122.00519488799881},rotation=90.0)));
  Real controlledCapacity;
  Real controlledPower;
  parameter.Modelica.SIunits.Power Elec_box_power=160;
  .Modelica.Blocks.Sources.RealExpression External_Pressure(
    y=node_out.p)
    annotation (Placement(transformation(extent={{-10.0,-10.0},{10.0,10.0}},origin={-4.75201389559944,219.6340188526507},rotation=-90.0)));
  .Modelica.Blocks.Interfaces.RealInput ActuatorPumpUser_A
    annotation (Placement(transformation(extent={{4.932002332575905,-4.321664051054373},{-4.932002332575905,4.321664051054373}},origin={-61.903424487071646,19.023908485458964},rotation=90.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={-110,70})));
  .Modelica.Blocks.Interfaces.RealInput ActuatorPumpUser_B
    annotation (Placement(transformation(extent={{4.666178092515224,-4.666178092515217},{-4.666178092515224,4.666178092515217}},origin={60.61616868002082,17.173645517409803},rotation=90.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={110,70})));
  .Modelica.Blocks.Sources.RealExpression gross_cap(
    y=BlockA.evapBPHE.summary.Q_flow_ref + BlockB.evapBPHE.summary.Q_flow_ref - pumpPolyA.summary.P_pump - pumpPolyB.summary.P_pump)
    annotation (Placement(transformation(extent={{-266.2591014056851,83.78691261857034},{-246.2591014056851,103.78691261857034}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression gross_pow(
    y=BlockA.systemVariables.summary.pow_total+BlockB.systemVariables.summary.pow_total+pumpPolyA.summary.P_motor+pumpPolyB.summary.P_motor+Elec_box_power*(1-Workspace.Auxiliary.Tools.booleanToReal(
      isOFF))+pumpPolyA.summary.P_motor+pumpPolyB.summary.P_motor)
    annotation (Placement(transformation(extent={{-266.51527475472784,70.50119833285603},{-246.51527475472784,90.50119833285603}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Auxiliary.Business_Factor.BusinessFactors_cooling businessFactors(
    use_business_factor=use_bf,
    bf_cap_max=bf_cap_max,
    bf_cap_min=bf_cap_min,
    bf_pow_max=bf_pow_max,
    bf_pow_min=bf_pow_min,
    const_bf_cap=const_bf_cap,
    load_bf_cap=load_bf_cap,
    OAT_bf_cap=OAT_bf_cap,
    const_bf_pow=const_bf_pow,
    load_bf_pow=load_bf_pow,
    OAT_bf_pow=OAT_bf_pow)
    annotation (Placement(transformation(extent={{-154.63140290261873,118.97383736620517},{-127.67582905801078,145.92941121081313}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression load(
    y=if not is_ULN_option then (BlockA.evapBPHE.summary.Q_flow_ref + BlockB.evapBPHE.summary.Q_flow_ref) / Cap_rating
    else (BlockA.evapBPHE.summary.Q_flow_ref + BlockB.evapBPHE.summary.Q_flow_ref) / Cap_rating_LN)
    annotation (Placement(transformation(extent={{-268.09584982116075,54.14652778316335},{-248.09584982116075,74.14652778316335}},origin={0.0,0.0},rotation=0.0)));
  parameter.BOLT.InternalLibrary.Compressors.PD_2Port.SubComponents.PD.DataBase.Polynomial.ARI_VS.Selector selector_Comp[2]
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Real capacity_design[2]
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Real CompVoltage[2]
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Real fmax[2]
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Real fmin[2]
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Real M_ref[2]
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter.BOLT.InternalLibrary.Refrigerant.Valves.TwoPort.DataBase.Selector EXV_main[2]
    annotation (Dialog(group="EXV",tab="Unit Characteristics"));
  parameter Real Dport_coolant_a[2]
    annotation (Dialog(group="BPHE",tab="Unit Characteristics"));
  parameter Real Dport_coolant_b[2]
    annotation (Dialog(group="BPHE",tab="Unit Characteristics"));
  parameter Real Dport_ref_a[2]
    annotation (Dialog(group="BPHE",tab="Unit Characteristics"));
  parameter Real Dport_ref_b[2]
    annotation (Dialog(group="BPHE",tab="Unit Characteristics"));
  parameter Integer nPlate[2]
    annotation (Dialog(group="BPHE",tab="Unit Characteristics"));
  parameter Real EvapFoulingFactor
    annotation (Dialog(group="BPHE",tab="Unit Characteristics"));
  parameter.BlackBoxLibrary.GeoSelector.GeoSelector_1C_Cond selector_geo_BPHE[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Integer nCoils[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Integer Itube[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Integer nCir[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Integer Ntube[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Integer Nrow[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Ltube[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Dotube[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Ttube[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Ptube[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Prow[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Dfin[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Real Tfin[2]
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter Boolean isCoating
    annotation (Dialog(group="RTPF",tab="Unit Characteristics"));
  parameter.BOLT.InternalLibrary.Coolant.Pumps.DataBase.PumpPoly.Selector selector_pump[2]
    annotation (Dialog(group="Pump",tab="Unit Characteristics"));
  parameter Real[2] Coef_filter
    annotation (Dialog(tab="Unit Characteristics",group="Pump"));
  parameter Real[2] Coef_bufferTank
    annotation (Dialog(tab="Unit Characteristics",group="Pump"));
  parameter Real Fw_fan[2]
    annotation (Dialog(group="Fan",tab="Unit Characteristics"));
  parameter Real max_fan_frequency[2]
    annotation (Dialog(group="Fan",tab="Unit Characteristics"));
  parameter Real fanCurveCoefficientsCooling[2,9]
    annotation (Dialog(group="Fan",tab="Unit Characteristics"));
  parameter Real fanCurveCoefficientsHeating[2,9]
    annotation (Dialog(group="Fan",tab="Unit Characteristics"));
  parameter Real Suction_line_diameter[2]
    annotation (Dialog(group="Suction Line",tab="Unit Characteristics"));
  parameter Real Suction_line_length[2]
    annotation (Dialog(group="Suction Line",tab="Unit Characteristics"));
  parameter Real coil_line_diameter[2]
    annotation (Dialog(group="Coil Line",tab="Unit Characteristics"));
  parameter Real coil_line_length[2]
    annotation (Dialog(group="Coil Line",tab="Unit Characteristics"));
  parameter Real liquid_line_diameter[2]
    annotation (Dialog(group="Liquid Line",tab="Unit Characteristics"));
  parameter Real liquid_line_length[2]
    annotation (Dialog(group="Liquid Line",tab="Unit Characteristics"));
  parameter Real discharge_line_diameter[2]
    annotation (Dialog(group="Discharge Line",tab="Unit Characteristics"));
  parameter Real discharge_line_length[2]
    annotation (Dialog(group="Discharge Line",tab="Unit Characteristics"));
  parameter Real EXV_in_line_diameter[2]
    annotation (Dialog(group="EXV in Line",tab="Unit Characteristics"));
  parameter Real EXV_in_line_length[2]
    annotation (Dialog(group="EXV in Line",tab="Unit Characteristics"));
  parameter Real Ac_duct[2]
    annotation (Dialog(group="Air Duct",tab="Unit Characteristics"));
  parameter Real Ka_duct[2]
    annotation (Dialog(group="Air Duct",tab="Unit Characteristics"));
  parameter Real UA_duct[2]
    annotation (Dialog(group="Air Duct",tab="Unit Characteristics"));
  parameter Real PDC_4WV[2]
    annotation (Dialog(group="4WV",tab="Unit Characteristics"));
  parameter Real max_fan_ULN_heating[2]
    annotation (Dialog(group="Fan",tab="Unit Characteristics"));
  parameter Real max_cp_ULN_heating[2]
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Real max_fan_ULN_cooling[2]
    annotation (Dialog(group="Fan",tab="Unit Characteristics"));
  parameter Real max_cp_ULN_cooling[2]
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Real Zevap_HPH_cst[2]
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_HPH_SST[2]
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_coated_HPH[2]
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_HPH_min[2]
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_HPH_max[2]
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_HPC_cst[2]
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_HPC_SST[2]
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_HPC_min[2]
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_HPC_max[2]
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_chaud_Coolcap[2]
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zevap_froid_Coolcap[2]
    annotation (Dialog(group="evaporator",tab="Calibration"));
  parameter Real Zcond_HPH_cst[2]
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPH_SDT[2]
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPH_heatcap[2]
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPH_SST[2]
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPH_min[2]
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPH_max[2]
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPC_cst[2]
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPC_SDT[2]
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPC_coated[2]
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPC_min[2]
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zcond_HPC_max[2]
    annotation (Dialog(group="condenser",tab="Calibration"));
  parameter Real Zflow_intercept[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zflow_SST[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zflow_SST2[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zflow_SST3[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zflow_SDT[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zflow_heatcap[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zflow_Ncomp[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zflow_min[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zflow_max[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zpower_intercept[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zpower_Ncomp[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zpower_heatcap[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zpower_DGT[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zpower_SST[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zpower_SST2[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zpower_SDT[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zpower_min[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Zpower_max[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real FW[2]
    annotation (Dialog(group="compressor",tab="Calibration"));
  parameter Real Cap_SEER_A[2]
    annotation (Dialog(group="",tab="Unit Characteristics"));
  parameter Real Cap_SEER_A_LN[2]
    annotation (Dialog(group="",tab="Unit Characteristics"));
  parameter Boolean use_en=true
    annotation (Dialog(tab="BusinessFactor&EN14511"));
  parameter Boolean use_bf=true
    annotation (Dialog(tab="BusinessFactor&EN14511"));
  //"if false BF's are set to 1 and controlledCapacity = engineering capacity"    
  parameter Real const_bf_cap=-0.387
    "capacity business factor coefficient"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real OAT_bf_cap=0.0045643
    "capacity business factor coefficient"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real load_bf_cap=0
    "capacity business factor coefficient"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real const_bf_pow=0.4766
    "power business factor coefficient"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real load_bf_pow=0
    "power business factor coefficient"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real OAT_bf_pow=0.0016851
    "power business factor coefficient"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real bf_cap_max=1.02
    "capacity business factor max value"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real bf_cap_min=0.95
    "capacity business factor min value"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real bf_pow_max=1
    "power business factor max value"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real bf_pow_min=0.98
    "power business factor min value"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real mdot_start
    annotation (Dialog(group="Pump ",tab="Initialization"));
  parameter Real[2] ssh_setPoint={5,5}
    "circuit A ssh set point"
    annotation (Dialog(group="Refrigerant"));
  parameter.Modelica.SIunits.TemperatureDifference[2] SC_setpoint={-2,-2}
    annotation (Dialog(group="Refrigerant"));
  parameter Boolean[2] SC_fixed={true,true}
    annotation (Dialog(group="Refrigerant"));
  parameter Boolean[2] Mref_fixed={false,false}
    annotation (Dialog(group="Refrigerant"));
  inner.BOLT.InternalLibrary.Refrigerant.Aggregation.AggregateStreams_2 systemVariablesPump(
    isOff={not Use_pump,not Use_pump})
    annotation (Placement(transformation(extent={{53.64941913376437,125.82725552858614},{73.64941913376437,145.82725552858614}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.Mixer mixerPumpUserA(
    mDot_a_start=
      if Use_fake_pump then
        mdot_start
      else
        0,
    mDot_b_start=
      if not Use_fake_pump then
        mdot_start
      else
        0,
    p_start=Water_pressure,
    T_start=EWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff_a=not Use_fake_pump or isOFF,
    isOff_b=not FakePumpA.isOff or isOFF,
    fa_set=
      if Use_pump then
        0
      else
        1)
    annotation (Placement(transformation(extent={{-5.319920743950348,-5.319920743950334},{5.319920743950348,5.319920743950334}},origin={-101.10972902044189,-7.854595463337819},rotation=90.0)));
  .BOLT.CoolantMisc.Mixer mixerPumpUserB(
    mDot_a_start=
      if not Use_fake_pump then
        mdot_start
      else
        0,
    mDot_b_start=
      if Use_fake_pump then
        mdot_start
      else
        0,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff_b=not Use_fake_pump or is_monobloc or isOFF,
    isOff_a=not FakePumpB.isOff or is_monobloc or isOFF,
    T_start=EWT,
    p_start=Water_pressure,
    fa_set=
      if Use_pump then
        1
      else
        0)
    annotation (Placement(transformation(extent={{-5.319920743950348,-5.319920743950334},{5.319920743950348,5.319920743950334}},origin={97.5852880366944,-7.6962519378710965},rotation=90.0)));
  inner.BOLT.GlobalParameters globalParameters(
    varLevel=.BOLT.InternalLibrary.BuildingBlocks.Types.Var_level.Advanced,
    RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R290)
    annotation (Placement(transformation(extent={{77.61415486105938,125.98794792344239},{97.61415486105938,145.9879479234424}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Auxiliary.EN14511.EN14511_HPC_HPH eN(
    heating_mode=false,
    ie=0.88,
    isOffB=isOFF or is_monobloc,
    isOffA=isOFF,
    integrated_pump=not Use_fake_pump,
    use_en=use_en and not(isOFF))
    annotation (Placement(transformation(extent={{-212.93010794659622,117.8515683778786},{-192.93010794659622,137.8515683778786}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression DPe(
    y=-node_out.summary.p + node_coolant_source.summary.p)
    annotation (Placement(transformation(extent={{-267.49551331235347,144.09044018297394},{-247.49551331235347,164.09044018297394}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression DPi(
    y=FakePumpA.summary.dp)
    annotation (Placement(transformation(extent={{-266.83050046699265,128.4941235783975},{-246.83050046699265,149.38084476122165}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression q(
    y=node_out.summary.Vd)
    annotation (Placement(transformation(extent={{-266.1950285583034,113.23673834422958},{-246.19502855830342,134.1234595270538}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression q_air_A_B(
    y=0)
    annotation (Placement(transformation(extent={{-266.1605261577143,98.49412357839749},{-246.1605261577143,119.3808447612217}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression DPe_air_A_B(
    y=0)
    annotation (Placement(transformation(extent={{-268.1605261577143,157.1825227058921},{-248.1605261577143,177.1825227058921}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.ReducedPipe FakePumpA(
    use_Ka_in=true,
    isOff=not Use_fake_pump or isOFF,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration)
    annotation (Placement(transformation(extent={{7.9306406917366985,-7.9306406917366985},{-7.9306406917366985,7.9306406917366985}},origin={-109.31748646936882,-29.55041877093332},rotation=-90.0)));
  .BOLT.CoolantMisc.ReducedPipe FakePumpB(
    isOff=not Use_fake_pump or is_monobloc or isOFF,
    use_Ka_in=true,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration)
    annotation (Placement(transformation(extent={{-7.9306406917366985,-7.9306406917367},{7.9306406917366985,7.9306406917367}},origin={107.5785931621858,-29.58381275290928},rotation=90.0)));
  .Modelica.Blocks.Sources.RealExpression LWT_1(
    y=node_out.summary.T)
    annotation (Placement(transformation(extent={{-268.0,40.0},{-248.0,60.0}},origin={0.0,0.0},rotation=0.0)));
  parameter Real Cap_rating=
    if is_monobloc then
      Cap_SEER_A[1]
    else
      Cap_SEER_A[1]+Cap_SEER_A[2];
    
    parameter Real Cap_rating_LN=
    if is_monobloc then
      Cap_SEER_A_LN[1]
    else
      Cap_SEER_A_LN[1]+Cap_SEER_A_LN[2];
  .BOLT.CoolantMisc.ReducedPipe buffer_tank(
    T_start=EWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    Kb=Coef_bufferTank[2]*.Workspace.Auxiliary.Tools.booleanToReal(
      isBufferTank)+0,
    Ka_set=Coef_bufferTank[1]*.Workspace.Auxiliary.Tools.booleanToReal(
      isBufferTank)+0.0001*(1-.Workspace.Auxiliary.Tools.booleanToReal(
      isBufferTank)),
    isOff=isOFF)
    annotation (Placement(transformation(extent={{-6.386404559420782,-6.386404559420782},{6.386404559420782,6.386404559420782}},origin={-1.8858671373448352,105.48638017068502},rotation=90.0)));
  .BOLT.BoundaryNode.Coolant.Node node_buffer_in(
    T_start=LWT,
    CoolantMedium=CoolantMedium,
    X=BrineConcentration,
    isOff=isOFF)
    annotation (Placement(transformation(extent={{-4.0,-4.0},{4.0,4.0}},origin={-1.6800016253665895,89.53432678007132},rotation=90.0)));
  .Modelica.SIunits.PressureDifference InternalPressureDrop=-FakePumpA.summary.dp+(pumpPolyA.summary.dp_pump-node_out.summary.p+node_coolant_source.summary.p)*(1-.Workspace.Auxiliary.Tools.booleanToReal(
    Use_fake_pump))
    "Internal hydraulic pressure drop ";
  .Modelica.SIunits.PressureDifference AvailableStaticPressure=node_out.summary.p-node_coolant_source.summary.p;
  Real BF_pow_out=
    if not isOFF then
      businessFactors.pub_net_pow/businessFactors.net_pow
    else
      1
    "final business factor";
  Real BF_cap_out=
    if not isOFF then
      businessFactors.pub_net_cool_cap/businessFactors.net_cool_cap
    else
      1
    "Final business factor for capacity ";
  Real EvapFlowRate=
    if not isOFF then
      BF_cap_out*node_coolant_source.summary.Vd
    else
      0
    "outlet flow rate included BF factor correction";
  Real Evap_ewt=
    if not isOFF then
      BF_cap_out*node_coolant_source.summary.T+(1-BF_cap_out)*node_out.summary.T
    else
      0
    "outlet flow rate included BF factor correction";
  Real Evap_lwt=
    if not isOFF then
      BF_cap_out*node_out.summary.T+(1-BF_cap_out)*node_coolant_source.summary.T
    else
      0
    "outlet flow rate included BF factor correction";
    parameter Real Altitude = 0;
    parameter Boolean is_ULN_option = false;
    parameter Boolean isOff_ref = false annotation(Dialog(group = "isOff"));
equation
  controlledCapacity=businessFactors.pub_net_cool_cap;
  controlledPower=businessFactors.pub_net_pow;
  connect(split_pumpB.port_a,pumpPolyB.port_a)
    annotation (Line(points={{95.1705875471087,-51.42541412346727},{95.1705875471087,-44.926847888774006},{58.11406188079442,-44.926847888774006},{58.11406188079442,-37.42569341417812}},color={0,127,0}));
  connect(split_pumpA.port_b,pumpPolyA.port_a)
    annotation (Line(points={{-96.98763091089418,-51.311992432272305},{-96.98763091089418,-44.55022524213727},{-64.17961562871741,-44.55022524213727},{-64.17961562871741,-38.0915576532357}},color={0,127,0}));
  connect(node_blocB_out.port_b,mixer.port_b)
    annotation (Line(points={{11.520887617875417,48.82155552108316},{2.0797696367109797,48.82155552108316},{2.0797696367109797,57.09976839691211}},color={0,127,0}));
  connect(node_blocA_out.port_b,mixer.port_a)
    annotation (Line(points={{-14.168644486891402,48.821555521083205},{-7.92023036328902,48.821555521083205},{-7.92023036328902,57.09976839691211}},color={0,127,0}));
  connect(split_bloc.port_b,node_coolant_in_pumpB.port_a)
    annotation (Line(points={{0.5742964341100705,-69.60618285695979},{6.5666666666666655,-69.60618285695979},{6.5666666666666655,-65.68433015997266},{39.44772772364737,-65.68433015997266}},color={0,127,0}));
  connect(node_coolant_in_pumpB.port_b,split_pumpB.port_c)
    annotation (Line(points={{47.44772772364737,-65.68433015997266},{98.01912393006732,-65.68433015997266},{98.01912393006732,-62.08105022268286}},color={0,127,0}));
  connect(split_bloc.port_a,node_coolant_in_pumpA.port_a)
    annotation (Line(points={{-4.911773636773205,-69.60618285695979},{-10.88581550923115,-69.60618285695979},{-10.88581550923115,-64.02469182603615},{-39.76097449252692,-64.02469182603615}},color={0,127,0}));
  connect(node_coolant_in_pumpA.port_b,split_pumpA.port_c)
    annotation (Line(points={{-47.76097449252692,-64.02469182603615},{-99.62516459881883,-64.02469182603615},{-99.62516459881883,-61.9676285314879}},color={0,127,0}));
  connect(node_coolant_in_splitbloc.port_b,split_bloc.port_c)
    annotation (Line(points={{-2.50207193466491,-85.1165772194211},{-2.50207193466491,-80.26181895617539},{-2.063237253814584,-80.26181895617539}},color={0,127,0}));
  connect(shafA.flange,pumpPolyA.flange)
    annotation (Line(points={{-40.2918498052081,-3.3283811868263804},{-31.988777703356632,-3.3283811868263804},{-31.988777703356632,-29.698159792787973},{-55.786217768269694,-29.698159792787973}},color={127,0,0}));
  connect(shafB.flange,pumpPolyB.flange)
    annotation (Line(points={{33.23789886228211,-2.6142645441622676},{27.23789886228211,-2.6142645441622676},{27.23789886228211,-29.032295553730396},{49.720664020346696,-29.032295553730396}},color={127,0,0}));
  connect(coolant_in,node_coolant_source.port_a)
    annotation (Line(points={{-3.480089871014542,-146.7059464254537},{-3.480089871014542,-131.20519966131292},{-3.4013608372831245,-131.20519966131292}},color={0,127,0}));
  connect(node_out.port_b,coolant_out)
    annotation (Line(points={{-1.2853267807020954,126.00519488799881},{-1.2853267807020954,150},{-1.9001342255929288,150}},color={0,127,0}));
  connect(gross_pow.y,businessFactors.gross_pow)
    annotation (Line(points={{-245.51527475472784,80.50119833285603},{-173.12230848155326,80.50119833285603},{-173.12230848155326,131.10384559627877},{-151.93584551815792,131.10384559627877}},color={0,0,127}));
  connect(businessFactors.gross_cool_cap,gross_cap.y)
    annotation (Line(points={{-151.93584551815792,127.06050951958757},{-172.99422180703186,127.06050951958757},{-172.99422180703186,93.78691261857034},{-245.25910140568513,93.78691261857034}},color={0,0,127}));
  connect(External_Pressure.y,measurementBusA.External_Pressure)
    annotation (Line(points={{-4.752013895599437,208.6340188526507},{-4.752013895599437,160.61132870676136},{-27.682450951782435,160.61132870676136}},color={0,0,127}));
  connect(External_Pressure.y,measurementBusB.External_Pressure)
    annotation (Line(points={{-4.752013895599437,208.6340188526507},{-4.752013895599437,160.61132870676136},{23.01016289913556,160.61132870676136}},color={0,0,127}));
  connect(PipingB.port_b,gaz_separatorB.port_a)
    annotation (Line(points={{97.57846085003577,19.799795340188318},{97.57846085003577,25.647303008535474},{97.20855048825722,25.647303008535474}},color={0,127,0}));
  connect(PipingA.port_b,gaz_separatorA.port_a)
    annotation (Line(points={{-101.26278268171433,21.38260042972899},{-101.26278268171433,25.92375383417358},{-102,25.92375383417358}},color={0,127,0}));
  connect(ActuatorPumpUser_B,shafB.speed_in)
    annotation (Line(points={{60.61616868002082,17.173645517409803},{60.61616868002082,-4.171242330637769},{41.23789886228211,-4.171242330637769},{41.23789886228211,-1.0142645441622675}},color={0,0,127}));
  connect(ActuatorPumpUser_A,shafA.speed_in)
    annotation (Line(points={{-61.903424487071646,19.023908485458964},{-61.903424487071646,6.39599733955696},{-57.53049747436645,6.39599733955696},{-57.53049747436645,-1.7283811868263803},{-48.2918498052081,-1.7283811868263803}},color={0,0,127}));
  connect(mixerPumpUserA.port_c,PipingA.port_a)
    annotation (Line(points={{-101.10972902044189,-2.321877889629457},{-101.10972902044189,3.4541151042233507},{-101.26278268171433,3.4541151042233507},{-101.26278268171433,9.230108098076158}},color={0,127,0}));
  connect(mixerPumpUserB.port_c,PipingB.port_a)
    annotation (Line(points={{97.5852880366944,-2.163534364162735},{97.5852880366944,2.4788740719228692},{97.57846085003577,2.4788740719228692},{97.57846085003577,7.647303008535481}},color={0,127,0}));
  connect(pumpPolyA.port_b,mixerPumpUserA.port_b)
    annotation (Line(points={{-64.17961562871741,-21.304761932340256},{-64.17961562871741,-16.530343327148906},{-98.34337023358772,-16.530343327148906},{-98.34337023358772,-13.174516207288168}},color={0,127,0}));
  connect(pumpPolyB.port_b,mixerPumpUserB.port_a)
    annotation (Line(points={{58.11406188079442,-20.63889769328268},{58.11406188079442,-16.380121806848173},{95.03172607959823,-16.380121806848173},{95.03172607959823,-13.016172681821445}},color={0,127,0}));
  connect(coolant_in,node_coolant_source.port_a)
    annotation (Line(points={{-3.931086697653874,-146.7059464254537},{-3.931086697653874,-125.88324388608778},{-3.0687386013315736,-125.88324388608778}},color={0,127,0}));
  connect(BlockA.port_b,node_blocA_out.port_a)
    annotation (Line(points={{-32.91983879885063,52.84558962403017},{-32.91983879885063,48.821555521083205},{-22.168644486891402,48.821555521083205}},color={0,127,0}));
  connect(BlockA.actual_FSFanSpd_in,Fan_controller_A)
    annotation (Line(points={{-55.51983879885063,56.84558962403017},{-76.90288730001818,56.84558962403017},{-76.90288730001818,59.88271552516719},{-98.35380587259328,59.88271552516719}},color={0,0,127}));
  connect(BlockA.EXV_controller,EXV_controller_A)
    annotation (Line(points={{-55.51983879885063,60.84558962403017},{-76.50394290903301,60.84558962403017},{-76.50394290903301,69.36437114069773},{-99.55423712065695,69.36437114069773}},color={0,0,127}));
  connect(BlockA.compressor_controller,Compressor_controller_A)
    annotation (Line(points={{-55.51983879885063,64.84558962403017},{-76.18453903904617,64.84558962403017},{-76.18453903904617,79.21770595805611},{-98.93896918087167,79.21770595805611}},color={0,0,127}));
  connect(BlockA.measurementBus,measurementBusA)
    annotation (Line(points={{-33.51983879885063,60.84558962403017},{-27.682450951782435,60.84558962403017},{-27.682450951782435,160.61132870676136}},color={255,204,51}));
  connect(BlockB.port_b,node_blocB_out.port_a)
    annotation (Line(points={{30.91983879885067,50.971316805432906},{30.91983879885067,48.82155552108316},{19.520887617875417,48.82155552108316}},color={0,127,0}));
  connect(BlockB.measurementBus,measurementBusB)
    annotation (Line(points={{31.51983879885067,58.971316805432906},{23.01016289913556,58.971316805432906},{23.01016289913556,160.61132870676136}},color={255,204,51}));
  connect(Fan_controller_B,BlockB.actual_FSFanSpd_in)
    annotation (Line(points={{87.02877281194978,58.2797029539722},{70.19975331193633,58.2797029539722},{70.19975331193633,54.971316805432906},{53.51983879885067,54.971316805432906}},color={0,0,127}));
  connect(EXV_controller_B,BlockB.EXV_controller)
    annotation (Line(points={{86.93858363968468,66.15031712701042},{70.15465872580378,66.15031712701042},{70.15465872580378,58.971316805432906},{53.51983879885067,58.971316805432906}},color={0,0,127}));
  connect(Compressor_controller_B,BlockB.compressor_controller)
    annotation (Line(points={{86.61211145474692,75.11086355386652},{86.61211145474692,68.78444357635209},{70.2636455748654,68.78444357635209},{70.2636455748654,62.971316805432906},{53.51983879885067,62.971316805432906}},color={0,0,127}));
  connect(eN.DPe,DPe.y)
    annotation (Line(points={{-212.93010794659622,134.0515683778786},{-229.8994483064625,134.0515683778786},{-229.8994483064625,154.09044018297394},{-246.49551331235347,154.09044018297394}},color={0,0,127}));
  connect(eN.DPi,DPi.y)
    annotation (Line(points={{-212.93010794659622,128.2515683778786},{-230.27884339448167,128.2515683778786},{-230.27884339448167,138.93748416980958},{-245.83050046699265,138.93748416980958}},color={0,0,127}));
  connect(eN.q,q.y)
    annotation (Line(points={{-212.93010794659622,126.4515683778786},{-229.24920592943744,126.4515683778786},{-229.24920592943744,123.68009893564168},{-245.19502855830342,123.68009893564168}},color={0,0,127}));
  connect(eN.q_air_A,q_air_A_B.y)
    annotation (Line(points={{-212.93010794659622,124.4515683778786},{-229.23195472914284,124.4515683778786},{-229.23195472914284,108.9374841698096},{-245.1605261577143,108.9374841698096}},color={0,0,127}));
  connect(eN.q_air_B,q_air_A_B.y)
    annotation (Line(points={{-212.93010794659622,122.4515683778786},{-229.23195472914284,122.4515683778786},{-229.23195472914284,108.9374841698096},{-245.1605261577143,108.9374841698096}},color={0,0,127}));
  connect(eN.DPe_air_A,DPe_air_A_B.y)
    annotation (Line(points={{-212.93010794659622,132.0515683778786},{-230.53097545487174,132.0515683778786},{-230.53097545487174,167.1825227058921},{-247.1605261577143,167.1825227058921}},color={0,0,127}));
  connect(eN.DPe_air_B,DPe_air_A_B.y)
    annotation (Line(points={{-212.93010794659622,130.0515683778786},{-230.53097545487174,130.0515683778786},{-230.53097545487174,167.1825227058921},{-247.1605261577143,167.1825227058921}},color={0,0,127}));
  connect(eN.inst_gross_cap,gross_cap.y)
    annotation (Line(points={{-212.93010794659622,120.4515683778786},{-229.58026307885714,120.4515683778786},{-229.58026307885714,93.78691261857034},{-245.2591014056851,93.78691261857034}},color={0,0,127}));
  connect(eN.inst_gross_pow,gross_pow.y)
    annotation (Line(points={{-212.93010794659622,118.4515683778786},{-229.7083497533785,118.4515683778786},{-229.7083497533785,80.50119833285603},{-245.51527475472784,80.50119833285603}},color={0,0,127}));
  connect(businessFactors.net_pow,eN.inst_net_pow)
    annotation (Line(points={{-151.93584551815795,139.19051774966113},{-172.91863513509355,139.19051774966113},{-172.91863513509355,124.8515683778786},{-192.93010794659622,124.8515683778786}},color={0,0,127}));
  connect(eN.inst_net_cap,businessFactors.net_cool_cap)
    annotation (Line(points={{-192.93010794659622,130.8515683778786},{-187.9014247520292,130.8515683778786},{-187.9014247520292,143.23385382635234},{-151.93584551815795,143.23385382635234}},color={0,0,127}));
  connect(FakePumpA.port_b,mixerPumpUserA.port_a)
    annotation (Line(points={{-109.31748646936882,-21.61977807919662},{-109.31748646936882,-16.972396494680986},{-103.66329097753805,-16.972396494680986},{-103.66329097753805,-13.174516207288168}},color={0,127,0}));
  connect(FakePumpA.port_a,split_pumpA.port_a)
    annotation (Line(points={{-109.31748646936882,-37.48105946267002},{-109.31748646936882,-43.971775298909755},{-102.47370098177745,-43.971775298909755},{-102.47370098177745,-51.311992432272305}},color={0,127,0}));
  connect(ActuatorPumpUser_A,FakePumpA.Ka_in)
    annotation (Line(points={{-61.903424487071646,19.023908485458964},{-61.903424487071646,-3.1431480445562414},{-86.42267165571931,-3.1431480445562414},{-86.42267165571931,-32.40544941995853},{-101.70407140530159,-32.40544941995853}},color={0,0,127}));
  connect(FakePumpB.port_b,mixerPumpUserB.port_b)
    annotation (Line(points={{107.5785931621858,-21.65317206117258},{107.5785931621858,-17.33467237149701},{100.35164682354856,-17.33467237149701},{100.35164682354856,-13.016172681821445}},color={0,127,0}));
  connect(FakePumpB.port_a,split_pumpB.port_b)
    annotation (Line(points={{107.5785931621858,-37.51445344464598},{107.5785931621858,-44.82449616730542},{100.65665761799197,-44.82449616730542},{100.65665761799197,-51.42541412346727}},color={0,127,0}));
  connect(ActuatorPumpUser_B,FakePumpB.Ka_in)
    annotation (Line(points={{60.61616868002082,17.173645517409803},{60.61616868002082,6.800718504704605},{76.45720036263518,6.800718504704605},{76.45720036263518,-32.43884340193449},{99.96517809811857,-32.43884340193449}},color={0,0,127}));
  connect(gaz_separatorA.port_b,node_blocA_in.port_a)
    annotation (Line(points={{-102,38.07624616582642},{-102,48.37055869444386},{-78.99424464345026,48.37055869444386}},color={0,127,0}));
  connect(node_blocA_in.port_b,BlockA.port_a)
    annotation (Line(points={{-70.99424464345026,48.37055869444386},{-63.86722822220807,48.37055869444386},{-63.86722822220807,52.84558962403017},{-55.51983879885063,52.84558962403017}},color={0,127,0}));
  connect(gaz_separatorB.port_b,node_blocB_in.port_a)
    annotation (Line(points={{97.20855048825722,37.79979534018831},{97.20855048825722,49.27255234772253},{74.2267335344223,49.27255234772253}},color={0,127,0}));
  connect(node_blocB_in.port_b,BlockB.port_a)
    annotation (Line(points={{66.2267335344223,49.27255234772253},{59.60560896305056,49.27255234772253},{59.60560896305056,50.971316805432906},{53.51983879885067,50.971316805432906}},color={0,127,0}));
  connect(load.y,businessFactors.load)
    annotation (Line(points={{-247.09584982116075,64.14652778316335},{-174,64.14652778316335},{-174,121.93895048911205},{-151.93584551815795,121.93895048911205}},color={0,0,127}));
  connect(node_coolant_source.port_b,filter.port_a)
    annotation (Line(points={{-3.0687386013315754,-117.88324388608778},{-3.0687386013315754,-111.16454748157973},{-2.414785579186551,-111.16454748157973}},color={0,127,0}));
  connect(node_coolant_in_splitbloc.port_a,filter.port_b)
    annotation (Line(points={{-2.502071934664908,-93.1165772194211},{-2.502071934664908,-98.39173836273817},{-2.4147855791865482,-98.39173836273817}},color={0,127,0}));
  connect(node_buffer_in.port_b,buffer_tank.port_a)
    annotation (Line(points={{-1.6800016253665895,93.53432678007132},{-1.6800016253665895,99.09997561126424},{-1.8858671373448366,99.09997561126424}},color={0,127,0}));
  connect(buffer_tank.port_b,node_out.port_a)
    annotation (Line(points={{-1.885867137344834,111.8727847301058},{-1.2853267807020972,111.8727847301058},{-1.2853267807020972,118.00519488799881}},color={0,127,0}));
  connect(node_buffer_in.port_a,mixer.port_c)
    annotation (Line(points={{-1.6800016253665904,85.53432678007132},{-1.6800016253665904,80.77803132568498},{-3.1202303632890156,80.77803132568498},{-3.1202303632890156,77.49976839691212}},color={0,127,0}));
    connect(LWT_1.y,businessFactors.LWT) annotation(Line(points = {{-247,50},{-168.13078591443167,50},{-168.13078591443167,117.3565029355287},{-151.93584551815795,117.3565029355287}},color = {0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={49,49,49},
          fillColor={181,124,237},
          fillPattern=FillPattern.Solid,
          extent={{-100,-119},{100,119}},
          origin={0,-19}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name"),
        Text(
          textString="MODULE",
          origin={0,-20},
          extent={{-100,24},{100,-24}},
          lineColor={255,255,255})}));
end Equipement_Modular;
