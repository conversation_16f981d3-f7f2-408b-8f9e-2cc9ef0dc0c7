within Workspace.Icons;
partial model InitializationCycle
  //extends VFD30XW.Icons.InitializationCycle2;
  annotation (
    Icon(
      graphics={
        Rectangle(
          extent={{-100,100},{100,-100}},
          lineColor={0,0,0},
          fillPattern=FillPattern.Solid,
          fillColor={255,255,255}),
        Rectangle(
          extent={{-20,94},{20,74}},
          lineColor={0,0,255},
          fillColor={255,0,0},
          fillPattern=FillPattern.Solid),
        Rectangle(
          extent={{-20,-74},{20,-94}},
          lineColor={0,0,255},
          fillPattern=FillPattern.Solid,
          fillColor={255,0,0}),
        Polygon(
          points={{-86,14},{-74,14},{-80,0},{-86,14}},
          lineColor={0,0,255},
          smooth=Smooth.None,
          fillPattern=FillPattern.Solid,
          fillColor={255,0,0}),
        Polygon(
          points={{-86,-16},{-74,-16},{-80,0},{-86,-16}},
          lineColor={0,0,255},
          smooth=Smooth.None,
          fillPattern=FillPattern.Solid,
          fillColor={255,0,0}),
        Ellipse(
          extent={{62,10},{86,-14}},
          lineColor={0,0,255},
          fillColor={255,0,0},
          fillPattern=FillPattern.Solid),
        Line(
          points={{68,8},{64,-8}},
          color={0,0,255},
          smooth=Smooth.None),
        Line(
          points={{80,8},{84,-8}},
          color={0,0,255},
          smooth=Smooth.None),
        Line(
          points={{-20,84},{-80,84},{-80,14}},
          color={0,0,0},
          smooth=Smooth.None),
        Line(
          points={{-80,-16},{-80,-84},{-20,-84}},
          color={0,0,0},
          smooth=Smooth.None),
        Line(
          points={{20,-84},{74,-84},{74,-14}},
          color={0,0,0},
          smooth=Smooth.None),
        Line(
          points={{74,10},{74,84},{20,84}},
          color={0,0,0},
          smooth=Smooth.None),
        Text(
          extent={{-132,-134},{136,-152}},
          lineColor={0,0,0},
          fillColor={255,0,0},
          fillPattern=FillPattern.Solid,
          textString="%name"),
        Line(
          points={{-38,-42},{-32,-40},{-28,-38},{-22,-32},{-18,-20},{-16,-6},{-14,8},{-12,16},{-6,22},{4,26},{12,24},{16,20},{20,10},{22,-2},{22,-16},{20,-30},{16,-40},{12,-42}},
          color={0,0,0},
          smooth=Smooth.None),
        Polygon(
          points={{-20,-36},{22,-36},{32,-18},{-20,-18},{-20,-18},{-20,-36}},
          lineColor={255,0,0},
          smooth=Smooth.None),
        Line(
          points={{-42,26},{-42,-42},{48,-42}},
          color={0,0,0},
          smooth=Smooth.None),
        Polygon(
          points={{-42,32},{-46,26},{-38,26},{-42,32}},
          lineColor={0,0,0},
          smooth=Smooth.None,
          fillPattern=FillPattern.Solid),
        Polygon(
          points={{48,-38},{48,-46},{54,-42},{48,-38}},
          lineColor={0,0,0},
          smooth=Smooth.None,
          fillPattern=FillPattern.Solid)}));
end InitializationCycle;
