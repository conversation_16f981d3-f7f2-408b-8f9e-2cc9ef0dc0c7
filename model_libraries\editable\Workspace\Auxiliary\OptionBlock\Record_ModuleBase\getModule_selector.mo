within Workspace.Auxiliary.OptionBlock.Record_ModuleBase;
function getModule_selector
  input Workspace.Auxiliary.OptionBlock.Record_ModuleBase.Module_selector Unit_selector;
  output Workspace.Auxiliary.OptionBlock.Record_ModuleBase.ModuleBase Module;
protected
  constant Workspace.Auxiliary.OptionBlock.Record_ModuleBase.Module_040 Module_040;
  constant Workspace.Auxiliary.OptionBlock.Record_ModuleBase.Module_050 Module_050;
  constant Workspace.Auxiliary.OptionBlock.Record_ModuleBase.Module_060 Module_060;
  constant Workspace.Auxiliary.OptionBlock.Record_ModuleBase.Module_070 Module_070;
  constant Workspace.Auxiliary.OptionBlock.Record_ModuleBase.Module_080 Module_080;
  constant Workspace.Auxiliary.OptionBlock.Record_ModuleBase.Module_100 Module_100;
  constant Workspace.Auxiliary.OptionBlock.Record_ModuleBase.Module_120 Module_120;
  constant Workspace.Auxiliary.OptionBlock.Record_ModuleBase.Module_140 Module_140;
  Workspace.Auxiliary.OptionBlock.Record_ModuleBase.ModuleBase[:] Module_base={Module_040,Module_050,Module_060,Module_070,Module_080,Module_100,Module_120,Module_140};
algorithm
  Module := Module_base[Integer(
    Unit_selector)];
end getModule_selector;
