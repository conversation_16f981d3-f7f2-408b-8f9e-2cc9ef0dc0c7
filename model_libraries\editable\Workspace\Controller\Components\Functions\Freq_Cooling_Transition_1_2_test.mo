within Workspace.Controller.Components.Functions;
model Freq_Cooling_Transition_1_2_test
  extends Modelica.Icons.Function;
  import Freq_Cooling_Transition_1_2=Workspace.Controller.Components.Functions.Freq_Cooling_Transition_1_2;
  parameter.Modelica.SIunits.Temperature T_lwt=280.15;
  parameter.Modelica.SIunits.Temperature T_oat=298.15;
  parameter Real freq_compressor=115;
  parameter Real target_cap=50000;
  parameter Real current_cap=50000;
  Real FreqTransition_1_2;
  Boolean Module_2_ON;
equation
  (FreqTransition_1_2,Module_2_ON)=Freq_Cooling_Transition_1_2(
    T_lwt,
    T_oat,
    freq_compressor,
    target_cap,
    current_cap,
    4);
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          fillColor={144,19,254},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name"),
        Text(
          textString="TEST",
          origin={0,44},
          extent={{74,-26},{-74,26}})}));
end Freq_Cooling_Transition_1_2_test;
