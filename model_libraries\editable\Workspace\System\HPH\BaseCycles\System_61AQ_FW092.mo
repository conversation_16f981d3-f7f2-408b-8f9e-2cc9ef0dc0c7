within Workspace.System.HPH.BaseCycles;
model System_61AQ_FW092
  extends.Workspace.Controller.CL_control_system_heating_zenith(
    controllerSettings_crkA(
      Capacity_setpoint=TargetCapacityA,
      SST_min=243.15,
      extPressure_setpoint=ECAT.ExternalSystemPressureDrop_Pa.setPoint,
      coefficients=Module.fanCurveCoefficientsHeating[1,:],
      minfanfreq=10,
      maxfanfreq=Module.max_fan_frequency[1]),
    controllerSettings_crkB(
      Capacity_setpoint=TargetCapacityB,
      SST_min=243.15,
      extPressure_setpoint=ECAT.ExternalSystemPressureDrop_Pa.setPoint,
      coefficients=Module.fanCurveCoefficientsHeating[2,:],
      minfanfreq=10,
      maxfanfreq=Module.max_fan_frequency[2]),
    controller_crkA(
      isOff=isOFFA,
      isOffDGTmax_fan=false,
      isOffSSTmin_EXV=false,
      isOffSSTmax_EXV=false,
      isOffDSHmin_EXV=false,
      isOffDGTmax_EXV=false,
      isOffSSTmin_comp=false,
      isOffSDTmax_comp=false,
      isOffDGTmax_comp=false,
      Fan_MaxFrequency=controllerSettings_crkA.maxfanfreq,
      completeCompressorControl_base(
        compressorControl(
          manualOff=controller_crkA.manualOff_compressor_block_A,
          AV_value_off=controller_crkA.frq_comp_sp_manual_block_A)),
      manualOff_fan_block_A=false,
      manualOff_fan_block_B=false,
      manualOff_compressor_block_A=false,
      manualOff_compressor_block_B=false,
      fanControl(
        manualOff=controller_crkA.manualOff_fan_block_A,
        AV_value_off=controller_crkA.frq_fan_sp_manual_block_A),
      Fan_MinFrequency=controllerSettings_crkA.minfanfreq,
      min_speed=Module.fmin[1],
      max_speed=Module.fmax[1],
      isOffSSTmax_fan=false,
      isOffSSTmin_fan=false,
      load_ratio=ECAT.LoadRatio_nd.setPoint,
      is_load_ratio=ECAT.LoadRatio_nd.fixed),
    controller_crkB(
      isOff=Module.is_monobloc or isOFFB,
      isOffDGTmax_fan=false,
      isOffSSTmin_EXV=false,
      isOffSSTmax_EXV=false,
      isOffDSHmin_EXV=false,
      isOffDGTmax_EXV=false,
      isOffSSTmin_comp=false,
      isOffSDTmax_comp=false,
      isOffDGTmax_comp=false,
      Fan_MaxFrequency=controllerSettings_crkB.maxfanfreq,
      fanControl(
        manualOff=controller_crkB.manualOff_compressor_block_B,
        AV_value_off=controller_crkB.frq_fan_sp_manual_block_B),
      completeCompressorControl_base(
        compressorControl(
          manualOff=controller_crkB.manualOff_compressor_block_B,
          AV_value_off=controller_crkB.frq_comp_sp_manual_block_B)),
      manualOff_fan_block_A=false,
      manualOff_fan_block_B=false,
      manualOff_compressor_block_A=false,
      manualOff_compressor_block_B=false,
      Fan_MinFrequency=controllerSettings_crkB.minfanfreq,
      min_speed=Module.fmin[2],
      max_speed=Module.fmax[2],
      isOffSSTmax_fan=false,
      isOffSSTmin_fan=false,
      load_ratio=ECAT.LoadRatio_nd.setPoint,
      is_load_ratio=ECAT.LoadRatio_nd.fixed));
  .Workspace.System.HPH.BaseCycles.Equipement_FW092 Module(
    CoolantMedium=CoolantMedium,
    isOFFB=isOFFB,
    isOFFA=isOFFA,
    OAT=OAT,
    LWT=LWT,
    EWT=EWT,
    Use_pump=choiceBlock.is_Pump,
    capacity_design={choiceBlock.Unit_Block_A.capacity_design,choiceBlock.Unit_Block_B.capacity_design},
    fmax={choiceBlock.Unit_Block_A.fmax,choiceBlock.Unit_Block_B.fmax},
    fmin={choiceBlock.Unit_Block_A.fmin,choiceBlock.Unit_Block_B.fmin},
    Dport_coolant_a={choiceBlock.Unit_Block_A.cond_diameter_brine_port_in,choiceBlock.Unit_Block_B.cond_diameter_brine_port_in},
    Dport_coolant_b={choiceBlock.Unit_Block_A.cond_diameter_brine_port_out,choiceBlock.Unit_Block_B.cond_diameter_brine_port_out},
    Dport_ref_a={choiceBlock.Unit_Block_A.cond_diameter_ref_port_in,choiceBlock.Unit_Block_B.cond_diameter_ref_port_in},
    Dport_ref_b={choiceBlock.Unit_Block_A.cond_diameter_ref_port_out,choiceBlock.Unit_Block_B.cond_diameter_ref_port_out},
    nPlate={choiceBlock.Unit_Block_A.nplate,choiceBlock.Unit_Block_B.nplate},
    nCoils={choiceBlock.Unit_Block_A.nCoils,choiceBlock.Unit_Block_B.nCoils},
    Itube={choiceBlock.Unit_Block_A.Itube,choiceBlock.Unit_Block_B.Itube},
    nCir={choiceBlock.Unit_Block_A.nCir,choiceBlock.Unit_Block_B.nCir},
    Ntube={choiceBlock.Unit_Block_A.Ntube,choiceBlock.Unit_Block_B.Ntube},
    Nrow={choiceBlock.Unit_Block_A.Nrow,choiceBlock.Unit_Block_B.Nrow},
    Ltube={choiceBlock.Unit_Block_A.Ltube,choiceBlock.Unit_Block_B.Ltube},
    Dotube={choiceBlock.Unit_Block_A.Dotube,choiceBlock.Unit_Block_B.Dotube},
    Ttube={choiceBlock.Unit_Block_A.Ttube,choiceBlock.Unit_Block_B.Ttube},
    Ptube={choiceBlock.Unit_Block_A.Ptube,choiceBlock.Unit_Block_B.Ptube},
    Prow={choiceBlock.Unit_Block_A.Prow,choiceBlock.Unit_Block_B.Prow},
    Dfin={choiceBlock.Unit_Block_A.Dfin,choiceBlock.Unit_Block_B.Dfin},
    Tfin={choiceBlock.Unit_Block_A.Tfin,choiceBlock.Unit_Block_B.Tfin},
    Fw_fan={choiceBlock.Unit_Block_A.Fan_FW,choiceBlock.Unit_Block_B.Fan_FW},
    max_fan_frequency={choiceBlock.Unit_Block_A.max_fan_frequency,choiceBlock.Unit_Block_B.max_fan_frequency},
    Suction_line_diameter={choiceBlock.Unit_Block_A.Suction_line_diameter,choiceBlock.Unit_Block_B.Suction_line_diameter},
    Suction_line_length={choiceBlock.Unit_Block_A.Suction_line_length,choiceBlock.Unit_Block_B.Suction_line_length},
    coil_line_diameter={choiceBlock.Unit_Block_A.Coil_line_diameter,choiceBlock.Unit_Block_B.Coil_line_diameter},
    coil_line_length={choiceBlock.Unit_Block_A.Coil_line_length,choiceBlock.Unit_Block_B.Coil_line_length},
    liquid_line_diameter={choiceBlock.Unit_Block_A.Liquid_line_diameter,choiceBlock.Unit_Block_B.Liquid_line_diameter},
    liquid_line_length={choiceBlock.Unit_Block_A.Liquid_line_length,choiceBlock.Unit_Block_B.Liquid_line_length},
    discharge_line_diameter={choiceBlock.Unit_Block_A.discharge_line_diameter,choiceBlock.Unit_Block_B.discharge_line_diameter},
    discharge_line_length={choiceBlock.Unit_Block_A.discharge_line_length,choiceBlock.Unit_Block_B.discharge_line_length},
    EXV_in_line_diameter={choiceBlock.Unit_Block_A.EXV_in_line_diameter,choiceBlock.Unit_Block_B.EXV_in_line_diameter},
    EXV_in_line_length={choiceBlock.Unit_Block_A.EXV_in_line_length,choiceBlock.Unit_Block_B.EXV_in_line_length},
    Ac_duct={choiceBlock.Unit_Block_A.duct_Ac,choiceBlock.Unit_Block_B.duct_Ac},
    Ka_duct={choiceBlock.Unit_Block_A.duct_Ka,choiceBlock.Unit_Block_B.duct_Ka},
    UA_duct={choiceBlock.Unit_Block_A.duct_UA,choiceBlock.Unit_Block_B.duct_UA},
    selector_Comp={choiceBlock.Unit_Block_A.selector_Comp,choiceBlock.Unit_Block_B.selector_Comp},
    EXV_main={choiceBlock.Unit_Block_A.EXV_main_A,choiceBlock.Unit_Block_B.EXV_main_A},
    selector_geo_BPHE={choiceBlock.Unit_Block_A.cond_select_geo,choiceBlock.Unit_Block_B.cond_select_geo},
    selector_pump={choiceBlock.Unit_Block_A.Pump_type,choiceBlock.Unit_Block_B.Pump_type},
    fanCurveCoefficientsHeating={choiceBlock.Unit_Block_A.fanCurveCoefficientsHeating,choiceBlock.Unit_Block_B.fanCurveCoefficientsHeating},
    CompVoltage={choiceBlock.Unit_Block_A.CompVoltage,choiceBlock.Unit_Block_B.CompVoltage},
    is_monobloc=choiceBlock.is_monobloc,
    fanCurveCoefficientsCooling={choiceBlock.Unit_Block_A.fanCurveCoefficientsCooling,choiceBlock.Unit_Block_B.fanCurveCoefficientsCooling},
    Use_EN=Use_EN14511,
    use_bf=use_bf,
    BrineConcentration=BrineConcentration,
    mdot_start=Module.capacity_design[1]/((((4180*(Module.LWT-Module.EWT))))),
    isCoating=choiceBlock.isCoatingOption,
    CondFoulingFactor=ECAT.CondFoulingFactor_m2KW.setPoint,
    relative_humidity=ECAT.AmbientAirRH_nd.setPoint,
    BlockA(
      node_EXV_in(
        dTsh_fixed=not ECAT.RefrigerantCharge_kg[1].fixed)),
    BlockB(
      node_EXV_in(
        dTsh_fixed=not ECAT.RefrigerantCharge_kg[2].fixed)),
    PDC_4WV={choiceBlock.Unit_Block_A.PDC_4WV,choiceBlock.Unit_Block_B.PDC_4WV},
    Zevap_coated_HPH={choiceBlock.Unit_Block_A.Zevap_coated_chaud,choiceBlock.Unit_Block_B.Zevap_coated_chaud},
    Zflow_intercept={choiceBlock.Unit_Block_A.Zflow_intercept,choiceBlock.Unit_Block_B.Zflow_intercept},
    Zflow_SST={choiceBlock.Unit_Block_A.Zflow_SST,choiceBlock.Unit_Block_B.Zflow_SST},
    Zflow_SDT={choiceBlock.Unit_Block_A.Zflow_SDT,choiceBlock.Unit_Block_B.Zflow_SDT},
    Zflow_heatcap={choiceBlock.Unit_Block_A.Zflow_Heatcap,choiceBlock.Unit_Block_B.Zflow_Heatcap},
    Zpower_intercept={choiceBlock.Unit_Block_A.Zpower_intercept,choiceBlock.Unit_Block_B.Zpower_intercept},
    Zpower_DGT={choiceBlock.Unit_Block_A.Zpower_DGT,choiceBlock.Unit_Block_B.Zpower_DGT},
    Zpower_Heatcap={choiceBlock.Unit_Block_A.Zpower_heatcap,choiceBlock.Unit_Block_B.Zpower_heatcap},
    Zpower_SST={choiceBlock.Unit_Block_A.Zpower_SST,choiceBlock.Unit_Block_B.Zpower_SST},
    Zpower_SDT={choiceBlock.Unit_Block_A.Zpower_SDT,choiceBlock.Unit_Block_B.Zpower_SDT},
    FW={choiceBlock.Unit_Block_A.FW,choiceBlock.Unit_Block_B.FW},
    use_Calib=use_Calib,
    Zevap_HPH_cst={choiceBlock.Unit_Block_A.Zevap_chaud_intercept,choiceBlock.Unit_Block_B.Zevap_chaud_intercept},
    Zevap_HPH_SST={choiceBlock.Unit_Block_A.Zevap_chaud_SST,choiceBlock.Unit_Block_B.Zevap_chaud_SST},
    Zevap_HPH_X={choiceBlock.Unit_Block_A.Zevap_chaud_X,choiceBlock.Unit_Block_B.Zevap_chaud_X},
    Zevap_HPC_cst={choiceBlock.Unit_Block_A.Zevap_froid_intercept,choiceBlock.Unit_Block_B.Zevap_froid_intercept},
    Zevap_HPC_X={choiceBlock.Unit_Block_A.Zevap_froid_X,choiceBlock.Unit_Block_B.Zevap_froid_X},
    Zevap_HPC_SST={choiceBlock.Unit_Block_A.Zevap_froid_SST,choiceBlock.Unit_Block_B.Zevap_froid_SST},
    Zcond_HPH_cst={choiceBlock.Unit_Block_A.Zcond_chaud_intercept,choiceBlock.Unit_Block_B.Zcond_chaud_intercept},
    Zcond_HPH_heatcap={choiceBlock.Unit_Block_A.Zcond_chaud_Heatcap,choiceBlock.Unit_Block_B.Zcond_chaud_Heatcap},
    Zcond_HPH_SST={choiceBlock.Unit_Block_A.Zcond_chaud_SST,choiceBlock.Unit_Block_B.Zcond_chaud_SST},
    Zcond_HPH_SDT={choiceBlock.Unit_Block_A.Zcond_chaud_SDT,choiceBlock.Unit_Block_B.Zcond_chaud_SDT},
    Zcond_HPC_cst={choiceBlock.Unit_Block_A.Zcond_froid_intercept,choiceBlock.Unit_Block_B.Zcond_froid_intercept},
    Zcond_HPC_SDT={choiceBlock.Unit_Block_A.Zcond_froid_SDT,choiceBlock.Unit_Block_B.Zcond_froid_SDT},
    use_Calib=use_Calib,
    Use_defrost=use_defrost,
    Pdispo=ECAT.ExternalSystemPressureDrop_Pa.setPoint,
    SC_fixed={not ECAT.RefrigerantCharge_kg[1].fixed,not ECAT.RefrigerantCharge_kg[2].fixed},
    Mref_fixed={ECAT.RefrigerantCharge_kg[1].fixed,ECAT.RefrigerantCharge_kg[2].fixed},
    Mref={choiceBlock.Unit_Block_A.m_ref_chaud,choiceBlock.Unit_Block_B.m_ref_chaud},
    Heatcap_Tbiv=choiceBlock.Unit_Block_A.Heatcap_Tbiv,
    Elec_box_power=160,
    Zpower_Ncomp={choiceBlock.Unit_Block_A.Zpower_Ncomp,choiceBlock.Unit_Block_B.Zpower_Ncomp},
    Zpower_Heatcap={choiceBlock.Unit_Block_A.Zpower_heatcap,choiceBlock.Unit_Block_B.Zpower_heatcap})
    annotation (Placement(transformation(extent={{-24.0,6.0},{-4.0,26.0}},origin={0.0,0.0},rotation=0.0)));
  parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector CoolantMedium=ECAT.CondBrineType_nd
    "Coolant Medium Selection"
    annotation (Dialog(group="Medium"));
  final parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Empty coolantInf=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.getSelector(
    CoolantMedium)
    "Coolant Medium"
    annotation ();
  .BOLT.InternalLibrary.Media.Coolant.CoolantCommon.Temperature FreezTemp=.BOLT.InternalLibrary.Media.Coolant.CoolantCommon.freezeTemp(
    coolantInf,
    BrineConcentration,
    1);
  parameter Real BrineConcentration=ECAT.CondBrineConcentration_nd.setPoint
    annotation (Dialog(group="Medium"));
  .BOLT.BoundaryNode.Coolant.Source sourceBrine(
    Vd_fixed=ECAT.CondBrineFlowRate_m3s.fixed,
    T_set=EWT,
    p_fixed=true,
    CoolantMedium=CoolantMedium,
    T_fixed=ECAT.CondBrineEWT_K.fixed,
    Vd_set=ECAT.CondBrineFlowRate_m3s.setPoint,
    X=BrineConcentration,
    p_set=2e5)
    annotation (Placement(transformation(extent={{-6.546045643035146,-6.546045643035143},{6.546045643035146,6.546045643035143}},origin={-13.437406280789684,-34.84048750397556},rotation=90.0)));
  .BOLT.BoundaryNode.Coolant.Sink sinkBrine(
    p_fixed=true,
    T_fixed=ECAT.CondBrineLWT_K.fixed,
    T_set=LWT,
    CoolantMedium=CoolantMedium,
    p_set=sourceBrine.p_set,
    X=BrineConcentration)
    annotation (Placement(transformation(extent={{-5.468581959242897,-5.468581959242897},{5.468581959242897,5.468581959242897}},origin={-14.001588872523737,58.0140973920173},rotation=-90.0)));
  parameter.Modelica.SIunits.Temperature LWT=ECAT.CondBrineLWT_K.setPoint
    annotation (Dialog(group="Conditions"));
  parameter.Modelica.SIunits.Temperature EWT=ECAT.CondBrineEWT_K.setPoint
    annotation (Dialog(group="Conditions"));
  parameter.Modelica.SIunits.Temperature OAT=ECAT.HeatingAmbientAirDBTemp_K.setPoint
    annotation (Dialog(group="Conditions"));
  parameter.Modelica.SIunits.Power TargetCapacityA=
    if Module.is_monobloc then
      ECAT.TargetHeatingCapacity_W.setPoint
    else
      ECAT.TargetHeatingCapacity_W.setPoint/((2))
    annotation (Dialog(group="Cp_control"));
  parameter.Modelica.SIunits.Power TargetCapacityB=ECAT.TargetHeatingCapacity_W.setPoint/(2)
    annotation (Dialog(group="Cp_control"));
  parameter Boolean isOFFB=false
    annotation (Dialog(group="Cp_control"));
  parameter Boolean isOFFA=false
    annotation (Dialog(group="Cp_control"));
  parameter Boolean use_bf=true
    annotation (Dialog(group="Use Parameters"));
  parameter Boolean Use_EN14511=true
    annotation (Dialog(group="Use Parameters"));
  parameter Boolean use_Calib=true
    annotation (Dialog(group="Use Parameters"));
  parameter Boolean use_defrost=true
    annotation (Dialog(group="Use Parameters"));
  .Workspace.Auxiliary.ECAT_Zenith.ECATBase ECAT(
    CondCoilAirPressDrop_Pa(
      value={Module.BlockA.evapAir1_MVB.summary.dPa,Module.BlockB.evapAir1_MVB.summary.dPa}),
    RefrigerantSST_K(
      value={Module.BlockA.node_suction.Tsat,Module.BlockB.node_suction.Tsat}),
    RefrigerantSDT_K(
      value={Module.BlockA.node_discharge.Tsat,Module.BlockB.node_discharge.Tsat}),
    RefrigerantSET_K(
      value={Module.BlockA.node_evapout.Tsat,Module.BlockB.node_evapout.Tsat}),
    RefrigerantSCT_K(
      value={Module.BlockA.node_condin.Tsat,Module.BlockB.node_condin.Tsat}),
    RefrigerantDGT_K(
      value={Module.BlockA.node_discharge.T,Module.BlockB.node_discharge.T}),
    SuctionSuperheat_K(
      value={Module.BlockA.node_suction.dTsh,Module.BlockB.node_suction.dTsh}),
    CondSubcooling_K(
      value={Module.BlockA.node_EXV_in.dTsh,Module.BlockB.node_EXV_in.dTsh}),
    DischargeSuperheat_K(
      value={Module.BlockA.node_discharge.dTsh,Module.BlockB.node_discharge.dTsh}),
    CondFanAirflowRate_m3s(
      value={2*Module.BlockA.sourceAir.Vd_flow,2*Module.BlockB.sourceAir.Vd_flow}),
    CompressorFrequency_Hz(
      value={Module.BlockA.compressor.summary.Ncomp,Module.BlockB.compressor.summary.Ncomp},
      setPoint={Module.BlockA.compressor.summary.Ncomp,Module.BlockB.compressor.summary.Ncomp}),
    CompressorSpeed_rpm(
      value={Module.BlockA.compressor.summary.Ncomp*60,Module.BlockB.compressor.summary.Ncomp*60}),
    CompressorPower_W(
      value={Module.BlockA.compressor.summary.P_compression,Module.BlockB.compressor.summary.P_compression}),
    FanPower_W(
      value={Module.BlockA.motor.summary.power_VFD,Module.BlockB.motor.summary.power_VFD}),
    AmbientAirDBTemp_K(
      setPoint=308.15),
    EvapBrineConcentration_nd(
      setPoint=0.4),
    EvapFoulingFactor_m2KW(
      setPoint=0),
    EvapBrineLWT_K(
      setPoint=280.15,
      value=-1),
    EvapBrineEWT_K(
      setPoint=285.15,
      value=-1),
    EvapBrineFlowRate_m3s(
      value=-1),
    TotalRefrigerantCharge_kg(
      value=choiceBlock.Unit_Block_A.m_ref_chaud+choiceBlock.Unit_Block_B.m_ref_chaud),
    TotalOilCharge_kg(
      value=choiceBlock.Unit_Block_A.Oil_charge+choiceBlock.Unit_Block_B.Oil_charge),
    TotalCompressorPower_W(
      value=sum(
        ECAT.CompressorPower_W.value)),
    TotalFanPower_W(
      value=sum(
        ECAT.FanPower_W.value)),
    PubUnitPower_W(
      value=Module.controlledPower),
    ExternalSystemPressureDrop_Pa(
      value=8000,
      setPoint=50000),
    CondBrineLWT_K(
      value=sinkBrine.summary.T,
      setPoint=308.15),
    CondBrineEWT_K(
      value=sourceBrine.summary.T,
      setPoint=303.15),
    CondBrineFlowRate_m3s(
      value=sourceBrine.summary.Vd,
      setPoint=0.01,
      fixed=false),
    PubHeatingCapacity_W(
      value=Module.controlledCapacity),
    CondPumpPower_W(
      value=Module.pumpPolyA.summary.P_motor+Module.pumpPolyB.summary.P_motor),
    CondBrineIntPressDrop_Pa(
      value=Module.BlockA.condBPHE.summary.dp_coolant),
    CondBrineDensity_kgm3(
      value=1/(sourceBrine.summary.v)),
    HeatingAmbientAirDBTemp_K(
      setPoint=280.15),
    AmbientAirRH_nd(
      setPoint=0.87),
    TargetHeatingCapacity_W(
      setPoint=20000),
    CondFoulingFactor_m2KW(
      setPoint=0),
    CondPumpSpeed_rpm(
      value={Module.pumpPolyA.summary.speed,Module.pumpPolyB.summary.speed},
      setPoint=1800),
    EvapPumpSpeed_rpm(
      setPoint=1800,
      value={Module.pumpPolyA.summary.speed,Module.pumpPolyB.summary.speed}),
    RefrigerantCharge_kg(
      value={Module.BlockA.systemVariables.mRef[1],Module.BlockB.systemVariables.mRef[1]}),
    LoadRatio_nd(
      value=controller_crkA.completeCompressorControl_base.capacity_controller.summary.AV*100,
      fixed=false),
    HeatingAmbientAirWBTemp_K(
      value=Module.BlockA.sourceAir.summary.Twb),
    PubHeatingCapacityInstantaneous_W(
      value=Module.controlledCapacity),
    ElecFanBoxFrequency_Hz(
      value={50,50}),
    FanFrequency_Hz(
      value={Module.BlockA.motor.summary.Motor_freq,Module.BlockB.motor.summary.Motor_freq}),
    CondPumpSpeed_Hz(
      value={Module.pumpPolyA.pump.speed_Hz,Module.pumpPolyB.pump.speed_Hz}),
    EvapPumpSpeed_Hz(
      value={Module.pumpPolyA.pump.speed_Hz,Module.pumpPolyB.pump.speed_Hz}),
    FanSpeed_rpm(
      value={Module.BlockA.fanCurve.summary.speed,Module.BlockB.fanCurve.summary.speed}))
    annotation (Placement(transformation(extent={{-60.75411426282274,69.24588573717726},{-39.24588573717726,90.75411426282274}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Auxiliary.OptionBlock.ChoiceBlock choiceBlock(
    is_monobloc=false,
    Pump_selector=.Workspace.Auxiliary.OptionBlock.PumpOption.Pump_Option.STANDARD,
    Selector_Block_A=.Workspace.Auxiliary.OptionBlock.Record_Base.Selector.Unit_Z_070,
    Selector_Block_B=.Workspace.Auxiliary.OptionBlock.Record_Base.Selector.Unit_Z_070)
    annotation (Placement(transformation(extent={{-88.81109126920019,69.18890873079978},{-68.04244898298215,89.95755101701782}},origin={0.0,0.0},rotation=0.0)));
equation
  connect(Module.measurementBusA,controllerSettings_crkA.measurementBus)
    annotation (Line(points={{-22,26},{-22,40.08035972173846},{-70.29681143199207,40.08035972173846}},color={255,204,51}));
  connect(Module.measurementBusB,controllerSettings_crkB.measurementBus)
    annotation (Line(points={{-6,26},{-6,40.8407638560912},{40.53140745356192,40.8407638560912}},color={255,204,51}));
  connect(sourceBrine.port,Module.coolant_in)
    annotation (Line(points={{-13.437406280789682,-28.294441860940413},{-13.437406280789682,1.329405357454629},{-14.348008987101455,1.329405357454629}},color={0,127,0}));
  connect(Module.coolant_out,sinkBrine.port)
    annotation (Line(points={{-14.26003915644383,28.40600907992107},{-14.26003915644383,40.81600727674263},{-14.001588872523735,40.81600727674263},{-14.001588872523735,52.5455154327744}},color={0,127,0}));
  connect(controller_crkA.compressor,Module.Compressor_controller_A)
    annotation (Line(points={{-49,5},{-36.90843151447778,5},{-36.90843151447778,8.5},{-25,8.5}},color={0,0,127}));
  connect(controller_crkA.exv,Module.EXV_controller_A)
    annotation (Line(points={{-49,2.5},{-36.90843151447778,2.5},{-36.90843151447778,18.5},{-25,18.5}},color={0,0,127}));
  connect(controller_crkA.fan,Module.Fan_controller_A)
    annotation (Line(points={{-49,0},{-36.90843151447778,0},{-36.90843151447778,13.5},{-25,13.5}},color={0,0,127}));
  connect(controller_crkA.pump,Module.ActuatorPumpUser_A)
    annotation (Line(points={{-49,-2.5},{-36.90843151447778,-2.5},{-36.90843151447778,23},{-25,23}},color={0,0,127}));
  connect(controller_crkB.compressor,Module.Compressor_controller_B)
    annotation (Line(points={{22.672455613343857,5.519176064177298},{-3,5.519176064177298},{-3,8.5}},color={0,0,127}));
  connect(controller_crkB.exv,Module.EXV_controller_B)
    annotation (Line(points={{22.672455613343857,3.019176064177298},{9.927796292194149,3.019176064177298},{9.927796292194149,18.5},{-3,18.5}},color={0,0,127}));
  connect(controller_crkB.fan,Module.Fan_controller_B)
    annotation (Line(points={{22.672455613343857,0.519176064177298},{9.927796292194149,0.519176064177298},{9.927796292194149,13.5},{-3,13.5}},color={0,0,127}));
  connect(controller_crkB.pump,Module.ActuatorPumpUser_B)
    annotation (Line(points={{22.672455613343857,-1.980823935822702},{9.927796292194149,-1.980823935822702},{9.927796292194149,23},{-3,23}},color={0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          fillColor={229,152,23},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end System_61AQ_FW092;
