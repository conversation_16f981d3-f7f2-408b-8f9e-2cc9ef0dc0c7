within Workspace.Icons;
partial class SourcePackage
  extends Workspace.Icons.PackageIcon;
  annotation (
    Icon(
      graphics={
        Ellipse(
          extent={{-58,46},{50,-56}},
          lineColor={0,0,0},
          fillColor={255,0,0},
          fillPattern=FillPattern.Solid),
        Ellipse(
          extent={{-38,12},{-18,-8}},
          lineColor={255,255,255},
          fillColor={255,255,255},
          fillPattern=FillPattern.Solid),
        Ellipse(
          extent={{10,12},{30,-8}},
          lineColor={255,255,255},
          fillColor={255,255,255},
          fillPattern=FillPattern.Solid)}));
end SourcePackage;
