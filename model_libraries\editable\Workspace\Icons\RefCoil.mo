within Workspace.Icons;
model RefCoil
  "Icon for air to refrigerant coils"
  annotation (
    Icon(
      graphics={
        Bitmap(
          extent={{-100,-94},{96,64}},
          fileName="../UTCTSD2/Images/Coil.png"),
        Text(
          extent={{-72,94},{70,66}},
          lineColor={0,0,255},
          textString="%name")},
      Bitmap(
        extent=[
          -100,-94;
          96,64],
        name="../UTCTSD2/Images/Coil.png"),
      Text(
        extent=[
          -72,94;
          70,66],
        style(
          color=3,
          rgbcolor={0,0,255}),
        string="%name")),
    Documentation(
      revisions="<html>
</html>"));
end RefCoil;
