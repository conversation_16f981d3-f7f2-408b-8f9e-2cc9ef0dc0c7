within Workspace.Controller.Components.Tools;
model test_Abs
    .Workspace.Controller.Components.Tools.Abs_max abs_max annotation(Placement(transformation(extent = {{-14.355331758257545,26.80663615635925},{15.891722482154432,57.05369039677123}},origin = {0.0,0.0},rotation = 0.0)));
    .Workspace.Controller.Components.Tools.Abs_min abs_min annotation(Placement(transformation(extent = {{-12.915333853186487,-53.671893581599825},{14.451724577083374,-26.30483515132996}},origin = {0.0,0.0},rotation = 0.0)));
    .BOLT.InternalLibrary.Controls_v2.SteadyState.SetpointControl.ErrorCalculation errorCalculation(ID = 1,measurement = Measure_1,setPoint = 20) annotation(Placement(transformation(extent = {{-89.03782010690612,32.16488685147362},{-69.03782010690612,52.16488685147362}},origin = {0,0},rotation = 0)));
    .BOLT.InternalLibrary.Controls_v2.SteadyState.SetpointControl.ErrorCalculation errorCalculation2(ID = 2,measurement = Measure_2,setPoint = 25) annotation(Placement(transformation(extent = {{-90.21143798144813,-49.0494700668313},{-70.21143798144813,-29.049470066831297}},origin = {0.0,0.0},rotation = 0.0)));
    parameter Real Measure_1 = 23;
    parameter Real Measure_2 = 24;
    .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointController(AV_min = 30,AV_max = 60,AV_start = 30) annotation(Placement(transformation(extent = {{56.71812468758574,-20.50176225133806},{76.71812468758574,-0.5017622513380608}},origin = {0,0},rotation = 0)));
equation
    connect(errorCalculation.sensor,abs_max.u1) annotation(Line(points = {{-69.23782010690613,41.36488685147362},{-41.79657593258184,41.36488685147362},{-41.79657593258184,50.399338463880596},{-14.355331758257545,50.399338463880596}},color = {28,108,200}));
    connect(errorCalculation.sensor,abs_min.u1) annotation(Line(points = {{-69.23782010690613,41.36488685147362},{-41.076576980046305,41.36488685147362},{-41.076576980046305,-32.32558800598933},{-12.915333853186487,-32.32558800598933}},color = {28,108,200}));
    connect(errorCalculation2.sensor,abs_max.u2) annotation(Line(points = {{-70.41143798144813,-39.849470066831294},{-42.38338486985284,-39.849470066831294},{-42.38338486985284,32.85604700444164},{-14.355331758257545,32.85604700444164}},color = {28,108,200}));
    connect(errorCalculation2.sensor,abs_min.u2) annotation(Line(points = {{-70.41143798144813,-39.849470066831294},{-41.66338591731731,-39.849470066831294},{-41.66338591731731,-48.198481895545854},{-12.915333853186487,-48.198481895545854}},color = {28,108,200}));
    connect(abs_max.y,setpointController.errorSignal) annotation(Line(points = {{15.89172248215443,41.93016327656524},{36.30492358487009,41.93016327656524},{36.30492358487009,-10.50176225133806},{56.71812468758574,-10.50176225133806}},color = {28,108,200}));
    annotation(Icon(coordinateSystem(preserveAspectRatio = false,extent = {{-100.0,-100.0},{100.0,100.0}}),graphics = {Rectangle(lineColor={0,0,0},fillColor={230,230,230},fillPattern=FillPattern.Solid,extent={{-100.0,-100.0},{100.0,100.0}}),Text(lineColor={0,0,255},extent={{-150,150},{150,110}},textString="%name")}));
end test_Abs;
