within Workspace.Controller;
model ControllerSettings_heating
  extends.BOLT.InternalLibrary.BuildingBlocks.Icons.CtrlSettings;
  // Import records
  //import .Workspace.Controller.Components.Records.fanCurveCoefficients;
  import Elec_Fan_Speed_Box=Workspace.Controller.Components.Functions.Elec_Fan_Speed_Box;
  import fanspeed=Workspace.Controller.Components.Functions.fanSpeed;
  Real fanspeed_sp;
  Real FanSpeedBox;
  //speed of electrical fan box
  parameter Real[9] coefficients={0.198957,5.398716,-0.010786,0.00956,0.046429,0.033962,0.01804,0.010765,152.821278};
  parameter Real minfanfreq=10;
  parameter Real maxfanfreq=720;
  // Import functions
  //import BOLT.InternalLibrary.BuildingBlocks.Utilities.Math.Smoothing.softMin;
  //import BOLT.InternalLibrary.BuildingBlocks.Utilities.Math.Smoothing.softMax;
  //import .Workspace.Controller.Components.Functions.subcooling;
  //import .Workspace.Controller.Components.Functions.fanSpeed;
  .Modelica.Blocks.Sources.RealExpression T_sst_max_limit_block(
    y=SST_max-1)
    annotation (Placement(transformation(extent={{8.960344849242773,58.0},{39.03965515075723,78.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_dgt_max_limit_comp_block(
    y=DGT_max-5)
    annotation (Placement(transformation(extent={{9.635157797414543,26.38886596498643},{39.714468098929,46.96075993135711}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression Freq_max_SDT_limit_comp_block(
    y=Table_Freq_max_SDT.y)
    annotation (Placement(transformation(extent={{6.841474359968359,-5.469417775666013},{36.92078466148281,14.530582224333987}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_sdt_min_limit_block(
    y=Table_SDT_min.y[1]+3)
    annotation (Placement(transformation(extent={{6.960344849242773,-40.0},{37.03965515075723,-20.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression capacity_setpoint_block(
    y=Capacity_setpoint)
    annotation (Placement(transformation(extent={{8.037267926165839,-95.86538461538461},{38.11657822768029,-75.86538461538461}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression fanSpeed_setpoint_block(
    y=fanspeed_sp)
    annotation (Placement(transformation(extent={{8.285292689110758,-109.86538461538461},{38.364602990625215,-89.86538461538461}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_sst_min_limit_comp_block(
    y=SST_min)
    annotation (Placement(transformation(extent={{9.74358094275302,42.27481968193342},{39.822891244267474,62.27481968193342}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_dgt_max_limit_fan_block(
    y=DGT_max-10)
    annotation (Placement(transformation(extent={{8.960344849242773,9.738921302163263},{39.03965515075723,29.738921302163263}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.MeasurementBus measurementBus
    annotation (Placement(transformation(extent={{-132.0,-10.0},{-92.0,30.0}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.LimitsBus limitsBus
    annotation (Placement(transformation(extent={{67.81944660314939,-50.208163989221056},{107.81944660314939,-10.208163989221056}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Tables.CombiTable2D Table_Freq_max_SDT(
    table={{0,333.15,338.15,343.15,349.15,355.15},{243.15,140,140,140,140,140},{258.15,140,140,140,130,120},{268.15,140,140,140,130,120},{273.15,140,130,120,110,100},{283.15,140,130,120,110,100},{288.15,120,120,120,100,100},{293.15,100,100,100,100,100},{298.15,100,100,100,100,100}},
    extrapolation=Modelica.Blocks.Types.Extrapolation.HoldLastPoint)
    annotation (Placement(transformation(extent={{-44.0,-84.0},{-24.0,-64.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Tables.CombiTable1D Table_freq_min_SST(
    table={{243.15,30},{283.15,30},{288.15,50},{298.15,50}})
    annotation (Placement(transformation(extent={{-44.0,-174.66666666666669},{-24.0,-154.66666666666669}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Tables.CombiTable1D Table_freq_max_SST(
    table={{243.15,140},{283.15,140},{288.15,120},{293.15,100},{298.15,100}})
    annotation (Placement(transformation(extent={{-44.0,-142.66666666666669},{-24.0,-122.66666666666669}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Tables.CombiTable1D Table_SDT_min(
    table={{243.15,283.15},{273.15,283.15},{298.15,308.15}})
    annotation (Placement(transformation(extent={{-44.0,-58.0},{-24.0,-38.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression dT_ssh_setpoint_block(
    y=
      if is_Manual_SSH then
        Manual_SSH
      else
        Table_ssh_setpoint.y[1])
    annotation (Placement(transformation(extent={{8.037267926165839,-68.61538461538461},{38.11657822768029,-48.61538461538461}},origin={0.0,0.0},rotation=0.0)));
  parameter.Modelica.SIunits.Temperature SST_min=244.15;
  parameter.Modelica.SIunits.Temperature DGT_max=423.15;
  .Modelica.Blocks.Tables.CombiTable1D Table_ssh_setpoint(
    table={{243.15,5},{283.15,5},{288.15,10},{298.15,10}})
    annotation (Placement(transformation(extent={{-44.0,-3.3333333333333357},{-24.0,16.666666666666664}},origin={0.0,0.0},rotation=0.0)));
  parameter.Modelica.SIunits.Power Capacity_setpoint=50000;
  .Modelica.Blocks.Sources.RealExpression Freq_mini(
    y=Table_freq_min_SST.y[1])
    annotation (Placement(transformation(extent={{7.430550864047692,-124.91676121952226},{37.50986116556214,-104.91676121952226}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression Freq_maxi(
    y=Table_freq_max_SST.y[1])
    annotation (Placement(transformation(extent={{6.960344849242773,-140.0},{37.03965515075723,-120.0}},origin={0.0,0.0},rotation=0.0)));
  parameter.Modelica.SIunits.Temperature SST_max=298.15;
  .Modelica.Blocks.Sources.RealExpression T_sst_max_limit_fan_block(
    y=SST_max_fan)
    annotation (Placement(transformation(extent={{6.960344849242773,-54.0},{37.03965515075723,-34.0}},origin={0.0,0.0},rotation=0.0)));
  parameter.Modelica.SIunits.Temperature SST_max_fan=288.15;
  .Modelica.Blocks.Sources.RealExpression T_sst_min_limit_fan_block(
    y=SST_min_limit_fan)
    annotation (Placement(transformation(extent={{7.784546010441197,-82.30638719450684},{37.86385631195565,-62.30638719450684}},origin={0.0,0.0},rotation=0.0)));
  parameter.Modelica.SIunits.Temperature SST_min_limit_fan=269.15;
  .Modelica.Blocks.Tables.CombiTable2D Table_Freq_min_SDT(
    table={{0,333.15,338.15,343.15,349.15,355.15},{243.15,30,30,30,30,30},{258.15,30,30,30,30,30},{268.15,30,30,30,30,30},{273.15,30,40,50,50,50},{283.15,30,40,50,50,50},{288.15,50,50,50,50,50},{293.15,50,50,50,50,50},{298.15,50,50,50,50,50}},
    extrapolation=Modelica.Blocks.Types.Extrapolation.HoldLastPoint)
    annotation (Placement(transformation(extent={{-44.0,-114.66666666666669},{-24.0,-94.66666666666669}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression Freq_min_SDT_limit_comp_block(
    y=Table_Freq_min_SDT.y)
    annotation (Placement(transformation(extent={{8.078410573123662,-22.167588788192},{38.157720874638116,-2.167588788191999}},origin={0.0,0.0},rotation=0.0)));
  parameter Modelica.SIunits.Pressure extPressure_setpoint=80000;
  parameter Modelica.SIunits.Temperature LWT_setpoint=308.15;
  parameter Modelica.SIunits.Temperature EWT_setpoint=306.15;
  .Modelica.Blocks.Sources.RealExpression extPressure_SetPoint(
    y=extPressure_setpoint)
    annotation (Placement(transformation(extent={{6.960344849242773,-160.0},{37.03965515075723,-140.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Tables.CombiTable1D Table_SDT_max(
    table={{243.15,333.15},{258.15,355.15},{288.15,355.15},{298.15,343.15}})
    annotation (Placement(transformation(extent={{-44,-32},{-24,-12}},origin={0,0},rotation=0)));
  .Modelica.Blocks.Sources.RealExpression T_sdt_max_limit_comp_block(
    y=Table_SDT_max.y[1])
    annotation (Placement(transformation(extent={{6.8533522714083155,-178.12424840827634},{37.82146067676348,-154.66030470248631}},origin={0.0,0.0},rotation=0.0)));
  parameter Boolean is_Manual_SSH=false;
  parameter Real Manual_SSH=5;
  .Modelica.Blocks.Sources.RealExpression LWT_SetPoint(
    y=LWT_setpoint)
    annotation (Placement(transformation(extent={{7.203510429972454,-192.23954947075464},{37.28282073148691,-172.23954947075464}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression EWT_SetPoint(
    y=EWT_setpoint)
    annotation (Placement(transformation(extent={{5.806956274878807,-207.3456254105125},{35.88626657639326,-187.3456254105125}},origin={0.0,0.0},rotation=0.0)));
protected
  .Modelica.Blocks.Interfaces.RealOutput T_lwt
    annotation (Placement(transformation(extent={{-91.04054495960074,22.944307605824214},{-51.04054495960074,62.944307605824214}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput T_oat
    annotation (Placement(transformation(extent={{-91.37584785966354,51.780357011223415},{-51.37584785966354,91.78035701122342}},origin={0,0},rotation=0)));
  .Modelica.Blocks.Interfaces.RealOutput Cp_frequency
    annotation (Placement(transformation(extent={{-93.05236235997744,78.26928611618315},{-53.05236235997744,118.26928611618315}},origin={0.0,0.0},rotation=0.0)));
equation
  connect(T_sst_min_limit_comp_block.y,limitsBus.T_sst_min_limit_comp)
    annotation (Line(points={{41.3268567593432,52.27481968193342},{87.81944660314939,52.27481968193342},{87.81944660314939,-30.208163989221056}},color={0,0,127}));
  connect(T_dgt_max_limit_comp_block.y,limitsBus.T_dgt_max_limit_comp)
    annotation (Line(points={{41.218433614004724,36.67481294817177},{87.81944660314939,36.67481294817177},{87.81944660314939,-30.208163989221056}},color={0,0,127}));
  connect(T_dgt_max_limit_fan_block.y,limitsBus.T_dgt_max_limit_fan)
    annotation (Line(points={{40.54362066583295,19.738921302163263},{87.81944660314939,19.738921302163263},{87.81944660314939,-30.208163989221056}},color={0,0,127}));
  connect(T_sdt_min_limit_block.y,limitsBus.T_sdt_min_limit)
    annotation (Line(points={{38.54362066583295,-30},{87.81944660314939,-30},{87.81944660314939,-30.208163989221056}},color={0,0,127}));
  connect(capacity_setpoint_block.y,limitsBus.capacity_setpoint)
    annotation (Line(points={{39.62054374275601,-85.86538461538461},{87.81944660314939,-85.86538461538461},{87.81944660314939,-30.208163989221056}},color={0,0,127}));
  connect(measurementBus.T_sst,Table_SDT_min.u[1])
    annotation (Line(points={{-112,10},{-80,10},{-80,-48},{-46,-48}},color={255,204,51}));
  connect(measurementBus.T_sst,Table_ssh_setpoint.u[1])
    annotation (Line(points={{-112,10},{-46,10},{-46,6.666666666666664}},color={255,204,51}));
  connect(fanSpeed_setpoint_block.y,limitsBus.fanSpeed_setpoint)
    annotation (Line(points={{39.868568505700935,-99.86538461538461},{87.81944660314939,-99.86538461538461},{87.81944660314939,-30.208163989221056}},color={0,0,127}));
  connect(dT_ssh_setpoint_block.y,limitsBus.dT_ssh_setpoint)
    annotation (Line(points={{39.62054374275601,-58.61538461538461},{87.81944660314939,-58.61538461538461},{87.81944660314939,-30.208163989221056}},color={0,0,127}));
  connect(Cp_frequency,measurementBus.compressorFrequency)
    annotation (Line(points={{-73.05236235997744,98.26928611618315},{-112,98.26928611618315},{-112,10}},color={0,0,127}));
  connect(T_oat,measurementBus.T_oat)
    annotation (Line(points={{-71.37584785966354,71.78035701122342},{-112,71.78035701122342},{-112,10}},color={0,0,127}));
  connect(T_lwt,measurementBus.T_lwt)
    annotation (Line(points={{-71.04054495960074,42.944307605824214},{-112,42.944307605824214},{-112,10}},color={0,0,127}));
  FanSpeedBox=Elec_Fan_Speed_Box(
    T_oat);
  fanspeed_sp=fanspeed(
    Cp_frequency,
    T_lwt,
    T_oat,
    coefficients,
    minfanfreq,
    maxfanfreq);
  connect(Freq_mini.y,limitsBus.Min_frequency)
    annotation (Line(points={{39.01382668063786,-114.91676121952226},{87.81944660314939,-114.91676121952226},{87.81944660314939,-30.208163989221056}},color={0,0,127}));
  connect(Freq_maxi.y,limitsBus.Max_frequency)
    annotation (Line(points={{38.54362066583295,-130},{87.81944660314939,-130},{87.81944660314939,-30.208163989221056}},color={0,0,127}));
  connect(T_sst_max_limit_block.y,limitsBus.T_sst_max_limit_EXV)
    annotation (Line(points={{40.54362066583295,68},{87.81944660314939,68},{87.81944660314939,-30.208163989221056}},color={0,0,127}));
  connect(T_sst_max_limit_fan_block.y,limitsBus.T_sst_max_limit_fan)
    annotation (Line(points={{38.54362066583295,-44},{87.81944660314939,-44},{87.81944660314939,-30.208163989221056}},color={0,0,127}));
  connect(T_sst_min_limit_fan_block.y,limitsBus.T_sst_min_limit_fan)
    annotation (Line(points={{39.36782182703138,-72.30638719450684},{87.81944660314939,-72.30638719450684},{87.81944660314939,-30.208163989221056}},color={0,0,127}));
  connect(measurementBus.T_sst,Table_Freq_max_SDT.u1)
    annotation (Line(points={{-112,10},{-80,10},{-80,-68},{-46,-68}},color={255,204,51}));
  connect(measurementBus.T_sdt,Table_Freq_max_SDT.u2)
    annotation (Line(points={{-112,10},{-80,10},{-80,-80},{-46,-80}},color={255,204,51}));
  connect(measurementBus.T_sst,Table_Freq_min_SDT.u1)
    annotation (Line(points={{-112,10},{-80,10},{-80,-98.66666666666669},{-46,-98.66666666666669}},color={255,204,51}));
  connect(measurementBus.T_sdt,Table_Freq_min_SDT.u2)
    annotation (Line(points={{-112,10},{-80,10},{-80,-110.66666666666669},{-46,-110.66666666666669}},color={255,204,51}));
  connect(Freq_min_SDT_limit_comp_block.y,limitsBus.Freq_min_SDT_limit_comp)
    annotation (Line(points={{39.661686389713836,-12.167588788191999},{87.81944660314939,-12.167588788191999},{87.81944660314939,-30.208163989221056}},color={0,0,127}));
  connect(extPressure_SetPoint.y,limitsBus.extPressure_SetPoint)
    annotation (Line(points={{38.54362066583295,-150},{87.81944660314939,-150},{87.81944660314939,-30.208163989221056}},color={0,0,127}));
  connect(measurementBus.T_sst,Table_SDT_max.u[1])
    annotation (Line(points={{-112,10},{-80,10},{-80,-22},{-46,-22}},color={255,204,51}));
  connect(Freq_max_SDT_limit_comp_block.y,limitsBus.Freq_max_SDT_limit_comp)
    annotation (Line(points={{38.42475017655853,4.530582224333987},{87.81944660314939,4.530582224333987},{87.81944660314939,-30.208163989221056}},color={0,0,127}));
  connect(T_sdt_max_limit_comp_block.y,limitsBus.T_sdt_max_limit_comp)
    annotation (Line(points={{39.369866097031235,-166.39227655538133},{87.81944660314939,-166.39227655538133},{87.81944660314939,-30.208163989221056}},color={0,0,127}));
  connect(measurementBus.T_sst,Table_freq_max_SST.u[1])
    annotation (Line(points={{-112,10},{-80.20011246049992,10},{-80.20011246049992,-132.66666666666669},{-46,-132.66666666666669}},color={255,204,51}));
  connect(measurementBus.T_sst,Table_freq_min_SST.u[1])
    annotation (Line(points={{-112,10},{-80,10},{-80,-164.66666666666669},{-46,-164.66666666666669}},color={255,204,51}));
  connect(LWT_SetPoint.y,limitsBus.LWT_setpoint)
    annotation (Line(points={{38.78678624656263,-182.23954947075464},{87.81944660314939,-182.23954947075464},{87.81944660314939,-30.208163989221056}},color={0,0,127}));
  connect(EWT_SetPoint.y,limitsBus.EWT_setpoint)
    annotation (Line(points={{37.39023209146899,-197.3456254105125},{87.81944660314939,-197.3456254105125},{87.81944660314939,-30.208163989221056}},color={0,0,127}));
  annotation (
    Placement(
      transformation(
        extent={{-80,-80},{-60,-60}})));
end ControllerSettings_heating;
