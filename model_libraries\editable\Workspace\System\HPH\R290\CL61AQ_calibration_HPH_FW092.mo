within Workspace.System.HPH.R290;
model CL61AQ_calibration_HPH_FW092
  extends.Workspace.System.HPH.BaseCycles.System_61AQ_FW092(
    Module(
      BlockA(
        sourceAir(
          RH_fixed=false,
          Twb_fixed=true,
          Twb_set=OAT_WB)),
      BlockB(
        sourceAir(
          RH_fixed=false,
          Twb_fixed=true,
          Twb_set=OAT_WB))),
    controllerSettings_crkA(
      Manual_SSH=Module.BlockA.node_suction.dTsh_set,
      is_Manual_SSH=true),
    controller_crkA(
      manualOff_fan_block_A=true,
      manualOff_compressor_block_A=true),
    choiceBlock(
      Selector_Block_B=Workspace.Auxiliary.OptionBlock.Record_Base.Selector.Unit_Z_060,
      Selector_Block_A=Workspace.Auxiliary.OptionBlock.Record_Base.Selector.Unit_Z_060),
    use_Calib=false,
    ECAT(
      CondBrineEWT_K(
        setPoint=303.15),
      CondBrineLWT_K(
        setPoint=308.15),
      TargetHeatingCapacity_W(
        setPoint=20000),
      HeatingAmbientAirDBTemp_K(
        setPoint=280.15)),
    use_bf=false,
    Use_EN14511=false,
    use_defrost=false);
  parameter Real OAT_WB=279.15;
end CL61AQ_calibration_HPH_FW092;
