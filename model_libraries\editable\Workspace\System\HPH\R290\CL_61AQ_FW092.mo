within Workspace.System.HPH.R290;
model CL_61AQ_FW092
  extends.Workspace.System.HPH.BaseCycles.System_61AQ_FW092(
    choiceBlock(
      is_monobloc=false,
      Selector_Block_A=Workspace.Auxiliary.OptionBlock.Record_Base.Selector.Unit_Z_060,
      Selector_Block_B=Workspace.Auxiliary.OptionBlock.Record_Base.Selector.Unit_Z_060,
      Pump_selector=Workspace.Auxiliary.OptionBlock.PumpOption.Pump_Option.STANDARD),
    ECAT(
      LoadRatio_nd(
        setPoint=100),
      CondBrineLWT_K(
        setPoint=308.15),
      CondBrineEWT_K(
        setPoint=303.15),
      TargetHeatingCapacity_W(
        setPoint=120000),
      AmbientAirRH_nd(
        setPoint=0.87),
      HeatingAmbientAirDBTemp_K(
        setPoint=275.15),
      RefrigerantCharge_kg(
        fixed={true,true},
        setPoint={2.1,2.1})),
    use_bf=false,
    Use_EN14511=true,
    use_Calib=true,
    use_defrost=true);
end CL_61AQ_FW092;
