within Workspace.Icons;
partial class Fan
  extends Workspace.Icons.PackageIcon;
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100,-100},{100,100}}),
      graphics={
        Polygon(
          points={{-60,16},{-60,-24},{0,-4},{-60,16}},
          smooth=Smooth.None,
          fillColor={255,128,0},
          fillPattern=FillPattern.Solid,
          lineColor={0,0,0}),
        Polygon(
          points={{60,16},{60,-24},{0,-4},{60,16}},
          lineColor={0,0,0},
          smooth=Smooth.None,
          fillColor={255,255,0},
          fillPattern=FillPattern.Solid),
        Polygon(
          points={{-20,-64},{20,-64},{0,-4},{-20,-64}},
          lineColor={0,0,0},
          smooth=Smooth.None,
          fillColor={255,170,85},
          fillPattern=FillPattern.Solid),
        Polygon(
          points={{-20,56},{20,56},{0,-4},{-20,56}},
          lineColor={0,0,0},
          smooth=Smooth.None,
          fillColor={255,0,0},
          fillPattern=FillPattern.Solid)}));
end Fan;
