within Workspace.Auxiliary.OptionBlock;
model ChoiceBlock_HPH
  parameter Workspace.Auxiliary.OptionBlock.BufferTankOption.BufferTank_selector BufferTank_selector=Workspace.Auxiliary.OptionBlock.BufferTankOption.BufferTank_selector.NONE
    annotation (choicesAllMatching=true,Evaluate=false);
  parameter Workspace.Auxiliary.OptionBlock.FilterOption.Filter_selector Filter_selector=Workspace.Auxiliary.OptionBlock.FilterOption.Filter_selector.NONE
    annotation (choicesAllMatching=true,Evaluate=false);
  parameter Workspace.Auxiliary.OptionBlock.PumpOption.Pump_Option Pump_Option=Workspace.Auxiliary.OptionBlock.PumpOption.Pump_Option.STANDARD
    annotation (choicesAllMatching=true,Evaluate=false);
  parameter Workspace.Auxiliary.OptionBlock.Record_Base.Selector Selector=Workspace.Auxiliary.OptionBlock.Record_Base.Selector.Unit_Z_040
    annotation (choicesAllMatching=true,Evaluate=false);
  final parameter Workspace.Auxiliary.OptionBlock.Record_Base.UnitBase_1C Unit=Workspace.Auxiliary.OptionBlock.Record_Base.getSelector(
    Selector);
  final parameter Boolean IsBufferTank=Workspace.Auxiliary.OptionBlock.BufferTankOption.getSelector_bool(
    BufferTank_selector);
  final parameter Boolean IsFilter=Workspace.Auxiliary.OptionBlock.FilterOption.getSelector_bool(
    Filter_selector);
  final parameter Boolean Use_pump=Workspace.Auxiliary.OptionBlock.PumpOption.getSelector(
    Pump_Option);
end ChoiceBlock_HPH;
