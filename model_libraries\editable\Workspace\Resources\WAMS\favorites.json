{"version": "2", "data": {"Workspace.System_ASHP_R290.HPC.BiBloc.BaseCycles.OL_modular": {}, "Workspace.System_ASHP_R290.HPC.BiBloc.BaseCycles.OL": {}, "Workspace.Controller.SubSystems.BaseClasses.CompleteCompressorCo_1C": {}, "Workspace.Controller.SubSystems.BaseClasses.CompleteCompressorControlBase_1C": {}, "Workspace.Controller.SubSystems.BaseClasses.ControllerBase_1C": {}, "Workspace.System_ASHP_R290.HPC.MonoBloc.BaseCycles.Equipment": {}, "Workspace.System_ASHP_R290.HPH.BiBloc_with_EVI.BaseCycles.OL_modular_4_blocks_wo_EVI": {}, "Workspace.System_ASHP_R290.HPH.BiBloc_with_EVI.BaseCycles.OL_modular_wo_EVI": {}, "Workspace.HPC_MODULAR_ILHAM": {"ECAT.CompressorFrequency_Hz.value|max": true}, "Workspace.System_ASHP_R290.HPC.System_model.BaseCycles.OL": {"compressor.userDefined": true}, "Workspace.HPH_MODULAR_ILHAM": {"summary_Instantaneous_Cap": false, "BlocA.fanCurve.summary.speed": true, "speed_A.setPoint": true, "BlocA.systemVariables.summary.HeatCap_total": true, "BlocA.systemVariables.summary.COPHeat": true, "BlocA.motor.shaftSpeed_set": true, "BlocA.node_evapout.summary.Tsat": true}, "Workspace.TestCOMPRESSEUR": {"speed.setPoint": true, "discharge.summary.dTsh": true, "discharge.dTsh_fixed": false, "liquid.dTsh_fixed": false, "liquid.summary.dTsh": true, "liquid.dTsh_set": true, "discharge.dTsh_set": true, "node.Sub_node.Tsat_fixed": false, "node.Sub_node.Tsat_set": true, "discharge.Tsat_set": true, "node.Sub_sink.Tsat_set": true, "node.Tsat_set": true}, "Workspace.OPtifan": {"motor.shaftSpeed_set": true, "systemVariables.summary.COPHeat": true, "actuator2.setPoint": true, "systemVariables.summary.HeatCap_total": true, "node2.Tsat": true}, "Workspace.OPtifanFroid": {"motor.shaftSpeed_set": true, "actuator2.setPoint": true, "systemVariables.EERcool": false, "systemVariables.summary.EERcool": true, "node2.Tsat": true, "systemVariables.summary.CoolCap_total": true}, "Workspace.System.HPH.BaseCycles.OL_modular": {"BlocB.condBPHE.summary.Q_flow_coolant": false, "split_bloc.X": true}, "Workspace.System.HPH.BaseCycles.OL": {}, "Workspace.System.HPC.BaseCycle.OL_modular": {}, "Workspace.System.HPH.BaseCycles.Unit": {}, "Workspace.System.HPH.BaseCycles.ECS1": {"oL_modular.BlocA.source_air.Tdb_fixed": true, "oL_modular.BlocA.source_air.Tdb_set": true, "oL_modular.BlocA.source_air.Twb_fixed": true, "oL_modular.BlocA.source_air.Twb_set": true}, "Workspace.System.HPC.BaseCycle.Unit_optimization": {"LWT": true, "EWT": true, "Target_capacityA": true, "Target_capacityB": true, "Fan_speedA": true, "Fan_speedB": true, "OAT": true}, "Workspace.System.HPC.BaseCycle.OL": {"max_motor_frequency": false}, "Workspace.Controller.ControllerSettings": {}, "Workspace.Controller.test_map_Cp": {}, "Workspace.System.HPH.BaseCycles.ECS3": {"oL_modular.BlocA.source_air.Tdb_fixed": true, "oL_modular.BlocA.source_air.Tdb_set": true, "oL_modular.BlocA.source_air.Twb_fixed": true, "oL_modular.BlocA.source_air.Twb_set": true}, "Workspace.System.HPC.BaseCycle.ECS2": {}, "Workspace.System.HPC.BaseCycle.Unit_optimization_1": {}, "Workspace.Controller.SubSystems.EXVControl": {}, "Workspace.Controller.SubSystems.Tests.FanControl.FanControlCooling": {"fanControl_cooling.T_sdt_min_error.setPoint": false}, "Workspace.Controller.SubSystems.CompressorControl_cooling": {}, "Workspace.Controller.SubSystems.Tests.Compressor_control.Cp_control": {}, "Workspace.Controller.SubSystems.Tests.EXVControl.Test_EXV_cooling": {}, "Workspace.Controller.Tests.Test_control_2C_cooling": {}, "Workspace.Controller.SubSystems.BaseClasses.CompressorControlBase": {}, "Workspace.Controller.SubSystems.BaseClasses.CompleteCompressorCooling_2C": {}, "Workspace.System.HPH.BaseCycles.ECS2": {"oL_modular.BlocA.source_air.Tdb_fixed": true, "oL_modular.BlocA.source_air.Tdb_set": true, "oL_modular.BlocA.source_air.Twb_fixed": true, "oL_modular.BlocA.source_air.Twb_set": true}, "Workspace.System.HPH.BaseCycles.OL_modular_2_blocks": {}, "Workspace.System.HPH.BaseCycles.Unit_optimization": {}, "Workspace.Controller.Tests.test_controllerSettings_1": {}, "Workspace.Controller.SubSystems.CompressorControl_heating": {}, "Workspace.Controller.SubSystems.Tests.Compressor_control.Cp_control_heating": {}, "Workspace.Controller.SubSystems.Tests.FanControl.FanControlHeating": {}, "Workspace.Controller.SubSystems.FanControl_heating": {}, "Workspace.Controller.ControllerSettings_heating": {}, "Workspace.System.HPH.BaseCycles.CL_system_ECAT": {"ECAT.CondPumpPower_W.value": false}, "Workspace.Auxiliary.ECAT_Zenith.ECATBase": {}, "Workspace.System.HPC.BaseCycle.CL_system_ECAT": {}, "Workspace.Controller.ControllerSettings_heating_1": {}, "Workspace.Controller.SubSystems.CompressorControl_heating_1": {}, "Workspace.Controller.ControllerSettings_cooling": {}, "Workspace.System.HPH.BaseCycles.CL_system": {"Pdispo": true, "oL_modular.errorCalculationPumpA.measurement": false}, "Workspace.Auxiliary.EN14511.PostCalc_Bloc_HPH": {}, "Workspace.Auxiliary.EN14511.Test_EN14511_Zenith": {}, "Workspace.Auxiliary.Business_Factor.Business_factor_empty": {}, "Workspace.Auxiliary.Business_Factor.Business_factor_empty_cooling": {}, "Workspace.Controller.SubSystems.PumpUserControl": {}, "Workspace.System.HPC.BaseCycle.Equipement": {}, "Workspace.System.HPC.BaseCycle.System_61AQ": {"Module.fmin": true, "Module.Mref_fixed": true}, "Workspace.System.HPC.BaseCycle.optimization.Unit_optimization": {"LWT": true, "EWT": true, "Target_capacityA": true, "Target_capacityB": true, "Fan_speedA": true, "Fan_speedB": true, "OAT": true}, "Workspace.System.HPH.BaseCycles.Equipement": {"split_bloc.X": true, "BlocB.condBPHE.summary.Q_flow_coolant": false}, "Workspace.System.HPC.BaseCycle.System_61AQ_1": {"Module.fmin": true}, "Workspace.System.HPH.BaseCycles.System_61AQ_1": {"Module.fmin": true}, "Workspace.System.HPH.BaseCycles.System_61AQ": {"Module.fmin": true}, "Workspace.System.HPH.BaseCycles.optimization.ECS1": {"oL_modular.BlocA.source_air.Tdb_fixed": true, "oL_modular.BlocA.source_air.Tdb_set": true, "oL_modular.BlocA.source_air.Twb_fixed": true, "oL_modular.BlocA.source_air.Twb_set": true}, "Workspace.System.HPH.BaseCycles.optimization.CL_system": {"Pdispo": true, "oL_modular.errorCalculationPumpA.measurement": false}, "Workspace.Controller.Tests.test_Table_Freq_max_SDT": {}, "Workspace.Controller.Tests.test_Table_Freq_max_SDT_1": {}, "Workspace.POC.System_61AQ": {"Module.fmin": true}, "Workspace.POC.Unity_61AQ": {"Module.fmin": true}, "Workspace.POC.System_61AQ_multimodule_bis": {}, "Workspace.POC.HPH.Equipement_multi_1": {"split_bloc.X": true, "BlocB.condBPHE.summary.Q_flow_coolant": false}, "Workspace.POC.HPH.Unity_61AQ_1": {"Module.fmin": true, "TargetCapacityA": false}, "Workspace.POC.HPH.Unity_61AQ_": {"Module.fmin": true, "TargetCapacityA": false}, "Workspace.POC.HPH.Unity_61AQ": {"Module.fmin": true, "TargetCapacityA": false}, "Workspace.POC.HPH.System_61AQ_multimodule_bis": {}, "Workspace.POC.HPH.System_61AQ": {"LWT": true, "EWT": true, "TargetCapacity": false}, "Workspace.System.Multimodule.HPC.System_61AQ": {"ECAT.EvapBrineEWT_K.setPoint": true, "ECAT.EvapBrineLWT_K.setPoint": true, "ECAT.TargetCoolingCapacity_W.setPoint": true, "ECAT.EvapBrineLWT_K.fixed": true, "ECAT.EvapBrineEWT_K.fixed": true, "ECAT.TargetCoolingCapacity_W.fixed": true, "ECAT.PubUnitPower_W.value": true, "ECAT.PubCoolingCapacity_W.value": true}, "Workspace.System.Multimodule.HPC.System_61AQ_multimodule_bis": {"ECAT.EvapBrineLWT_K.setPoint": true, "ECAT.EvapBrineLWT_K.fixed": true, "ECAT.EvapBrineEWT_K.setPoint": true, "ECAT.EvapBrineEWT_K.fixed": true, "ECAT.TargetCoolingCapacity_W.setPoint": true, "ECAT.TargetCoolingCapacity_W.fixed": false, "ECAT.PubCoolingCapacity_W.setPoint": false, "ECAT.PubCoolingCapacity_W.value": true, "ECAT.PubUnitPower_W.value": true, "ECAT.ExternalSystemPressureDrop_Pa.setPoint": true, "ECAT.AmbientAirDBTemp_K.setPoint": true, "IsOFF1": true, "IsOFF2": true, "IsOFF3": true, "IsOFF4": true, "use_bf": true, "Use_EN14511": true, "use_Calib": true, "ECAT.RefrigerantSST_K.value": true, "ECAT.RefrigerantSDT_K.value": true, "ECAT.AmbientAirRH_nd.setPoint": true, "ECAT.CondBrineLWT_K.setPoint": true, "ECAT.CondBrineEWT_K.setPoint": true, "ECAT.CondBrineLWT_K.value": true, "ECAT.CondBrineEWT_K.value": true, "ECAT.CondBrineFlowRate_m3s.setPoint": true, "ECAT.TargetHeatingCapacity_W.setPoint": true, "ECAT.PubHeatingCapacity_W.value": true, "ECAT.HeatingAmbientAirDBTemp_K.setPoint": true, "Module_1.Module.BlockA.compressor.summary.Ncomp": true, "Module_1.Module.BlockA.systemVariables.summary.pow_total": true, "Module_1.Module.BlockA.systemVariables.summary.CoolCap_total": true, "Module_1.Module.BlockB.compressor.summary.Ncomp": true, "Module_1.Module.BlockB.systemVariables.summary.pow_total": true, "Module_1.Module.BlockB.systemVariables.summary.CoolCap_total": true, "choiceBlock_multi.Module_1_selector": true, "choiceBlock_multi.Pump_selector": true, "Module_1.Module.BlockA.condBPHE.summary.Z_dp_ref": false}, "Workspace.System.HPC.R290.CL_61AQ": {"ECAT.EvapBrineLWT_K.setPoint": true, "ECAT.EvapBrineLWT_K.fixed": true, "ECAT.EvapBrineEWT_K.setPoint": true, "ECAT.EvapBrineEWT_K.fixed": true, "ECAT.AmbientAirDBTemp_K.setPoint": true, "ECAT.TargetCoolingCapacity_W.setPoint": true, "ECAT.PubUnitPower_W.setPoint": false, "ECAT.PubUnitPower_W.value": true, "ECAT.PubCoolingCapacity_W.value": true, "choiceBlock.Selector_Block_A": true, "choiceBlock.Selector_Block_B": true, "choiceBlock.Pump_selector": true}, "Workspace.System.HPH.R290.CL_61AQ": {"choiceBlock.Selector_Block_A": true, "choiceBlock.is_monobloc": true, "choiceBlock.Selector_Block_B": true, "choiceBlock.Pump_selector": true, "choiceBlock.Coating_selector": true, "ECAT.CondBrineLWT_K.setPoint": true, "ECAT.CondBrineEWT_K.setPoint": true, "ECAT.CondBrineFlowRate_m3s.value": true, "ECAT.CondBrineEWT_K.value": true, "ECAT.CondBrineLWT_K.value": true, "ECAT.CondBrineFlowRate_m3s.setPoint": true, "ECAT.TargetHeatingCapacity_W.setPoint": true, "ECAT.PubUnitPower_W.value": true, "ECAT.PubHeatingCapacity_W.value": true, "Module.BlockA.systemVariables.summary.pow": true, "Module.BlockA.systemVariables.summary.CoolCap_total": true, "Module.BlockA.compressor.summary.Ncomp": true, "Module.BlockB.systemVariables.summary.pow_total": true, "Module.BlockB.systemVariables.summary.CoolCap_total": true, "Module.BlockB.compressor.summary.Ncomp": true, "ECAT.HeatingAmbientAirWBTemp_K.setPoint": false, "ECAT.HeatingAmbientAirDBTemp_K.setPoint": true, "ECAT.HeatingAmbientAirWBTemp_K.value": true, "use_Calib": true, "Use_EN14511": true, "use_defrost": true}, "Workspace.System.Multimodule.HPH.System_61AQ_multimodule_bis": {"ECAT.AmbientAirRH_nd.setPoint": true, "ECAT.CondBrineLWT_K.setPoint": true, "ECAT.CondBrineEWT_K.setPoint": true, "ECAT.CondBrineLWT_K.value": true, "ECAT.CondBrineEWT_K.value": true, "ECAT.CondBrineFlowRate_m3s.setPoint": true, "ECAT.TargetHeatingCapacity_W.setPoint": true, "ECAT.PubUnitPower_W.value": true, "ECAT.PubHeatingCapacity_W.value": true, "ECAT.HeatingAmbientAirDBTemp_K.setPoint": true, "IsOFF1": true, "IsOFF2": true, "IsOFF3": true, "IsOFF4": true, "Module_1.Module.BlockA.compressor.summary.Ncomp": true, "Module_1.Module.BlockA.systemVariables.summary.pow_total": true, "Module_1.Module.BlockA.systemVariables.summary.CoolCap_total": true, "Module_1.Module.BlockB.compressor.summary.Ncomp": true, "Module_1.Module.BlockB.systemVariables.summary.pow_total": true, "Module_1.Module.BlockB.systemVariables.summary.CoolCap_total": true, "choiceBlock_multi.Module_1_selector": true, "choiceBlock_multi.Pump_selector": true, "Module_1.Module.BlockA.condBPHE.summary.Z_dp_ref": false}, "Workspace.System.Multimodule.System_61AQ_multimodule_bis": {"ECAT.AmbientAirRH_nd.setPoint": true, "ECAT.CondBrineLWT_K.setPoint": true, "ECAT.CondBrineEWT_K.setPoint": true, "ECAT.CondBrineLWT_K.value": true, "ECAT.CondBrineEWT_K.value": true, "ECAT.CondBrineFlowRate_m3s.setPoint": true, "ECAT.TargetHeatingCapacity_W.setPoint": true, "ECAT.PubUnitPower_W.value": true, "ECAT.PubHeatingCapacity_W.value": true, "ECAT.HeatingAmbientAirDBTemp_K.setPoint": true, "IsOFF1": true, "IsOFF2": true, "IsOFF3": true, "IsOFF4": true, "Module_1.Module.BlockA.compressor.summary.Ncomp": true, "Module_1.Module.BlockA.systemVariables.summary.pow_total": true, "Module_1.Module.BlockA.systemVariables.summary.CoolCap_total": true, "Module_1.Module.BlockB.compressor.summary.Ncomp": true, "Module_1.Module.BlockB.systemVariables.summary.pow_total": true, "Module_1.Module.BlockB.systemVariables.summary.CoolCap_total": true, "choiceBlock_multi.Module_1_selector": true, "choiceBlock_multi.Pump_selector": true, "Module_1.Module.BlockA.condBPHE.summary.Z_dp_ref": false, "ECAT.EvapBrineLWT_K.setPoint": true, "ECAT.EvapBrineLWT_K.fixed": true, "ECAT.EvapBrineEWT_K.setPoint": true, "ECAT.EvapBrineEWT_K.fixed": true, "ECAT.TargetCoolingCapacity_W.setPoint": true, "ECAT.PubCoolingCapacity_W.value": true, "ECAT.ExternalSystemPressureDrop_Pa.setPoint": true, "ECAT.AmbientAirDBTemp_K.setPoint": true, "use_bf": true, "Use_EN14511": true, "use_Calib": true, "ECAT.RefrigerantSST_K.value": true, "ECAT.RefrigerantSDT_K.value": true, "ECAT.TargetCoolingCapacity_W.fixed": false, "ECAT.PubCoolingCapacity_W.setPoint": false}, "Workspace.System.Multimodule.HPC.R290.System_61AQ": {"ECAT.EvapBrineEWT_K.setPoint": true, "ECAT.EvapBrineLWT_K.setPoint": true, "ECAT.TargetCoolingCapacity_W.setPoint": true, "ECAT.EvapBrineLWT_K.fixed": true, "ECAT.EvapBrineEWT_K.fixed": true, "ECAT.TargetCoolingCapacity_W.fixed": true, "ECAT.PubUnitPower_W.value": true, "ECAT.PubCoolingCapacity_W.value": true}, "Workspace.System.Multimodule.HPH.System_61AQ_Modular": {"ECAT.AmbientAirRH_nd.setPoint": true, "ECAT.CondBrineLWT_K.setPoint": true, "ECAT.CondBrineEWT_K.setPoint": true, "ECAT.CondBrineLWT_K.value": true, "ECAT.CondBrineEWT_K.value": true, "ECAT.CondBrineFlowRate_m3s.setPoint": true, "ECAT.TargetHeatingCapacity_W.setPoint": true, "ECAT.PubUnitPower_W.value": true, "ECAT.PubHeatingCapacity_W.value": true, "ECAT.HeatingAmbientAirDBTemp_K.setPoint": true, "IsOFF1": true, "IsOFF2": true, "IsOFF3": true, "IsOFF4": true, "Module_1.Module.BlockA.compressor.summary.Ncomp": true, "Module_1.Module.BlockA.systemVariables.summary.pow_total": true, "Module_1.Module.BlockA.systemVariables.summary.CoolCap_total": true, "Module_1.Module.BlockB.compressor.summary.Ncomp": true, "Module_1.Module.BlockB.systemVariables.summary.pow_total": true, "Module_1.Module.BlockB.systemVariables.summary.CoolCap_total": true, "choiceBlock_multi.Module_1_selector": true, "choiceBlock_multi.Pump_selector": true, "Module_1.Module.BlockA.condBPHE.summary.Z_dp_ref": false}, "Workspace.System.Multimodule.HPC.System_61AQ_Modular": {"ECAT.EvapBrineLWT_K.setPoint": true, "ECAT.EvapBrineLWT_K.fixed": true, "ECAT.EvapBrineEWT_K.setPoint": true, "ECAT.EvapBrineEWT_K.fixed": true, "ECAT.TargetCoolingCapacity_W.setPoint": true, "ECAT.PubCoolingCapacity_W.value": true, "ECAT.PubUnitPower_W.value": true, "ECAT.ExternalSystemPressureDrop_Pa.setPoint": true, "ECAT.AmbientAirDBTemp_K.setPoint": true, "IsOFF1": true, "IsOFF2": true, "IsOFF3": true, "IsOFF4": true, "use_bf": true, "Use_EN14511": true, "use_Calib": true, "ECAT.RefrigerantSST_K.value": true, "ECAT.RefrigerantSDT_K.value": true, "ECAT.AmbientAirRH_nd.setPoint": true, "ECAT.CondBrineLWT_K.setPoint": true, "ECAT.CondBrineEWT_K.setPoint": true, "ECAT.CondBrineLWT_K.value": true, "ECAT.CondBrineEWT_K.value": true, "ECAT.CondBrineFlowRate_m3s.setPoint": true, "ECAT.TargetHeatingCapacity_W.setPoint": true, "ECAT.PubHeatingCapacity_W.value": true, "ECAT.HeatingAmbientAirDBTemp_K.setPoint": true, "Module_1.Module.BlockA.compressor.summary.Ncomp": true, "Module_1.Module.BlockA.systemVariables.summary.pow_total": true, "Module_1.Module.BlockA.systemVariables.summary.CoolCap_total": true, "Module_1.Module.BlockB.compressor.summary.Ncomp": true, "Module_1.Module.BlockB.systemVariables.summary.pow_total": true, "Module_1.Module.BlockB.systemVariables.summary.CoolCap_total": true, "choiceBlock_multi.Module_1_selector": true, "choiceBlock_multi.Pump_selector": true, "ECAT.TargetCoolingCapacity_W.fixed": false, "ECAT.PubCoolingCapacity_W.setPoint": false, "Module_1.Module.BlockA.condBPHE.summary.Z_dp_ref": false}, "Workspace.System.Multimodule.HPC.R290.System_61AQ_Modular": {"ECAT.EvapBrineEWT_K.setPoint": true, "ECAT.EvapBrineLWT_K.setPoint": true, "ECAT.TargetCoolingCapacity_W.setPoint": true, "ECAT.EvapBrineLWT_K.fixed": true, "ECAT.EvapBrineEWT_K.fixed": true, "ECAT.TargetCoolingCapacity_W.fixed": true, "ECAT.PubUnitPower_W.value": true, "ECAT.PubCoolingCapacity_W.value": true}, "Workspace.System.Multimodule.HPC.R290.CL_61AQ_Modular": {"ECAT.EvapBrineEWT_K.setPoint": true, "ECAT.EvapBrineLWT_K.setPoint": true, "ECAT.TargetCoolingCapacity_W.setPoint": true, "ECAT.EvapBrineLWT_K.fixed": true, "ECAT.EvapBrineEWT_K.fixed": true, "ECAT.TargetCoolingCapacity_W.fixed": true, "ECAT.PubUnitPower_W.value": true, "ECAT.PubCoolingCapacity_W.value": true}}}