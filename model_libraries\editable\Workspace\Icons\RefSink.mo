within Workspace.Icons;
partial model RefSink
  annotation (
    Icon(
      graphics={
        Ellipse(
          extent={{-100,100},{100,-100}},
          lineColor={0,0,0},
          fillPattern=FillPattern.Solid,
          fillColor={255,255,255}),
        Ellipse(
          extent={{-68,38},{4,-34}},
          lineColor={0,0,0},
          fillColor={255,0,0},
          fillPattern=FillPattern.Solid),
        Ellipse(
          extent={{4,38},{76,-34}},
          lineColor={0,0,0},
          fillColor={255,0,0},
          fillPattern=FillPattern.Solid),
        Text(
          extent={{-102,-102},{126,-136}},
          lineColor={0,0,0},
          fillColor={85,255,170},
          fillPattern=FillPattern.Solid,
          textString="%name")}));
end RefSink;
