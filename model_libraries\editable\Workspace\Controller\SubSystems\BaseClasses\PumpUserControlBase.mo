within Workspace.Controller.SubSystems.BaseClasses;
partial model PumpUserControlBase
  extends BOLT.InternalLibrary.BuildingBlocks.Icons.Pump;
  .Workspace.Interfaces.MeasurementBus measurementBus
    annotation (Placement(transformation(extent={{-120.0,40.0},{-80.0,80.0}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-120,40},{-80,80}})));
  .Workspace.Interfaces.LimitsBus limitsBus
    annotation (Placement(transformation(extent={{-46,-56},{-26,-36}},origin={-64,-14},rotation=0),iconTransformation(extent={{-110,-70},{-90,-50}})));
  .Modelica.Blocks.Interfaces.RealOutput actuatorSignal
    annotation (Placement(transformation(extent={{90.0,-10.0},{110.0,10.0}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{90,-10},{110,10}},origin={10,0})));
  parameter Boolean crkIsOff=false
    "Specify whether the controller is on or off";
end PumpUserControlBase;
