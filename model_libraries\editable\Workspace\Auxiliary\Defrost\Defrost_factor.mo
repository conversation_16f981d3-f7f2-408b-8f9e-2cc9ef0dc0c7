within Workspace.Auxiliary.Defrost;
model Defrost_factor
  //Input
  .Modelica.Blocks.Interfaces.RealInput OAT
    annotation (Placement(transformation(extent={{-110.2007804301496,16.74857247062129},{-90.75510123740686,36.194251663364035}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput RH
    annotation (Placement(transformation(extent={{-110.2007804301496,1.648572470621275},{-90.75510123740686,21.094251663364048}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput Gross_Heatcap
    annotation (Placement(transformation(extent={{-110.2007804301496,-74.35142752937871},{-90.75510123740686,-54.905748336635966}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput Net_Heatcap
    annotation (Placement(transformation(extent={{-110.2007804301496,-60.351427529378725},{-90.75510123740686,-40.90574833663598}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput Gross_Power
    annotation (Placement(transformation(extent={{-110.2007804301496,-44.351427529378725},{-90.75510123740686,-24.90574833663598}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput Net_Power
    annotation (Placement(transformation(extent={{-110.2007804301496,-28.351427529378718},{-90.75510123740686,-8.905748336635973}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput Ncomp
    annotation (Placement(transformation(extent={{-109.74759588394248,44.40481956183889},{-90.30191669119974,63.850498754581636}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput Pcompresseur
    annotation (Placement(transformation(extent={{-110.74759588394248,61.40481956183888},{-91.30191669119974,80.85049875458165}},origin={0.0,0.0},rotation=0.0)));
  //Output
  .Modelica.Blocks.Interfaces.RealOutput Integrated_net_heatcap
    annotation (Placement(transformation(extent={{101.8693605167545,-39.02757032085094},{121.8693605167545,-19.02757032085094}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Integrated_net_power
    annotation (Placement(transformation(extent={{101.42714861602563,9.756389013405144},{121.42714861602563,29.756389013405144}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Integrated_gross_heatcap
    annotation (Placement(transformation(extent={{101.8693605167545,-11.972272802629629},{121.8693605167545,8.027727197370368}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Integrated_gross_power
    annotation (Placement(transformation(extent={{101.42714861602563,38.791593638186136},{121.42714861602563,58.791593638186136}},origin={0.0,0.0},rotation=0.0)));
  parameter Boolean use_DefrostFactor=true
    " True to apply Defrost factor "
    annotation (Dialog(tab="General",group="Boolean"));
  parameter Boolean is_monobloc=false
    " true if there is only one circuit "
    annotation (Dialog);
  parameter Integer Nrow=2
    " true if there is only one circuit "
    annotation (Dialog);
  parameter Real Frequence_degi=60
    " true if there is only one circuit "
    annotation (Dialog);
  Real Defrost_factor_capacity40_50
    annotation (Dialog);
  Real Defrost_factor_power40_50
    annotation (Dialog);
  Real Defrost_factor_capacity60_70
    annotation (Dialog);
  Real Defrost_factor_power60_70
    annotation (Dialog);
  Real Defrost_factor_capacity80_100
    annotation (Dialog);
  Real Defrost_factor_power80_100
    annotation (Dialog);
  Real Defrost_factor_capacity120_140
    annotation (Dialog);
  Real Defrost_factor_power120_140
    annotation (Dialog);
  Real DefrostCorr
    annotation (Dialog);
  Real Coolcap
    annotation (Dialog);
  Real RH_Pct
    annotation (Dialog);
  Real Ratio_frequence
    annotation (Dialog);
  //Limits
  parameter Real Defrost_cap_min=0.7
    annotation (Dialog(tab="Capacity",group="Limits"));
  parameter Real Defrost_cap_max=1
    annotation (Dialog(tab="Capacity",group="Limits"));
  parameter Real Defrost_pow_min=0.9
    annotation (Dialog(tab="Power",group="Limits"));
  parameter Real Defrost_pow_max=1.1
    annotation (Dialog(tab="Power",group="Limits"));
  parameter Real DefrostCorr_min=1
    annotation (Dialog);
equation
  RH_Pct=RH*100;
  DefrostCorr=max(
    0.00028*(OAT-273.15)*RH_Pct-0.00179*RH_Pct-0.024*(OAT-273.15)+1.1554,
    DefrostCorr_min);
  Coolcap=
    if is_monobloc then
      Gross_Heatcap-0.95*Pcompresseur
    else
      (Gross_Heatcap-0.95*Pcompresseur)/2;
  Ratio_frequence=Ncomp/Frequence_degi;
  
    //Capacity
    
    Defrost_factor_capacity40_50=
    if OAT < 265.15 then
      min(max((-0.13159009638942168*265.15*265.15 + 34.85097555123612*265.15 -0.005108848190846202*Coolcap*0.001 -3073.143826366048 + 0.00016548480708606327*265.15*265.15*265.15)*DefrostCorr,Defrost_cap_min),Defrost_cap_max)
    elseif OAT < 280.10 then
      min(max((-0.13159009638942168*OAT*OAT + 34.85097555123612*OAT -0.005108848190846202*Coolcap*0.001 -3073.143826366048 + 0.00016548480708606327*OAT*OAT*OAT)*DefrostCorr,Defrost_cap_min),Defrost_cap_max)
    else 1;
    
    Defrost_factor_capacity60_70 = 
    if OAT< 266.15 then
      min(max((0.003860780228932565*266.15*266.15 -2.095060729773861*266.15 -0.003770699084655764*Coolcap/1000 + 285.08348394337246)*DefrostCorr,Defrost_cap_min),Defrost_cap_max)
    elseif OAT < 280.10 then
      min(max((0.003860780228932565*OAT*OAT -2.095060729773861*OAT -0.003770699084655764*Coolcap/1000 + 285.09348394337246)*DefrostCorr,Defrost_cap_min),Defrost_cap_max)
    else 1;
    
    Defrost_factor_capacity80_100=
    if OAT < 263.15 then
      min(max((-0.13159009638942168*265.15*265.15 + 34.85097555123612*265.15 -0.005108848190846202*Coolcap*0.001 -3073.143826366048 + 0.00016548480708606327*265.15*265.15*265.15)*DefrostCorr,Defrost_cap_min),Defrost_cap_max)
    elseif OAT < 280.10 then
      min(max((-0.13159009638942168*OAT*OAT + 34.85097555123612*OAT -0.005108848190846202*Coolcap*0.001 -3073.143826366048 + 0.00016548480708606327*OAT*OAT*OAT)*DefrostCorr,Defrost_cap_min),Defrost_cap_max)
    else 1;
    
  Defrost_factor_capacity120_140=
    if OAT < 265.15 then
      min(max((-0.14575868166409267*265.15*265.15 + 38.71911782402885*265.15 -0.004566032065261222*Coolcap*0.001 -3425.0307355916552 + 0.00018277720316440207*265.15*265.15*265.15)*DefrostCorr,Defrost_cap_min),Defrost_cap_max)
    elseif OAT < 280.10 then
      min(max((-0.14575868166409267*OAT*OAT + 38.71911782402885*OAT -0.004566032065261222*Coolcap*0.001 -3425.0307355916552 + 0.00018277720316440207*OAT*OAT*OAT)*DefrostCorr,Defrost_cap_min),Defrost_cap_max)
    else 1;
    
  //Power
    
    Defrost_factor_power40_50=
    if Defrost_factor_capacity40_50 < 0.97 then
      min(max((-7.2872e-2*Ratio_frequence+1.0892)*DefrostCorr,Defrost_pow_min),Defrost_pow_max)
    else 1;
    
  Defrost_factor_power60_70 = 
    if Defrost_factor_capacity60_70<0.97 then 
    min(max((-0.6190365210919384*OAT + 0.0011515608621521295*OAT*OAT -0.006055199230854102*Coolcap/1000 + 84.30980430577341)*DefrostCorr, Defrost_pow_min),Defrost_pow_max) 
    else 1;

  Defrost_factor_power80_100=
    if Defrost_factor_capacity80_100 < 0.97 then
      min(max((-7.2872e-2*Ratio_frequence+1.0892)*DefrostCorr,Defrost_pow_min),Defrost_pow_max)
    else 1;
    
  Defrost_factor_power120_140=
    if Defrost_factor_capacity120_140 < 0.97 then
      min(max((-0.006750132368691894*Ratio_frequence -0.600170175186334*OAT + 0.0011166170332955311*OAT*OAT -0.005709855405848101*Coolcap*0.001 + 81.76224142779762)*DefrostCorr,0.85),Defrost_pow_max)
    else 1;
    
  Integrated_gross_heatcap=
    if is_monobloc then
      (if Nrow == 2 then
          Defrost_factor_capacity40_50*Gross_Heatcap
        else
          Defrost_factor_capacity60_70*Gross_Heatcap)
    else
      (if Nrow == 2 then
          Defrost_factor_capacity80_100*Gross_Heatcap
        else
          Defrost_factor_capacity120_140*Gross_Heatcap);
    
  Integrated_net_heatcap=
    if is_monobloc then
      (if Nrow == 2 then
          Defrost_factor_capacity40_50*Net_Heatcap
        else
          Defrost_factor_capacity60_70*Net_Heatcap)
    else
      (if Nrow == 2 then
          Defrost_factor_capacity80_100*Net_Heatcap
        else
          Defrost_factor_capacity120_140*Net_Heatcap);
    
  Integrated_gross_power=
    if is_monobloc then
      (if Nrow == 2 then
          Defrost_factor_power40_50*Gross_Power
        else
          Defrost_factor_power60_70*Gross_Power)
    else
      (if Nrow == 2 then
          Defrost_factor_power80_100*Gross_Power
        else
          Defrost_factor_power120_140*Gross_Power);
    
  Integrated_net_power=
    if is_monobloc then
      (if Nrow == 2 then
          Defrost_factor_power40_50*Net_Power
        else
          Defrost_factor_power60_70*Net_Power)
    else
      (if Nrow == 2 then
          Defrost_factor_power80_100*Net_Power
        else
          Defrost_factor_power120_140*Net_Power);
    
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name"),
        Rectangle(
          origin={1,0},
          extent={{-101,100},{101,-100}},
          fillPattern=FillPattern.Solid,
          fillColor={251,165,165}),
        Text(
          textString="Bloc Defrost",
          origin={4,0},
          extent={{-85,42},{85,-42}})}));
end Defrost_factor;
