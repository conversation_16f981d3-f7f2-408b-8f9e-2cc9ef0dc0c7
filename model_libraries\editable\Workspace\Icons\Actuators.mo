within Workspace.Icons;
partial class Actuators
  extends Workspace.Icons.PackageIcon;
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100,-100},{100,100}}),
      graphics={
        Polygon(
          points={{-68,48},{-68,-52},{-18,-2},{-68,48}},
          lineColor={0,0,0},
          smooth=Smooth.None,
          fillColor={255,0,0},
          fillPattern=FillPattern.Solid),
        Polygon(
          points={{-14,38},{-14,-42},{26,-2},{-14,38}},
          lineColor={0,0,0},
          smooth=Smooth.None,
          fillColor={128,255,0},
          fillPattern=FillPattern.Solid),
        Polygon(
          points={{36,28},{36,-32},{76,-2},{36,28}},
          lineColor={0,0,0},
          smooth=Smooth.None,
          fillColor={255,128,0},
          fillPattern=FillPattern.Solid)}));
end Actuators;
