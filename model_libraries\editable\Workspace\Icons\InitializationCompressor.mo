within Workspace.Icons;
partial model InitializationCompressor
  //extends VFD30XW.Icons.InitializationCycle2;
  annotation (
    Icon(
      graphics={
        Text(
          extent={{-132,-134},{136,-152}},
          lineColor={0,0,0},
          fillColor={255,0,0},
          fillPattern=FillPattern.Solid,
          textString="%name"),
        Ellipse(
          extent={{-82,82},{82,-82}},
          lineColor={0,0,0},
          fillColor={255,255,255},
          fillPattern=FillPattern.Solid),
        Polygon(
          points={{-66,-48},{-40,72},{40,72},{66,-48},{-66,-48}},
          lineColor={0,0,0},
          smooth=Smooth.None,
          fillColor={255,0,0},
          fillPattern=FillPattern.Solid)}));
end InitializationCompressor;
