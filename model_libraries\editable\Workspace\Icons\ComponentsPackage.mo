within Workspace.Icons;
partial class ComponentsPackage
  extends Workspace.Icons.PackageIcon;
  annotation (
    Icon(
      graphics={
        Polygon(
          points={{32,8},{32,36},{-20,36},{-20,24},{-20,-12},{-20,-64},{22,-64},{32,-64},{32,-36},{20,-36},{20,-56},{-8,-56},{-8,-34},{-8,-22},{-8,-16},{-8,28},{20,28},{20,8},{32,8}},
          smooth=Smooth.None,
          fillColor={0,0,255},
          fillPattern=FillPattern.Solid,
          pattern=LinePattern.None,
          lineColor={0,0,0})}));
end ComponentsPackage;
