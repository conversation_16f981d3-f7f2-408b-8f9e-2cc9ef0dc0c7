within Workspace.Controller.Tests;
model Test_control_1C_Zenithheating
  .Workspace.Controller.Controller_1C_heating controller_1C(
    Fan_MaxFrequency=950,
    isOffSDTmin_fan=true,
    isOffSDTmax_fan=true,
    isOffDGTmax_fan=true,
    isOffSSTmin_EXV=true,
    isOffSSTmax_EXV=true,
    isOffDSHmin_EXV=true,
    isOffDGTmax_EXV=true,
    isOffSSTmin_comp=true,
    isOffSDTmax_comp=true,
    isOffDGTmax_comp=true,
    manualOff_fan=false,
    frq_fan_sp_manual=1,
    manualOff_compressor_crkA=false,
    manualOff_compressor_crkB=false,
    frq_comp_sp_manual_crkA=60,
    frq_comp_sp_manual_crkB=60,
    min_speed=30,
    max_speed=140)
    annotation (Placement(transformation(extent={{-21.27422097784322,-20.44345335690181},{20.16653081658803,20.99729843752944}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Controller.Tests.y_equalto_kx_plus_b y_equalto_kx_plus_b()
    annotation (Placement(transformation(extent={{52.79661853080103,-42.92645892888519},{142.6495363885714,46.92645892888519}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.MeasurementBus measurementBus
    annotation (Placement(transformation(extent={{28.09005754006398,36.303938623931735},{68.09005754006398,76.30393862393174}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Controller.ControllerSettings_heating controllerSettings(
    SST_min=274.15)
    annotation (Placement(transformation(extent={{10.459537733192278,76.0},{-9.540462266807722,96.0}},origin={0.0,0.0},rotation=0.0)));
equation
  connect(controller_1C.fan,y_equalto_kx_plus_b.x[1])
    annotation (Line(points={{23.481790960142533,12.71038499461713},{50.0108355828082,12.71038499461713},{50.0108355828082,1.5507354107111482},{75.70911258453248,1.5507354107111482}},color={0,0,127}));
  connect(controller_1C.exv,y_equalto_kx_plus_b.x[2])
    annotation (Line(points={{23.481790960142533,7.943152393290113},{50.0108355828082,7.943152393290113},{50.0108355828082,1.5507354107111482},{75.70911258453248,1.5507354107111482}},color={0,0,127}));
  connect(controller_1C.compressor,y_equalto_kx_plus_b.x[3])
    annotation (Line(points={{23.481790960142533,-7.804333288593764},{50.0108355828082,-7.804333288593764},{50.0108355828082,1.5507354107111482},{75.70911258453248,1.5507354107111482}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[1],measurementBus.capacity)
    annotation (Line(points={{120.63557151341766,1.5507354107111482},{130,1.5507354107111482},{130,56.303938623931735},{48.09005754006398,56.303938623931735}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[2],measurementBus.dT_ssh)
    annotation (Line(points={{120.63557151341766,1.5507354107111482},{130,1.5507354107111482},{130,56.303938623931735},{48.09005754006398,56.303938623931735}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[3],measurementBus.T_sdt)
    annotation (Line(points={{120.63557151341766,1.5507354107111482},{130,1.5507354107111482},{130,56.303938623931735},{48.09005754006398,56.303938623931735}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[4],measurementBus.T_sst)
    annotation (Line(points={{120.63557151341766,1.5507354107111482},{130,1.5507354107111482},{130,56.303938623931735},{48.09005754006398,56.303938623931735}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[5],measurementBus.Fan_signal)
    annotation (Line(points={{120.63557151341766,1.5507354107111482},{126.91249405373145,1.5507354107111482},{126.91249405373145,56.303938623931735},{48.09005754006398,56.303938623931735}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[6],measurementBus.compressorFrequency)
    annotation (Line(points={{120.63557151341766,1.5507354107111482},{126.91249405373145,1.5507354107111482},{126.91249405373145,56.303938623931735},{48.09005754006398,56.303938623931735}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[7],measurementBus.T_dgt)
    annotation (Line(points={{120.63557151341766,1.5507354107111482},{126.91249405373145,1.5507354107111482},{126.91249405373145,56.303938623931735},{48.09005754006398,56.303938623931735}},color={0,0,127}));
  connect(controllerSettings.limitsBus,controller_1C.limitsBus)
    annotation (Line(points={{-8.322406927122662,82.97918360107789},{-40.11624547307057,82.97918360107789},{-40.11624547307057,6.493035309478504},{-21.27422097784322,6.493035309478504}},color={255,204,51}));
  connect(measurementBus,controller_1C.measurementBus)
    annotation (Line(points={{48.09005754006398,56.303938623931735},{-26.720375897215625,56.303938623931735},{-26.720375897215625,14.781185668364754},{-21.27422097784322,14.781185668364754}},color={255,204,51}));
  connect(measurementBus,controllerSettings.measurementBus)
    annotation (Line(points={{48.09005754006398,56.30393862393174},{29.821408717452716,56.30393862393174},{29.821408717452716,85.45699314828894},{12.012297628033727,85.45699314828894}},color={255,204,51}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end Test_control_1C_Zenithheating;
