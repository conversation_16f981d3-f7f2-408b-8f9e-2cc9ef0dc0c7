within Workspace.Controller.SubSystems;
model EXVControl_cooling
  extends.Workspace.Controller.SubSystems.BaseClasses.EXVControlBase
    annotation (Icon(coordinateSystem(preserveAspectRatio=false)),Diagram(coordinateSystem(preserveAspectRatio=false)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointController(
    AV_min=0.05,
    AV_max=1,
    AV_start=0.5,
    isOff=crkIsOff)
    annotation (Placement(transformation(extent={{54.0,-10.0},{74.0,10.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation T_sst_max_error(
    ID=2,
    measurement=Measurement_SSTmax,
    setPoint=setpoint_SSTmax,
    gain=gain_SSTmax,
    isOff=isOffSSTmax or crkIsOff)
    annotation (Placement(transformation(extent={{-100.67753538407626,8.316769387328883},{-59.322464615923735,28.30380545542959}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Min min
    annotation (Placement(transformation(extent={{6.843901550625681,14.843901550625684},{21.15609844937432,29.156098449374316}},origin={0.0,0.0},rotation=0.0)));
  parameter Boolean isOffSSTmax=false;
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation dT_ssh_error(
    setPoint=setpoint_ssh,
    ID=1,
    gain=gain_ssh,
    measurement=Measurement_ssh,
    isOff=crkIsOff)
    annotation (Placement(transformation(extent={{-101.09841898566239,26.028301170601623},{-58.90158101433761,45.97169882939838}},origin={0.0,0.0},rotation=0.0)));
  parameter Real offset_limit_sst_max=0;
  .Modelica.SIunits.TemperatureDifference Measurement_ssh=dT_ssh
    annotation (Dialog(group="SSH_control"));
  .Modelica.SIunits.TemperatureDifference setpoint_ssh=dT_ssh_setpoint
    annotation (Dialog(group="SSH_control"));
  parameter Real gain_ssh=-1/(5)
    annotation (Dialog(group="SSH_control"));
  .Modelica.SIunits.Temperature Measurement_SSTmax=T_sst
    annotation (Dialog(group="SSTmax_control"));
  .Modelica.SIunits.Temperature setpoint_SSTmax=T_sst_max_limit
    annotation (Dialog(group="SSTmax_control"));
  parameter Real gain_SSTmax=1/(298.15-220)
    annotation (Dialog(group="SSTmax_control"));
protected
  .Modelica.Blocks.Interfaces.RealOutput T_sst
    "Suction saturated temperature"
    annotation (Placement(transformation(extent={{-82.49941158124145,81.50058841875855},{-57.500588418758554,106.49941158124145}},origin={0,0},rotation=0)));
  .Modelica.Blocks.Interfaces.RealOutput dT_ssh
    "Subcooling"
    annotation (Placement(transformation(extent={{-82.49941158124145,100.50058841875855},{-57.500588418758554,125.49941158124145}},origin={0,0},rotation=0)));
  .Modelica.Blocks.Interfaces.RealOutput T_sst_max_limit
    "Suction saturated temperature max limit"
    annotation (Placement(transformation(extent={{-79.83274491457479,-42.499411581241446},{-54.8339217520919,-17.500588418758554}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput dT_ssh_setpoint
    "Subcooling setpoint"
    annotation (Placement(transformation(extent={{-79.83274491457479,-24.499411581241446},{-54.8339217520919,0.49941158124144636}},origin={0.0,0.0},rotation=0.0)));
equation
  connect(dT_ssh,measurementBus.dT_ssh)
    annotation (Line(points={{-70,113},{-99.9,113},{-99.9,60.1}},color={0,0,127}));
  connect(T_sst,measurementBus.T_sst)
    annotation (Line(points={{-70,94},{-70,77},{-99.9,77},{-99.9,60.1}},color={0,0,127}));
  connect(dT_ssh_setpoint,limitsBus.dT_ssh_setpoint)
    annotation (Line(points={{-67.3333,-12},{-87,-12},{-87,-59.95},{-99.95,-59.95}},color={0,0,127}));
  connect(dT_ssh_error.sensor,min.u1)
    annotation (Line(points={{-59.32354939405086,35.20226409364813},{-26.23982392171259,35.20226409364813},{-26.23982392171259,26.007415131649616},{6.843901550625681,26.007415131649616}},color={28,108,200}));
  connect(T_sst_max_error.sensor,min.u2)
    annotation (Line(points={{-59.73601532360526,17.510805978655206},{-26.44605688648979,17.510805978655206},{-26.44605688648979,17.70634093037541},{6.843901550625681,17.70634093037541}},color={28,108,200}));
  connect(setpointController.actuatorSignal,actuatorSignal)
    annotation (Line(points={{74.6,0},{100,0}},color={0,0,127}));
  connect(min.y,setpointController.errorSignal)
    annotation (Line(points={{21.15609844937432,22},{37.57804922468716,22},{37.57804922468716,0},{54,0}},color={28,108,200}));
  connect(T_sst_max_limit,limitsBus.T_sst_max_limit_EXV)
    annotation (Line(points={{-67.3333,-30},{-83.6667,-30},{-83.6667,-59.95},{-99.95,-59.95}},color={0,0,127}));
end EXVControl_cooling;
