within Workspace.Controller.SubSystems.Tests.FanControl;
model FanControlHeating
  .Workspace.Controller.SubSystems.FanControl_heating fanControl_Heating(
    isOffSSTmax=false,
    isOffDGTmax=false)
    annotation (Placement(transformation(extent={{-11.358175323898891,2.0},{8.641824676101109,22.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_DGT(
    y=DGT)
    annotation (Placement(transformation(extent={{-84.32091233805055,23.679087661949453},{-64.32091233805055,43.67908766194945}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression DGT_max(
    y=DGTmax)
    annotation (Placement(transformation(extent={{-86.95385205654827,-19.200055807365636},{-66.95385205654827,0.7999441926343636}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression Fan_speed_setpoint(
    y=Fan_speed_set)
    annotation (Placement(transformation(extent={{-88.0,-4.0},{-68.0,16.0}},origin={0.0,0.0},rotation=0.0)));
  parameter Real Fan_speed_set=720;
  parameter.Modelica.SIunits.Temperature DGT=373.15;
  parameter.Modelica.SIunits.Temperature SST=268.15;
  parameter.Modelica.SIunits.Temperature DGTmax=418.15;
  .Modelica.Blocks.Interfaces.RealOutput actuatorSignal
    annotation (Placement(transformation(extent={{20.0,2.0},{40.0,22.0}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{90,-10},{110,10}})));
  .Modelica.Blocks.Sources.RealExpression SST_max_fan(
    y=SST_max)
    annotation (Placement(transformation(extent={{-85.79034557857025,-34.40307856996564},{-65.79034557857025,-14.403078569965647}},origin={0.0,0.0},rotation=0.0)));
  parameter.Modelica.SIunits.Temperature SST_max=288.15;
  .Modelica.Blocks.Sources.RealExpression SST_min_fan(
    y=SST_min)
    annotation (Placement(transformation(extent={{-86.23140079084497,-56.987085107728305},{-66.23140079084497,-36.987085107728305}},origin={0.0,0.0},rotation=0.0)));
  parameter.Modelica.SIunits.Temperature SST_min=270.15;
  .Modelica.Blocks.Sources.RealExpression T_sst(
    y=SST)
    annotation (Placement(transformation(extent={{-85.72470456447694,41.53003571855605},{-65.72470456447694,61.53003571855605}},origin={0.0,0.0},rotation=0.0)));
equation
  connect(T_DGT.y,fanControl_Heating.measurementBus.T_dgt)
    annotation (Line(points={{-63.32091233805055,33.67908766194945},{-63.32091233805055,18},{-11.358175323898891,18}},color={0,0,127}));
  connect(DGT_max.y,fanControl_Heating.limitsBus.T_dgt_max_limit_fan)
    annotation (Line(points={{-65.95385205654827,-9.200055807365636},{-56,-9.200055807365636},{-56,6},{-11.358175323898891,6}},color={0,0,127}));
  connect(Fan_speed_setpoint.y,fanControl_Heating.limitsBus.fanSpeed_setpoint)
    annotation (Line(points={{-67,6},{-11.358175323898891,6}},color={0,0,127}));
  connect(fanControl_Heating.actuatorSignal,actuatorSignal)
    annotation (Line(points={{8.641824676101109,12},{30,12}},color={0,0,127}));
  connect(SST_max_fan.y,fanControl_Heating.limitsBus.T_sst_max_limit_fan)
    annotation (Line(points={{-64.79034557857025,-24.403078569965643},{-38.39517278928513,-24.403078569965643},{-38.39517278928513,6},{-11.358175323898891,6}},color={0,0,127}));
  connect(SST_min_fan.y,fanControl_Heating.limitsBus.T_sst_min_limit_fan)
    annotation (Line(points={{-65.23140079084497,-46.987085107728305},{-38.615700395422486,-46.987085107728305},{-38.615700395422486,6},{-11.358175323898891,6}},color={0,0,127}));
  connect(T_sst.y,fanControl_Heating.measurementBus.T_sst)
    annotation (Line(points={{-64.72470456447694,51.53003571855605},{-38.201896113213195,51.53003571855605},{-38.201896113213195,18},{-11.358175323898891,18}},color={0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end FanControlHeating;
