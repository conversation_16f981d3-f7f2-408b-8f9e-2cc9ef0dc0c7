within Workspace.Auxiliary.ECAT_Zenith.EcatBase_1;
model ECATVariable
  "ECAT variable that can be fixed/unfixed in from ECAT request "
  replaceable type VariableType=Real;
  input VariableType value=1
    "Variable value calculated in the model  RRH 2019/05/28 changed VariableType to input VariableType"
    annotation (Dialog);
  parameter VariableType setPoint=1
    "Set-point value from the ECAT request";
  parameter <PERSON><PERSON><PERSON> fixed=true
    "Fixed flag";
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false),
      graphics={
        Rectangle(
          extent={{-100,38},{100,-100}},
          lineColor={28,108,200},
          fillColor={255,255,255},
          fillPattern=FillPattern.Solid),
        Text(
          extent={{0,-2},{0,-62}},
          lineColor={28,108,200},
          textString="%value"),
        Text(
          extent={{-132,72},{140,40}},
          lineColor={28,108,200},
          textString="%name")}),
    defaultComponentPrefixes="replaceable",
    Diagram(
      coordinateSystem(
        preserveAspectRatio=false)));
end ECATVariable;
