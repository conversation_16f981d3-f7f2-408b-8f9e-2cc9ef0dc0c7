within Workspace.Icons;
model BPHX_Ref
  "Icon for brazed plate heat exchangers"
  annotation (
    Icon(
      graphics={
        Bitmap(
          extent={{-100,-94},{96,64}},
          fileName="../UTCTSD2/Images/BPHX_Ref.png"),
        Text(
          extent={{-72,94},{70,66}},
          lineColor={0,0,255},
          textString="%name")},
      Bitmap(
        extent=[
          -100,-94;
          96,64],
        name="../UTCTSD2/Images/BPHX_Ref.png"),
      Text(
        extent=[
          -72,94;
          70,66],
        style(
          color=3,
          rgbcolor={0,0,255}),
        string="%name")),
    Documentation(
      revisions="<html>
</html>"));
//     annotation (
//           Documentation(revisions="<html>
// </html>"), Icon(graphics={
//         Ellipse(
//           extent={{-48,-60},{-30,-78}},
//           lineColor={0,0,0},
//           fillColor={255,255,255},
//           fillPattern=FillPattern.Solid),
//         Ellipse(
//           extent={{14,-60},{32,-78}},
//           lineColor={0,0,0},
//           fillColor={255,255,255},
//           fillPattern=FillPattern.Solid),
//         Ellipse(
//           extent={{14,72},{32,54}},
//           lineColor={0,0,0},
//           fillColor={255,255,255},
//           fillPattern=FillPattern.Solid),
//         Ellipse(
//           extent={{-48,72},{-30,54}},
//           lineColor={0,0,0},
//           fillColor={255,255,255},
//           fillPattern=FillPattern.Solid),
//         Ellipse(
//           extent={{110,80},{132,58}},
//           lineColor={4,200,43},
//           fillColor={255,255,255},
//           fillPattern=FillPattern.Solid,
//           lineThickness=1),
//         Ellipse(
//           extent={{114,26},{132,8}},
//           lineColor={28,108,200},
//           fillColor={255,0,0},
//           fillPattern=FillPattern.Solid),
//         Ellipse(
//           extent={{118,50},{136,32}},
//           lineColor={28,108,200},
//           fillColor={255,0,0},
//           fillPattern=FillPattern.Solid),
//         Ellipse(
//           extent={{92,94},{110,76}},
//           lineColor={0,0,0},
//           fillColor={255,255,255},
//           fillPattern=FillPattern.Solid),
//         Line(points={{-38,72},{24,72}}, color={0,0,0}),
//         Line(points={{-40,-78},{24,-78}}, color={0,0,0}),
//         Line(points={{-48,64},{-48,62},{-48,-68}}, color={0,0,0}),
//         Line(points={{32,64},{32,-72}}, color={0,0,0})}));
end BPHX_Ref;
