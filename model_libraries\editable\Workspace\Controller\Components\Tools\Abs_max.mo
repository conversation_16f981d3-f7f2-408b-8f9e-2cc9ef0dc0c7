within Workspace.Controller.Components.Tools;
block Abs_max
  "Max Absolute value of two input signals"

  output BOLT.InternalLibrary.Controls_v2.SteadyState.SetpointControl.SubComponents.SensorSignal y(ID(start = -1))
    "Maximum of the two inputs"
    annotation (Placement(transformation(extent = {{90, -10}, {110, 10}}), iconTransformation(extent = {{90, -10}, {110, 10}})));
  input BOLT.InternalLibrary.Controls_v2.SteadyState.SetpointControl.SubComponents.SensorSignal u1
    "Input u1"
    annotation (
      Placement(
        transformation(extent = {{-110, 46}, {-90, 66}}),
        iconTransformation(extent = {{-110, 46}, {-90, 66}})));
  input BOLT.InternalLibrary.Controls_v2.SteadyState.SetpointControl.SubComponents.SensorSignal u2
    "Input u2"
    annotation (
      Placement(
        transformation(extent = {{-110, -70}, {-90, -50}}),
        iconTransformation(extent = {{-110, -70}, {-90, -50}})));
  parameter Real alpha = 1e-4
    "Smoothing factor";
  Real sign_u1 = BOLT.InternalLibrary.BuildingBlocks.Utilities.Math.Smoothing.softSign(u1.sensor);
  Real sign_u2 = BOLT.InternalLibrary.BuildingBlocks.Utilities.Math.Smoothing.softSign(u2.sensor);

  outer BOLT.Control.SteadyState.Utilities.CalculateExtraOutputs calculateExtraOutputs
    "Triggers computation of signal IDs"
    annotation (Dialog(enable = false));

equation

  y.setOff = u1.setOff and u2.setOff;

  if y.setOff then
    // Both sensors off
    y.sensor = 0;
  elseif u1.setOff and not u2.setOff then
    // Only input u2 is active
    y.sensor = u2.sensor;
  elseif u2.setOff and not u1.setOff then
    // Only input u1 is active
    y.sensor = u1.sensor;
  else
    // Both inputs are active then take the maximum value
    if noEvent(BOLT.InternalLibrary.BuildingBlocks.Utilities.Math.Smoothing.softAbs(u1.sensor,alpha) > BOLT.InternalLibrary.BuildingBlocks.Utilities.Math.Smoothing.softAbs(u2.sensor,alpha)) then
        y.sensor = u1.sensor;
    else
        y.sensor = u2.sensor;
    end if;
   end if;

  when (calculateExtraOutputs) then
    if y.setOff then
      // Both sensors are off
      y.ID = -1;
    elseif u1.setOff and not u2.setOff then
      // Only input u2 is active
      y.ID = u2.ID;
    elseif u2.setOff and not u1.setOff then
      // Only input u1 is active
      y.ID = u1.ID;
    else
      // Both inputs are active, propagate ID of signal with maximum value to y
      if noEvent(BOLT.InternalLibrary.BuildingBlocks.Utilities.Math.Smoothing.softAbs(u1.sensor,alpha) > BOLT.InternalLibrary.BuildingBlocks.Utilities.Math.Smoothing.softAbs(u2.sensor,alpha)) then
        y.ID = u1.ID;
      else
        y.ID = u2.ID;
      end if;
    end if;
  end when;

  annotation (
    Icon(
      coordinateSystem(preserveAspectRatio = false),
      graphics = {Rectangle(extent={{-100,100},{100,-100}},fillColor={210,210,210},lineThickness=5.0,fillPattern=FillPattern.Solid,borderPattern=BorderPattern.Raised),Text(extent={{-2,12},{-2,-12}},lineColor={28,108,200},textString="y = Max(|u1|, |u2|)")}),
    Diagram(coordinateSystem(preserveAspectRatio = false)),
    Documentation(
      info = "<html>
<body>
<p>
This component will compare two input signal absolute values and output the maximum value. 

The signal <b>ID</b> of the selected input will be propagated to the output signal <b>y</b>.  


<br>
The smoothing factor <b>alpha</b> is used in <a href=\"BOLT.InternalLibrary.BuildingBlocks.Utilities.Math.Smoothing.softMax\">softMax</a> to smoothen the max calculations.
</br>

</p>
</body> 
</html>
",
      revisions = "<html>
<ul>
<li><i>05 may 2025</i> by Adrien Abbate:<br>Prototype developed.</li>
</ul>
</html>"));

end Abs_max;
