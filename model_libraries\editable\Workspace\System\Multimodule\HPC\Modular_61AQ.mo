within Workspace.System.Multimodule.HPC;
model Modular_61AQ
  extends.Workspace.Controller.CL_control_system_cooling_zenith(
    controllerSettings_crkA(
      Capacity_setpoint=TargetCapacity,
      SST_min=273.65,
      fancoefficients=Module.fanCurveCoefficientsCooling[1,:],
      extPressure_setpoint=Pdispo,
      maxfanfreq=if is_ULN_option then Module.max_fan_ULN_cooling[1] else Module.max_fan_frequency[1],
      Manual_ssh=5,
      LWT_setpoint=LWT,
      EWT_setpoint=EWT,
      minfanfreq=10),
    controllerSettings_crkB(
      Capacity_setpoint=TargetCapacity,
      SST_min=273.65,
      fancoefficients=Module.fanCurveCoefficientsCooling[2,:],
      extPressure_setpoint=Pdispo,
      maxfanfreq=if is_ULN_option then Module.max_fan_ULN_cooling[2] else Module.max_fan_frequency[2],
      Manual_ssh=5,
      LWT_setpoint=LWT,
      EWT_setpoint=EWT,
      minfanfreq=10),
    controller_crkA(
      isOff=isOFF,
      isOffSDTmin_fan=false,
      isOffSDTmax_fan=false,
      isOffDGTmax_fan=false,
      isOffSSTmin_EXV=false,
      isOffSSTmax_EXV=false,
      isOffDSHmin_EXV=false,
      isOffDGTmax_EXV=false,
      isOffSSTmin_comp=false,
      isOffSDTmax_comp=false,
      isOffDGTmax_comp=false,
      Fan_MaxFrequency=controllerSettings_crkA.maxfanfreq,
      completeCompressorControl_base(
        compressorControl(
          manualOff=controller_crkA.manualOff_compressor_block_A,
          AV_value_off=controller_crkA.frq_comp_sp_manual_block_A)),
      fanControl(
        manualOff=controller_crkA.manualOff_fan_block_A,
        AV_value_off=controller_crkA.frq_fan_sp_manual_block_A),
      manualOff_fan_block_A=false,
      manualOff_fan_block_B=false,
      manualOff_compressor_block_A=false,
      manualOff_compressor_block_B=false,
      Fan_MinFrequency=controllerSettings_crkA.minfanfreq,
      min_speed=Module.fmin[1],
      max_speed=if is_ULN_option then Module.max_cp_ULN_cooling[1] else Module.fmax[1],
      load_ratio=LoadRatio,
      is_load_ratio=LoadRatio_fixed,
      is_LWTcontrol=LWT_fixed,
      is_EWTcontrol=EWT_fixed and not LWT_fixed,
      Use_fake_pump=Module.Use_fake_pump),
    controller_crkB(
      isOff=Module.is_monobloc or isOFF,
      isOffSDTmin_fan=false,
      isOffSDTmax_fan=false,
      isOffDGTmax_fan=false,
      isOffSSTmin_EXV=false,
      isOffSSTmax_EXV=false,
      isOffDSHmin_EXV=false,
      isOffDGTmax_EXV=false,
      isOffSSTmin_comp=false,
      isOffSDTmax_comp=false,
      isOffDGTmax_comp=false,
      Fan_MaxFrequency=controllerSettings_crkB.maxfanfreq,
      fanControl(
        manualOff=controller_crkB.manualOff_fan_block_B,
        AV_value_off=controller_crkB.frq_fan_sp_manual_block_B),
      completeCompressorControl_base(
        compressorControl(
          manualOff=controller_crkB.manualOff_compressor_block_B,
          AV_value_off=controller_crkB.frq_comp_sp_manual_block_B)),
      Fan_MinFrequency=controllerSettings_crkB.minfanfreq,
      min_speed=Module.fmin[2],
      max_speed=if is_ULN_option then Module.max_cp_ULN_cooling[2] else Module.fmax[2],
      load_ratio=LoadRatio,
      is_load_ratio=LoadRatio_fixed,
      is_LWTcontrol=LWT_fixed,
      is_EWTcontrol=EWT_fixed and not LWT_fixed,
      Use_fake_pump=Module.Use_fake_pump));
  .Workspace.System.Multimodule.HPC.Equipement_Modular Module(
    capacity_design={choiceBlock.Unit_Block_A.capacity_design,choiceBlock.Unit_Block_B.capacity_design},
    fmax={choiceBlock.Unit_Block_A.fmax,choiceBlock.Unit_Block_B.fmax},
    fmin={choiceBlock.Unit_Block_A.fmin,choiceBlock.Unit_Block_B.fmin},
    Dport_coolant_a={choiceBlock.Unit_Block_A.cond_diameter_brine_port_in,choiceBlock.Unit_Block_B.cond_diameter_brine_port_in},
    Dport_coolant_b={choiceBlock.Unit_Block_A.cond_diameter_brine_port_out,choiceBlock.Unit_Block_B.cond_diameter_brine_port_out},
    Dport_ref_a={choiceBlock.Unit_Block_A.cond_diameter_ref_port_in,choiceBlock.Unit_Block_B.cond_diameter_ref_port_in},
    Dport_ref_b={choiceBlock.Unit_Block_A.cond_diameter_ref_port_out,choiceBlock.Unit_Block_B.cond_diameter_ref_port_out},
    nPlate={choiceBlock.Unit_Block_A.nplate,choiceBlock.Unit_Block_B.nplate},
    nCoils={choiceBlock.Unit_Block_A.nCoils,choiceBlock.Unit_Block_B.nCoils},
    Itube={choiceBlock.Unit_Block_A.Itube,choiceBlock.Unit_Block_B.Itube},
    nCir={choiceBlock.Unit_Block_A.nCir,choiceBlock.Unit_Block_B.nCir},
    Ntube={choiceBlock.Unit_Block_A.Ntube,choiceBlock.Unit_Block_B.Ntube},
    Nrow={choiceBlock.Unit_Block_A.Nrow,choiceBlock.Unit_Block_B.Nrow},
    Ltube={choiceBlock.Unit_Block_A.Ltube,choiceBlock.Unit_Block_B.Ltube},
    Dotube={choiceBlock.Unit_Block_A.Dotube,choiceBlock.Unit_Block_B.Dotube},
    Ttube={choiceBlock.Unit_Block_A.Ttube,choiceBlock.Unit_Block_B.Ttube},
    Ptube={choiceBlock.Unit_Block_A.Ptube,choiceBlock.Unit_Block_B.Ptube},
    Prow={choiceBlock.Unit_Block_A.Prow,choiceBlock.Unit_Block_B.Prow},
    Dfin={choiceBlock.Unit_Block_A.Dfin,choiceBlock.Unit_Block_B.Dfin},
    Tfin={choiceBlock.Unit_Block_A.Tfin,choiceBlock.Unit_Block_B.Tfin},
    Fw_fan={choiceBlock.Unit_Block_A.Fan_FW,choiceBlock.Unit_Block_B.Fan_FW},
    max_fan_frequency={choiceBlock.Unit_Block_A.max_fan_frequency,choiceBlock.Unit_Block_B.max_fan_frequency},
    Suction_line_diameter={choiceBlock.Unit_Block_A.Suction_line_diameter,choiceBlock.Unit_Block_B.Suction_line_diameter},
    Suction_line_length={choiceBlock.Unit_Block_A.Suction_line_length,choiceBlock.Unit_Block_B.Suction_line_length},
    coil_line_diameter={choiceBlock.Unit_Block_A.Coil_line_diameter,choiceBlock.Unit_Block_B.Coil_line_diameter},
    coil_line_length={choiceBlock.Unit_Block_A.Coil_line_length,choiceBlock.Unit_Block_B.Coil_line_length},
    liquid_line_diameter={choiceBlock.Unit_Block_A.Liquid_line_diameter,choiceBlock.Unit_Block_B.Liquid_line_diameter},
    liquid_line_length={choiceBlock.Unit_Block_A.Liquid_line_length,choiceBlock.Unit_Block_B.Liquid_line_length},
    discharge_line_diameter={choiceBlock.Unit_Block_A.discharge_line_diameter,choiceBlock.Unit_Block_B.discharge_line_diameter},
    discharge_line_length={choiceBlock.Unit_Block_A.discharge_line_length,choiceBlock.Unit_Block_B.discharge_line_length},
    EXV_in_line_diameter={choiceBlock.Unit_Block_A.EXV_in_line_diameter,choiceBlock.Unit_Block_B.EXV_in_line_diameter},
    EXV_in_line_length={choiceBlock.Unit_Block_A.EXV_in_line_length,choiceBlock.Unit_Block_B.EXV_in_line_length},
    Ac_duct={choiceBlock.Unit_Block_A.duct_Ac,choiceBlock.Unit_Block_B.duct_Ac},
    Ka_duct={choiceBlock.Unit_Block_A.duct_Ka,choiceBlock.Unit_Block_B.duct_Ka},
    UA_duct={choiceBlock.Unit_Block_A.duct_UA,choiceBlock.Unit_Block_B.duct_UA},
    selector_Comp={choiceBlock.Unit_Block_A.selector_Comp,choiceBlock.Unit_Block_B.selector_Comp},
    EXV_main={choiceBlock.Unit_Block_A.EXV_main_A,choiceBlock.Unit_Block_B.EXV_main_A},
    selector_geo_BPHE={choiceBlock.Unit_Block_A.cond_select_geo,choiceBlock.Unit_Block_B.cond_select_geo},
    selector_pump={choiceBlock.Unit_Block_A.Pump_type,choiceBlock.Unit_Block_B.Pump_type},
    fanCurveCoefficientsHeating={choiceBlock.Unit_Block_A.fanCurveCoefficientsHeating,choiceBlock.Unit_Block_B.fanCurveCoefficientsHeating},
    CompVoltage={choiceBlock.Unit_Block_A.CompVoltage,choiceBlock.Unit_Block_B.CompVoltage},
    fanCurveCoefficientsCooling={choiceBlock.Unit_Block_A.fanCurveCoefficientsCooling,choiceBlock.Unit_Block_B.fanCurveCoefficientsCooling},
    use_bf=use_bf,
    BrineConcentration=BrineConcentration,
    mdot_start=Module.capacity_design[1]/(((4180*(Module.EWT-Module.LWT)))),
    isCoating=choiceBlock.isCoatingOption,
    relative_humidity=Relative_humidity,
    PDC_4WV={choiceBlock.Unit_Block_A.PDC_4WV,choiceBlock.Unit_Block_B.PDC_4WV},
    Zflow_intercept={choiceBlock.Unit_Block_A.Zflow_intercept,choiceBlock.Unit_Block_B.Zflow_intercept},
    Zflow_SST={choiceBlock.Unit_Block_A.Zflow_SST,choiceBlock.Unit_Block_B.Zflow_SST},
    Zflow_SDT={choiceBlock.Unit_Block_A.Zflow_SDT,choiceBlock.Unit_Block_B.Zflow_SDT},
    Zflow_heatcap={choiceBlock.Unit_Block_A.Zflow_Heatcap,choiceBlock.Unit_Block_B.Zflow_Heatcap},
    Zpower_intercept={choiceBlock.Unit_Block_A.Zpower_intercept,choiceBlock.Unit_Block_B.Zpower_intercept},
    Zpower_DGT={choiceBlock.Unit_Block_A.Zpower_DGT,choiceBlock.Unit_Block_B.Zpower_DGT},
    Zpower_SST={choiceBlock.Unit_Block_A.Zpower_SST,choiceBlock.Unit_Block_B.Zpower_SST},
    Zpower_SST2={choiceBlock.Unit_Block_A.Zpower_SST2,choiceBlock.Unit_Block_B.Zpower_SST2},
    FW={choiceBlock.Unit_Block_A.FW,choiceBlock.Unit_Block_B.FW},
    use_Calib=use_Calib,
    Zevap_HPH_cst={choiceBlock.Unit_Block_A.Zevap_chaud_intercept,choiceBlock.Unit_Block_B.Zevap_chaud_intercept},
    Zevap_HPH_SST={choiceBlock.Unit_Block_A.Zevap_chaud_SST,choiceBlock.Unit_Block_B.Zevap_chaud_SST},
    Zevap_HPC_cst={choiceBlock.Unit_Block_A.Zevap_froid_intercept,choiceBlock.Unit_Block_B.Zevap_froid_intercept},
    Zevap_HPC_SST={choiceBlock.Unit_Block_A.Zevap_froid_SST,choiceBlock.Unit_Block_B.Zevap_froid_SST},
    Zcond_HPH_cst={choiceBlock.Unit_Block_A.Zcond_chaud_intercept,choiceBlock.Unit_Block_B.Zcond_chaud_intercept},
    Zcond_HPH_heatcap={choiceBlock.Unit_Block_A.Zcond_chaud_Heatcap,choiceBlock.Unit_Block_B.Zcond_chaud_Heatcap},
    Zcond_HPH_SST={choiceBlock.Unit_Block_A.Zcond_chaud_SST,choiceBlock.Unit_Block_B.Zcond_chaud_SST},
    Zcond_HPC_cst={choiceBlock.Unit_Block_A.Zcond_froid_intercept,choiceBlock.Unit_Block_B.Zcond_froid_intercept},
    Zcond_HPC_SDT={choiceBlock.Unit_Block_A.Zcond_froid_SDT,choiceBlock.Unit_Block_B.Zcond_froid_SDT},
    Zcond_HPH_SDT={choiceBlock.Unit_Block_A.Zcond_chaud_SDT,choiceBlock.Unit_Block_B.Zcond_chaud_SDT},
    EvapFoulingFactor=0,
    SC_setpoint=SC_setpoint,
    SC_fixed=SC_fixed,
    Mref_fixed=Mref_fixed,
    use_en=use_en,
    Use_fake_pump=not choiceBlock.is_Pump,
    EWT=EWT,
    LWT=LWT,
    OAT=OAT,
    M_ref={choiceBlock.Unit_Block_A.m_ref_froid,choiceBlock.Unit_Block_B.m_ref_froid},
    isOFF=isOFF,
    is_monobloc=is_monobloc,
    CoolantMedium=CoolantMedium,
    Use_pump=choiceBlock.is_Pump,
    Zpower_Ncomp={choiceBlock.Unit_Block_A.Zpower_Ncomp,choiceBlock.Unit_Block_B.Zpower_Ncomp},
    Zpower_SDT={choiceBlock.Unit_Block_A.Zpower_SDT,choiceBlock.Unit_Block_B.Zpower_SDT},
    Zpower_heatcap={choiceBlock.Unit_Block_A.Zpower_heatcap,choiceBlock.Unit_Block_B.Zpower_heatcap},
    Zevap_coated_HPH={choiceBlock.Unit_Block_A.Zevap_coated_chaud,choiceBlock.Unit_Block_B.Zevap_coated_chaud},
    Cap_SEER_A={choiceBlock.Unit_Block_A.SEER_A_cap,choiceBlock.Unit_Block_B.SEER_A_cap},
    isFilter=isFilter,
    Coef_filter=Coef_filter,
    isBufferTank=isBufferTank,
    Coef_bufferTank=Coef_bufferTank,
    Zcond_HPC_coated={choiceBlock.Unit_Block_A.Zcond_chaud_coated,choiceBlock.Unit_Block_B.Zcond_chaud_coated},
    const_bf_cap=choiceBlock.Unit_Block_A.const_bf_cap_froid,
    OAT_bf_cap=choiceBlock.Unit_Block_A.OAT_bf_cap_froid,
    load_bf_cap=choiceBlock.Unit_Block_A.load_bf_cap_froid,
    const_bf_pow=choiceBlock.Unit_Block_A.const_bf_pow_froid,
    load_bf_pow=choiceBlock.Unit_Block_A.load_bf_pow_froid,
    OAT_bf_pow=choiceBlock.Unit_Block_A.OAT_bf_pow_froid,
    bf_cap_max=choiceBlock.Unit_Block_A.max_bf_cap_froid,
    bf_cap_min=choiceBlock.Unit_Block_A.min_bf_cap_froid,
    bf_pow_max=choiceBlock.Unit_Block_A.max_bf_pow_froid,
    bf_pow_min=choiceBlock.Unit_Block_A.min_bf_pow_froid,Zevap_chaud_Coolcap = {choiceBlock.Unit_Block_A.Zevap_chaud_Coolcap,choiceBlock.Unit_Block_B.Zevap_chaud_Coolcap},
    Zevap_froid_Coolcap = {choiceBlock.Unit_Block_A.Zevap_froid_Coolcap,choiceBlock.Unit_Block_B.Zevap_froid_Coolcap},
    max_cp_ULN_heating = {choiceBlock.Unit_Block_A.max_cp_ULN_heating,choiceBlock.Unit_Block_B.max_cp_ULN_heating},
    max_cp_ULN_cooling = {choiceBlock.Unit_Block_A.max_cp_ULN_cooling,choiceBlock.Unit_Block_B.max_cp_ULN_cooling},
    max_fan_ULN_heating = {choiceBlock.Unit_Block_A.max_fan_ULN_heating,choiceBlock.Unit_Block_B.max_fan_ULN_heating},
    max_fan_ULN_cooling = {choiceBlock.Unit_Block_A.max_fan_ULN_cooling,choiceBlock.Unit_Block_B.max_fan_ULN_cooling},
    Altitude = Altitude,
    is_ULN_option = is_ULN_option,
    Cap_SEER_A_LN = {choiceBlock.Unit_Block_A.SEER_A_cap_LN_option,choiceBlock.Unit_Block_B.SEER_A_cap_LN_option},
    Zflow_Ncomp = {choiceBlock.Unit_Block_A.Zflow_Ncomp,choiceBlock.Unit_Block_B.Zflow_Ncomp},
    Zflow_SST2 = {choiceBlock.Unit_Block_A.Zflow_SST2,choiceBlock.Unit_Block_B.Zflow_SST2},
    Zflow_SST3 = {choiceBlock.Unit_Block_A.Zflow_SST3,choiceBlock.Unit_Block_B.Zflow_SST3},
    Zevap_HPH_min = {choiceBlock.Unit_Block_A.Z_evap_HPH_min,choiceBlock.Unit_Block_B.Z_evap_HPH_min},Zevap_HPH_max = {choiceBlock.Unit_Block_A.Z_evap_HPH_max,choiceBlock.Unit_Block_B.Z_evap_HPH_max},Zevap_HPC_min = {choiceBlock.Unit_Block_A.Z_evap_HPC_min,choiceBlock.Unit_Block_B.Z_evap_HPC_min},Zevap_HPC_max = {choiceBlock.Unit_Block_A.Z_evap_HPC_max,choiceBlock.Unit_Block_B.Z_evap_HPC_max},Zcond_HPH_min = {choiceBlock.Unit_Block_A.Z_cond_HPH_min,choiceBlock.Unit_Block_B.Z_cond_HPH_min},Zcond_HPH_max = {choiceBlock.Unit_Block_A.Z_cond_HPH_max,choiceBlock.Unit_Block_B.Z_cond_HPH_max},Zcond_HPC_min = {choiceBlock.Unit_Block_A.Z_cond_HPC_min,choiceBlock.Unit_Block_B.Z_cond_HPC_min},Zcond_HPC_max = {choiceBlock.Unit_Block_A.Z_cond_HPC_max,choiceBlock.Unit_Block_B.Z_cond_HPC_max},Zpower_min = {choiceBlock.Unit_Block_A.Z_power_min,choiceBlock.Unit_Block_B.Z_power_min},Zpower_max = {choiceBlock.Unit_Block_A.Z_power_max,choiceBlock.Unit_Block_B.Z_power_max},Zflow_min = {choiceBlock.Unit_Block_A.Z_flow_min,choiceBlock.Unit_Block_B.Z_flow_min},Zflow_max = {choiceBlock.Unit_Block_A.Z_flow_max,choiceBlock.Unit_Block_B.Z_flow_max})
    annotation (Placement(transformation(extent={{-25.14564182511713,7.135197287110209},{-5.145641825117133,27.13519728711021}},origin={0.0,0.0},rotation=0.0)));
  parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector CoolantMedium=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector.FW
    "Coolant Medium Selection"
    annotation (Dialog(group="Medium"));
  final parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Empty coolantInf=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.getSelector(
    CoolantMedium)
    "Coolant Medium"
    annotation ();
  .BOLT.InternalLibrary.Media.Coolant.CoolantCommon.Temperature FreezTemp=.BOLT.InternalLibrary.Media.Coolant.CoolantCommon.freezeTemp(
    coolantInf,
    BrineConcentration,
    1);
  parameter Boolean isOFF=false
    annotation (Dialog(group="isOff"));
  parameter Boolean use_en=true
    annotation (Dialog(group="Use Parameters"));
  parameter Boolean use_bf=false
    annotation (Dialog(group="Use Parameters"));
  parameter Boolean use_Calib=false
    annotation (Dialog(group="Use Parameters"));
  parameter Boolean LWT_fixed=true
    annotation (Dialog(group="Conditions"));
  parameter.Modelica.SIunits.Temperature LWT=280.15
    annotation (Dialog(group="Conditions"));
  parameter Boolean EWT_fixed=true
    annotation (Dialog(group="Conditions"));
  parameter.Modelica.SIunits.Temperature EWT=285.15
    annotation (Dialog(group="Conditions"));
  parameter Boolean FlowRate_fixed=false
    annotation (Dialog(group="Conditions"));
  parameter.Modelica.SIunits.VolumeFlowRate FlowRate=0.015
    annotation (Dialog(group="Conditions"));
  parameter.Modelica.SIunits.Temperature OAT=308.15
    annotation (Dialog(group="Conditions"));
  parameter Real BrineConcentration=0.35
    annotation (Dialog(group="Medium"));
  .Workspace.Auxiliary.OptionBlock.ChoiceBlock choiceBlock(
    is_monobloc=is_monobloc,
    Selector_Block_A=Selector_block_A,
    Selector_Block_B=Selector_block_B,
    Pump_selector=Pump_selector,
    Coating_selector=Coating_selector,
    SoundOption_selector = Sound_selector)
    annotation (Placement(transformation(extent={{-88.31364868719947,71.85908337599243},{-67.54500640098144,92.62772566221047}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.InternalLibrary.BuildingBlocks.Coolant.Ports.FluidPort_a inlet
    annotation (Placement(transformation(extent={{-24.404909738719212,-60.7852707838424},{-4.404909738719214,-40.7852707838424}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={0,-100})));
  .BOLT.InternalLibrary.BuildingBlocks.Coolant.Ports.FluidPort_b outlet
    annotation (Placement(transformation(extent={{-24.0,82.19990902251452},{-4.0,102.19990902251452}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={0,100})));
  .Modelica.Blocks.Interfaces.RealInput capacity_total
    annotation (Placement(transformation(extent={{-161.28852756003997,-43.04314230407182},{-137.88787862115456,-19.64249336518641}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-10,-10},{10,10}},rotation=0,origin={-110,0})));
  parameter.Modelica.SIunits.Power TargetCapacity=80000
    annotation (Dialog(group="Cp_control"));
  .Modelica.SIunits.Frequency CompressorFreq=Module.BlockA.compressor.summary.Ncomp
    "Only compressor Block A since module are symetric";
  parameter.Workspace.Auxiliary.OptionBlock.Record_Base.Selector Selector_block_A=.Workspace.Auxiliary.OptionBlock.Record_Base.Selector.Unit_Z_040
    annotation (Dialog(group="Unit",tab="ChoiceBlock"));
  parameter.Workspace.Auxiliary.OptionBlock.Record_Base.Selector Selector_block_B=.Workspace.Auxiliary.OptionBlock.Record_Base.Selector.Unit_Z_040
    annotation (Dialog(group="Unit",tab="ChoiceBlock"));
  parameter Boolean is_monobloc=false
    annotation (Dialog(group="Units",tab="ChoiceBlock"));
  parameter.Workspace.Auxiliary.OptionBlock.PumpOption.Pump_Option Pump_selector=.Workspace.Auxiliary.OptionBlock.PumpOption.Pump_Option.STANDARD
    annotation (Dialog(group="Options",tab="ChoiceBlock"));
  parameter.Workspace.Auxiliary.OptionBlock.SoundOption.Selector Sound_selector=.Workspace.Auxiliary.OptionBlock.SoundOption.Selector.STANDARD
    annotation (Dialog(group="Options",tab="ChoiceBlock"));
  parameter.Workspace.Auxiliary.OptionBlock.CoatingOption.Coating_selector Coating_selector=.Workspace.Auxiliary.OptionBlock.CoatingOption.Coating_selector.STANDARD
    annotation (Dialog(group="Options",tab="ChoiceBlock"));
  parameter Real Relative_humidity=0.87
    annotation (Dialog(group="Conditions",tab="General"));
  parameter.Modelica.SIunits.PressureDifference Pdispo=0
    annotation (Dialog(group="Conditions",tab="General"));
  parameter Real LoadRatio=1
    annotation (Dialog(group="Cp_control",tab="General"));
  parameter Boolean LoadRatio_fixed=false
    annotation (Dialog(group="Cp_control",tab="General"));
  parameter.Modelica.SIunits.TemperatureDifference[2] SC_setpoint={-2,-2}
    annotation (Dialog(group="Refrigerant"));
  parameter Boolean[2] SC_fixed={true,true}
    annotation (Dialog(group="Refrigerant"));
  parameter Boolean[2] Mref_fixed={false,false}
    annotation (Dialog(group="Refrigerant"));
  parameter Boolean is_monobloc_db_initialization
    annotation (Dialog(tab="Initialization"));
  parameter Boolean isFilter=false
    annotation (Dialog(group="Options",tab="ChoiceBlock"));
  parameter Real[2] Coef_filter={0.00001,0}
    annotation (Dialog(group="Options",tab="ChoiceBlock"));
  parameter Boolean isBufferTank=false
    annotation (Dialog(group="Options",tab="ChoiceBlock"));
  parameter Real[2] Coef_bufferTank={0.00001,0}
    annotation (Dialog(group="Options",tab="ChoiceBlock"));
  .Modelica.SIunits.PressureDifference InternalPressureDrop=Module.InternalPressureDrop;
  .Modelica.SIunits.PressureDifference AvailableStaticPressure=Module.AvailableStaticPressure;
  .Workspace.Interfaces.MeasurementBus measurementBus
    annotation (Placement(transformation(extent={{-118.80703337193755,79.8320989500465},{-78.80703337193755,119.8320989500465}},origin={0,0},rotation=0)));
    parameter Real Altitude;
    parameter Boolean is_ULN_option = if Integer(choiceBlock.SoundOption_selector) == 1 then false else true;
    parameter Real Max_targetcap = if is_ULN_option then (if is_monobloc then choiceBlock.Unit_Block_A.SEER_A_cap_LN_option else choiceBlock.Unit_Block_A.SEER_A_cap_LN_option + choiceBlock.Unit_Block_B.SEER_A_cap_LN_option) else (if is_monobloc then choiceBlock.Unit_Block_A.SEER_A_cap else choiceBlock.Unit_Block_A.SEER_A_cap + choiceBlock.Unit_Block_B.SEER_A_cap) annotation(Dialog(group = "Cp_control"));
equation
  connect(Module.Compressor_controller_A,controller_crkA.compressor)
    annotation (Line(points={{-26.14564182511713,9.63519728711021},{-35.99161292982694,9.63519728711021},{-35.99161292982694,11.142786159859877},{-49.910042394053335,11.142786159859877}},color={0,0,127}));
  connect(controller_crkA.exv,Module.EXV_controller_A)
    annotation (Line(points={{-49.910042394053335,8.642786159859877},{-35.99161292982694,8.642786159859877},{-35.99161292982694,19.63519728711021},{-26.14564182511713,19.63519728711021}},color={0,0,127}));
  connect(controller_crkA.fan,Module.Fan_controller_A)
    annotation (Line(points={{-49.910042394053335,6.142786159859877},{-35.99161292982694,6.142786159859877},{-35.99161292982694,14.63519728711021},{-26.14564182511713,14.63519728711021}},color={0,0,127}));
  connect(controller_crkA.pump,Module.ActuatorPumpUser_A)
    annotation (Line(points={{-49.910042394053335,3.6427861598598774},{-35.99161292982694,3.6427861598598774},{-35.99161292982694,24.13519728711021},{-26.14564182511713,24.13519728711021}},color={0,0,127}));
  connect(controller_crkB.pump,Module.ActuatorPumpUser_B)
    annotation (Line(points={{20.877596756835317,4.266347038854235},{10.40220664561738,4.266347038854235},{10.40220664561738,24.13519728711021},{-4.145641825117133,24.13519728711021}},color={0,0,127}));
  connect(Module.Fan_controller_B,controller_crkB.fan)
    annotation (Line(points={{-4.145641825117133,14.63519728711021},{10.40220664561738,14.63519728711021},{10.40220664561738,6.766347038854235},{20.877596756835317,6.766347038854235}},color={0,0,127}));
  connect(Module.EXV_controller_B,controller_crkB.exv)
    annotation (Line(points={{-4.145641825117133,19.63519728711021},{10.40220664561738,19.63519728711021},{10.40220664561738,9.266347038854235},{20.877596756835317,9.266347038854235}},color={0,0,127}));
  connect(Module.Compressor_controller_B,controller_crkB.compressor)
    annotation (Line(points={{-4.145641825117133,9.63519728711021},{10.40220664561738,9.63519728711021},{10.40220664561738,11.766347038854235},{20.877596756835317,11.766347038854235}},color={0,0,127}));
  connect(controllerSettings_crkA.measurementBus,Module.measurementBusA)
    annotation (Line(points={{-81.48150413305495,40.82205673936891},{-23.14564182511713,40.82205673936891},{-23.14564182511713,27.13519728711021}},color={255,204,51}));
  connect(Module.coolant_in,inlet)
    annotation (Line(points={{-15.538750494882521,2.464602644564838},{-15.538750494882521,-24.335297321272684},{-14.404909738719214,-24.335297321272684},{-14.404909738719214,-50.7852707838424}},color={0,127,0}));
  connect(outlet,Module.coolant_out)
    annotation (Line(points={{-14,92.19990902251452},{-14,78.20300453996053},{-15.335655247676426,78.20300453996053},{-15.335655247676426,28.28627150452148}},color={0,127,0}));
  connect(capacity_total,controller_crkA.measurementBus.capacity)
    annotation (Line(points={{-149.58820309059726,-31.342817834629116},{-89.62381534761826,-31.342817834629116},{-89.62381534761826,13.142786159859877},{-70.91004239405333,13.142786159859877}},color={0,0,127}));
  connect(capacity_total,controller_crkB.measurementBus.capacity)
    annotation (Line(points={{-149.58820309059726,-31.342817834629116},{52.165016424179804,-31.342817834629116},{52.165016424179804,13.766347038854235},{41.87759675683532,13.766347038854235}},color={0,0,127}));
  connect(Module.measurementBusB,controllerSettings_crkB.measurementBus)
    annotation (Line(points={{-7.145641825117133,27.13519728711021},{-7.145641825117133,40.99059642133661},{49.78236321898647,40.99059642133661}},color={255,204,51}));
  connect(Module.measurementBusA,measurementBus)
    annotation (Line(points={{-23.14564182511713,27.13519728711021},{-23.14564182511713,63.91604947502325},{-98.80703337193755,63.91604947502325},{-98.80703337193755,99.8320989500465}},color={255,204,51}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name"),
        Rectangle(
          extent={{-100,100},{100,-100}},
          fillPattern=FillPattern.Solid,
          fillColor={80,32,135}),
        Text(
          textString="Module",
          origin={0,-7},
          extent={{-104,33},{104,-33}},
          lineColor={255,255,255})}));
end Modular_61AQ;
