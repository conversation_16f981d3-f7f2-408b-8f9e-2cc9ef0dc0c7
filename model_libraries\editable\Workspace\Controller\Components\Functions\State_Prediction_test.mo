within Workspace.Controller.Components.Functions;
model State_Prediction_test
  extends.Modelica.Icons.Function;
  parameter Real freq_compressor=139.97;
  parameter Integer N_module=4;
  parameter Real FreqTransition_1_2=102.03;
  parameter Real FreqTransition_2_3=76.26;
  parameter Real FreqTransition_3_4=67.3;
  parameter Real CapDesign_1=140;
  parameter Real CapDesign_2=140;
  parameter Real CapDesign_3=120;
  parameter Real CapDesign_4=120;
  parameter Real Target_cap=153.60;
  parameter Real Cap_1unit=120.11;
  parameter Boolean notMode1=false;
  Real Stateid;
equation
  Stateid=Workspace.Controller.Components.Functions.State_prediction(
    freq_compressor,
    N_module,
    FreqTransition_1_2,
    FreqTransition_2_3,
    FreqTransition_3_4,
    CapDesign_1,
    CapDesign_2,
    CapDesign_3,
    CapDesign_4,
    Target_cap,
    Cap_1unit,
    notMode1,
    15);
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          fillColor={144,19,254},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name"),
        Text(
          textString="TEST",
          origin={0,44},
          extent={{74,-26},{-74,26}})}));
end State_Prediction_test;
