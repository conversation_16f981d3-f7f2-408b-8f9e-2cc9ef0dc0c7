within Workspace.Auxiliary;
model FanSignalProcessor
  "Processor fan signal based on specific fan option"
  import BOLT.InternalLibrary.BuildingBlocks.Utilities.Math.Smoothing .*;
  Modelica.Blocks.Interfaces.RealInput ControllerSignal
    annotation (Placement(transformation(extent={{-120,-10},{-80,30}})));
  Modelica.Blocks.Interfaces.RealOutput SetPoint_ECmotor
    annotation (Placement(transformation(extent={{80,32},{100,52}})));
  parameter Boolean is_AC=false
    "if EC then false, AC then true";
  parameter Modelica.SIunits.Conversions.NonSIunits.AngularVelocity_rpm max_fan_rpm=720
    "maximum fan speed";
  parameter Modelica.SIunits.Frequency max_motor_frequency=50
    "maximum motor frequency";
  Modelica.SIunits.Frequency actual_motor_frequency=actual_FSFanSpd/950*50;
  Modelica.Blocks.Interfaces.RealOutput SetPoint_ACmotor
    annotation (Placement(transformation(extent={{80,64},{100,84}})));
  Modelica.Blocks.Interfaces.RealInput actual_FSFanSpd
    annotation (Placement(transformation(extent={{-14,-14},{14,14}},rotation=-90,origin={-10,104})));
equation
  SetPoint_ECmotor=max_fan_rpm*ControllerSignal;
  SetPoint_ACmotor=max_motor_frequency*ControllerSignal;
  //SetPoint_fan =
  //if is_fixedSpeed then softMin(1,ControllerSignal*max_motor_frequency/actual_motor_frequency,1e-3)
  //else 1;
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false)),
    Diagram(
      coordinateSystem(
        preserveAspectRatio=false)));
end FanSignalProcessor;
